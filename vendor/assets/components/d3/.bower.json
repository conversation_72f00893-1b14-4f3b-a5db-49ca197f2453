{"name": "d3", "version": "3.4.13", "main": "d3.js", "scripts": ["d3.js"], "ignore": [".DS_Store", ".git", ".giti<PERSON>re", ".n<PERSON><PERSON><PERSON>", ".travis.yml", "<PERSON><PERSON><PERSON>", "bin", "component.json", "index.js", "lib", "node_modules", "package.json", "src", "test"], "homepage": "https://github.com/mbostock/d3", "_release": "3.4.13", "_resolution": {"type": "version", "tag": "v3.4.13", "commit": "e2dc80f5385c153066150075a4208131e0b78c68"}, "_source": "git://github.com/mbostock/d3.git", "_target": "~3.4.1", "_originalSource": "d3"}