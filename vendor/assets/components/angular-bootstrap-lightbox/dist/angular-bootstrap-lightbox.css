.lightbox-nav {
  position: relative;
  margin-bottom: 12px; /* the font-size of .btn-xs */
  text-align: top;
  /*font-size: 0;  prevent the otherwise inherited font-size and line-height from adding extra space to the bottom of this div */
}

.lightbox-nav .btn-group {
  vertical-align: top;
}

.glyphicon-remove-circle {
  font-size: 1.5em;
  margin-right: 15px;
}

.lightbox-nav .close {
  /* absolutely position this in order to center the nav buttons */
  position: absolute;
  top: -5px;
  right: 3px;
  height: 25px;
  width: 25px;
  padding-bottom: 5px;
  margin-top: 0;
  border: 1px solid black;
  border-radius: 15px;
  background:rgba(180,180, 180, 0.7);
  &:hover {
    background:rgba(255,255, 255, 0.9);
  }
}

.lightbox-image-container .btn-circular {
  vertical-align: middle;
}

button.btn-circular {
  position: relative;
  top: 50%;
}

.btn-circular {
  margin-left: 50px;
  margin-right: 50px;
  height: 25px;
  width: 25px;
  padding-bottom: 5px;
  margin-top: 0;
  border: 1px solid grey;
  border-radius: 15px;
  vertical-align: middle;
  background:rgba(180,180, 180, 0.7);
  &:hover {
    background:rgba(255,255, 255, 0.9);
  }
}

.btn-circular:focus {
  outline: none;
}

.lightbox-image-container {
  position: relative;
  text-align: center; /* center the image */
}

/* the caption overlays the top left corner of the image */
.lightbox-image-caption {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0.5em 0.9em; /* the left and right margins are offset by 0.4em for the span box-shadow */
  color: #000;
  font-size: 1.4em;
  line-height: 1em;
  font-weight: bold;
  text-align: left;
  text-shadow: 0.1em 0.1em 0.2em rgba(255, 255, 255, 0.5);
}

.lightbox-image-caption span {
  padding-top: 0.1em;
  padding-bottom: 0.1em;
  /*background-color: rgba(255, 255, 255, 0.75);*/
  /* pad the left and right of each line of text */
  /*box-shadow: 0.4em 0 0 rgba(255, 255, 255, 0.75),
    -0.4em 0 0 rgba(255, 255, 255, 0.75);*/
}
