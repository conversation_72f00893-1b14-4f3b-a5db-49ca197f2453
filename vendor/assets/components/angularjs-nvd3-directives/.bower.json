{"name": "angularjs-nvd3-directives", "version": "0.0.7", "description": "Angular.js directives for nvd3.js.", "authors": ["<PERSON> Ma<PERSON>"], "license": "Apache License, v2.0", "homepage": "http://cmaurer.github.io/angularjs-nvd3-directives/", "main": "./dist/angularjs-nvd3-directives.js", "keywords": ["d3", "nvd3", "angular", "directives", "visualization", "svg", "charts"], "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"], "dependencies": {"angular": "~1.2.4", "d3": "~3.4.1", "nvd3": "~v1.1.15-beta"}, "devDependencies": {"moment": "~2.5.0", "angular-route": "~1.2.13"}, "_release": "0.0.7", "_resolution": {"type": "version", "tag": "v0.0.7", "commit": "2e9ddf7a4204c76664f2f1a9dd242ceed78855c8"}, "_source": "git://github.com/cmaurer/angularjs-nvd3-directives.git", "_target": "*", "_originalSource": "angularjs-nvd3-directives"}