<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <title>Scatter Chart Example</title>
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <script>
        var app = angular.module("nvd3TestApp", ['nvd3ChartDirectives']);

        function ExampleCtrl($scope){

            var getData = function(groups, points) {
                var data = [],
                        shapes = ['circle', 'cross', 'triangle-up', 'triangle-down', 'diamond', 'square'],
                        random = d3.random.normal();

                for (i = 0; i < groups; i++) {
                    data.push({
                        key: 'Group ' + i,
                        values: []
                    });

                    for (j = 0; j < points; j++) {
                        data[i].values.push({
                            x: random()
                            , y: random()
                            , size: Math.random()
                            //, shape: shapes[j % 6]
                        });
                    }
                }
                return data;
            }

            $scope.exampleData =  getData(4, 40);

            $scope.xaxislabel = 'initial x label';
            $scope.yaxislabel = 'initial y label';

            $scope.tooltipXContentFunction = function(){
                return function(key, x, y) {
                    return '<strong>YO!' + x + '</strong>'
                }
            }

            $scope.getShapeCross = function(){
                return function(d){
                    return 'cross';
                }
            }

            $scope.getShapeDiamond = function(){
                return function(d){
                    return 'diamond';
                }
            }

            $scope.setData = function (id) {
                if (id == 1) {
                    $scope.exampleData =  getData(8, 20);
                    $scope.xaxislabel = 'x label for set 1';
                    $scope.yaxislabel = 'y label for set 1';
                } else {
                    $scope.exampleData =  getData(2, 10);
                    $scope.xaxislabel = 'x label for set 2';
                    $scope.yaxislabel = 'y label for set 2';
                }
            }

        };

    </script>

</head>
<body ng-app='nvd3TestApp'>

<div ng-controller="ExampleCtrl">
    <nvd3-scatter-chart
            data="exampleData"
            id="exampleId"
            xaxislabel="{{ xaxislabel }}"
            yaxislabel="{{ yaxislabel }}"
            width="600"
            height="300"
            margin="{left:100,top:50,bottom:50,right:50}"
            tooltips="true"
            interactive="true"
            tooltipContent="tooltipXContentFunction()"
            shape="getShapeCross()">
        <svg style="height: 300px;"></svg>
    </nvd3-scatter-chart>
    <button ng-click="setData(1)">load data 1</button>
    <button ng-click="setData(2)">load data 2</button>
</div>



</body>
</html>