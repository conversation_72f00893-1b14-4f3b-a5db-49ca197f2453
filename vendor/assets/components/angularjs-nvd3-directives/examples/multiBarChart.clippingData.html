<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <title>Angular.js nvd3.js Line Chart Directive</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <script>
        var app = angular.module("nvd3TestApp", ['nvd3ChartDirectives']);

        function ExampleCtrl($scope){
            $scope.exampleData = [
                {
                    "key": "Series 1",
                    "values": [ [ 1025409600000 , 0.5] , [ 1028088000000 , 8.5] , [ 1030766400000 , 6]  ]
                }
            ]

            $scope.xFunction = function(){
                return function(d){
                    return d[0];
                }
            }

            $scope.yFunction = function(){
                return function(d){
                    return d[1];
                }
            }

            $scope.xAxisTickFormatFunction = function(){
                return function(d){
                    return d3.time.format('%x')(new Date(d));
                }
            }
        }


    </script>

</head>
<body ng-app='nvd3TestApp'>

<div ng-controller="ExampleCtrl">
    <nvd3-multi-bar-chart
            data="exampleData"
            id="exampleId"
            width="960"
            height="400"
            forcey="[0]"
            x="xFunction()"
            y="yFunction()"
            yAxisShowMaxMin="true"
            xAxisShowMaxMin="true"
            xAxisTickFormat="xAxisTickFormatFunction()"
            showLegend="false"
            showControls="false"
           >
        <svg></svg>
    </nvd3-multi-bar-chart>
</div>

</body>
</html>