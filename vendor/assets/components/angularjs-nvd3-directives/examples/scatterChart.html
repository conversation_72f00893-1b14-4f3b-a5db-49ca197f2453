<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <title>Scatter Chart Example</title>
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <script>
        var app = angular.module("nvd3TestApp", ['nvd3ChartDirectives']);

        function ExampleCtrl($scope){

            var getData = function(groups, points) {
                var data = [],
                        shapes = ['circle', 'cross', 'triangle-up', 'triangle-down', 'diamond', 'square'],
                        random = d3.random.normal();

                for (i = 0; i < groups; i++) {
                    data.push({
                        key: 'Group ' + i,
                        values: []
                    });

                    for (j = 0; j < points; j++) {
                        data[i].values.push({
                            x: random()
                            , y: random()
                            , size: Math.random()
                            //, shape: shapes[j % 6]
                        });
                    }
                }
                return data;
            }

            $scope.exampleData =  getData(4, 40);

            $scope.tooltipXContentFunction = function(){
                return function(key, x, y) {
                    return '<strong>YO!' + x + '</strong>'
                }
            }

            $scope.getShapeCross = function(){
                return function(d){
                    return 'cross';
                }
            }

            $scope.getShapeDiamond = function(){
                return function(d){
                    return 'diamond';
                }
            }

            $scope.getShapeDiamond = function(){
                return function(d){
                    return 'diamond';
                }
            }

        };

    </script>

</head>
<body ng-app='nvd3TestApp'>

<div ng-controller="ExampleCtrl">
    <nvd3-scatter-chart
            data="exampleData"
            id="exampleId"
            width="600"
            height="300"
            margin="{left:150,top:100,bottom:100,right:150}"
            tooltips="true"
            interactive="true"
            tooltipContent="tooltipXContentFunction()"
            shape="getShapeCross()">
        <svg></svg>
    </nvd3-scatter-chart>
</div>



</body>
</html>