<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <title>Angular.js nvd3.js Line Chart Directive</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <script src="js/ui-bootstrap-0.6.0.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <link href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.3.1/css/bootstrap.min.css" rel="stylesheet"/>
    <script>
        var app = angular.module("nvd3TestApp", ['ui.bootstrap', 'nvd3ChartDirectives']);

        function IndicatorsCtrl($scope){
            $scope.indicatorsData = [
                {
                    "key": "Critical",
                    "values": [ [ "A" , 45], [ "B" , 80] ]
                },
                {
                    "key": "Non-Critical",
                    "values": [ [ "A" , 150], [ "B" , 40] ]
                }
            ];

            $scope.tabs = [
                { title:"Dynamic Title 1", content:"Dynamic content 1" }
            ];

            $scope.refresh = function(chartid){
                nv.graphs[0].update();
            }

        }
    </script>

</head>
<body ng-app='nvd3TestApp'>

<div ng-controller="IndicatorsCtrl">

    <tabset>
        <tab heading="Static title">Static content</tab>
        <tab ng-repeat="tab in tabs" heading="{{tab.title}}" active="tab.active" disabled="tab.disabled" select="refresh('id1')">
            <!-- nvd3-multi-bar-chart data="indicatorsData" id="id1" width="800" height="400" showlegend="true" showcontrols="true" -->
            <nvd3-multi-bar-chart data="indicatorsData" id="id1" width="800" height="400" showlegend="true" showcontrols="true"/>
        </tab>
    </tabset>
    <hr />


</div>



</body>
</html>