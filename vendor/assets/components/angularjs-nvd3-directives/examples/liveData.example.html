<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <title>Angular.js nvd3.js Live Data Chart Example</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="js/moment.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <script>
        var app = angular.module("nvd3TestApp", ['nvd3ChartDirectives']);

        app.config(['$httpProvider', function($httpProvider) {
            $httpProvider.defaults.useXDomain = true;
            delete $httpProvider.defaults.headers.common['X-Requested-With'];
            }
        ]);


        app.factory('openWeatherService', ['$http', function($http){
                var getForecastForCityId = function(cityId, units){
                    return $http({
                        method: 'JSONP',
                        url: 'http://api.openweathermap.org/data/2.5/forecast?id='+cityId+'&units='+units+'&callback=JSON_CALLBACK'
                    });
                }
                return{
                    getForecast: function(cityId, units){return getForecastForCityId(cityId, units);}
                }
        }]);

        app.factory('forecast', function(){
           return {
               cityId: 0,
               units: 'imperial'
           }
        });

        function ExampleCtrl($scope, openWeatherService, forecast){
            function fetchData(){
                openWeatherService.getForecast(5506956, 'imperial')
                        .success(function(response){
                            console.log(response);
                            var dta = [{key:"Las Vegas Weather", values:[]}];
                                dta[0].values = response.list.map(function(d){
                                return [d.dt, d.main.temp];
                            });
                            $scope.exampleData = dta;
                        });
            }

            fetchData();

            $scope.xAxisTickFormatFunction = function(){
                return function(d){
                    return d3.time.format('%x-%H:%M')(new Date(d*1000));
                }
            }


        }
    </script>
    <style>
        div{
            font-family: sans-serif;
        }
    </style>
</head>
<body ng-app='nvd3TestApp'>

<div ng-controller="ExampleCtrl">

    <input type="text" ng-model="cityName"/>

    <div>Las Vegas, Nevada - Temperature Forecast</div>
    <div style="font-size: 10px">Weather Data Provided by <a href="http://api.openweathermap.org">http://api.openweathermap.org</a></div>
    <nvd3-line-chart
            data="exampleData"
            id="exampleId"
            width="800"
            height="400"
            showXAxis="true"
            showYAxis="true"
            tooltips="true"
            interactive="true"
            xAxisTickFormat="xAxisTickFormatFunction()"
            margin="{left:100,top:20,bottom:20,right:10}"
            yAxisLabel="Temperature (F)"
            xAxisLabel="Date"
            >
        <svg></svg>
    </nvd3-line-chart>
</div>

</body>
</html>