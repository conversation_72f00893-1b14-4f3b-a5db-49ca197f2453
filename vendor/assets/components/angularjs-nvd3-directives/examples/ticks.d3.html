<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <script src="js/d3.js" charset="utf-8"></script>
    <script src="js/nv.d3.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css" />

</head>

<body>

<div id="test"><svg height="400"></svg></div>

<script>

    function renderChart(chart, data, container) {
        d3.select(container)
                .datum(data)
                .transition().duration(1000).call(chart);

        nv.utils.windowResize(
                function() {
                    chart.update();
                }
        );
    }

    function createMultiBarChartByDate(data, container) {

        var chart = nv.models.multiBarChart()
                .width(500)
                .height(200)
                .x(function(d,i) { return d[0] })
                .y(function(d) { return d[1] })
                .margin({left:150,top:10,right:10,bottom:20});

        chart.showLegend(false);
//        chart.showControls(false);

        chart.yAxis.axisLabel('Y Axis Label')
        chart.xAxis.axisLabel('X Axis Label')
        chart.xAxis.rotateLabels(90)
        chart.xAxis = chart.xAxis
                .height(500)
                .highlightZero(true)
//            .showMaxMin(true)
//            .tickFormat(function(d) {
//                return d3.time.format('%H:%M')(new Date(d * 1000));
//            })
//                .tickSize(24);
        renderChart(chart, data, container);
        return chart;

    }

    var dta = [
        {
            "key": "Series 1",
            "values": [[1378499400.0, 42.48492777777778], [1378499700.0, 47.88234], [1378500000.0, 44.84776333333333], [1378500300.0, 47.109006666666666], [1378500600.0, 43.58863666666666], [1378500900.0, 47.441563333333335], [1378501200.0, 49.18689666666666], [1378501500.0, 54.136046666666665], [1378501800.0, 45.91582], [1378502100.0, 44.993363333333335]]
        }
    ];

    createMultiBarChartByDate(dta, '#test svg');
//            "values": [[1378499400.0, 42.48492777777778], [1378499700.0, 47.88234], [1378500000.0, 44.84776333333333], [1378500300.0, 47.109006666666666], [1378500600.0, 43.58863666666666], [1378500900.0, 47.441563333333335], [1378501200.0, 49.18689666666666], [1378501500.0, 54.136046666666665], [1378501800.0, 45.91582], [1378502100.0, 44.993363333333335], [1378502400.0, 47.11236666666666], [1378502700.0, 45.64142666666667], [1378503000.0, 42.54392666666667], [1378503300.0, 44.25646999999999], [1378503600.0, 42.615916666666664], [1378503900.0, 44.68276], [1378504200.0, 41.195029999999996], [1378504500.0, 43.10806], [1378504800.0, 40.17754333333333], [1378505100.0, 41.70777666666667], [1378505400.0, 37.74328], [1378505700.0, 41.09789333333334], [1378506000.0, 35.19004666666667], [1378506300.0, 37.66643666666666], [1378506600.0, 36.94606], [1378506900.0, 48.45341666666667], [1378507200.0, 50.07483333333333], [1378507500.0, 41.8583], [1378507800.0, 35.19555666666667], [1378508100.0, 39.42828333333333], [1378508400.0, 38.777773333333336], [1378508700.0, 38.21801], [1378509000.0, 34.11682333333333], [1378509300.0, 37.42897333333333], [1378509600.0, 33.81621666666667], [1378509900.0, 37.66354333333333], [1378510200.0, 34.79157333333334], [1378510500.0, 39.96856666666666], [1378510800.0, 35.35819333333333], [1378511100.0, 38.852086666666665], [1378511400.0, 34.12006], [1378511700.0, 38.587740000000004], [1378512000.0, 36.727540000000005], [1378512300.0, 49.304359999999996], [1378512600.0, 35.824576666666665], [1378512900.0, 38.041573333333325], [1378513200.0, 33.37989666666666], [1378513500.0, 37.452906666666664], [1378513800.0, 34.32699666666667], [1378514100.0, 37.194336666666665], [1378514400.0, 36.135200000000005], [1378514700.0, 39.13176], [1378515000.0, 34.904226666666666], [1378515300.0, 39.11863], [1378515600.0, 35.71040000000001], [1378515900.0, 44.32563], [1378516200.0, 43.52562333333333], [1378516500.0, 43.813386666666666], [1378516800.0, 34.273073333333336], [1378517100.0, 37.39768333333334], [1378517400.0, 32.85422333333334], [1378517700.0, 37.95509], [1378518000.0, 35.641099999999994], [1378518300.0, 38.68072333333334], [1378518600.0, 34.28395666666667], [1378518900.0, 39.11672333333333], [1378519200.0, 36.52790666666667], [1378519500.0, 38.57375], [1378519800.0, 43.625463333333336], [1378520100.0, 37.89967666666667], [1378520400.0, 32.451593333333335], [1378520700.0, 38.51917666666667], [1378521000.0, 35.60178333333333], [1378521300.0, 38.31116], [1378521600.0, 36.037013333333334], [1378521900.0, 38.339863333333334], [1378522200.0, 36.19885], [1378522500.0, 37.43489], [1378522800.0, 37.882466666666666], [1378523100.0, 37.26560333333333], [1378523400.0, 35.580276666666656], [1378523700.0, 36.288673333333335], [1378524000.0, 34.83732333333334], [1378524300.0, 35.63569], [1378524600.0, 36.89978666666667], [1378524900.0, 35.193356666666666], [1378525200.0, 36.76336], [1378525500.0, 35.86881666666667], [1378525800.0, 36.50062], [1378526100.0, 35.84009999999999], [1378526400.0, 38.39757], [1378526700.0, 38.63100333333334], [1378527000.0, 43.111059999999995], [1378527300.0, 37.64858], [1378527600.0, 35.73519], [1378527900.0, 34.93261], [1378528200.0, 36.60717666666666], [1378528500.0, 35.07233], [1378528800.0, 36.65923333333333], [1378529100.0, 36.76741666666667], [1378529400.0, 36.25996666666667], [1378529700.0, 35.639536666666665], [1378530000.0, 39.73153333333333], [1378530300.0, 36.71425333333333], [1378530600.0, 35.98138], [1378530900.0, 36.00459666666667], [1378531200.0, 34.82700333333334], [1378531500.0, 35.495596666666664], [1378531800.0, 36.91672], [1378532100.0, 35.89139], [1378532400.0, 34.695840000000004], [1378532700.0, 37.31284333333333], [1378533000.0, 34.9039], [1378533300.0, 37.543303333333334], [1378533600.0, 37.16708666666667], [1378533900.0, 49.969449999999995], [1378534200.0, 34.837646666666664], [1378534500.0, 44.482119999999995], [1378534800.0, 33.00140333333333], [1378535100.0, 36.78384], [1378535400.0, 34.18490666666667], [1378535700.0, 36.85124666666666], [1378536000.0, 34.02258666666667], [1378536300.0, 35.72992], [1378536600.0, 35.82659666666667], [1378536900.0, 38.41800333333333], [1378537200.0, 37.86895666666667], [1378537500.0, 37.99196666666667], [1378537800.0, 33.24258], [1378538100.0, 37.49733666666666], [1378538400.0, 33.581540000000004], [1378538700.0, 36.86904666666667], [1378539000.0, 36.61933333333334], [1378539300.0, 38.955740000000006], [1378539600.0, 36.37692333333333], [1378539900.0, 40.73408666666667], [1378540200.0, 36.06186], [1378540500.0, 39.54541999999999], [1378540800.0, 36.50673], [1378541100.0, 40.60429333333333], [1378541400.0, 53.92896333333333], [1378541700.0, 41.564370000000004], [1378542000.0, 37.93267], [1378542300.0, 39.08100333333333], [1378542600.0, 36.23848666666667], [1378542900.0, 41.453603333333334], [1378543200.0, 35.49902], [1378543500.0, 40.09918333333333], [1378543800.0, 38.15365333333334], [1378544100.0, 40.55233333333334], [1378544400.0, 37.29647333333333], [1378544700.0, 38.48210666666667], [1378545000.0, 36.251983333333335], [1378545300.0, 39.58111], [1378545600.0, 36.77090666666667], [1378545900.0, 40.754736666666666], [1378546200.0, 35.36837], [1378546500.0, 39.791623333333334], [1378546800.0, 37.31489], [1378547100.0, 40.52099666666667], [1378547400.0, 37.38050333333334], [1378547700.0, 40.72969], [1378548000.0, 37.55632333333334], [1378548300.0, 39.763720000000006], [1378548600.0, 53.59684], [1378548900.0, 42.09791333333333], [1378549200.0, 35.76724], [1378549500.0, 39.16517666666667], [1378549800.0, 36.434], [1378550100.0, 38.81169666666666], [1378550400.0, 37.66773333333333], [1378550700.0, 39.604256666666664], [1378551000.0, 38.28234333333333], [1378551300.0, 38.9794], [1378551600.0, 36.00511333333334], [1378551900.0, 38.58762333333333], [1378552200.0, 35.48797], [1378552500.0, 38.62863], [1378552800.0, 36.21290333333334], [1378553100.0, 39.26557666666666], [1378553400.0, 36.543400000000005], [1378553700.0, 39.586976666666665], [1378554000.0, 37.1197], [1378554300.0, 40.64621666666667], [1378554600.0, 38.14898], [1378554900.0, 42.625703333333334], [1378555200.0, 37.884856666666664], [1378555500.0, 50.98418333333333], [1378555800.0, 41.01629666666667], [1378556100.0, 37.06437], [1378556400.0, 34.67540666666667], [1378556700.0, 37.71212666666666], [1378557000.0, 35.19329666666666], [1378557300.0, 36.87242666666667], [1378557600.0, 37.34919], [1378557900.0, 39.14329333333333], [1378558200.0, 37.608243333333334], [1378558500.0, 40.43891333333333], [1378558800.0, 39.102023333333335], [1378559100.0, 37.881396666666674], [1378559400.0, 36.0796], [1378559700.0, 40.14324333333334], [1378560000.0, 36.39974333333333], [1378560300.0, 38.19493000000001], [1378560600.0, 38.23703333333333], [1378560900.0, 65.69198], [1378561200.0, 45.65651999999999], [1378561500.0, 38.266239999999996], [1378561800.0, 37.088296666666665], [1378562100.0, 40.761070000000004], [1378562400.0, 40.203183333333335], [1378562700.0, 39.90282666666666], [1378563000.0, 35.178673333333336], [1378563300.0, 36.39866333333334], [1378563600.0, 34.43063333333333], [1378563900.0, 36.79238333333333], [1378564200.0, 34.71053666666666], [1378564500.0, 37.17414333333333], [1378564800.0, 37.020093333333335], [1378565100.0, 38.59335333333333], [1378565400.0, 35.756733333333344], [1378565700.0, 39.345866666666666], [1378566000.0, 38.89345333333333], [1378566300.0, 39.41156333333333], [1378566600.0, 34.78255333333333], [1378566900.0, 37.63054666666667], [1378567200.0, 34.064663333333336], [1378567500.0, 36.294536666666666], [1378567800.0, 35.95910666666667], [1378568100.0, 36.99800666666667], [1378568400.0, 37.326843333333336], [1378568700.0, 38.65357], [1378569000.0, 35.28346666666667], [1378569300.0, 38.32443333333333], [1378569600.0, 39.773563333333335], [1378569900.0, 37.84119], [1378570200.0, 35.17482666666667], [1378570500.0, 35.902379999999994], [1378570800.0, 36.785446666666665], [1378571100.0, 37.06063666666667], [1378571400.0, 35.99228], [1378571700.0, 35.79807], [1378572000.0, 36.792683333333336], [1378572300.0, 39.48568], [1378572600.0, 36.78885666666667], [1378572900.0, 38.41809333333333], [1378573200.0, 38.94006666666667], [1378573500.0, 36.821643333333334], [1378573800.0, 34.53883333333333], [1378574100.0, 36.02654333333333], [1378574400.0, 34.60578333333333], [1378574700.0, 36.19274666666667], [1378575000.0, 36.74134], [1378575300.0, 36.70950666666666], [1378575600.0, 36.17396], [1378575900.0, 37.71935666666666], [1378576200.0, 36.51696666666667], [1378576500.0, 38.81339333333334], [1378576800.0, 39.01378333333333], [1378577100.0, 38.60122666666666], [1378577400.0, 35.33431], [1378577700.0, 36.19998666666667], [1378578000.0, 35.565333333333335], [1378578300.0, 36.56986333333333], [1378578600.0, 35.71168], [1378578900.0, 36.25843666666667], [1378579200.0, 36.149053333333335], [1378579500.0, 37.69941999999999], [1378579800.0, 36.30086333333334], [1378580100.0, 38.892849999999996], [1378580400.0, 39.014636666666675], [1378580700.0, 39.50131333333333], [1378581000.0, 35.74019333333332], [1378581300.0, 36.63977333333333], [1378581600.0, 34.38661999999999], [1378581900.0, 37.281789999999994], [1378582200.0, 36.05694], [1378582500.0, 37.65333666666667], [1378582800.0, 36.822739999999996], [1378583100.0, 39.25359999999999], [1378583400.0, 36.39669666666667], [1378583700.0, 37.53325], [1378584000.0, 38.99477333333333], [1378584300.0, 40.02987], [1378584600.0, 35.28563666666666], [1378584900.0, 35.86883333333333], [1378585200.0, 33.848620000000004], [1378585500.0, 35.94095], [1378585800.0, 35.94095]]


</script>
</body>

</html>
