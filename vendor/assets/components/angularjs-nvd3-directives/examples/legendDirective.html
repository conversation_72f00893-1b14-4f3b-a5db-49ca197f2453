<!DOCTYPE html>
<meta charset="utf-8">
<html>
<head>
    <title>Angular.js nvd3.js Legend Directive</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF8">
    <script src="js/angular.js"></script>
    <script src="js/d3.js"></script>
    <script src="js/nv.d3.js"></script>
    <script src="js/moment.js"></script>
    <script src="../dist/angularjs-nvd3-directives.js"></script>
    <script src="../src/directives/legendDirectives.js"></script>
    <link rel="stylesheet" href="stylesheets/nv.d3.css"/>
    <style>

        .legend-bullet-1 {
            fill: gray;
        }
        .legend-bullet-2 {
            fill: blue;
        }
        .legend-bullet-3 {
            fill: red;
        }
    </style>
    <script>
        var app = angular.module("legendTestApp", ['legendDirectives']);

        function LegendController($scope){



        }

    </script>

</head>
<body  ng-app='legendTestApp'>
<div ng-controller="LegendController">
    <simple-svg-legend
        width="50"
        height="20"
        labels="['label1', 'label2', 'label 3']"
        classes="['legend-bullet-1','legend-bullet-2','legend-bullet-3']"
        shapes="['circle','circle', 'circle']"
        columns="3"
        x="10"
        y="0">
    </simple-svg-legend>
</div>

</body>
</html>