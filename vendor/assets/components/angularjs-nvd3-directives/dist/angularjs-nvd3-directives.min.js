!function(){"use strict";function initializeLegendMargin(scope,attrs){var margin=scope.$eval(attrs.legendmargin)||{left:0,top:5,bottom:5,right:0};"object"!=typeof margin&&(margin={left:margin,top:margin,bottom:margin,right:margin}),scope.legendmargin=margin}function configureLegend(chart,scope,attrs){chart.legend&&attrs.showlegend&&"true"===attrs.showlegend&&(initializeLegendMargin(scope,attrs),chart.legend.margin(scope.legendmargin),chart.legend.width(void 0===attrs.legendwidth?400:+attrs.legendwidth),chart.legend.height(void 0===attrs.legendheight?20:+attrs.legendheight),chart.legend.key(void 0===attrs.legendkey?function(d){return d.key}:scope.legendkey()),chart.legend.color(void 0===attrs.legendcolor?nv.utils.defaultColor():scope.legendcolor()),chart.legend.align(void 0===attrs.legendalign?!0:"true"===attrs.legendalign),chart.legend.rightAlign(void 0===attrs.legendrightalign?!0:"true"===attrs.legendrightalign),chart.legend.updateState(void 0===attrs.legendupdatestate?!0:"true"===attrs.legendupdatestate),chart.legend.radioButtonMode(void 0===attrs.legendradiobuttonmode?!1:"true"===attrs.legendradiobuttonmode))}function processEvents(chart,scope){chart.dispatch&&(chart.dispatch.tooltipShow&&chart.dispatch.on("tooltipShow.directive",function(event){scope.$emit("tooltipShow.directive",event)}),chart.dispatch.tooltipHide&&chart.dispatch.on("tooltipHide.directive",function(event){scope.$emit("tooltipHide.directive",event)}),chart.dispatch.beforeUpdate&&chart.dispatch.on("beforeUpdate.directive",function(event){scope.$emit("beforeUpdate.directive",event)}),chart.dispatch.stateChange&&chart.dispatch.on("stateChange.directive",function(event){scope.$emit("stateChange.directive",event)}),chart.dispatch.changeState&&chart.dispatch.on("changeState.directive",function(event){scope.$emit("changeState.directive",event)})),chart.lines&&(chart.lines.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.lines.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseout.tooltip.directive",event)}),chart.lines.dispatch.on("elementClick.directive",function(event){scope.$emit("elementClick.directive",event)})),chart.stacked&&chart.stacked.dispatch&&(chart.stacked.dispatch.on("areaClick.toggle.directive",function(event){scope.$emit("areaClick.toggle.directive",event)}),chart.stacked.dispatch.on("tooltipShow.directive",function(event){scope.$emit("tooltipShow.directive",event)}),chart.stacked.dispatch.on("tooltipHide.directive",function(event){scope.$emit("tooltipHide.directive",event)})),chart.interactiveLayer&&(chart.interactiveLayer.elementMouseout&&chart.interactiveLayer.dispatch.on("elementMouseout.directive",function(event){scope.$emit("elementMouseout.directive",event)}),chart.interactiveLayer.elementMousemove&&chart.interactiveLayer.dispatch.on("elementMousemove.directive",function(event){scope.$emit("elementMousemove.directive",event)})),chart.discretebar&&(chart.discretebar.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.discretebar.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.discretebar.dispatch.on("elementClick.directive",function(event){scope.$emit("elementClick.directive",event)})),chart.multibar&&(chart.multibar.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.multibar.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.multibar.dispatch.on("elementClick.directive",function(event){scope.$emit("elementClick.directive",event)})),chart.pie&&(chart.pie.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.pie.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.pie.dispatch.on("elementClick.directive",function(event){scope.$emit("elementClick.directive",event)})),chart.scatter&&(chart.scatter.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.scatter.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)})),chart.bullet&&(chart.bullet.dispatch.on("elementMouseover.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)}),chart.bullet.dispatch.on("elementMouseout.tooltip.directive",function(event){scope.$emit("elementMouseover.tooltip.directive",event)})),chart.legend&&(chart.legend.dispatch.on("stateChange.legend.directive",function(event){scope.$emit("stateChange.legend.directive",event)}),chart.legend.dispatch.on("legendClick.directive",function(d,i){scope.$emit("legendClick.directive",d,i)}),chart.legend.dispatch.on("legendDblclick.directive",function(d,i){scope.$emit("legendDblclick.directive",d,i)}),chart.legend.dispatch.on("legendMouseover.directive",function(d,i){scope.$emit("legendMouseover.directive",d,i)})),chart.controls&&chart.controls.legendClick&&chart.controls.dispatch.on("legendClick.directive",function(d,i){scope.$emit("legendClick.directive",d,i)})}function configureXaxis(chart,scope,attrs){attrs.xaxisorient&&chart.xAxis.orient(attrs.xaxisorient),attrs.xaxisticks&&chart.xAxis.scale().ticks(attrs.xaxisticks),attrs.xaxistickvalues&&(Array.isArray(scope.$eval(attrs.xaxistickvalues))?chart.xAxis.tickValues(scope.$eval(attrs.xaxistickvalues)):"function"==typeof scope.xaxistickvalues()&&chart.xAxis.tickValues(scope.xaxistickvalues())),attrs.xaxisticksubdivide&&chart.xAxis.tickSubdivide(scope.xaxisticksubdivide()),attrs.xaxisticksize&&chart.xAxis.tickSize(scope.xaxisticksize()),attrs.xaxistickpadding&&chart.xAxis.tickPadding(scope.xaxistickpadding()),attrs.xaxistickformat&&chart.xAxis.tickFormat(scope.xaxistickformat()),attrs.xaxislabel&&chart.xAxis.axisLabel(attrs.xaxislabel),attrs.xaxisscale&&chart.xAxis.scale(scope.xaxisscale()),attrs.xaxisdomain&&(Array.isArray(scope.$eval(attrs.xaxisdomain))?chart.xDomain(scope.$eval(attrs.xaxisdomain)):"function"==typeof scope.xaxisdomain()&&chart.xDomain(scope.xaxisdomain())),attrs.xaxisrange&&(Array.isArray(scope.$eval(attrs.xaxisrange))?chart.xRange(scope.$eval(attrs.xaxisrange)):"function"==typeof scope.xaxisrange()&&chart.xRange(scope.xaxisrange())),attrs.xaxisrangeband&&chart.xAxis.rangeBand(scope.xaxisrangeband()),attrs.xaxisrangebands&&chart.xAxis.rangeBands(scope.xaxisrangebands()),attrs.xaxisshowmaxmin&&chart.xAxis.showMaxMin("true"===attrs.xaxisshowmaxmin),attrs.xaxishighlightzero&&chart.xAxis.highlightZero("true"===attrs.xaxishighlightzero),attrs.xaxisrotatelabels&&chart.xAxis.rotateLabels(+attrs.xaxisrotatelabels),attrs.xaxisstaggerlabels&&chart.xAxis.staggerLabels("true"===attrs.xaxisstaggerlabels),attrs.xaxislabeldistance&&chart.xAxis.axisLabelDistance(+attrs.xaxislabeldistance)}function configureX2axis(chart,scope,attrs){attrs.x2axisorient&&chart.x2Axis.orient(attrs.x2axisorient),attrs.x2axisticks&&chart.x2Axis.scale().ticks(attrs.x2axisticks),attrs.x2axistickvalues&&(Array.isArray(scope.$eval(attrs.x2axistickvalues))?chart.x2Axis.tickValues(scope.$eval(attrs.x2axistickvalues)):"function"==typeof scope.xaxistickvalues()&&chart.x2Axis.tickValues(scope.x2axistickvalues())),attrs.x2axisticksubdivide&&chart.x2Axis.tickSubdivide(scope.x2axisticksubdivide()),attrs.x2axisticksize&&chart.x2Axis.tickSize(scope.x2axisticksize()),attrs.x2axistickpadding&&chart.x2Axis.tickPadding(scope.x2axistickpadding()),attrs.x2axistickformat&&chart.x2Axis.tickFormat(scope.x2axistickformat()),attrs.x2axislabel&&chart.x2Axis.axisLabel(attrs.x2axislabel),attrs.x2axisscale&&chart.x2Axis.scale(scope.x2axisscale()),attrs.x2axisdomain&&(Array.isArray(scope.$eval(attrs.x2axisdomain))?chart.x2Axis.domain(scope.$eval(attrs.x2axisdomain)):"function"==typeof scope.x2axisdomain()&&chart.x2Axis.domain(scope.x2axisdomain())),attrs.x2axisrange&&(Array.isArray(scope.$eval(attrs.x2axisrange))?chart.x2Axis.range(scope.$eval(attrs.x2axisrange)):"function"==typeof scope.x2axisrange()&&chart.x2Axis.range(scope.x2axisrange())),attrs.x2axisrangeband&&chart.x2Axis.rangeBand(scope.x2axisrangeband()),attrs.x2axisrangebands&&chart.x2Axis.rangeBands(scope.x2axisrangebands()),attrs.x2axisshowmaxmin&&chart.x2Axis.showMaxMin("true"===attrs.x2axisshowmaxmin),attrs.x2axishighlightzero&&chart.x2Axis.highlightZero("true"===attrs.x2axishighlightzero),attrs.x2axisrotatelables&&chart.x2Axis.rotateLabels(+attrs.x2axisrotatelables),attrs.x2axisstaggerlabels&&chart.x2Axis.staggerLabels("true"===attrs.x2axisstaggerlabels),attrs.x2axislabeldistance&&chart.x2Axis.axisLabelDistance(+attrs.x2axislabeldistance)}function configureYaxis(chart,scope,attrs){attrs.yaxisorient&&chart.yAxis.orient(attrs.yaxisorient),attrs.yaxisticks&&chart.yAxis.scale().ticks(attrs.yaxisticks),attrs.yaxistickvalues&&(Array.isArray(scope.$eval(attrs.yaxistickvalues))?chart.yAxis.tickValues(scope.$eval(attrs.yaxistickvalues)):"function"==typeof scope.yaxistickvalues()&&chart.yAxis.tickValues(scope.yaxistickvalues())),attrs.yaxisticksubdivide&&chart.yAxis.tickSubdivide(scope.yaxisticksubdivide()),attrs.yaxisticksize&&chart.yAxis.tickSize(scope.yaxisticksize()),attrs.yaxistickpadding&&chart.yAxis.tickPadding(scope.yaxistickpadding()),attrs.yaxistickformat&&chart.yAxis.tickFormat(scope.yaxistickformat()),attrs.yaxislabel&&chart.yAxis.axisLabel(attrs.yaxislabel),attrs.yaxisscale&&chart.yAxis.scale(scope.yaxisscale()),attrs.yaxisdomain&&(Array.isArray(scope.$eval(attrs.yaxisdomain))?chart.yDomain(scope.$eval(attrs.yaxisdomain)):"function"==typeof scope.yaxisdomain()&&chart.yDomain(scope.yaxisdomain())),attrs.yaxisrange&&(Array.isArray(scope.$eval(attrs.yaxisrange))?chart.yRange(scope.$eval(attrs.yaxisrange)):"function"==typeof scope.yaxisrange()&&chart.yRange(scope.yaxisrange())),attrs.yaxisrangeband&&chart.yAxis.rangeBand(scope.yaxisrangeband()),attrs.yaxisrangebands&&chart.yAxis.rangeBands(scope.yaxisrangebands()),attrs.yaxisshowmaxmin&&chart.yAxis.showMaxMin("true"===attrs.yaxisshowmaxmin),attrs.yaxishighlightzero&&chart.yAxis.highlightZero("true"===attrs.yaxishighlightzero),attrs.yaxisrotatelabels&&chart.yAxis.rotateLabels(+attrs.yaxisrotatelabels),attrs.yaxisrotateylabel&&chart.yAxis.rotateYLabel("true"===attrs.yaxisrotateylabel),attrs.yaxisstaggerlabels&&chart.yAxis.staggerLabels("true"===attrs.yaxisstaggerlabels),attrs.yaxislabeldistance&&chart.yAxis.axisLabelDistance(+attrs.yaxislabeldistance)}function configureY1axis(chart,scope,attrs){attrs.y1axisticks&&chart.y1Axis.scale().ticks(attrs.y1axisticks),attrs.y1axistickvalues&&(Array.isArray(scope.$eval(attrs.y1axistickvalues))?chart.y1Axis.tickValues(scope.$eval(attrs.y1axistickvalues)):"function"==typeof scope.y1axistickvalues()&&chart.y1Axis.tickValues(scope.y1axistickvalues())),attrs.y1axisticksubdivide&&chart.y1Axis.tickSubdivide(scope.y1axisticksubdivide()),attrs.y1axisticksize&&chart.y1Axis.tickSize(scope.y1axisticksize()),attrs.y1axistickpadding&&chart.y1Axis.tickPadding(scope.y1axistickpadding()),attrs.y1axistickformat&&chart.y1Axis.tickFormat(scope.y1axistickformat()),attrs.y1axislabel&&chart.y1Axis.axisLabel(attrs.y1axislabel),attrs.y1axisscale&&chart.y1Axis.yScale(scope.y1axisscale()),attrs.y1axisdomain&&(Array.isArray(scope.$eval(attrs.y1axisdomain))?chart.y1Axis.domain(scope.$eval(attrs.y1axisdomain)):"function"==typeof scope.y1axisdomain()&&chart.y1Axis.domain(scope.y1axisdomain())),attrs.y1axisrange&&(Array.isArray(scope.$eval(attrs.y1axisrange))?chart.y1Axis.range(scope.$eval(attrs.y1axisrange)):"function"==typeof scope.y1axisrange()&&chart.y1Axis.range(scope.y1axisrange())),attrs.y1axisrangeband&&chart.y1Axis.rangeBand(scope.y1axisrangeband()),attrs.y1axisrangebands&&chart.y1Axis.rangeBands(scope.y1axisrangebands()),attrs.y1axisshowmaxmin&&chart.y1Axis.showMaxMin("true"===attrs.y1axisshowmaxmin),attrs.y1axishighlightzero&&chart.y1Axis.highlightZero("true"===attrs.y1axishighlightzero),attrs.y1axisrotatelabels&&chart.y1Axis.rotateLabels(+scope.y1axisrotatelabels),attrs.y1axisrotateylabel&&chart.y1Axis.rotateYLabel("true"===attrs.y1axisrotateylabel),attrs.y1axisstaggerlabels&&chart.y1Axis.staggerlabels("true"===attrs.y1axisstaggerlabels),attrs.y1axislabeldistance&&chart.y1Axis.axisLabelDistance(+attrs.y1axislabeldistance)}function configureY2axis(chart,scope,attrs){attrs.y2axisticks&&chart.y2Axis.scale().ticks(attrs.y2axisticks),attrs.y2axistickvalues&&chart.y2Axis.tickValues(scope.$eval(attrs.y2axistickvalues)),attrs.y2axisticksubdivide&&chart.y2Axis.tickSubdivide(scope.y2axisticksubdivide()),attrs.y2axisticksize&&chart.y2Axis.tickSize(scope.y2axisticksize()),attrs.y2axistickpadding&&chart.y2Axis.tickPadding(scope.y2axistickpadding()),attrs.y2axistickformat&&chart.y2Axis.tickFormat(scope.y2axistickformat()),attrs.y2axislabel&&chart.y2Axis.axisLabel(attrs.y2axislabel),attrs.y2axisscale&&chart.y2Axis.yScale(scope.y2axisscale()),attrs.y2axisdomain&&(Array.isArray(scope.$eval(attrs.y2axisdomain))?chart.y2Axis.domain(scope.$eval(attrs.y2axisdomain)):"function"==typeof scope.y2axisdomain()&&chart.y2Axis.domain(scope.y2axisdomain())),attrs.y2axisrange&&(Array.isArray(scope.$eval(attrs.y2axisrange))?chart.y2Axis.range(scope.$eval(attrs.y2axisrange)):"function"==typeof scope.y2axisrange()&&chart.y2Axis.range(scope.y2axisrange())),attrs.y2axisrangeband&&chart.y2Axis.rangeBand(scope.y2axisrangeband()),attrs.y2axisrangebands&&chart.y2Axis.rangeBands(scope.y2axisrangebands()),attrs.y2axisshowmaxmin&&chart.y2Axis.showMaxMin("true"===attrs.y2axisshowmaxmin),attrs.y2axishighlightzero&&chart.y2Axis.highlightZero("true"===attrs.y2axishighlightzero),attrs.y2axisrotatelabels&&chart.y2Axis.rotateLabels(+scope.y2axisrotatelabels),attrs.y2axisrotateylabel&&chart.y2Axis.rotateYLabel("true"===attrs.y2axisrotateylabel),attrs.y2axisstaggerlabels&&chart.y2Axis.staggerlabels("true"===attrs.y2axisstaggerlabels),attrs.y2axislabeldistance&&chart.y2Axis.axisLabelDistance(+attrs.y2axislabeldistance)}function initializeMargin(scope,attrs){var margin=scope.$eval(attrs.margin)||{left:50,top:50,bottom:50,right:50};"object"!=typeof margin&&(margin={left:margin,top:margin,bottom:margin,right:margin}),scope.margin=margin}function checkElementID(scope,attrs,element,chart,data){configureXaxis(chart,scope,attrs),configureX2axis(chart,scope,attrs),configureYaxis(chart,scope,attrs),configureY1axis(chart,scope,attrs),configureY2axis(chart,scope,attrs),configureLegend(chart,scope,attrs),processEvents(chart,scope);var dataAttributeChartID;attrs.id?(angular.isArray(data)&&0===data.length&&d3.select("#"+attrs.id+" svg").remove(),d3.select("#"+attrs.id+" svg").empty()&&d3.select("#"+attrs.id).append("svg"),d3.select("#"+attrs.id+" svg").attr("height",scope.height).attr("width",scope.width).datum(data).transition().duration(void 0===attrs.transitionduration?250:+attrs.transitionduration).call(chart)):(dataAttributeChartID="chartid"+Math.floor(1000000001*Math.random()),angular.element(element).attr("data-chartid",dataAttributeChartID),d3.select("[data-chartid="+dataAttributeChartID+"] svg").empty()?d3.select("[data-chartid="+dataAttributeChartID+"]").append("svg").attr("height",scope.height).attr("width",scope.width).datum(data).transition().duration(void 0===attrs.transitionduration?250:+attrs.transitionduration).call(chart):d3.select("[data-chartid="+dataAttributeChartID+"] svg").attr("height",scope.height).attr("width",scope.width).datum(data).transition().duration(void 0===attrs.transitionduration?250:+attrs.transitionduration).call(chart))}angular.module("legendDirectives",[]).directive("simpleSvgLegend",function(){return{restrict:"EA",scope:{id:"@",width:"@",height:"@",margin:"@",x:"@",y:"@",labels:"@",styles:"@",classes:"@",shapes:"@",padding:"@",columns:"@"},compile:function(){return function(scope,element,attrs){var id,width,height,margin,paddingStr,svg,g,labels,styles,classes,shapes,widthTracker=0,heightTracker=0,columns=1,columnTracker=0,padding=10,svgNamespace="http://www.w3.org/2000/svg",x=0,y=0;margin=scope.$eval(attrs.margin)||{left:5,top:5,bottom:5,right:5},width="undefined"===attrs.width?element[0].parentElement.offsetWidth-(margin.left+margin.right):+attrs.width-(margin.left+margin.right),height="undefined"===attrs.height?element[0].parentElement.offsetHeight-(margin.top+margin.bottom):+attrs.height-(margin.top+margin.bottom),id=attrs.id?attrs.id:"legend-"+Math.random(),attrs.columns&&(columns=+attrs.columns),attrs.padding&&(padding=+attrs.padding),paddingStr=padding+"",svg=document.createElementNS(svgNamespace,"svg"),attrs.width&&svg.setAttribute("width",width+""),attrs.height&&svg.setAttribute("height",height+""),svg.setAttribute("id",id),attrs.x&&(x=+attrs.x),attrs.y&&(y=+attrs.y),element.append(svg),g=document.createElementNS(svgNamespace,"g"),g.setAttribute("transform","translate("+x+","+y+")"),svg.appendChild(g),attrs.labels&&(labels=scope.$eval(attrs.labels)),attrs.styles&&(styles=scope.$eval(attrs.styles)),attrs.classes&&(classes=scope.$eval(attrs.classes)),attrs.shapes&&(shapes=scope.$eval(attrs.shapes));for(var i in labels)if(labels.hasOwnProperty(i)){var shape,text,textSize,g1,shpe=shapes[i];columnTracker%columns===0&&(widthTracker=0,heightTracker+=padding*****padding),g1=document.createElementNS(svgNamespace,"g"),g1.setAttribute("transform","translate("+widthTracker+", "+heightTracker+")"),"rect"===shpe?(shape=document.createElementNS(svgNamespace,"rect"),shape.setAttribute("y",0-padding/2+""),shape.setAttribute("width",paddingStr),shape.setAttribute("height",paddingStr)):"ellipse"===shpe?(shape=document.createElementNS(svgNamespace,"ellipse"),shape.setAttribute("rx",paddingStr),shape.setAttribute("ry",padding+padding/2+"")):(shape=document.createElementNS(svgNamespace,"circle"),shape.setAttribute("r",padding/2+"")),styles&&styles[i]&&shape.setAttribute("style",styles[i]),classes&&classes[i]&&shape.setAttribute("class",classes[i]),g1.appendChild(shape),widthTracker=widthTracker+shape.clientWidth+(padding+padding/2),text=document.createElementNS(svgNamespace,"text"),text.setAttribute("transform","translate(10, 5)"),text.appendChild(document.createTextNode(labels[i])),g1.appendChild(text),g.appendChild(g1),textSize=text.clientWidth,widthTracker=widthTracker+textSize+(padding+.75*padding),columnTracker++}}}}}).directive("nvd3Legend",[function(){var margin,width,height,id;return{restrict:"EA",scope:{data:"=",id:"@",margin:"&",width:"@",height:"@",key:"&",color:"&",align:"@",rightalign:"@",updatestate:"@",radiobuttonmode:"@",x:"&",y:"&"},link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return d3.select("#"+attrs.id+" svg").attr("height",height).attr("width",width).datum(data).transition().duration(250).call(scope.chart);margin=scope.$eval(attrs.margin)||{top:5,right:0,bottom:5,left:0},width=void 0===attrs.width?element[0].parentElement.offsetWidth-(margin.left+margin.right):+attrs.width-(margin.left+margin.right),height=void 0===attrs.height?element[0].parentElement.offsetHeight-(margin.top+margin.bottom):+attrs.height-(margin.top+margin.bottom),(void 0===width||0>width)&&(width=400),(void 0===height||0>height)&&(height=20),id=attrs.id?attrs.id:"legend-"+Math.random(),nv.addGraph({generate:function(){var chart=nv.models.legend().width(width).height(height).margin(margin).align(void 0===attrs.align?!0:"true"===attrs.align).rightAlign(void 0===attrs.rightalign?!0:"true"===attrs.rightalign).updateState(void 0===attrs.updatestate?!0:"true"===attrs.updatestate).radioButtonMode(void 0===attrs.radiobuttonmode?!1:"true"===attrs.radiobuttonmode).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).key(void 0===attrs.key?function(d){return d.key}:scope.key());return d3.select("#"+attrs.id+" svg")[0][0]||d3.select("#"+attrs.id).append("svg"),d3.select("#"+attrs.id+" svg").attr("height",height).attr("width",width).datum(data).transition().duration(250).call(chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart}})}})}}}]),angular.module("nvd3ChartDirectives",[]).directive("nvd3LineChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",showxaxis:"@",showyaxis:"@",rightalignyaxis:"@",defaultstate:"@",nodata:"@",margin:"&",tooltipcontent:"&",color:"&",x:"&",y:"&",forcex:"@",forcey:"@",isArea:"@",interactive:"@",clipedge:"@",clipvoronoi:"@",interpolate:"@",callback:"&",useinteractiveguideline:"@",xaxisorient:"&",xaxisticks:"@",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.lineChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceX(void 0===attrs.forcex?[]:scope.$eval(attrs.forcex)).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).rightAlignYAxis(void 0===attrs.rightalignyaxis?!1:"true"===attrs.rightalignyaxis).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).clipEdge(void 0===attrs.clipedge?!1:"true"===attrs.clipedge).clipVoronoi(void 0===attrs.clipvoronoi?!1:"true"===attrs.clipvoronoi).interpolate(void 0===attrs.interpolate?"linear":attrs.interpolate).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).isArea(void 0===attrs.isarea?function(d){return d.area}:function(){return"true"===attrs.isarea});return chart.useInteractiveGuideline&&chart.useInteractiveGuideline(void 0===attrs.useinteractiveguideline?!1:"true"===attrs.useinteractiveguideline),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3CumulativeLineChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",showxaxis:"@",showyaxis:"@",rightalignyaxis:"@",defaultstate:"@",nodata:"@",margin:"&",tooltipcontent:"&",color:"&",x:"&",y:"&",forcex:"@",forcey:"@",isArea:"@",interactive:"@",clipedge:"@",clipvoronoi:"@",usevoronoi:"@",average:"&",rescaley:"@",callback:"&",useinteractiveguideline:"@",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.cumulativeLineChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceX(void 0===attrs.forcex?[]:scope.$eval(attrs.forcex)).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).rightAlignYAxis(void 0===attrs.rightalignyaxis?!1:"true"===attrs.rightalignyaxis).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).clipEdge(void 0===attrs.clipedge?!1:"true"===attrs.clipedge).clipVoronoi(void 0===attrs.clipvoronoi?!1:"true"===attrs.clipvoronoi).useVoronoi(void 0===attrs.usevoronoi?!1:"true"===attrs.usevoronoi).average(void 0===attrs.average?function(d){return d.average}:scope.average()).color(void 0===attrs.color?d3.scale.category10().range():scope.color()).isArea(void 0===attrs.isarea?function(d){return d.area}:"true"===attrs.isarea);return chart.useInteractiveGuideline&&chart.useInteractiveGuideline(void 0===attrs.useinteractiveguideline?!1:"true"===attrs.useinteractiveguideline),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3StackedAreaChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",showcontrols:"@",nodata:"@",margin:"&",tooltipcontent:"&",color:"&",x:"&",y:"&",forcex:"@",forcey:"@",forcesize:"@",interactive:"@",usevoronoi:"@",clipedge:"@",interpolate:"@",style:"@",order:"@",offset:"@",size:"&",xScale:"&",yScale:"&",xDomain:"&",yDomain:"&",xRange:"&",yRange:"&",sizeDomain:"&",callback:"&",showxaxis:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",showyaxis:"&",useinteractiveguideline:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.stackedAreaChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceX(void 0===attrs.forcex?[]:scope.$eval(attrs.forcex)).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).size(void 0===attrs.size?function(d){return void 0===d.size?1:d.size}:scope.size()).forceSize(void 0===attrs.forcesize?[]:scope.$eval(attrs.forcesize)).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).showControls(void 0===attrs.showcontrols?!1:"true"===attrs.showcontrols).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).clipEdge(void 0===attrs.clipedge?!1:"true"===attrs.clipedge).color(void 0===attrs.color?nv.utils.defaultColor():scope.color());return chart.useInteractiveGuideline&&chart.useInteractiveGuideline(void 0===attrs.useinteractiveguideline?!1:"true"===attrs.useinteractiveguideline),attrs.usevoronoi&&chart.useVoronoi("true"===attrs.usevoronoi),attrs.style&&chart.style(attrs.style),attrs.order&&chart.order(attrs.order),attrs.offset&&chart.offset(attrs.offset),attrs.interpolate&&chart.interpolate(attrs.interpolate),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),attrs.xscale&&chart.xScale(scope.xscale()),attrs.yscale&&chart.yScale(scope.yscale()),attrs.xdomain&&(Array.isArray(scope.$eval(attrs.xdomain))?chart.xDomain(scope.$eval(attrs.xdomain)):"function"==typeof scope.xdomain()&&chart.xDomain(scope.xdomain())),attrs.ydomain&&(Array.isArray(scope.$eval(attrs.ydomain))?chart.yDomain(scope.$eval(attrs.ydomain)):"function"==typeof scope.ydomain()&&chart.yDomain(scope.ydomain())),attrs.sizedomain&&chart.sizeDomain(scope.sizedomain()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3MultiBarChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",tooltipcontent:"&",color:"&",showcontrols:"@",nodata:"@",reducexticks:"@",staggerlabels:"@",rotatelabels:"@",margin:"&",x:"&",y:"&",forcey:"@",delay:"@",stacked:"@",callback:"&",showxaxis:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",showyaxis:"&",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)
}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.multiBarChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).showControls(void 0===attrs.showcontrols?!1:"true"===attrs.showcontrols).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).reduceXTicks(void 0===attrs.reducexticks?!1:"true"===attrs.reducexticks).staggerLabels(void 0===attrs.staggerlabels?!1:"true"===attrs.staggerlabels).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).rotateLabels(void 0===attrs.rotatelabels?0:attrs.rotatelabels).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).delay(void 0===attrs.delay?1200:attrs.delay).stacked(void 0===attrs.stacked?!1:"true"===attrs.stacked);return attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3DiscreteBarChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",tooltips:"@",showxaxis:"@",showyaxis:"@",tooltipcontent:"&",staggerlabels:"@",color:"&",margin:"&",nodata:"@",x:"&",y:"&",forcey:"@",showvalues:"@",valueformat:"&",callback:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.discreteBarChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).showValues(void 0===attrs.showvalues?!1:"true"===attrs.showvalues).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).staggerLabels(void 0===attrs.staggerlabels?!1:"true"===attrs.staggerlabels).color(void 0===attrs.color?nv.utils.defaultColor():scope.color());return attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),attrs.valueformat&&chart.valueFormat(scope.valueformat()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3HistoricalBarChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",tooltips:"@",tooltipcontent:"&",color:"&",margin:"&",nodata:"@",x:"&",y:"&",forcey:"@",isarea:"@",interactive:"@",clipedge:"@",clipvoronoi:"@",interpolate:"@",highlightPoint:"@",clearHighlights:"@",callback:"&",useinteractiveguideline:"@",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.historicalBarChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).color(void 0===attrs.color?nv.utils.defaultColor():scope.color());return chart.useInteractiveGuideline&&chart.useInteractiveGuideline(void 0===attrs.useinteractiveguideline?!1:"true"===attrs.useinteractiveguideline),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),attrs.valueformat&&chart.valueFormat(scope.valueformat()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3MultiBarHorizontalChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",tooltipcontent:"&",color:"&",showcontrols:"@",margin:"&",nodata:"@",x:"&",y:"&",forcey:"@",stacked:"@",showvalues:"@",valueformat:"&",callback:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.multiBarHorizontalChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).showXAxis(void 0===attrs.showxaxis?!1:"true"===attrs.showxaxis).showYAxis(void 0===attrs.showyaxis?!1:"true"===attrs.showyaxis).forceY(void 0===attrs.forcey?[0]:scope.$eval(attrs.forcey)).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).showControls(void 0===attrs.showcontrols?!1:"true"===attrs.showcontrols).showValues(void 0===attrs.showvalues?!1:"true"===attrs.showvalues).stacked(void 0===attrs.stacked?!1:"true"===attrs.stacked);return attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),attrs.valueformat&&chart.valueFormat(scope.valueformat()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3PieChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlabels:"@",showlegend:"@",donutLabelsOutside:"@",pieLabelsOutside:"@",labelType:"@",nodata:"@",margin:"&",x:"&",y:"&",color:"&",donut:"@",donutRatio:"@",labelthreshold:"@",description:"&",tooltips:"@",tooltipcontent:"&",valueFormat:"&",callback:"&",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.pieChart().x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).width(scope.width).height(scope.height).margin(scope.margin).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).showLabels(void 0===attrs.showlabels?!1:"true"===attrs.showlabels).labelThreshold(void 0===attrs.labelthreshold?.02:attrs.labelthreshold).labelType(void 0===attrs.labeltype?"key":attrs.labeltype).pieLabelsOutside(void 0===attrs.pielabelsoutside?!0:"true"===attrs.pielabelsoutside).valueFormat(void 0===attrs.valueformat?d3.format(",.2f"):attrs.valueformat).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).description(void 0===attrs.description?function(d){return d.description}:scope.description()).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).donutLabelsOutside(void 0===attrs.donutlabelsoutside?!1:"true"===attrs.donutlabelsoutside).donut(void 0===attrs.donut?!1:"true"===attrs.donut).donutRatio(void 0===attrs.donutratio?.5:attrs.donutratio);return attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3ScatterChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",showcontrols:"@",showDistX:"@",showDistY:"@",rightAlignYAxis:"@",fisheye:"@",xPadding:"@",yPadding:"@",tooltipContent:"&",tooltipXContent:"&",tooltipYContent:"&",color:"&",margin:"&",nodata:"@",transitionDuration:"@",shape:"&",onlyCircles:"@",interactive:"@",x:"&",y:"&",size:"&",forceX:"@",forceY:"@",forceSize:"@",xrange:"&",xdomain:"&",xscale:"&",yrange:"&",ydomain:"&",yscale:"&",sizerange:"&",sizedomain:"&",zscale:"&",callback:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.scatterChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d.x}:scope.x()).y(void 0===attrs.y?function(d){return d.y}:scope.y()).size(void 0===attrs.size?function(d){return void 0===d.size?1:d.size}:scope.size()).forceX(void 0===attrs.forcex?[]:scope.$eval(attrs.forcex)).forceY(void 0===attrs.forcey?[]:scope.$eval(attrs.forcey)).forceSize(void 0===attrs.forcesize?[]:scope.$eval(attrs.forcesize)).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).tooltipContent(void 0===attrs.tooltipContent?null:scope.tooltipContent()).tooltipXContent(void 0===attrs.tooltipxcontent?function(key,x){return"<strong>"+x+"</strong>"}:scope.tooltipXContent()).tooltipYContent(void 0===attrs.tooltipycontent?function(key,x,y){return"<strong>"+y+"</strong>"}:scope.tooltipYContent()).showControls(void 0===attrs.showcontrols?!1:"true"===attrs.showcontrols).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).showDistX(void 0===attrs.showdistx?!1:"true"===attrs.showdistx).showDistY(void 0===attrs.showdisty?!1:"true"===attrs.showdisty).xPadding(void 0===attrs.xpadding?0:+attrs.xpadding).yPadding(void 0===attrs.ypadding?0:+attrs.ypadding).fisheye(void 0===attrs.fisheye?0:+attrs.fisheye).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).transitionDuration(void 0===attrs.transitionduration?250:+attrs.transitionduration);return attrs.shape&&(chart.scatter.onlyCircles(!1),chart.scatter.shape(void 0===attrs.shape?function(d){return d.shape||"circle"}:scope.shape())),attrs.xdomain&&(Array.isArray(scope.$eval(attrs.xdomain))?chart.xDomain(scope.$eval(attrs.xdomain)):"function"==typeof scope.xdomain()&&chart.xDomain(scope.xdomain())),attrs.ydomain&&(Array.isArray(scope.$eval(attrs.ydomain))?chart.yDomain(scope.$eval(attrs.ydomain)):"function"==typeof scope.ydomain()&&chart.yDomain(scope.ydomain())),attrs.xscale&&(chart.xDomain(scope.xdomain()),chart.xRange(scope.xrange()),chart.xScale(scope.xscale())),attrs.yscale&&(chart.yDomain(scope.ydomain()),chart.yRange(scope.yrange()),chart.yScale(scope.yscale())),attrs.zscale&&(chart.sizeDomain(scope.sizedomain()),chart.sizeRange(scope.sizerange()),chart.zScale(scope.zscale())),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3ScatterPlusLineChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",callback:"&"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.scatterPlusLineChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d.x}:scope.x()).y(void 0===attrs.y?function(d){return d.y}:scope.y()).size(void 0===attrs.size?function(d){return void 0===d.size?1:d.size}:scope.size()).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).tooltipContent(void 0===attrs.tooltipContent?null:scope.tooltipContent()).tooltipXContent(void 0===attrs.tooltipxcontent?function(key,x){return"<strong>"+x+"</strong>"}:scope.tooltipXContent()).tooltipYContent(void 0===attrs.tooltipycontent?function(key,x,y){return"<strong>"+y+"</strong>"}:scope.tooltipYContent()).showControls(void 0===attrs.showcontrols?!1:"true"===attrs.showcontrols).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).showDistX(void 0===attrs.showdistx?!1:"true"===attrs.showdistx).showDistY(void 0===attrs.showdisty?!1:"true"===attrs.showdisty).xPadding(void 0===attrs.xpadding?0:+attrs.xpadding).yPadding(void 0===attrs.ypadding?0:+attrs.ypadding).fisheye(void 0===attrs.fisheye?0:+attrs.fisheye).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).transitionDuration(void 0===attrs.transitionduration?250:+attrs.transitionduration);return attrs.shape&&(chart.scatter.onlyCircles(!1),chart.scatter.shape(void 0===attrs.shape?function(d){return d.shape||"circle"}:scope.shape())),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}})}}}]).directive("nvd3LinePlusBarChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",showlegend:"@",tooltips:"@",showxaxis:"@",showyaxis:"@",forceX:"@",forceY:"@",forceY2:"@",rightalignyaxis:"@",defaultstate:"@",nodata:"@",margin:"&",tooltipcontent:"&",color:"&",x:"&",y:"&",clipvoronoi:"@",interpolate:"@",callback:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",y1axisorient:"&",y1axisticks:"&",y1axistickvalues:"&y1axistickvalues",y1axisticksubdivide:"&",y1axisticksize:"&",y1axistickpadding:"&",y1axistickformat:"&",y1axislabel:"@",y1axisscale:"&",y1axisdomain:"&",y1axisrange:"&",y1axisrangeband:"&",y1axisrangebands:"&",y1axisshowmaxmin:"@",y1axishighlightzero:"@",y1axisrotatelabels:"@",y1axisrotateylabel:"@",y1axisstaggerlabels:"@",y1axisaxislabeldistance:"@",y2axisorient:"&",y2axisticks:"&",y2axistickvalues:"&y2axistickvalues",y2axisticksubdivide:"&",y2axisticksize:"&",y2axistickpadding:"&",y2axistickformat:"&",y2axislabel:"@",y2axisscale:"&",y2axisdomain:"&",y2axisrange:"&",y2axisrangeband:"&",y2axisrangebands:"&",y2axisshowmaxmin:"@",y2axishighlightzero:"@",y2axisrotatelabels:"@",y2axisrotateylabel:"@",y2axisstaggerlabels:"@",y2axisaxislabeldistance:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.linePlusBarChart().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).interpolate(void 0===attrs.interpolate?"linear":attrs.interpolate).color(void 0===attrs.color?nv.utils.defaultColor():scope.color());return attrs.forcex&&(chart.lines.forceX(scope.$eval(attrs.forcex)),chart.bars.forceX(scope.$eval(attrs.forcex))),attrs.forcey&&(chart.lines.forceY(scope.$eval(attrs.forcey)),chart.bars.forceY(scope.$eval(attrs.forcey))),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3LineWithFocusChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",height2:"@",id:"@",showlegend:"@",tooltips:"@",showxaxis:"@",showyaxis:"@",rightalignyaxis:"@",defaultstate:"@",nodata:"@",margin:"&",margin2:"&",tooltipcontent:"&",color:"&",x:"&",y:"&",forceX:"@",forceY:"@",clipedge:"@",clipvoronoi:"@",interpolate:"@",isArea:"@",size:"&",defined:"&",interactive:"@",callback:"&",xaxisorient:"&",xaxisticks:"&",xaxistickvalues:"&xaxistickvalues",xaxisticksubdivide:"&",xaxisticksize:"&",xaxistickpadding:"&",xaxistickformat:"&",xaxislabel:"@",xaxisscale:"&",xaxisdomain:"&",xaxisrange:"&",xaxisrangeband:"&",xaxisrangebands:"&",xaxisshowmaxmin:"@",xaxishighlightzero:"@",xaxisrotatelabels:"@",xaxisrotateylabel:"@",xaxisstaggerlabels:"@",xaxisaxislabeldistance:"@",x2axisorient:"&",x2axisticks:"&",x2axistickvalues:"&xaxistickvalues",x2axisticksubdivide:"&",x2axisticksize:"&",x2axistickpadding:"&",x2axistickformat:"&",x2axislabel:"@",x2axisscale:"&",x2axisdomain:"&",x2axisrange:"&",x2axisrangeband:"&",x2axisrangebands:"&",x2axisshowmaxmin:"@",x2axishighlightzero:"@",x2axisrotatelables:"@",x2axisrotateylabel:"@",x2axisstaggerlabels:"@",yaxisorient:"&",yaxisticks:"&",yaxistickvalues:"&yaxistickvalues",yaxisticksubdivide:"&",yaxisticksize:"&",yaxistickpadding:"&",yaxistickformat:"&",yaxislabel:"@",yaxisscale:"&",yaxisdomain:"&",yaxisrange:"&",yaxisrangeband:"&",yaxisrangebands:"&",yaxisshowmaxmin:"@",yaxishighlightzero:"@",yaxisrotatelabels:"@",yaxisrotateylabel:"@",yaxisstaggerlabels:"@",yaxislabeldistance:"@",y2axisorient:"&",y2axisticks:"&",y2axistickvalues:"&",y2axisticksubdivide:"&",y2axisticksize:"&",y2axistickpadding:"&",y2axistickformat:"&",y2axislabel:"@",y2axisscale:"&",y2axisdomain:"&",y2axisrange:"&",y2axisrangeband:"&",y2axisrangebands:"&",y2axisshowmaxmin:"@",y2axishighlightzero:"@",y2axisrotatelabels:"@",y2axisrotateylabel:"@",y2axisstaggerlabels:"@",legendmargin:"&",legendwidth:"@",legendheight:"@",legendkey:"@",legendcolor:"&",legendalign:"@",legendrightalign:"@",legendupdatestate:"@",legendradiobuttonmode:"@",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){if(initializeMargin(scope,attrs),attrs.margin2){var margin2=scope.$eval(attrs.margin2);"object"!=typeof margin2&&(margin2={left:margin2,top:margin2,bottom:margin2,right:margin2}),scope.margin2=margin2}else scope.margin2={top:0,right:30,bottom:20,left:60};var chart=nv.models.lineWithFocusChart().width(scope.width).height(scope.height).height2(void 0===attrs.height2?100:+attrs.height2).margin(scope.margin).margin2(scope.margin2).x(void 0===attrs.x?function(d){return d[0]}:scope.x()).y(void 0===attrs.y?function(d){return d[1]}:scope.y()).forceX(void 0===attrs.forcex?[]:scope.$eval(attrs.forcex)).forceY(void 0===attrs.forcey?[]:scope.$eval(attrs.forcey)).showLegend(void 0===attrs.showlegend?!1:"true"===attrs.showlegend).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata).color(void 0===attrs.color?nv.utils.defaultColor():scope.color()).isArea(void 0===attrs.isarea?function(d){return d.area}:function(){return"true"===attrs.isarea}).size(void 0===attrs.size?function(d){return void 0===d.size?1:d.size}:scope.size()).interactive(void 0===attrs.interactive?!1:"true"===attrs.interactive).interpolate(void 0===attrs.interpolate?"linear":attrs.interpolate);return attrs.defined&&chart.defined(scope.defined()),attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3BulletChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",margin:"&",tooltips:"@",tooltipcontent:"&",orient:"@",ranges:"&",markers:"&",measures:"&",tickformat:"&",nodata:"@",callback:"&",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.bulletChart().width(scope.width).height(scope.height).margin(scope.margin).orient(void 0===attrs.orient?"left":attrs.orient).tickFormat(void 0===attrs.tickformat?null:scope.tickformat()).tooltips(void 0===attrs.tooltips?!1:"true"===attrs.tooltips).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata);return attrs.tooltipcontent&&chart.tooltipContent(scope.tooltipcontent()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3SparklineChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",margin:"&",x:"&",y:"&",color:"&",xscale:"&",yscale:"&",showvalue:"@",alignvalue:"@",rightalignvalue:"@",nodata:"@",callback:"&",xtickformat:"&",ytickformat:"&",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){checkElementID($scope,$attrs,$element,chart,data)}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){initializeMargin(scope,attrs);var chart=nv.models.sparklinePlus().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d.x}:scope.x()).y(void 0===attrs.y?function(d){return d.y}:scope.y()).xTickFormat(void 0===attrs.xtickformat?d3.format(",r"):scope.xtickformat()).yTickFormat(void 0===attrs.ytickformat?d3.format(",.2f"):scope.ytickformat()).color(void 0===attrs.color?nv.utils.getColor(["#000"]):scope.color()).showValue(void 0===attrs.showvalue?!0:"true"===attrs.showvalue).alignValue(void 0===attrs.alignvalue?!0:"true"===attrs.alignvalue).rightAlignValue(void 0===attrs.rightalignvalue?!1:"true"===attrs.rightalignvalue).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata);return attrs.xScale&&chart.xScale(scope.xScale()),attrs.yScale&&chart.yScale(scope.yScale()),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}]).directive("nvd3SparklineWithBandlinesChart",[function(){return{restrict:"EA",scope:{data:"=",width:"@",height:"@",id:"@",margin:"&",x:"&",y:"&",color:"&",xscale:"&",yscale:"&",showvalue:"@",alignvalue:"@",rightalignvalue:"@",nodata:"@",callback:"&",xtickformat:"&",ytickformat:"&",objectequality:"@",transitionduration:"@"},controller:["$scope","$element","$attrs",function($scope,$element,$attrs){$scope.d3Call=function(data,chart){var dataAttributeChartID,selectedChart,sLineSelection,bandlineData,bandLines;$attrs.id?(d3.select("#"+$attrs.id+" svg")||d3.select("#"+$attrs.id).append("svg"),selectedChart=d3.select("#"+$attrs.id+" svg").attr("height",$scope.height).attr("width",$scope.width).datum(data),sLineSelection=d3.select("svg#"+$attrs.id+" g.nvd3.nv-wrap.nv-sparkline"),bandlineData=[$scope.bandlineProperties.min,$scope.bandlineProperties.twentyFithPercentile,$scope.bandlineProperties.median,$scope.bandlineProperties.seventyFithPercentile,$scope.bandlineProperties.max],bandLines=sLineSelection.selectAll(".nv-bandline").data([bandlineData]),bandLines.enter().append("g").attr("class","nv-bandline"),selectedChart.transition().duration(void 0===$attrs.transitionduration?250:+$attrs.transitionduration).call(chart)):(dataAttributeChartID="chartid"+Math.floor(1000000001*Math.random()),angular.element($element).attr("data-chartid",dataAttributeChartID),selectedChart=d3.select("[data-iem-chartid="+dataAttributeChartID+"] svg").attr("height",$scope.height).attr("width",$scope.width).datum(data),sLineSelection=d3.select("[data-iem-chartid="+dataAttributeChartID+"] svg g.nvd3.nv-wrap.nv-sparkline"),bandlineData=[$scope.bandlineProperties.min,$scope.bandlineProperties.twentyFithPercentile,$scope.bandlineProperties.median,$scope.bandlineProperties.seventyFithPercentile,$scope.bandlineProperties.max],bandLines=sLineSelection.selectAll(".nv-bandline").data([bandlineData]),bandLines.enter().append("g").attr("class","nv-bandline"),selectedChart.transition().duration(void 0===$attrs.transitionduration?250:+$attrs.transitionduration).call(chart))}}],link:function(scope,element,attrs){scope.$watch("data",function(data){if(data){if(scope.chart)return scope.d3Call(data,scope.chart);nv.addGraph({generate:function(){scope.bandlineProperties={};var sortedValues;initializeMargin(scope,attrs);var chart=nv.models.sparklinePlus().width(scope.width).height(scope.height).margin(scope.margin).x(void 0===attrs.x?function(d){return d.x}:scope.x()).y(void 0===attrs.y?function(d){return d.y}:scope.y()).xTickFormat(void 0===attrs.xtickformat?d3.format(",r"):scope.xtickformat()).yTickFormat(void 0===attrs.ytickformat?d3.format(",.2f"):scope.ytickformat()).color(void 0===attrs.color?nv.utils.getColor(["#000"]):scope.color()).showValue(void 0===attrs.showvalue?!0:"true"===attrs.showvalue).alignValue(void 0===attrs.alignvalue?!0:"true"===attrs.alignvalue).rightAlignValue(void 0===attrs.rightalignvalue?!1:"true"===attrs.rightalignvalue).noData(void 0===attrs.nodata?"No Data Available.":scope.nodata);return scope.bandlineProperties.min=d3.min(data,function(d){return d[1]}),scope.bandlineProperties.max=d3.max(data,function(d){return d[1]}),sortedValues=data.map(function(d){return d[1]
}).sort(function(a,b){return a[0]<b[0]?-1:a[0]===b[0]?0:1}),scope.bandlineProperties.twentyFithPercentile=d3.quantile(sortedValues,.25),scope.bandlineProperties.median=d3.median(sortedValues),scope.bandlineProperties.seventyFithPercentile=d3.quantile(sortedValues,.75),attrs.xScale&&chart.xScale(scope.xScale()),attrs.yScale&&chart.yScale(scope.yScale()),configureXaxis(chart,scope,attrs),configureYaxis(chart,scope,attrs),processEvents(chart,scope),scope.d3Call(data,chart),nv.utils.windowResize(chart.update),scope.chart=chart,chart},callback:void 0===attrs.callback?null:scope.callback()})}},void 0===attrs.objectequality?!1:"true"===attrs.objectequality)}}}])}();