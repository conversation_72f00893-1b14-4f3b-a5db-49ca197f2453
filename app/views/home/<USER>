<%if @status%>
  <div class="row">
    <div ui-view="header"></div>
      <div class="container-fluid">
       <div ui-view="container"></div>
      <div ui-view></div>
    </div>
  </div>
  <div class="content_for_extension" style="display: none;"></div>
  <!-- Please dont remove this html code. This is only to load editable when layout is loaded -->
  <div groov-editable="gridOptions"></div>
<%else%>
<!--   <!DOCTYPE html>
  <html>
    <head> -->
      <title>The page you were looking for doesn't exist (503)</title>
      <style type="text/css">
        body {
          /*font-family: 'Play', sans-serif;*/
          /*font-family: 'Helvetica Neue';*/
          font-size: 14px;
          line-height: 1.42857143;
          color: #333;
        }
        .container-well {
          background: url('https://s3-us-west-2.amazonaws.com/groove-staging/background_large.png') fixed;
          /*font-family: 'Play', sans-serif;*/
        }
        .container-fluid{
          padding-right:30px;
          padding-left:30px;
          *zoom:1;
        }
        .gradient-well {
          border: 5px solid rgba(201, 201, 201, .69);
          padding: 0px;
          border-radius: 20px;
          box-shadow: 1px 1px 10px rgba(0,0,1,0.86);
        }
        .gradient-well-inner {
          position: relative;
          border: 2px solid rgba(202, 203, 203, .15);
          /*border-radius: @border-radius-base + 10;*/
          border-radius: 14px;
          padding-top: 10px;
          padding-bottom: 10px;
          background: url('https://s3-us-west-2.amazonaws.com/groove-staging/brush_metal_bkg_large.png') no-repeat center center fixed;
        }
        .container-fluid:before,.container-fluid:after{
          display:table;
          content:"";
          line-height:0;
        }
        .row{
          margin-left: -15px;
          margin-right: -15px;
          /*margin-left:-20px;*/
          *zoom:1;
        }
        .row:before,.row:after{
          display:table;content:"";
          line-height:0;
        }
        .row:after{clear:both;}
        .row.bottom-well {
          margin-top: 10px;
        }
        .main-body {
          margin-top: 28px;
        }
        .container {
          width: 1135px;
          margin-right: auto;
          margin-left: auto;
          padding-left: 15px;
          padding-right: 15px;
        }
        .box-outer {
          position: relative;
          float: left;
          width: 100%;
        }
        .box {
          height: 50px;
          top: 0px;
          left: 0px;
          width: 100%;
          position: absolute;
          background: rgba(237, 237, 237, 0.41);
          border: 2px solid rgba(241, 241, 241, .19);
          box-shadow: 0px 1px 1px rgba(200,200,200,0.75),0px -1px 1px rgba(200,200,200,0.75);
          border-left: 0px;
          border-right: 0px;
          font-family: inherit;
        }
        .col-lg-12, .col-md-12, .col-sm-12, .col-xs-12 {
          position: relative;
          width: 45%;
        }
        h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
          font-family: 'Play', sans-serif;
          font-weight: 500;
          line-height: 1.1;
          color: inherit;
        }
        h3, .h3 {
          font-size: 24px;
        }
        h4, .h4 {
          font-size: 18px;
        }
        a {
          color: #428bca;
          text-decoration: none;
        }
        a:hover {
          color: #30699A;
          text-decoration: underline;
        }
      </style>
      <link href='https://fonts.googleapis.com/css?family=Play:400,700' rel='stylesheet' type='text/css'>
    <!-- </head> -->
    <body class="container-well">
      <div class="container main-body">
        <div class="gradient-well">
          <div class="container-fluid gradient-well-inner">
            <div class="row bottom-well">
              <div class="row">
                <div class="box-outer">
                  <!-- <div class="box"></div> -->
                  <div >
                    <center>
                      <img src="https://s3-us-west-2.amazonaws.com/groove-staging/logo.png" alt="GroovePacker"/>
                    </center>
                  </div>
                  <div>
                    <!-- <br/> -->
                    <center>
                      <h3>We are not able to find an account at the url you provided.</h3>
                      <h4>Please enter your account name in the field below.</h4>
                      <h4>https:// <input type="text" id="tenant_name">.groovepacker.com (ie. https://example.groovepacker.com)</h4>

                      <button type="button" class="button" id="tenant">
                        Submit
                      </button>
                    </center>
                    <br/>
                    <br/>
                    <br/>
                    <br/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </body>
  <!-- </html> -->
<%end%>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.40/moment-timezone-with-data.min.js"></script>

<script type="text/javascript">
  host = window.location.href.split(".")[1];
  if (["https://admin." + host + ".com/#/", "https://admin." + host + ".com/", "https://admin." + host + ".com"].indexOf(location.href) != -1) {
    location.href = "https://notfound." + host + ".com/#/";
  }
  $("#tenant").click(function(){
    host = window.location.href.split(".")[1];
    tenant_name = $("#tenant_name").val();
    if (tenant_name != ""){
      window.open(window.location.href.split(":")[0]+"://" + tenant_name+ "."+host+".com","_self")
    } else{
      alert("please specify tenant name")
    }
  })
</script>
<!-- ...................................................................................... -->
<style type="text/css">

.button {
  color: #ffffff;
  background-color: #47a447;
  border-color: #398439;
}
/*  .gradient-well {
    display: -webkit-box !important;
  }

  .gradient-well-inner {
    width: 100% !important;
    max-width: 100% !important;
  }
  table.table-well{
    width: 100%;
  }
  .table-parent{
    width: 100% !important;
  }
*/
</style>