<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
  <title><%= content_for?(:title) ? yield(:title) : "GroovePacker - Online Inventory and Barcode Packing Solution"
    %></title>
    <meta name="description" content="<%= content_for?(:description) ? yield(:description) : 'Groovepacks' %>">
    <link href='https://fonts.googleapis.com/css?family=Play:400,700' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.4.2/css/all.css" integrity="sha384-/rXc/GQVaYpyDdyxK+ecHPVYJSN9bmVFBvjA/9eOB+pb3F2w2N6fc5qB9Ew5yIns" crossorigin="anonymous">
    <link
    href='https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800'
    rel='stylesheet' type='text/css'>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins&display=swap" rel="stylesheet">
    <%= stylesheet_link_tag "application", :media => "all" %>
    <%= javascript_include_tag "https://js.stripe.com/v3/", socket_server() + "/socket/socket.io.js", "https://cdnjs.cloudflare.com/ajax/libs/webcamjs/1.0.26/webcam.min.js", "application" %>

    <%= csrf_meta_tags %>
    <%= tag :meta, :name => "stripe-key", :content => ENV['STRIPE_PUBLIC_KEY'] %>
    <%= yield(:head) %>
  </head>
  <body class="container-well"
  ng-app="<%= (@groovepacks_admin == true)? 'groovepacks_admin' : 'groovepacks' %>"
  ng-controller="appCtrl">
  <input type="hidden" id="rails_env" value="<%= Rails.env %>"/>
  <input type="hidden" id="current_protocol_from_env" value="<%= Rails.env.development? ? 'http://' : 'https://' %>"/>
  <input type="hidden" id="current_tenant" value="<%= @current_tenant %>"/>
  <input type="hidden" id="domain" value="<%= ENV['GROOV_ANALYTIC']%>"/>
  <input type="hidden" id="gl_app_url" value="<%= ENV['GROOV_ANALYTIC_URL'] %>"/>
  <input type="hidden" id="site_host" value="<%= ENV['SITE_HOST']%>"/>
  <input type="hidden" id="frontend_host" value="<%= ENV['SOCKET_ENDPOINT']%>"/>
  <input id="socket_server" type="hidden" value="<%= socket_server() %>"/>
<!--   <input id="analytics_server" type="hidden" value="<%#= analytics_server(@current_tenant) %>"/> -->

  <% if %w[development production].exclude?(Rails.env) %>
    <div class="alert alert-danger text-center" style="margin-bottom: 0;">
      <strong>Test Environment:</strong> <%= Rails.env.capitalize %>
    </div>
  <% end %>

  <div class="logout-box fade" ng-show="show_logout_box">
    <div class="center-message">
      <h2>Continue session?</h2>
      <button class="modal-save-button" ng-click="log_out('everyone_else')">Yes, Sign Out all other sessions</button>
      <button ng-click="log_out('me')" class="modal-cancel-button">No, Sign this session out</button>
    </div>
  </div>
  <div class="editing-mode" ng-class="{in: currently_editing}">
    <div class="top-message">
      <button class="groove-button label label-info" ng-click="stop_editing()">Exit Edit Mode</button>
    </div>
  </div>

  <div ng-if="path=='cost_calculator'" class ="text-center" style="margin: 25px">
    <a href="https://www.groovepacker.com" target="_blank" style="float: left; margin-top: 30px;"><img class="icons" src="/assets/images/return_home.png"></img></a>
    <img class="icons" src="/assets/images/cost-calculator-header-image-02.png"></img>
  </div>
  <div class="container main-body">
    <div class="gradient-well">
      <div class="container-fluid gradient-well-inner">
        <%= yield %>
      </div>
    </div>
  </div>
  <% if @current_tenant != 'admintools' %>
  <div id="dashboard" groov-dashboard ng-if="current_user.isLoggedIn() && (current_user.get().username == 'gpadmin' || current_user.get().view_dashboard)"></div>
  <% end %>
  <%#= raw GeneralSetting.try(:get_custom_fields) %>

  <script type="text/javascript">
    window.custom_fields = [];
  </script>
  <script type="text/javascript" src="https://s3.amazonaws.com/assets.freshdesk.com/widget/freshwidget.js"></script>
  <script type="text/javascript">
    FreshWidget.init("", {"queryString": "&widgetType=popup&formTitle=Customer+Support&submitThanks=Thank+you!+We'll+be+in+touch+shortly.", "utf8": "✓", "widgetType": "popup", "buttonType": "text", "buttonText": "Get Support", "buttonColor": "white", "buttonBg": "#73ae0c ", "alignment": "2", "offset": "500px", "submitThanks": "Thank you! We'll be in touch shortly.", "formHeight": "620px", "url": "https://groovepacker.freshdesk.com"} );
  </script>

  <script type="text/javascript">
    http_protocol = document.getElementById('current_protocol_from_env').value

    qz.security.setCertificatePromise(function(resolve, reject) {
      current_tenant_val = document.getElementById('current_tenant').value
      site_host = document.getElementById('site_host').value;
      fetch(http_protocol + "admin." + site_host + "/print/qz_certificate", {cache: 'no-store', headers: {'Content-Type': 'text/plain'}})
      .then(function(data) {
        data.ok ? resolve(data.text()) : reject(data.text());
      });
    });

    qz.security.setSignatureAlgorithm("SHA512"); // Since 2.1

    qz.security.setSignaturePromise(function(toSign) {
        return function(resolve, reject) {
          fetch(http_protocol + "admin." + site_host +  "/print/qz_sign?request=" + toSign, {method: 'POST', cache: 'no-store', headers: {'Content-Type': 'text/plain'}})
          .then(function(data) {
            data.ok ? resolve(data.text()) : reject(data.text());
          });
        };
    });
  </script>

  <style type="text/css">
    .freshwidget-button{
      top: 500px !important;
    }
  </style>
</body>
</html>
