<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
  <title><%= content_for?(:title) ? yield(:title) : "GroovePacker - Online Inventory and Barcode Packing Solution" %></title>
  <meta name="description" content="<%= content_for?(:description) ? yield(:description) : 'Groovepacks' %>">
  <link href='https://fonts.googleapis.com/css?family=Play:400,700' rel='stylesheet' type='text/css'>
  <link href='https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800' rel='stylesheet' type='text/css'>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins&display=swap" rel="stylesheet">
  <%= stylesheet_link_tag "application", :media => "all" %>
  <%= javascript_include_tag "https://js.stripe.com/v3/", socket_server + "/socket/socket.io.js", "https://cdnjs.cloudflare.com/ajax/libs/webcamjs/1.0.26/webcam.min.js", "application" %>

  <%= csrf_meta_tags %>
  <%= tag :meta, :name => "stripe-key", :content => ENV['STRIPE_PUBLIC_KEY'] %>
  <%= yield(:head) %>
  <script>
    (function (i, s, o, g, r, a, m) {
      i['GoogleAnalyticsObject'] = r;
      i[r] = i[r] || function () {
          (i[r].q = i[r].q || []).push(arguments)
        }
        , i[r].l = 1 * new Date();
      a = s.createElement(o),
        m = s.getElementsByTagName(o)[0];
      a.async = 1;
      a.src = g;
      m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');
    ga('create', 'UA-50463409-1', 'auto');
    ga('send', 'pageview');
  </script>
</head>
<body class="container-well">
<div class="container main-body">
  <%if params["controller"] == "cost_calculators" && params["action"] == "index"%>
    <div class ="text-center" style="margin: 25px">
      <img class="icons" src="/assets/images/cost-calculator-header-image-02.png"></img>
    </div>
  <%end%>
  <div class="gradient-well">
    <div class="container-fluid gradient-well-inner">
      <%= yield %>
    </div>
  </div>
</div>
<script type="text/javascript">
  window.custom_fields = <%= raw GeneralSetting.try(:get_custom_fields) %>;
</script>
<script type="text/javascript" src="https://s3.amazonaws.com/assets.freshdesk.com/widget/freshwidget.js"></script>
<script type="text/javascript">
  $(document).ready(function(){
    var current_href = window.location.href;
    var is_admin = current_href.search("admin.groovepacker.com");
    if(is_admin!=-1) return;
    FreshWidget.init("", {"queryString": "&widgetType=popup&formTitle=Customer+Support&submitThanks=Thank+you!+We'll+be+in+touch+shortly.", "utf8": "✓", "widgetType": "popup", "buttonType": "text", "buttonText": "Get Support", "buttonColor": "white", "buttonBg": "#73ae0c ", "alignment": "2", "offset": "825px", "submitThanks": "Thank you! We'll be in touch shortly.", "formHeight": "500px", "url": "https://groovepacker.freshdesk.com"} );
  });
</script>
<style type="text/css">
  .freshwidget-button{
    top: 500px !important;
  }
</style>
</body>

</html>
