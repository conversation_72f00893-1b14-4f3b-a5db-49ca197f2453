<div class="errorcalculator">     
  <input type="hidden" id ="montly_expenses" name="montly_expenses" value="<%= @params[:monthly_shipping] %>">
    <div class="col-md-12 col-sm-12 col-xs-12">
      <div>
        <h4 style="width:100%; color: #333333">
          <b>
            <div contenteditable="true" class="text-center" id="cost_header" name="cost_header" style="max-width: 100%; background-color: white;"  rows="1" cols="130">
            <%=
              (URI.decode(@params[:cost_header]) rescue nil) ||
              'Modify the estimates below to match your costs and see what your company is currently spending on errors.' %>
            </div>
          </b>
        </h4>
      </div>
    </div>
    <div class="col-md-12 col-sm-12 col-xs-12">
      <form id="cost_calc">
        <div class="row out_box">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Error Volume</strong></h4>
            <h1><strong id="error_per_day"><%= @params[:error_per_day] %></strong></h1>
            <p>Errors Per day</p>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>Number of packers.</h4>
                  <input id="packer_count" type="text" name="packer_count" value="<%= @params[:packer_count] %>">
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>Number of orders an average packer packs per day.</h4>
                  <input id="order_count" type="text" name="order_count" value="<%= @params[:order_count] %>">
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox right-inner-addon">
                  <h4>Error rate for the average packer.</h4>
                  <span style="padding: 7px 90px">%</span>
                  <input id="avg_error" type="text" name="avg_error" value="<%= @params[:avg_error] %>">
                </div>
              </div>
            </div>

          </div>
        </div>

        <h2>Tangible Costs</h2>
        <div class="row comunication out_box">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Communication & Customer Service Costs</strong></h4>
            <h1><strong>$<span id="cost_of_comm"><%= @params[:cost_of_comm] %></span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4 class="right-inner-addon"><span style="padding: 7px 170px">%</span>
                    <input id="regular_percentage" type="text" name="regular_percentage" value="<%= @params[:regular_percentage] %>"> &nbsp;are routine communications handled by regular staff.
                  </h4>
                  <h4 class="left-inner-addon">
                    The average cost is: <span style="padding: 7px 5px">$</span>&nbsp;<input id="regular_comm" type="text" name="regular_comm" value="<%= @params[:regular_comm] %>" style="width: 60px;">  per communication.
                  </h4>
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4><strong id="escalated_percentage"><%= @params[:escalated_percentage] %></strong><b>%</b> become escalated communications requiring the owner or management.</h4>
                  <h4 class="left-inner-addon">
                    The average cost is: <span style="padding: 16px 6px;">$</span>&nbsp;<input id="escalated_comm" type="text" name="escalated_comm" value="<%= @params[:escalated_comm] %>" style="width: 60px;">  per communication.
                  </h4>
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>The average number of communications required to resolve an error related issue.</h4>
                  <input id="avg_comm" type="text" name="avg_comm" value="<%= @params[:avg_comm] %>">
                </div>
              </div>
            </div>

            <h4>
              For example: One email or call when the issue is reported.<br> One email or call to let the customer know the reshipment is in in progress,<br> a final email or call to verify the customer has successfully received the item.<br> If the customer will be returning an incorrect item an additional email will likely be sent to the customer to let<br> them know it was recieved.
            </h4>

          </div>
        </div>



        <div class="row replacement out_box box_margin">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Replacement Shipment Costs</strong></h4>
            <h1><strong>$<span id="total_replacement_costs"><%= @params[:total_replacement_costs] %></span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox">

                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_ship_replacement" type="text" name="cost_ship_replacement" value="<%= @params[:cost_ship_replacement] %>"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                      <h4>
                         Postage for shipping the replacement item (include insurance and other applicable fees)
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="total_expedited" value="&nbsp; &nbsp; &nbsp;<%= @params[:total_expedited] %>" disabled></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                      <h4 class="right-inner-addon">
                        1 in <input id="expedited_count" type="text" name="expedited_count" value="<%= @params[:expedited_count] %>"> replacement shipments (<input id="expedited_percentage" type="text" name="expedited_percentage" value="<%= @params[:expedited_percentage] %>"><span class="override_span input_align">%</span>) require expedited shipping to prevent loosing the customer. The average cost of expedited shipping is 
                        <addon class="left-inner-addon">
                          &nbsp;&nbsp;<span class="override_span input_align">$</span>
                          <input type="text" id="expedited_avg" name="expedited_avg" value="<%= @params[:expedited_avg] %>" style="margin: 4px -15px 10px">
                        </addon>
                      </h4>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="total_international" value="&nbsp; &nbsp; &nbsp;<%= @params[:total_international] %>" disabled></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4 class="right-inner-addon">
                         1 in <input id="international_count" type="text" name="international_count" value="<%= @params[:international_count] %>"> replacement shipments (<input id="international_percentage" type="text" name="international_percentage" value="<%= @params[:international_percentage] %>"><span class="override_span input_align">%</span> ) require international shipping to prevent loosing the customer. The average cost of international shipping is <addon class="left-inner-addon">
                         &nbsp;&nbsp;<span class="override_span input_align">$</span>
                         <input id="avg_order_profit" type="text" name="avg_order_profit" value="<%= @params[:avg_order_profit] %>" style="margin: 4px -15px 10px"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="reshipment" type="text" name="reshipment" value="<%= @params[:reshipment] %>"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                        Estimated cost of Box, Packing Paper, Label, Tape & printed or promotional materials in Reshipment
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_labor_reshipment" type="text" name="cost_labor_reshipment" value="<%= @params[:cost_labor_reshipment] %>"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                         Cost of labor for pick pack on reshipment
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_apology" type="text" name="cost_apology" value="<%= @params[:cost_apology] %>"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                        Cost of Gift/Apology item
                      </h4>
                    </div>
                  </div>

                </div>
                <h4>
                  These costs are related to getting the correct or missing item(s) to the customer after the error is reported.
                  This cost will be part of every error except when the customer cancels the order as a result of the error.
                  Return shipment costs related to the customer returning an incorrect item are covered in the next section.
                </h4>
              </div>

            </div>

            


          </div>
        </div>


        <div class="row replacement out_box box_margin">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Return Shipment OR Product Abandonment</strong></h4>
            <h1><strong>$<span id="return_shipment_or_abandonment"><%= @params[:return_shipment_or_abandonment] %></span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox">
                  <h4>
                    Any time an incorrect item is sent there will either be a return shipment cost or a product abandonment cost. The calculation is based on an incorrect item getting sent in <addon class="right-inner-addon"><input type="text" id="total_error_shipment" name="total_error_shipment" value="<%= @params[:total_error_shipment] %>"><span class="override_span input_align">%</span></addon> of the error shipments.
                  </h4>
                  <h4 class="right-inner-addon">
                    In <addon class="right-inner-addon"><input type="text" id="product_abandonment_percentage" value="<%= @params[:product_abandonment_percentage] %>"><span class="override_span input_align">%</span></addon> of these instances the incorrect item will be gifted to the customer as the cost is less than the cost of the return. The average cost of the abandoned item is <addon class="left-inner-addon">&nbsp;&nbsp;<span class="override_span input_align">$</span> <input id="avg_product_abandonment" type="text" name="avg_product_abandonment" value="<%= @params[:avg_product_abandonment] %>" style="margin: 4px -15px 10px"></addon>
                  </h4>
                  <h4 class="right-inner-addon">
                    In <input id="return_shipping_percentage" type="text" name="return_shipping_percentage" value="<%= @params[:return_shipping_percentage] %>"><span class="override_span input_align">%</span> of these instances the incorrect item will need to be returned and the list of costs below will apply:
                  </h4>
                  <ul>
                    <li class="left-inner-addon"><span>$</span>
                      <input type="text" id="cost_return" name="cost_return" value="<%= @params[:cost_return] %>"> Optional Cost of return shipping to 3PL and associated 3PL fees
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="return_shipping_cost" type="text" name="return_shipping_cast" value="<%= @params[:return_shipping_cost] %>"> 6.00 Cost of return postage from the Customer (or 3PL facility)
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="return_shipping_insurance" type="text" name="return_shipping_insurance" value="<%= @params[:return_shipping_insurance] %>"> Cost of insurance and or package tracking on return shipment
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="cost_recieving_process" type="text" name="cost_recieving_process" value="<%= @params[:cost_recieving_process] %>"> Cost of receiving and processing the physical return
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="cost_confirm" type="text" name="cost_confirm" value="<%= @params[:cost_confirm] %>"> Cost of communication to confirm it's receipt with the customer or follow up if it is not received as expected.
                    </li>
                  </ul>
                  
                </div>
              </div>
              
            </div>

            <h4>
              These costs only apply when the wrong item has been sent to the customer and will need to be returned or abandoned because the cost of bringing it back is more than the cost of the product.
            </h4>

          </div>
        </div>


        <h2>Intangible Costs</h2>
        <div class="row replacement out_box" style="padding-bottom: 10px;">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4></h4>
            <br><br><br><br>
            <h1><strong>$<span id="intangible_cost"><%= @params[:intangible_cost] %></span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox out_box_margin">
                  <br>

                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="misc_cost" value="<%= @params[:misc_cost] %>"></div>
                    <div class="col-md-11">
                      <h4>
                        Misc. Cost not accounted for elsewhere
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="cancel_order_shipment" value="<%= @params[:cancel_order_shipment] %>" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="incorrect_current_order" value="<%= @params[:incorrect_current_order] %>"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="incorrect_current_order_per" value="<%= @params[:incorrect_current_order_per] %>"> <span class="override_span input_align">%</span></addon>) cause the customer to cancel the current order. The estimated profit for an average order is &nbsp; &nbsp;<addon class="left-inner-addon"><span class="input_align">$</span><input type="text" id="avg_current_order" value="<%= @params[:avg_current_order] %>"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span><input type="text" id="lifetime_order_val" value="<%= @params[:lifetime_order_val] %>"  disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="incorrect_lifetime_order" value="<%= @params[:incorrect_lifetime_order] %>"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="incorrect_lifetime_order_per" value="<%= @params[:incorrect_lifetime_order_per] %>"> <span class="override_span input_align">%</span></addon>) cause the customer to place all future orders with competitors. The estimated lifetime value of a customer is &nbsp; &nbsp;<addon class="left-inner-addon"><span class="input_align">$</span><input type="text" id="lifetime_val" value="<%= @params[:lifetime_val] %>"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="negative_post_review" value="<%= @params[:negative_post_review] %>" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="negative_shipment" value="<%= @params[:negative_shipment] %>"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="negative_shipment_per" value="<%= @params[:negative_shipment_per] %>"><span class="override_span input_align">%</span> </addon>) result in a negative post or review which causes a potential customer to do business with a competitor instead. Based on the lifetime value of $<span id="lifetime_value"></span>.
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon"><span>$</span><input type="text" id="inventory_shortage" value="<%= @params[:inventory_shortage] %>" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="inventory_shortage_order" value="<%= @params[:inventory_shortage_order] %>"> incorrect shipments (<addon class="right-inner-addon"> <input type="text" id="inventory_shortage_order_per" value="<%= @params[:inventory_shortage_order_per] %>"><span class="override_span input_align">%</span> </addon>) result in an inventory shortage or oversell which costs a sale. The estimated profit per sale of $<span id="avg_current_order_val"><%= @params[:avg_current_order_val] %></span> is used for this calculation.
                      </h4>
                    </div>
                  </div>
                  <br>
                  <br>
                  <br>
                  <br><br>
                                
                </div>
              </div>
              
            </div>


          </div>
        </div>

        <div class="col-md-12">
          <div class="col-md-3"><h2 style="padding-top: 10px;"><strong>Totals</strong></h2></div>
          <div class="col-md-2"></div>
          <div class="col-md-7">
            <button class="right btn left" id="save_link_button" data-remote="true" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 25px;margin-top: 25px;">Save & Continue</button>
          </div>
        </div>

        <div class="row total" >
          <div class="col-md-12 col-sm-12 col-xs-12 pd-none out_box" style="padding-top: 15px;">

            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Total Cost per error</h3>
                  <h1><strong>$<span id="total_cost"><%= @params[:total_cost] %></span></strong></h1>
                </div>
              </div>
              <div class="col-md-3 col-sm-3 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Error cost per day</h3>
                  <h1><strong>$<span id ="error_cost_per_day"><%= @params[:error_cost_per_day] %></span></strong></h1>
                </div>
              </div>
              <div class="col-md-5 col-sm-5 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Monthly cost of shipping errors</h3>
                  <h1 class="data_div" style="color: #be1e2d;"><strong>$<span id="monthly_shipping">
                    <%= @params[:monthly_shipping] %></span></strong></h1>
                  <h2 class="save_and_continue"><b style="color: #8e8d8d">Click Save & Continue</b></h2>
                </div>
              </div>
            </div>

            <div class="row">

              <div class="col-md-5 col-sm-5 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Montly Cost of GroovePacker</h3>
                  <h1><strong>$<span id="gp_cost"><%= @params[:gp_cost] %></span></strong></h1>
                </div>
              </div>

              <div class="col-md-1 col-sm-1 col-xs-12"></div>

              <div class="col-md-6 col-sm-6 col-xs-12 text-center right pd-lft-nn">
                <div class="box overridebox">
                  <h3>Expected Savings Per month using GroovePacker</h3>
                  <h1 class="data_div" style="color: #006838;"><strong>$<span id="monthly_saving"><%= @params[:monthly_saving] %></span></strong></h1>
                  <h2 class="save_and_continue"><b style="color: #8e8d8d">Click Save & Continue</b></h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
      <% if params[:from_app].blank? %>
        <h3 style="padding-left: 5px;">Tell your team</h3>
        <div class="out_box out_box_margin" style="padding: 20px;">
          <h4 style="padding-left: 5px;">
            Everyone knows that shipping errors are costly, both financially and to the reputation of your brand, but without putting a number on the actual costs it’s difficult to justify an investment which solves the problem.<br/><br/>

            Our aim with this calculator is to make these costs clear, and in some cases, highlight situations where implementing GroovePacker would more than pay for itself many times over.<br/><br/>

            You can share these results with your team using the form below. <br/><br/>

            If you would like to receive a couple additional emails which explore other benefits like simplified training, packer performance visibility and more, please check the box beside your email address below.
          </h4>
          <div class="row total out_box_margin" style="background-color: rgba(0,0,0,0.5); opacity: 0.8;">
            <div class="col-md-12 col-sm-12 col-xs-12 pd-none">
            <div class="row">
              <div>
                <h3>
               ​<textarea style="max-width: 100%;" id="email_text" name="email_text"  rows="5" cols="87"><%= (URI.decode(@params[:email_text]) rescue nil) || 
            "Hi Guys, 
  Looks like we are spending about $#{@params[:monthly_shipping]} each month on shipping errors. Please follow the link below to see the details of this estimate. You can adjust any of the inputs to refine the calculation." %></textarea>
                </h3>
              </div>
              <h3 style="color: white;">Save and email my cost calculator link to me and my team at the following addresses: </h3>
              <div class="col-md-12 out_box email_body" style="padding-top: 20px;">

                <div class="alert alert-success alert-dismissible" role="alert" id="message"> 
                  <button  id="remove_message" type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span></button>
                    <span class="notice"></span>
                </div>

                <!-- <div class="alert alert-success" id="message" style="width: 100% ">
                  <p class="notice" style="width: 90%"></p> &nbsp;&nbsp;<i style="width: 10%" id="remove_message" class="fa fa-times" style="float: right;"></i></div> -->
                <div class="col-md-1"></div>
                <div class="col-md-7" style="line-height: 48px;">
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Your Name</b></h4> <input type="text" id="recipient_name" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Your Email</b></h4><input type="text" id="recipient_one" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Team Email 1</b></h4><input type="text" id="recipient_two" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Team Email 2</b></h4><input type="text" id="recipient_three" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div style="display:flex; margin-left: 186px;">  
                    <input type="checkbox" name="follow_up_email" id="follow_up_email" style="margin: 15px 0 0; width: 123px; height: 21px;">
                    <h5 style="color: white;"><b>Check this box if you would like to recieve  special offers (your email only)</b></h5>
                    <div style="text-align: right;" ><button id="send_calculated_email" class="right btn" style="padding: 6px 30px;font-size: 20px;">Send</button>
                    </div>
                  </div>
                </div>
               <!--   <div> 
                    <input type="checkbox" name="follow_up_email" id="follow_up_email" style="margin: 15px 0 0">
                    </div>
                  <div> 
                    <h5 style="color: white;"> You may send up to 2 follow up emails about GroovePacker to this address only</h5>
                  </div><br> -->
                <div class="col-md-4"></div>
              </div>
              <!-- <div class="col-md-5 left">
                <div class="col-md-1 right"> 
                  <input type="checkbox" name="follow_up_email" id="follow_up_email" style="margin: 15px 0 0">
                  </div>
                <div class="col-md-11 left"> 
                  <h5 style="color: white;"> You may send up to 2 follow up emails about GroovePacker to this address only</h5>
                </div><br>
              </div> -->
            </div>
            </div>
          </div>
          <div class="box_margin">
            <div style="display: flex;">
              <div class="col-md-2"></div>
              <div class="col-md-6 right footer_out_box">
                <div class="col-sm-3" style="padding-top: 6px;">
                  <i class="fa fa-graduation-cap fa-5x" aria-hidden="true"></i>
                </div>
                <div class="col-sm-9">
                  <h3>Interested in learning more?</h3>&emsp;
                  <p style="margin-top: -30px;">See a demo and get your questions answered</p>
                </div>
              </div>
              <button class="right btn col-md-2" onclick="window.open('https://groovepacker.com/see-a-demo', '_blank');" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 15px;margin-top: 15px; margin-left: 8px;">Learn More</button>
            </div>

            <div style="display: flex;">
              <div class="col-md-2"></div>
              <div class="col-md-6 right footer_out_box">
                <div class="col-sm-3" style="padding-left: 30px; padding-top: 15px;"><i class="fa fa-play-circle fa-5x" aria-hidden="true"></i></div>
                <div class="col-sm-9">
                  <h3>Ready to Groove?</h3>&emsp;
                  <p style="margin-top: -30px;">Select a plan from our pricing page and start your <br/> free trial today.</p>
                </div>
              </div>
              <button class="right btn col-md-2" onclick="window.open('https://groovepacker.com/pricing','_blank');" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 25px;margin-top: 25px; margin-left: 8px;">Get Started</button>
            </div>
          </div>
        </div>
      <%end%>

    <div class="clearfix"></div>
  </div>
<div class="clearfix"></div>
<style type="text/css">
.gradient-well-inner{
  max-width: 1200px !important;
}

.main-body{
  margin: 0vh auto !important;
}
</style>