<div class="row">
  <div class="box-outer">
    <div class="box">
      <%= render 'shared/existing_customer', shop: @shop_name %>
    </div>
    <div class="col-sm-offset-3 col-sm-6">
      <img src="/assets/images/logo.png" class="col-xs-12" alt="GroovePacker"/>
    </div>
  </div>
</div>
<div class="row bottom-well col-md-offset-1 col-md-10">
  <div class="row">
    <div class="col-md-offset-3 col-md-6 well-header text-center">
      <p>Choose Your Free Trial Account Type</p>
    </div>
  </div>
  <% @plans.each do |plan| %>
    <div class="col-md-2 price-column">
      <div class="col-box-primary">
        <%= plan['name'] %><br>$<%= plan['amount'] %><br> /Month
      </div>
      <div class="col-box">
        <%= plan['shipments'] %></br> Shipments<br/> <i class="fa fa-dropbox" style="font-size: 40px;"></i>
      </div>
      <div class="col-box">
        <%= plan['users'] %> Users<br/>
        <% for j in 1..plan['users'].to_i %>
          <% if plan['users'].to_i > 4 %>
            <i class="fa fa-user" style="font-size: 20px; margin-top: 5px;"></i>
          <% else %>
            <i class="fa fa-user" style="font-size: 40px; margin-top: 5px;"></i>
          <% end %>
        <% end %>
      </div>
      <div class="col-box">
        <%= plan['stores'] %><br/>&nbsp;Import&nbsp;Sources&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br/> <i class="fa fa-cloud-download" style="font-size: 40px; margin-top: 0px;"></i>
      </div>
      <br/>

      <div class="text-center">
        <%= link_to 'Get Started', {action: 'new', controller: 'subscriptions', plan_id: plan['plan_id'], shop_name: params[:shop_name], shop_type: params[:shop_type], email: params[:email]}, class: "btn btn-success" %>
      </div>
    </div>
  <% end %>
  <div class="row">
    <% if @plan_error != '' %>
      <div class="col-md-offset-3 col-md-6 well-footer text-center">
        <p><%=@plan_error%></p>
      </div>
    <% end %>
  </div>
</div>

