<div class="row">
  <div class="box-outer">
    <div class="box"></div>
    <div class="col-sm-offset-3 col-sm-6">
      <img src="/assets/images/logo.png" class="col-xs-12" alt="GroovePacker"/>
    </div>
  </div>
</div>

<div class="row bottom-well col-sm-offset-3 col-md-offset-3 col-lg-offset-3 col-md-6 col-sm-6 col-lg-6">
  <legend class="text-center">Subscribe here</legend>
  <div>
    <form action="" method="POST" class="form-vertical" name="myForm" id="my_form">
      <fieldset>
        <div class="form-group">
          <input type="hidden" id="plan_id" value="<%= params[:plan_id] %>"/>
          <label class="control-label">Choose your site address*</label>

          <div class="input-group">
            <div class="input-group-addon">https://</div>
            <input type="text" id="tenant_name" class="form-control" autofocus value="<%= non_hyphenated_string(params[:shop_name]) %>"/>

            <div class="input-group-addon">.groovepacker.com</div>
          </div>
          <div class="form-group error" style="color: red;">
            <div id="error-tenant-block" class="help-inline"></div>
          </div>
        </div>

        <div class="form-group">
          <label for="email" class="control-label">Email*</label>
          <input type="email" id="email" class="form-control"
                 value="<%= params[:email] %>"/>

          <div class="form-group error" style="color: red;">
            <div id="error-email-block" class="help-inline"></div>
          </div>
        </div>

        <hr>

        <div class="form-group">
          <label for="user_name" class="control-label">Username*</label>
          <input type="text" id="user_name" class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div id="error-username-block" class="help-inline"></div>
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="control-label">Password*</label>
          <input type="password" id="password" class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div id="error-password-block" class="help-inline"></div>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label">Password Confirmation*</label>
          <input type="password" id="password_conf" class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div id="error-password-conf-block" class="help-inline"></div>

            <input type="hidden" id="stripe_public_key" class="input-large"
                   value="<%= ENV['STRIPE_PUBLIC_KEY'] %>"/>
            <input type="hidden" id="one_time_payment" class="input-large"
                   value="<%=  one_time_payment(params) %>"/>
          </div>
        </div>

        <div class="form-group">
          <div class="checkbox control-label" style="font-weight:bold;"><input type="checkbox" id="tos_checkbox"/>
            I agree with the <a href="" id="tos_link">Terms of Service</a> and
            <a href="" id="privacy_link">Privacy Policy</a></div>
          <div class="form-group error" style="color: red;">
            <div id="error-tos-block" class="help-inline"></div>
          </div>
        </div>
        <div class="form-group" id="privacy_iframe" style="display:none;">
          <iframe src="/assets/views/privacypolicy.html" width="100%" height="200px"></iframe>
        </div>
        <div class="form-group" id="tos_iframe" style="display:none;">
          <iframe src="/assets/views/termsofservicetos.html" width="100%" height="200px"></iframe>
        </div>
        <hr width="95%">
        <% if params[:shop_name].nil? %>
          <div>
            <h4>
              <center>Risk Free 30-day Evaluation</center>
            </h4>
          </div>
          <div style="padding-left:12px;font-size: 14px;">
            <label>If you are not completely satisfied, please let us know at any time during the evaluation period and
              all charges will be credited in full.</label>
          </div>

          <div style="padding-left:23px;"><h4>Please choose how you would like to be billed:</h4></div>
          <div class="form-group" style="padding-top: 2px;margin-top: -5px;">
            <div>
              <label class="radio" style="margin-left: -5px;"><input type="radio" id="radio_subscription1" name="radio_subscription" value="monthly" checked/>Monthly:($<%= @monthly_amount/100 %>
                .00)</label>

              <div style="padding-left:23px;width: 97%;font-size: 14px; margin-top: -10px;">
                <label id="monthly_billing_label">The first monthly billing will be charged after 30 days. A one time
                  initialization charge of $500 is paid today on deployment.</label>
                <label id="monthly_discount_label"></label>
              </div>
            </div>
            <div style="padding-top: 5px;">
              <label class="radio" style="margin-left: -5px;"><input type="radio" id="radio_subscription2" name="radio_subscription" value="annually" style="-webkit-appearance:block; background:green;"/>Annually:(<strike>$<%= @monthly_amount*12/100 %>
                .00</strike>)($<%= @annually_amount/100 %>.00) Save 10%</label>

              <div style="padding-left:23px;width: 85%;font-size: 14px; margin-top: -10px;">
                <label id="annual_billing_label">The first annual billing will be charged after 30 days at a 10%
                  discount. A one time initialization charge of $500 is paid today on deployment.</label>
                <label id="annual_discount_label"></label>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="coupon_id" class="control-label">Promotional Code</label>

            <div class="input-group">
              <input type="text" id="coupon_id" class="form-control" value="<%= params[:coupon_id] %>"/>
              <span class="input-group-btn">
                <button id="apply" class="btn btn-primary">Apply</button>
              </span>
            </div>
            <div class="form-group error">
              <div id="success-coupon-block" class="help-inline" style="color: green;"></div>
              <div id="error-coupon-block" class="help-inline" style="color: red;"></div>
            </div>
          </div>

          <script src="https://checkout.stripe.com/checkout.js"></script>
          <div class="">
            <button id="customButton" class="btn btn-primary" style="margin-bottom: 15px;">Purchase</button>

            <div class="alert alert-success" id="processing_alert_success" style="display:none;">
              <div><i class="glyphicon glyphicon-refresh spin" style="font-size: 12px;"></i> Your subscription is being
                processed.
              </div>
            </div>
            <div class="alert alert-danger" id="processing_alert_error" style="display:none;">
            </div>
          </div>
        <% else %>
          <%= render 'payment_form' %>
        <% end %>
      </fieldset>
    </form>
    <script>
      var valid = {
        tenant_name: false,
        email: false,
        user_name: false,
        password: false,
        password_conf: false,
        tos: false
      };

      var radio_button_value = null;
      var amount = 0;
      var plan_id = null;
      var coupon_id = null;
      var one_time_payment = $('#one_time_payment').val();
      var show_coupon_block = false;

      $('html').click(function () {
        $('#tos_iframe').hide();
        $('#privacy_iframe').hide();
      });
      $('#tos_link').click(function (event) {
        event.preventDefault();
        event.stopPropagation();
        $('#privacy_iframe').hide();
        $('#tos_iframe').show();
      });
      $('#privacy_link').click(function (event) {
        event.preventDefault();
        event.stopPropagation();
        $('#tos_iframe').hide();
        $('#privacy_iframe').show();
      });
      $('#shopButton').on('click', function (event) {
        event.preventDefault();
        plan_id = $('#plan_id').val();
        open_handler(plan_id, "<%= params[:shop_name] %>");
      })

      var handler = StripeCheckout.configure({
        key: $('#stripe_public_key').val(),
        image: '/assets/images/apple-touch-icon.png',
        token: function (token) {
          $.ajax({
            type: "POST",
            contentType: "application/json; charset=utf-8",
            url: "/subscriptions/confirm_payment",
            data: JSON.stringify({
              tenant_name: $('#tenant_name').val(),
              stripe_user_token: token.id,
              email: $('#email').val(),
              amount: amount,
              plan_id: plan_id,
              user_name: $('#user_name').val(),
              password: $('#password').val(),
              radio_subscription: radio_button_value,
              coupon_id: coupon_id,
              shop_name: "<%= params[:shop_name] %>",
              shop_type: "<%= params[:shop_type] %>"
            }),
            dataType: "json"

          }).error(function (response) {
            $('#processing_alert_success').hide();
            $('#customButton').show();
            $('#shopButton').show();
            $('#processing_alert_error').show();
            $('#processing_alert_error').append(
              "<p>Your subscription could not be processed. Please try again.</p>"
            )
          }).success(function (response) {
            if (!response.valid) {
              $('#processing_alert_success').hide();
              $('#customButton').show();
              $('#shopButton').show();
              $('#processing_alert_error').show();
              $('#processing_alert_error').append(
                "<p>" + response.errors + "</p>"
              )
            }
            else {
              var redirect_url = 'subscriptions/show?transaction_id=' + response.transaction_id + '&notice=' + response.notice + '&email=' + response.email + '&next_date=' + response.next_date
              window.location.href = redirect_url
            }
          });
          $('#customButton').hide();
          $('#shopButton').hide();
          $('#processing_alert_success').show();
          $('#processing_alert_error').hide();
        }
      });

      $(document).ready(function () {
        show_coupon_block = false;
        checkOneTimePayment();
      });

      $('#apply').on('click', function (a) {
        a.preventDefault();
        show_coupon_block = true;
        checkOneTimePayment();
      });

      var checkOneTimePayment = function () {
        var coupon = $('#coupon_id').val();
        if (coupon != '') {
          $.ajax({
            type: "GET",
            contentType: "application/json; charset=utf-8",
            url: "/subscriptions/validate_coupon_id",
            data: {coupon_id: $('#coupon_id').val()},
            dataType: "json"
          }).error(function (response) {

          }).success(function (response) {
            if (response.status == false) {
              coupon_id = null;
              one_time_payment = $('#one_time_payment').val();
              if (show_coupon_block) {
                $('#error-coupon-block').show();
                $('#success-coupon-block').hide();
              }
              ;
              $('#error-coupon-block').html(response.messages);
              $('#monthly_discount_label').hide();
              $('#annual_discount_label').hide();
              $('#monthly_billing_label').show();
              $('#annual_billing_label').show();
            }
            else {
              coupon_id = $('#coupon_id').val();
              one_time_payment = $('#one_time_payment').val() - response.discount_amount;
              if (show_coupon_block) {
                $('#error-coupon-block').hide();
                $('#success-coupon-block').show();
                $('#success-coupon-block').html(response.messages);
              }
              ;
              $('#monthly_billing_label').hide();
              $('#monthly_discount_label').show();
              $('#monthly_discount_label').html('The first monthly billing will be charged after 30 days. A one time initialization charge of <strike>$500</strike> $' + one_time_payment / 100 + '($' + response.discount_amount / 100 + ' off) is paid today on deployment.');
              $('#annual_billing_label').hide();
              $('#annual_discount_label').show();
              $('#annual_discount_label').html('The first annual billing will be charged after 30 days at a 10% discount. A one time initialization charge of <strike>$500</strike> $' + one_time_payment / 100 + '($' + response.discount_amount / 100 + ' off) is paid today on deployment.');
            }
          });
        }
        ;
      };

      $('#tenant_name').blur(function () {
        Subscription.validate_tenant_name();
      });

      $('#email').blur(function () {
        Subscription.validate_email();
      });

      $('#user_name').blur(function () {
        Subscription.validate_username();
      });

      $('#password').blur(function () {
        Subscription.validate_password();
      });

      $('#password_conf').blur(function () {
        Subscription.validate_password_confirmation();
      });

      $('#tos_checkbox').click(function () {
        Subscription.validate_tos();
      });

      var open_handler = function (plan_id, channel) {
        $.ajax({
          type: "GET",
          contentType: "application/json; charset=utf-8",
          url: "/subscriptions/plan_info",
          data: {plan_id: plan_id},
          dataType: "json"
        }).error(function (response) {

        }).success(function (response) {
          if (response.status == true) {
            amount = response.plan_info.amount;
          }
          setTimeout(function () {
            if (Subscription.validate()) {
              // Open Checkout with further options
              var description = null;
              var amount = 0;
              if (channel == 'Shopify' || channel == 'BigCommerce') {
                amount = 0;
                description = 'Add a card to your account';
              } else {
                amount = one_time_payment;
                description = 'Initialization fee ($' +
                  Number(one_time_payment / 100).toFixed(2) + ')';
              }

              handler.open({
                name: 'Groovepacker',
                description: description,
                amount: amount,
                email: $('#email').val(),
                allowRememberMe: false
              });
            }
          }, 400);
        });
      }
      $('#customButton').on('click', function (e) {
        e.preventDefault();
        if ($("input[name='radio_subscription']:checked").length > 0) {
          radio_button_value = $('input:radio[name=radio_subscription]:checked').val();
          if (radio_button_value == 'annually') {
            var str = $('#plan_id').val();
            //var plan_str = str.substring(0,str.lastIndexOf('-'));
            plan_id = 'an-' + str;
          }
          else {
            plan_id = $('#plan_id').val();
          }
          open_handler(plan_id, "regular");
        }
      });
    </script>

    <script type="text/javascript" src="https://static.leaddyno.com/js"></script>
    <script>
       LeadDyno.key = "43a989cd4a60d892c484bbfc92d2db94da03236b";
       LeadDyno.recordVisit();
       LeadDyno.autoWatch();
    </script>
  </div>
</div>
</div>

