!function (a, b) {
  b["true"] = a, /**
   * @license Rangy, a cross-browser JavaScript range and selection library
   * http://code.google.com/p/rangy/
   *
   * Copyright 2012, <PERSON>
   * Licensed under the MIT license.
   * Version: 1.2.3
   * Build date: 26 February 2012
   */
    window.rangy = function () {
      function a(a, b) {
        var c = typeof a[b];
        return c == l || !(c != k || !a[b]) || "unknown" == c
      }

      function b(a, b) {
        return !(typeof a[b] != k || !a[b])
      }

      function c(a, b) {
        return typeof a[b] != m
      }

      function d(a) {
        return function (b, c) {
          for (var d = c.length; d--;)if (!a(b, c[d]))return !1;
          return !0
        }
      }

      function e(a) {
        return a && r(a, q) && t(a, p)
      }

      function f(a) {
        window.alert("Rangy not supported in your browser. Reason: " + a), u.initialized = !0, u.supported = !1
      }

      function g(a) {
        var b = "Rangy warning: " + a;
        u.config.alertOnWarn ? window.alert(b) : typeof window.console != m && typeof window.console.log != m && window.console.log(b)
      }

      function h() {
        if (!u.initialized) {
          var c, d = !1, g = !1;
          a(document, "createRange") && (c = document.createRange(), r(c, o) && t(c, n) && (d = !0), c.detach());
          var h = b(document, "body") ? document.body : document.getElementsByTagName("body")[0];
          h && a(h, "createTextRange") && (c = h.createTextRange(), e(c) && (g = !0)), d || g || f("Neither Range nor TextRange are implemented"), u.initialized = !0, u.features = {
            implementsDomRange: d,
            implementsTextRange: g
          };
          for (var i = w.concat(v), j = 0, k = i.length; k > j; ++j)try {
            i[j](u)
          } catch (l) {
            b(window, "console") && a(window.console, "log") && window.console.log("Init listener threw an exception. Continuing.", l)
          }
        }
      }

      function i(a) {
        a = a || window, h();
        for (var b = 0, c = x.length; c > b; ++b)x[b](a)
      }

      function j(a) {
        this.name = a, this.initialized = !1, this.supported = !1
      }

      var k = "object", l = "function", m = "undefined", n = ["startContainer", "startOffset", "endContainer", "endOffset", "collapsed", "commonAncestorContainer", "START_TO_START", "START_TO_END", "END_TO_START", "END_TO_END"], o = ["setStart", "setStartBefore", "setStartAfter", "setEnd", "setEndBefore", "setEndAfter", "collapse", "selectNode", "selectNodeContents", "compareBoundaryPoints", "deleteContents", "extractContents", "cloneContents", "insertNode", "surroundContents", "cloneRange", "toString", "detach"], p = ["boundingHeight", "boundingLeft", "boundingTop", "boundingWidth", "htmlText", "text"], q = ["collapse", "compareEndPoints", "duplicate", "getBookmark", "moveToBookmark", "moveToElementText", "parentElement", "pasteHTML", "select", "setEndPoint", "getBoundingClientRect"], r = d(a), s = d(b), t = d(c), u = {
        version: "1.2.3",
        initialized: !1,
        supported: !0,
        util: {
          isHostMethod: a,
          isHostObject: b,
          isHostProperty: c,
          areHostMethods: r,
          areHostObjects: s,
          areHostProperties: t,
          isTextRange: e
        },
        features: {},
        modules: {},
        config: {alertOnWarn: !1, preferTextRange: !1}
      };
      u.fail = f, u.warn = g, {}.hasOwnProperty ? u.util.extend = function (a, b) {
        for (var c in b)b.hasOwnProperty(c) && (a[c] = b[c])
      } : f("hasOwnProperty not supported");
      var v = [], w = [];
      u.init = h, u.addInitListener = function (a) {
        u.initialized ? a(u) : v.push(a)
      };
      var x = [];
      u.addCreateMissingNativeApiListener = function (a) {
        x.push(a)
      }, u.createMissingNativeApi = i, j.prototype.fail = function (a) {
        throw this.initialized = !0, this.supported = !1, new Error("Module '" + this.name + "' failed to load: " + a)
      }, j.prototype.warn = function (a) {
        u.warn("Module " + this.name + ": " + a)
      }, j.prototype.createError = function (a) {
        return new Error("Error in Rangy " + this.name + " module: " + a)
      }, u.createModule = function (a, b) {
        var c = new j(a);
        u.modules[a] = c, w.push(function (a) {
          b(a, c), c.initialized = !0, c.supported = !0
        })
      }, u.requireModules = function (a) {
        for (var b, c, d = 0, e = a.length; e > d; ++d) {
          if (c = a[d], b = u.modules[c], !(b && b instanceof j))throw new Error("Module '" + c + "' not found");
          if (!b.supported)throw new Error("Module '" + c + "' not supported")
        }
      };
      var y = !1, z = function () {
        y || (y = !0, u.initialized || h())
      };
      return typeof window == m ? void f("No window found") : typeof document == m ? void f("No document found") : (a(document, "addEventListener") && document.addEventListener("DOMContentLoaded", z, !1), a(window, "addEventListener") ? window.addEventListener("load", z, !1) : a(window, "attachEvent") ? window.attachEvent("onload", z) : f("Window does not have required addEventListener or attachEvent method"), u)
    }(), rangy.createModule("DomUtil", function (a, b) {
    function c(a) {
      var b;
      return typeof a.namespaceURI == z || null === (b = a.namespaceURI) || "http://www.w3.org/1999/xhtml" == b
    }

    function d(a) {
      var b = a.parentNode;
      return 1 == b.nodeType ? b : null
    }

    function e(a) {
      for (var b = 0; a = a.previousSibling;)b++;
      return b
    }

    function f(a) {
      var b;
      return j(a) ? a.length : (b = a.childNodes) ? b.length : 0
    }

    function g(a, b) {
      var c, d = [];
      for (c = a; c; c = c.parentNode)d.push(c);
      for (c = b; c; c = c.parentNode)if (D(d, c))return c;
      return null
    }

    function h(a, b, c) {
      for (var d = c ? b : b.parentNode; d;) {
        if (d === a)return !0;
        d = d.parentNode
      }
      return !1
    }

    function i(a, b, c) {
      for (var d, e = c ? a : a.parentNode; e;) {
        if (d = e.parentNode, d === b)return e;
        e = d
      }
      return null
    }

    function j(a) {
      var b = a.nodeType;
      return 3 == b || 4 == b || 8 == b
    }

    function k(a, b) {
      var c = b.nextSibling, d = b.parentNode;
      return c ? d.insertBefore(a, c) : d.appendChild(a), a
    }

    function l(a, b) {
      var c = a.cloneNode(!1);
      return c.deleteData(0, b), a.deleteData(b, a.length - b), k(c, a), c
    }

    function m(a) {
      if (9 == a.nodeType)return a;
      if (typeof a.ownerDocument != z)return a.ownerDocument;
      if (typeof a.document != z)return a.document;
      if (a.parentNode)return m(a.parentNode);
      throw new Error("getDocument: no document found for node")
    }

    function n(a) {
      var b = m(a);
      if (typeof b.defaultView != z)return b.defaultView;
      if (typeof b.parentWindow != z)return b.parentWindow;
      throw new Error("Cannot get a window object for node")
    }

    function o(a) {
      if (typeof a.contentDocument != z)return a.contentDocument;
      if (typeof a.contentWindow != z)return a.contentWindow.document;
      throw new Error("getIframeWindow: No Document object found for iframe element")
    }

    function p(a) {
      if (typeof a.contentWindow != z)return a.contentWindow;
      if (typeof a.contentDocument != z)return a.contentDocument.defaultView;
      throw new Error("getIframeWindow: No Window object found for iframe element")
    }

    function q(a) {
      return A.isHostObject(a, "body") ? a.body : a.getElementsByTagName("body")[0]
    }

    function r(a) {
      for (var b; b = a.parentNode;)a = b;
      return a
    }

    function s(a, b, c, d) {
      var f, h, j, k, l;
      if (a == c)return b === d ? 0 : d > b ? -1 : 1;
      if (f = i(c, a, !0))return b <= e(f) ? -1 : 1;
      if (f = i(a, c, !0))return e(f) < d ? -1 : 1;
      if (h = g(a, c), j = a === h ? h : i(a, h, !0), k = c === h ? h : i(c, h, !0), j === k)throw new Error("comparePoints got to case 4 and childA and childB are the same!");
      for (l = h.firstChild; l;) {
        if (l === j)return -1;
        if (l === k)return 1;
        l = l.nextSibling
      }
      throw new Error("Should not be here!")
    }

    function t(a) {
      for (var b, c = m(a).createDocumentFragment(); b = a.firstChild;)c.appendChild(b);
      return c
    }

    function u(a) {
      if (!a)return "[No node]";
      if (j(a))return '"' + a.data + '"';
      if (1 == a.nodeType) {
        var b = a.id ? ' id="' + a.id + '"' : "";
        return "<" + a.nodeName + b + ">[" + a.childNodes.length + "]"
      }
      return a.nodeName
    }

    function v(a) {
      this.root = a, this._next = a
    }

    function w(a) {
      return new v(a)
    }

    function x(a, b) {
      this.node = a, this.offset = b
    }

    function y(a) {
      this.code = this[a], this.codeName = a, this.message = "DOMException: " + this.codeName
    }

    var z = "undefined", A = a.util;
    A.areHostMethods(document, ["createDocumentFragment", "createElement", "createTextNode"]) || b.fail("document missing a Node creation method"), A.isHostMethod(document, "getElementsByTagName") || b.fail("document missing getElementsByTagName method");
    var B = document.createElement("div");
    A.areHostMethods(B, ["insertBefore", "appendChild", "cloneNode"] || !A.areHostObjects(B, ["previousSibling", "nextSibling", "childNodes", "parentNode"])) || b.fail("Incomplete Element implementation"), A.isHostProperty(B, "innerHTML") || b.fail("Element is missing innerHTML property");
    var C = document.createTextNode("test");
    A.areHostMethods(C, ["splitText", "deleteData", "insertData", "appendData", "cloneNode"] || !A.areHostObjects(B, ["previousSibling", "nextSibling", "childNodes", "parentNode"]) || !A.areHostProperties(C, ["data"])) || b.fail("Incomplete Text Node implementation");
    var D = function (a, b) {
      for (var c = a.length; c--;)if (a[c] === b)return !0;
      return !1
    };
    v.prototype = {
      _current: null, hasNext: function () {
        return !!this._next
      }, next: function () {
        var a, b, c = this._current = this._next;
        if (this._current)if (a = c.firstChild)this._next = a; else {
          for (b = null; c !== this.root && !(b = c.nextSibling);)c = c.parentNode;
          this._next = b
        }
        return this._current
      }, detach: function () {
        this._current = this._next = this.root = null
      }
    }, x.prototype = {
      equals: function (a) {
        return this.node === a.node & this.offset == a.offset
      }, inspect: function () {
        return "[DomPosition(" + u(this.node) + ":" + this.offset + ")]"
      }
    }, y.prototype = {
      INDEX_SIZE_ERR: 1,
      HIERARCHY_REQUEST_ERR: 3,
      WRONG_DOCUMENT_ERR: 4,
      NO_MODIFICATION_ALLOWED_ERR: 7,
      NOT_FOUND_ERR: 8,
      NOT_SUPPORTED_ERR: 9,
      INVALID_STATE_ERR: 11
    }, y.prototype.toString = function () {
      return this.message
    }, a.dom = {
      arrayContains: D,
      isHtmlNamespace: c,
      parentElement: d,
      getNodeIndex: e,
      getNodeLength: f,
      getCommonAncestor: g,
      isAncestorOf: h,
      getClosestAncestorIn: i,
      isCharacterDataNode: j,
      insertAfter: k,
      splitDataNode: l,
      getDocument: m,
      getWindow: n,
      getIframeWindow: p,
      getIframeDocument: o,
      getBody: q,
      getRootContainer: r,
      comparePoints: s,
      inspectNode: u,
      fragmentFromNodeChildren: t,
      createIterator: w,
      DomPosition: x
    }, a.DOMException = y
  }), rangy.createModule("DomRange", function (a) {
    function b(a, b) {
      return 3 != a.nodeType && (L.isAncestorOf(a, b.startContainer, !0) || L.isAncestorOf(a, b.endContainer, !0))
    }

    function c(a) {
      return L.getDocument(a.startContainer)
    }

    function d(a, b, c) {
      var d = a._listeners[b];
      if (d)for (var e = 0, f = d.length; f > e; ++e)d[e].call(a, {target: a, args: c})
    }

    function e(a) {
      return new M(a.parentNode, L.getNodeIndex(a))
    }

    function f(a) {
      return new M(a.parentNode, L.getNodeIndex(a) + 1)
    }

    function g(a, b, c) {
      var d = 11 == a.nodeType ? a.firstChild : a;
      return L.isCharacterDataNode(b) ? c == b.length ? L.insertAfter(a, b) : b.parentNode.insertBefore(a, 0 == c ? b : L.splitDataNode(b, c)) : c >= b.childNodes.length ? b.appendChild(a) : b.insertBefore(a, b.childNodes[c]), d
    }

    function h(a) {
      for (var b, d, e, f = c(a.range).createDocumentFragment(); d = a.next();) {
        if (b = a.isPartiallySelectedSubtree(), d = d.cloneNode(!b), b && (e = a.getSubtreeIterator(), d.appendChild(h(e)), e.detach(!0)), 10 == d.nodeType)throw new N("HIERARCHY_REQUEST_ERR");
        f.appendChild(d)
      }
      return f
    }

    function i(a, b, c) {
      var d, e;
      c = c || {stop: !1};
      for (var f, g; f = a.next();)if (a.isPartiallySelectedSubtree()) {
        if (b(f) === !1)return void(c.stop = !0);
        if (g = a.getSubtreeIterator(), i(g, b, c), g.detach(!0), c.stop)return
      } else for (d = L.createIterator(f); e = d.next();)if (b(e) === !1)return void(c.stop = !0)
    }

    function j(a) {
      for (var b; a.next();)a.isPartiallySelectedSubtree() ? (b = a.getSubtreeIterator(), j(b), b.detach(!0)) : a.remove()
    }

    function k(a) {
      for (var b, d, e = c(a.range).createDocumentFragment(); b = a.next();) {
        if (a.isPartiallySelectedSubtree() ? (b = b.cloneNode(!1), d = a.getSubtreeIterator(), b.appendChild(k(d)), d.detach(!0)) : a.remove(), 10 == b.nodeType)throw new N("HIERARCHY_REQUEST_ERR");
        e.appendChild(b)
      }
      return e
    }

    function l(a, b, c) {
      var d, e = !(!b || !b.length), f = !!c;
      e && (d = new RegExp("^(" + b.join("|") + ")$"));
      var g = [];
      return i(new n(a, !1), function (a) {
        e && !d.test(a.nodeType) || f && !c(a) || g.push(a)
      }), g
    }

    function m(a) {
      var b = "undefined" == typeof a.getName ? "Range" : a.getName();
      return "[" + b + "(" + L.inspectNode(a.startContainer) + ":" + a.startOffset + ", " + L.inspectNode(a.endContainer) + ":" + a.endOffset + ")]"
    }

    function n(a, b) {
      if (this.range = a, this.clonePartiallySelectedTextNodes = b, !a.collapsed) {
        this.sc = a.startContainer, this.so = a.startOffset, this.ec = a.endContainer, this.eo = a.endOffset;
        var c = a.commonAncestorContainer;
        this.sc === this.ec && L.isCharacterDataNode(this.sc) ? (this.isSingleCharacterDataNode = !0, this._first = this._last = this._next = this.sc) : (this._first = this._next = this.sc !== c || L.isCharacterDataNode(this.sc) ? L.getClosestAncestorIn(this.sc, c, !0) : this.sc.childNodes[this.so], this._last = this.ec !== c || L.isCharacterDataNode(this.ec) ? L.getClosestAncestorIn(this.ec, c, !0) : this.ec.childNodes[this.eo - 1])
      }
    }

    function o(a) {
      this.code = this[a], this.codeName = a, this.message = "RangeException: " + this.codeName
    }

    function p(a, b, c) {
      this.nodes = l(a, b, c), this._next = this.nodes[0], this._position = 0
    }

    function q(a) {
      return function (b, c) {
        for (var d, e = c ? b : b.parentNode; e;) {
          if (d = e.nodeType, L.arrayContains(a, d))return e;
          e = e.parentNode
        }
        return null
      }
    }

    function r(a, b) {
      if (W(a, b))throw new o("INVALID_NODE_TYPE_ERR")
    }

    function s(a) {
      if (!a.startContainer)throw new N("INVALID_STATE_ERR")
    }

    function t(a, b) {
      if (!L.arrayContains(b, a.nodeType))throw new o("INVALID_NODE_TYPE_ERR")
    }

    function u(a, b) {
      if (0 > b || b > (L.isCharacterDataNode(a) ? a.length : a.childNodes.length))throw new N("INDEX_SIZE_ERR")
    }

    function v(a, b) {
      if (U(a, !0) !== U(b, !0))throw new N("WRONG_DOCUMENT_ERR")
    }

    function w(a) {
      if (V(a, !0))throw new N("NO_MODIFICATION_ALLOWED_ERR")
    }

    function x(a, b) {
      if (!a)throw new N(b)
    }

    function y(a) {
      return !L.arrayContains(P, a.nodeType) && !U(a, !0)
    }

    function z(a, b) {
      return b <= (L.isCharacterDataNode(a) ? a.length : a.childNodes.length)
    }

    function A(a) {
      return !!a.startContainer && !!a.endContainer && !y(a.startContainer) && !y(a.endContainer) && z(a.startContainer, a.startOffset) && z(a.endContainer, a.endOffset)
    }

    function B(a) {
      if (s(a), !A(a))throw new Error("Range error: Range is no longer valid after DOM mutation (" + a.inspect() + ")")
    }

    function C() {
    }

    function D(a) {
      a.START_TO_START = ab, a.START_TO_END = bb, a.END_TO_END = cb, a.END_TO_START = db, a.NODE_BEFORE = eb, a.NODE_AFTER = fb, a.NODE_BEFORE_AND_AFTER = gb, a.NODE_INSIDE = hb
    }

    function E(a) {
      D(a), D(a.prototype)
    }

    function F(a, b) {
      return function () {
        B(this);
        var c, d, e = this.startContainer, g = this.startOffset, h = this.commonAncestorContainer, j = new n(this, !0);
        e !== h && (c = L.getClosestAncestorIn(e, h, !0), d = f(c), e = d.node, g = d.offset), i(j, w), j.reset();
        var k = a(j);
        return j.detach(), b(this, e, g, e, g), k
      }
    }

    function G(c, d, g) {
      function h(a, b) {
        return function (c) {
          s(this), t(c, O), t(T(c), P);
          var d = (a ? e : f)(c);
          (b ? i : l)(this, d.node, d.offset)
        }
      }

      function i(a, b, c) {
        var e = a.endContainer, f = a.endOffset;
        (b !== a.startContainer || c !== a.startOffset) && ((T(b) != T(e) || 1 == L.comparePoints(b, c, e, f)) && (e = b, f = c), d(a, b, c, e, f))
      }

      function l(a, b, c) {
        var e = a.startContainer, f = a.startOffset;
        (b !== a.endContainer || c !== a.endOffset) && ((T(b) != T(e) || -1 == L.comparePoints(b, c, e, f)) && (e = b, f = c), d(a, e, f, b, c))
      }

      function m(a, b, c) {
        (b !== a.startContainer || c !== a.startOffset || b !== a.endContainer || c !== a.endOffset) && d(a, b, c, b, c)
      }

      c.prototype = new C, a.util.extend(c.prototype, {
        setStart: function (a, b) {
          s(this), r(a, !0), u(a, b), i(this, a, b)
        },
        setEnd: function (a, b) {
          s(this), r(a, !0), u(a, b), l(this, a, b)
        },
        setStartBefore: h(!0, !0),
        setStartAfter: h(!1, !0),
        setEndBefore: h(!0, !1),
        setEndAfter: h(!1, !1),
        collapse: function (a) {
          B(this), a ? d(this, this.startContainer, this.startOffset, this.startContainer, this.startOffset) : d(this, this.endContainer, this.endOffset, this.endContainer, this.endOffset)
        },
        selectNodeContents: function (a) {
          s(this), r(a, !0), d(this, a, 0, a, L.getNodeLength(a))
        },
        selectNode: function (a) {
          s(this), r(a, !1), t(a, O);
          var b = e(a), c = f(a);
          d(this, b.node, b.offset, c.node, c.offset)
        },
        extractContents: F(k, d),
        deleteContents: F(j, d),
        canSurroundContents: function () {
          B(this), w(this.startContainer), w(this.endContainer);
          var a = new n(this, !0), c = a._first && b(a._first, this) || a._last && b(a._last, this);
          return a.detach(), !c
        },
        detach: function () {
          g(this)
        },
        splitBoundaries: function () {
          B(this);
          var a = this.startContainer, b = this.startOffset, c = this.endContainer, e = this.endOffset, f = a === c;
          L.isCharacterDataNode(c) && e > 0 && e < c.length && L.splitDataNode(c, e), L.isCharacterDataNode(a) && b > 0 && b < a.length && (a = L.splitDataNode(a, b), f ? (e -= b, c = a) : c == a.parentNode && e >= L.getNodeIndex(a) && e++, b = 0), d(this, a, b, c, e)
        },
        normalizeBoundaries: function () {
          B(this);
          var a = this.startContainer, b = this.startOffset, c = this.endContainer, e = this.endOffset, f = function (a) {
            var b = a.nextSibling;
            b && b.nodeType == a.nodeType && (c = a, e = a.length, a.appendData(b.data), b.parentNode.removeChild(b))
          }, g = function (d) {
            var f = d.previousSibling;
            if (f && f.nodeType == d.nodeType) {
              a = d;
              var g = d.length;
              if (b = f.length, d.insertData(0, f.data), f.parentNode.removeChild(f), a == c)e += b, c = a; else if (c == d.parentNode) {
                var h = L.getNodeIndex(d);
                e == h ? (c = d, e = g) : e > h && e--
              }
            }
          }, h = !0;
          if (L.isCharacterDataNode(c))c.length == e && f(c); else {
            if (e > 0) {
              var i = c.childNodes[e - 1];
              i && L.isCharacterDataNode(i) && f(i)
            }
            h = !this.collapsed
          }
          if (h) {
            if (L.isCharacterDataNode(a))0 == b && g(a); else if (b < a.childNodes.length) {
              var j = a.childNodes[b];
              j && L.isCharacterDataNode(j) && g(j)
            }
          } else a = c, b = e;
          d(this, a, b, c, e)
        },
        collapseToPoint: function (a, b) {
          s(this), r(a, !0), u(a, b), m(this, a, b)
        }
      }), E(c)
    }

    function H(a) {
      a.collapsed = a.startContainer === a.endContainer && a.startOffset === a.endOffset, a.commonAncestorContainer = a.collapsed ? a.startContainer : L.getCommonAncestor(a.startContainer, a.endContainer)
    }

    function I(a, b, c, e, f) {
      var g = a.startContainer !== b || a.startOffset !== c, h = a.endContainer !== e || a.endOffset !== f;
      a.startContainer = b, a.startOffset = c, a.endContainer = e, a.endOffset = f, H(a), d(a, "boundarychange", {
        startMoved: g,
        endMoved: h
      })
    }

    function J(a) {
      s(a), a.startContainer = a.startOffset = a.endContainer = a.endOffset = null, a.collapsed = a.commonAncestorContainer = null, d(a, "detach", null), a._listeners = null
    }

    function K(a) {
      this.startContainer = a, this.startOffset = 0, this.endContainer = a, this.endOffset = 0, this._listeners = {
        boundarychange: [],
        detach: []
      }, H(this)
    }

    a.requireModules(["DomUtil"]);
    var L = a.dom, M = L.DomPosition, N = a.DOMException;
    n.prototype = {
      _current: null,
      _next: null,
      _first: null,
      _last: null,
      isSingleCharacterDataNode: !1,
      reset: function () {
        this._current = null, this._next = this._first
      },
      hasNext: function () {
        return !!this._next
      },
      next: function () {
        var a = this._current = this._next;
        return a && (this._next = a !== this._last ? a.nextSibling : null, L.isCharacterDataNode(a) && this.clonePartiallySelectedTextNodes && (a === this.ec && (a = a.cloneNode(!0)).deleteData(this.eo, a.length - this.eo), this._current === this.sc && (a = a.cloneNode(!0)).deleteData(0, this.so))), a
      },
      remove: function () {
        var a, b, c = this._current;
        !L.isCharacterDataNode(c) || c !== this.sc && c !== this.ec ? c.parentNode && c.parentNode.removeChild(c) : (a = c === this.sc ? this.so : 0, b = c === this.ec ? this.eo : c.length, a != b && c.deleteData(a, b - a))
      },
      isPartiallySelectedSubtree: function () {
        var a = this._current;
        return b(a, this.range)
      },
      getSubtreeIterator: function () {
        var a;
        if (this.isSingleCharacterDataNode)a = this.range.cloneRange(), a.collapse(); else {
          a = new K(c(this.range));
          var b = this._current, d = b, e = 0, f = b, g = L.getNodeLength(b);
          L.isAncestorOf(b, this.sc, !0) && (d = this.sc, e = this.so), L.isAncestorOf(b, this.ec, !0) && (f = this.ec, g = this.eo), I(a, d, e, f, g)
        }
        return new n(a, this.clonePartiallySelectedTextNodes)
      },
      detach: function (a) {
        a && this.range.detach(), this.range = this._current = this._next = this._first = this._last = this.sc = this.so = this.ec = this.eo = null
      }
    }, o.prototype = {BAD_BOUNDARYPOINTS_ERR: 1, INVALID_NODE_TYPE_ERR: 2}, o.prototype.toString = function () {
      return this.message
    }, p.prototype = {
      _current: null, hasNext: function () {
        return !!this._next
      }, next: function () {
        return this._current = this._next, this._next = this.nodes[++this._position], this._current
      }, detach: function () {
        this._current = this._next = this.nodes = null
      }
    };
    var O = [1, 3, 4, 5, 7, 8, 10], P = [2, 9, 11], Q = [5, 6, 10, 12], R = [1, 3, 4, 5, 7, 8, 10, 11], S = [1, 3, 4, 5, 7, 8], T = L.getRootContainer, U = q([9, 11]), V = q(Q), W = q([6, 10, 12]), X = document.createElement("style"), Y = !1;
    try {
      X.innerHTML = "<b>x</b>", Y = 3 == X.firstChild.nodeType
    } catch (Z) {
    }
    a.features.htmlParsingConforms = Y;
    var $ = Y ? function (a) {
      var b = this.startContainer, c = L.getDocument(b);
      if (!b)throw new N("INVALID_STATE_ERR");
      var d = null;
      return 1 == b.nodeType ? d = b : L.isCharacterDataNode(b) && (d = L.parentElement(b)), d = null === d || "HTML" == d.nodeName && L.isHtmlNamespace(L.getDocument(d).documentElement) && L.isHtmlNamespace(d) ? c.createElement("body") : d.cloneNode(!1), d.innerHTML = a, L.fragmentFromNodeChildren(d)
    } : function (a) {
      s(this);
      var b = c(this), d = b.createElement("body");
      return d.innerHTML = a, L.fragmentFromNodeChildren(d)
    }, _ = ["startContainer", "startOffset", "endContainer", "endOffset", "collapsed", "commonAncestorContainer"], ab = 0, bb = 1, cb = 2, db = 3, eb = 0, fb = 1, gb = 2, hb = 3;
    C.prototype = {
      attachListener: function (a, b) {
        this._listeners[a].push(b)
      }, compareBoundaryPoints: function (a, b) {
        B(this), v(this.startContainer, b.startContainer);
        var c, d, e, f, g = a == db || a == ab ? "start" : "end", h = a == bb || a == ab ? "start" : "end";
        return c = this[g + "Container"], d = this[g + "Offset"], e = b[h + "Container"], f = b[h + "Offset"], L.comparePoints(c, d, e, f)
      }, insertNode: function (a) {
        if (B(this), t(a, R), w(this.startContainer), L.isAncestorOf(a, this.startContainer, !0))throw new N("HIERARCHY_REQUEST_ERR");
        var b = g(a, this.startContainer, this.startOffset);
        this.setStartBefore(b)
      }, cloneContents: function () {
        B(this);
        var a, b;
        if (this.collapsed)return c(this).createDocumentFragment();
        if (this.startContainer === this.endContainer && L.isCharacterDataNode(this.startContainer))return a = this.startContainer.cloneNode(!0), a.data = a.data.slice(this.startOffset, this.endOffset), b = c(this).createDocumentFragment(), b.appendChild(a), b;
        var d = new n(this, !0);
        return a = h(d), d.detach(), a
      }, canSurroundContents: function () {
        B(this), w(this.startContainer), w(this.endContainer);
        var a = new n(this, !0), c = a._first && b(a._first, this) || a._last && b(a._last, this);
        return a.detach(), !c
      }, surroundContents: function (a) {
        if (t(a, S), !this.canSurroundContents())throw new o("BAD_BOUNDARYPOINTS_ERR");
        var b = this.extractContents();
        if (a.hasChildNodes())for (; a.lastChild;)a.removeChild(a.lastChild);
        g(a, this.startContainer, this.startOffset), a.appendChild(b), this.selectNode(a)
      }, cloneRange: function () {
        B(this);
        for (var a, b = new K(c(this)), d = _.length; d--;)a = _[d], b[a] = this[a];
        return b
      }, toString: function () {
        B(this);
        var a = this.startContainer;
        if (a === this.endContainer && L.isCharacterDataNode(a))return 3 == a.nodeType || 4 == a.nodeType ? a.data.slice(this.startOffset, this.endOffset) : "";
        var b = [], c = new n(this, !0);
        return i(c, function (a) {
          (3 == a.nodeType || 4 == a.nodeType) && b.push(a.data)
        }), c.detach(), b.join("")
      }, compareNode: function (a) {
        B(this);
        var b = a.parentNode, c = L.getNodeIndex(a);
        if (!b)throw new N("NOT_FOUND_ERR");
        var d = this.comparePoint(b, c), e = this.comparePoint(b, c + 1);
        return 0 > d ? e > 0 ? gb : eb : e > 0 ? fb : hb
      }, comparePoint: function (a, b) {
        return B(this), x(a, "HIERARCHY_REQUEST_ERR"), v(a, this.startContainer), L.comparePoints(a, b, this.startContainer, this.startOffset) < 0 ? -1 : L.comparePoints(a, b, this.endContainer, this.endOffset) > 0 ? 1 : 0
      }, createContextualFragment: $, toHtml: function () {
        B(this);
        var a = c(this).createElement("div");
        return a.appendChild(this.cloneContents()), a.innerHTML
      }, intersectsNode: function (a, b) {
        if (B(this), x(a, "NOT_FOUND_ERR"), L.getDocument(a) !== c(this))return !1;
        var d = a.parentNode, e = L.getNodeIndex(a);
        x(d, "NOT_FOUND_ERR");
        var f = L.comparePoints(d, e, this.endContainer, this.endOffset), g = L.comparePoints(d, e + 1, this.startContainer, this.startOffset);
        return b ? 0 >= f && g >= 0 : 0 > f && g > 0
      }, isPointInRange: function (a, b) {
        return B(this), x(a, "HIERARCHY_REQUEST_ERR"), v(a, this.startContainer), L.comparePoints(a, b, this.startContainer, this.startOffset) >= 0 && L.comparePoints(a, b, this.endContainer, this.endOffset) <= 0
      }, intersectsRange: function (a, b) {
        if (B(this), c(a) != c(this))throw new N("WRONG_DOCUMENT_ERR");
        var d = L.comparePoints(this.startContainer, this.startOffset, a.endContainer, a.endOffset), e = L.comparePoints(this.endContainer, this.endOffset, a.startContainer, a.startOffset);
        return b ? 0 >= d && e >= 0 : 0 > d && e > 0
      }, intersection: function (a) {
        if (this.intersectsRange(a)) {
          var b = L.comparePoints(this.startContainer, this.startOffset, a.startContainer, a.startOffset), c = L.comparePoints(this.endContainer, this.endOffset, a.endContainer, a.endOffset), d = this.cloneRange();
          return -1 == b && d.setStart(a.startContainer, a.startOffset), 1 == c && d.setEnd(a.endContainer, a.endOffset), d
        }
        return null
      }, union: function (a) {
        if (this.intersectsRange(a, !0)) {
          var b = this.cloneRange();
          return -1 == L.comparePoints(a.startContainer, a.startOffset, this.startContainer, this.startOffset) && b.setStart(a.startContainer, a.startOffset), 1 == L.comparePoints(a.endContainer, a.endOffset, this.endContainer, this.endOffset) && b.setEnd(a.endContainer, a.endOffset), b
        }
        throw new o("Ranges do not intersect")
      }, containsNode: function (a, b) {
        return b ? this.intersectsNode(a, !1) : this.compareNode(a) == hb
      }, containsNodeContents: function (a) {
        return this.comparePoint(a, 0) >= 0 && this.comparePoint(a, L.getNodeLength(a)) <= 0
      }, containsRange: function (a) {
        return this.intersection(a).equals(a)
      }, containsNodeText: function (a) {
        var b = this.cloneRange();
        b.selectNode(a);
        var c = b.getNodes([3]);
        if (c.length > 0) {
          b.setStart(c[0], 0);
          var d = c.pop();
          b.setEnd(d, d.length);
          var e = this.containsRange(b);
          return b.detach(), e
        }
        return this.containsNodeContents(a)
      }, createNodeIterator: function (a, b) {
        return B(this), new p(this, a, b)
      }, getNodes: function (a, b) {
        return B(this), l(this, a, b)
      }, getDocument: function () {
        return c(this)
      }, collapseBefore: function (a) {
        s(this), this.setEndBefore(a), this.collapse(!1)
      }, collapseAfter: function (a) {
        s(this), this.setStartAfter(a), this.collapse(!0)
      }, getName: function () {
        return "DomRange"
      }, equals: function (a) {
        return K.rangesEqual(this, a)
      }, isValid: function () {
        return A(this)
      }, inspect: function () {
        return m(this)
      }
    }, G(K, I, J), a.rangePrototype = C.prototype, K.rangeProperties = _, K.RangeIterator = n, K.copyComparisonConstants = E, K.createPrototypeRange = G, K.inspect = m, K.getRangeDocument = c, K.rangesEqual = function (a, b) {
      return a.startContainer === b.startContainer && a.startOffset === b.startOffset && a.endContainer === b.endContainer && a.endOffset === b.endOffset
    }, a.DomRange = K, a.RangeException = o
  }), rangy.createModule("WrappedRange", function (a) {
    function b(a) {
      var b = a.parentElement(), c = a.duplicate();
      c.collapse(!0);
      var d = c.parentElement();
      c = a.duplicate(), c.collapse(!1);
      var e = c.parentElement(), f = d == e ? d : g.getCommonAncestor(d, e);
      return f == b ? f : g.getCommonAncestor(b, f)
    }

    function c(a) {
      return 0 == a.compareEndPoints("StartToEnd", a)
    }

    function d(a, b, c, d) {
      var e = a.duplicate();
      e.collapse(c);
      var f = e.parentElement();
      if (g.isAncestorOf(b, f, !0) || (f = b), !f.canHaveHTML)return new h(f.parentNode, g.getNodeIndex(f));
      var i, j, k, l, m, n = g.getDocument(f).createElement("span"), o = c ? "StartToStart" : "StartToEnd";
      do f.insertBefore(n, n.previousSibling), e.moveToElementText(n); while ((i = e.compareEndPoints(o, a)) > 0 && n.previousSibling);
      if (m = n.nextSibling, -1 == i && m && g.isCharacterDataNode(m)) {
        e.setEndPoint(c ? "EndToStart" : "EndToEnd", a);
        var p;
        if (/[\r\n]/.test(m.data)) {
          var q = e.duplicate(), r = q.text.replace(/\r\n/g, "\r").length;
          for (p = q.moveStart("character", r); -1 == (i = q.compareEndPoints("StartToEnd", q));)p++, q.moveStart("character", 1)
        } else p = e.text.length;
        l = new h(m, p)
      } else j = (d || !c) && n.previousSibling, k = (d || c) && n.nextSibling, l = k && g.isCharacterDataNode(k) ? new h(k, 0) : j && g.isCharacterDataNode(j) ? new h(j, j.length) : new h(f, g.getNodeIndex(n));
      return n.parentNode.removeChild(n), l
    }

    function e(a, b) {
      var c, d, e, f, h = a.offset, i = g.getDocument(a.node), j = i.body.createTextRange(), k = g.isCharacterDataNode(a.node);
      return k ? (c = a.node, d = c.parentNode) : (f = a.node.childNodes, c = h < f.length ? f[h] : null, d = a.node), e = i.createElement("span"), e.innerHTML = "&#feff;", c ? d.insertBefore(e, c) : d.appendChild(e), j.moveToElementText(e), j.collapse(!b), d.removeChild(e), k && j[b ? "moveStart" : "moveEnd"]("character", h), j
    }

    a.requireModules(["DomUtil", "DomRange"]);
    var f, g = a.dom, h = g.DomPosition, i = a.DomRange;
    if (!a.features.implementsDomRange || a.features.implementsTextRange && a.config.preferTextRange) {
      if (a.features.implementsTextRange) {
        f = function (a) {
          this.textRange = a, this.refresh()
        }, f.prototype = new i(document), f.prototype.refresh = function () {
          var a, e, f = b(this.textRange);
          c(this.textRange) ? e = a = d(this.textRange, f, !0, !0) : (a = d(this.textRange, f, !0, !1), e = d(this.textRange, f, !1, !1)), this.setStart(a.node, a.offset), this.setEnd(e.node, e.offset)
        }, i.copyComparisonConstants(f);
        var j = function () {
          return this
        }();
        "undefined" == typeof j.Range && (j.Range = f), a.createNativeRange = function (a) {
          return a = a || document, a.body.createTextRange()
        }
      }
    } else!function () {
      function b(a) {
        for (var b, c = k.length; c--;)b = k[c], a[b] = a.nativeRange[b]
      }

      function c(a, b, c, d, e) {
        var f = a.startContainer !== b || a.startOffset != c, g = a.endContainer !== d || a.endOffset != e;
        (f || g) && (a.setEnd(d, e), a.setStart(b, c))
      }

      function d(a) {
        a.nativeRange.detach(), a.detached = !0;
        for (var b, c = k.length; c--;)b = k[c], a[b] = null
      }

      var e, h, j, k = i.rangeProperties;
      f = function (a) {
        if (!a)throw new Error("Range must be specified");
        this.nativeRange = a, b(this)
      }, i.createPrototypeRange(f, c, d), e = f.prototype, e.selectNode = function (a) {
        this.nativeRange.selectNode(a), b(this)
      }, e.deleteContents = function () {
        this.nativeRange.deleteContents(), b(this)
      }, e.extractContents = function () {
        var a = this.nativeRange.extractContents();
        return b(this), a
      }, e.cloneContents = function () {
        return this.nativeRange.cloneContents()
      }, e.surroundContents = function (a) {
        this.nativeRange.surroundContents(a), b(this)
      }, e.collapse = function (a) {
        this.nativeRange.collapse(a), b(this)
      }, e.cloneRange = function () {
        return new f(this.nativeRange.cloneRange())
      }, e.refresh = function () {
        b(this)
      }, e.toString = function () {
        return this.nativeRange.toString()
      };
      var l = document.createTextNode("test");
      g.getBody(document).appendChild(l);
      var m = document.createRange();
      m.setStart(l, 0), m.setEnd(l, 0);
      try {
        m.setStart(l, 1), h = !0, e.setStart = function (a, c) {
          this.nativeRange.setStart(a, c), b(this)
        }, e.setEnd = function (a, c) {
          this.nativeRange.setEnd(a, c), b(this)
        }, j = function (a) {
          return function (c) {
            this.nativeRange[a](c), b(this)
          }
        }
      } catch (n) {
        h = !1, e.setStart = function (a, c) {
          try {
            this.nativeRange.setStart(a, c)
          } catch (d) {
            this.nativeRange.setEnd(a, c), this.nativeRange.setStart(a, c)
          }
          b(this)
        }, e.setEnd = function (a, c) {
          try {
            this.nativeRange.setEnd(a, c)
          } catch (d) {
            this.nativeRange.setStart(a, c), this.nativeRange.setEnd(a, c)
          }
          b(this)
        }, j = function (a, c) {
          return function (d) {
            try {
              this.nativeRange[a](d)
            } catch (e) {
              this.nativeRange[c](d), this.nativeRange[a](d)
            }
            b(this)
          }
        }
      }
      e.setStartBefore = j("setStartBefore", "setEndBefore"), e.setStartAfter = j("setStartAfter", "setEndAfter"), e.setEndBefore = j("setEndBefore", "setStartBefore"), e.setEndAfter = j("setEndAfter", "setStartAfter"), m.selectNodeContents(l), e.selectNodeContents = m.startContainer == l && m.endContainer == l && 0 == m.startOffset && m.endOffset == l.length ? function (a) {
        this.nativeRange.selectNodeContents(a), b(this)
      } : function (a) {
        this.setStart(a, 0), this.setEnd(a, i.getEndOffset(a))
      }, m.selectNodeContents(l), m.setEnd(l, 3);
      var o = document.createRange();
      o.selectNodeContents(l), o.setEnd(l, 4), o.setStart(l, 2), e.compareBoundaryPoints = -1 == m.compareBoundaryPoints(m.START_TO_END, o) & 1 == m.compareBoundaryPoints(m.END_TO_START, o) ? function (a, b) {
        return b = b.nativeRange || b, a == b.START_TO_END ? a = b.END_TO_START : a == b.END_TO_START && (a = b.START_TO_END), this.nativeRange.compareBoundaryPoints(a, b)
      } : function (a, b) {
        return this.nativeRange.compareBoundaryPoints(a, b.nativeRange || b)
      }, a.util.isHostMethod(m, "createContextualFragment") && (e.createContextualFragment = function (a) {
        return this.nativeRange.createContextualFragment(a)
      }), g.getBody(document).removeChild(l), m.detach(), o.detach()
    }(), a.createNativeRange = function (a) {
      return a = a || document, a.createRange()
    };
    a.features.implementsTextRange && (f.rangeToTextRange = function (a) {
      if (a.collapsed) {
        var b = e(new h(a.startContainer, a.startOffset), !0);
        return b
      }
      var c = e(new h(a.startContainer, a.startOffset), !0), d = e(new h(a.endContainer, a.endOffset), !1), f = g.getDocument(a.startContainer).body.createTextRange();
      return f.setEndPoint("StartToStart", c), f.setEndPoint("EndToEnd", d), f
    }), f.prototype.getName = function () {
      return "WrappedRange"
    }, a.WrappedRange = f, a.createRange = function (b) {
      return b = b || document, new f(a.createNativeRange(b))
    }, a.createRangyRange = function (a) {
      return a = a || document, new i(a)
    }, a.createIframeRange = function (b) {
      return a.createRange(g.getIframeDocument(b))
    }, a.createIframeRangyRange = function (b) {
      return a.createRangyRange(g.getIframeDocument(b))
    }, a.addCreateMissingNativeApiListener(function (b) {
      var c = b.document;
      "undefined" == typeof c.createRange && (c.createRange = function () {
        return a.createRange(this)
      }), c = b = null
    })
  }), rangy.createModule("WrappedSelection", function (a, b) {
    function c(a) {
      return (a || window).getSelection()
    }

    function d(a) {
      return (a || window).document.selection
    }

    function e(a, b, c) {
      var d = c ? "end" : "start", e = c ? "start" : "end";
      a.anchorNode = b[d + "Container"], a.anchorOffset = b[d + "Offset"], a.focusNode = b[e + "Container"], a.focusOffset = b[e + "Offset"]
    }

    function f(a) {
      var b = a.nativeSelection;
      a.anchorNode = b.anchorNode, a.anchorOffset = b.anchorOffset, a.focusNode = b.focusNode, a.focusOffset = b.focusOffset
    }

    function g(a) {
      a.anchorNode = a.focusNode = null, a.anchorOffset = a.focusOffset = 0, a.rangeCount = 0, a.isCollapsed = !0, a._ranges.length = 0
    }

    function h(b) {
      var c;
      return b instanceof y ? (c = b._selectionNativeRange, c || (c = a.createNativeRange(w.getDocument(b.startContainer)), c.setEnd(b.endContainer, b.endOffset), c.setStart(b.startContainer, b.startOffset), b._selectionNativeRange = c, b.attachListener("detach", function () {
        this._selectionNativeRange = null
      }))) : b instanceof z ? c = b.nativeRange : a.features.implementsDomRange && b instanceof w.getWindow(b.startContainer).Range && (c = b), c
    }

    function i(a) {
      if (!a.length || 1 != a[0].nodeType)return !1;
      for (var b = 1, c = a.length; c > b; ++b)if (!w.isAncestorOf(a[0], a[b]))return !1;
      return !0
    }

    function j(a) {
      var b = a.getNodes();
      if (!i(b))throw new Error("getSingleElementFromRange: range " + a.inspect() + " did not consist of a single element");
      return b[0]
    }

    function k(a) {
      return !!a && "undefined" != typeof a.text
    }

    function l(a, b) {
      var c = new z(b);
      a._ranges = [c], e(a, c, !1), a.rangeCount = 1, a.isCollapsed = c.collapsed
    }

    function m(b) {
      if (b._ranges.length = 0, "None" == b.docSelection.type)g(b); else {
        var c = b.docSelection.createRange();
        if (k(c))l(b, c); else {
          b.rangeCount = c.length;
          for (var d, f = w.getDocument(c.item(0)), h = 0; h < b.rangeCount; ++h)d = a.createRange(f), d.selectNode(c.item(h)), b._ranges.push(d);
          b.isCollapsed = 1 == b.rangeCount && b._ranges[0].collapsed, e(b, b._ranges[b.rangeCount - 1], !1)
        }
      }
    }

    function n(a, b) {
      for (var c = a.docSelection.createRange(), d = j(b), e = w.getDocument(c.item(0)), f = w.getBody(e).createControlRange(), g = 0, h = c.length; h > g; ++g)f.add(c.item(g));
      try {
        f.add(d)
      } catch (i) {
        throw new Error("addRange(): Element within the specified Range could not be added to control selection (does it have layout?)")
      }
      f.select(), m(a)
    }

    function o(a, b, c) {
      this.nativeSelection = a, this.docSelection = b, this._ranges = [], this.win = c, this.refresh()
    }

    function p(a, b) {
      for (var c, d = w.getDocument(b[0].startContainer), e = w.getBody(d).createControlRange(), f = 0; f < rangeCount; ++f) {
        c = j(b[f]);
        try {
          e.add(c)
        } catch (g) {
          throw new Error("setRanges(): Element within the one of the specified Ranges could not be added to control selection (does it have layout?)")
        }
      }
      e.select(), m(a)
    }

    function q(a, b) {
      if (a.anchorNode && w.getDocument(a.anchorNode) !== w.getDocument(b))throw new A("WRONG_DOCUMENT_ERR")
    }

    function r(a) {
      var b = [], c = new B(a.anchorNode, a.anchorOffset), d = new B(a.focusNode, a.focusOffset), e = "function" == typeof a.getName ? a.getName() : "Selection";
      if ("undefined" != typeof a.rangeCount)for (var f = 0, g = a.rangeCount; g > f; ++f)b[f] = y.inspect(a.getRangeAt(f));
      return "[" + e + "(Ranges: " + b.join(", ") + ")(anchor: " + c.inspect() + ", focus: " + d.inspect() + "]"
    }

    a.requireModules(["DomUtil", "DomRange", "WrappedRange"]), a.config.checkSelectionRanges = !0;
    var s, t, u = "boolean", v = "_rangySelection", w = a.dom, x = a.util, y = a.DomRange, z = a.WrappedRange, A = a.DOMException, B = w.DomPosition, C = "Control", D = a.util.isHostMethod(window, "getSelection"), E = a.util.isHostObject(document, "selection"), F = E && (!D || a.config.preferTextRange);
    F ? (s = d, a.isSelectionValid = function (a) {
      var b = (a || window).document, c = b.selection;
      return "None" != c.type || w.getDocument(c.createRange().parentElement()) == b
    }) : D ? (s = c, a.isSelectionValid = function () {
      return !0
    }) : b.fail("Neither document.selection or window.getSelection() detected."), a.getNativeSelection = s;
    var G = s(), H = a.createNativeRange(document), I = w.getBody(document), J = x.areHostObjects(G, ["anchorNode", "focusNode"] && x.areHostProperties(G, ["anchorOffset", "focusOffset"]));
    a.features.selectionHasAnchorAndFocus = J;
    var K = x.isHostMethod(G, "extend");
    a.features.selectionHasExtend = K;
    var L = "number" == typeof G.rangeCount;
    a.features.selectionHasRangeCount = L;
    var M = !1, N = !0;
    x.areHostMethods(G, ["addRange", "getRangeAt", "removeAllRanges"]) && "number" == typeof G.rangeCount && a.features.implementsDomRange && !function () {
      var a = document.createElement("iframe");
      a.frameBorder = 0, a.style.position = "absolute", a.style.left = "-10000px", I.appendChild(a);
      var b = w.getIframeDocument(a);
      b.open(), b.write("<html><head></head><body>12</body></html>"), b.close();
      var c = w.getIframeWindow(a).getSelection(), d = b.documentElement, e = d.lastChild, f = e.firstChild, g = b.createRange();
      g.setStart(f, 1), g.collapse(!0), c.addRange(g), N = 1 == c.rangeCount, c.removeAllRanges();
      var h = g.cloneRange();
      g.setStart(f, 0), h.setEnd(f, 2), c.addRange(g), c.addRange(h), M = 2 == c.rangeCount, g.detach(), h.detach(), I.removeChild(a)
    }(), a.features.selectionSupportsMultipleRanges = M, a.features.collapsedNonEditableSelectionsSupported = N;
    var O, P = !1;
    I && x.isHostMethod(I, "createControlRange") && (O = I.createControlRange(), x.areHostProperties(O, ["item", "add"]) && (P = !0)), a.features.implementsControlRange = P, t = J ? function (a) {
      return a.anchorNode === a.focusNode && a.anchorOffset === a.focusOffset
    } : function (a) {
      return a.rangeCount ? a.getRangeAt(a.rangeCount - 1).collapsed : !1
    };
    var Q;
    x.isHostMethod(G, "getRangeAt") ? Q = function (a, b) {
      try {
        return a.getRangeAt(b)
      } catch (c) {
        return null
      }
    } : J && (Q = function (b) {
      var c = w.getDocument(b.anchorNode), d = a.createRange(c);
      return d.setStart(b.anchorNode, b.anchorOffset), d.setEnd(b.focusNode, b.focusOffset), d.collapsed !== this.isCollapsed && (d.setStart(b.focusNode, b.focusOffset), d.setEnd(b.anchorNode, b.anchorOffset)), d
    }), a.getSelection = function (a) {
      a = a || window;
      var b = a[v], c = s(a), e = E ? d(a) : null;
      return b ? (b.nativeSelection = c, b.docSelection = e, b.refresh(a)) : (b = new o(c, e, a), a[v] = b), b
    }, a.getIframeSelection = function (b) {
      return a.getSelection(w.getIframeWindow(b))
    };
    var R = o.prototype;
    if (!F && J && x.areHostMethods(G, ["removeAllRanges", "addRange"])) {
      R.removeAllRanges = function () {
        this.nativeSelection.removeAllRanges(), g(this)
      };
      var S = function (b, c) {
        var d = y.getRangeDocument(c), e = a.createRange(d);
        e.collapseToPoint(c.endContainer, c.endOffset), b.nativeSelection.addRange(h(e)), b.nativeSelection.extend(c.startContainer, c.startOffset), b.refresh()
      };
      R.addRange = L ? function (b, c) {
        if (P && E && this.docSelection.type == C)n(this, b); else if (c && K)S(this, b); else {
          var d;
          if (M ? d = this.rangeCount : (this.removeAllRanges(), d = 0), this.nativeSelection.addRange(h(b)), this.rangeCount = this.nativeSelection.rangeCount, this.rangeCount == d + 1) {
            if (a.config.checkSelectionRanges) {
              var f = Q(this.nativeSelection, this.rangeCount - 1);
              f && !y.rangesEqual(f, b) && (b = new z(f))
            }
            this._ranges[this.rangeCount - 1] = b, e(this, b, V(this.nativeSelection)), this.isCollapsed = t(this)
          } else this.refresh()
        }
      } : function (a, b) {
        b && K ? S(this, a) : (this.nativeSelection.addRange(h(a)), this.refresh())
      }, R.setRanges = function (a) {
        if (P && a.length > 1)p(this, a); else {
          this.removeAllRanges();
          for (var b = 0, c = a.length; c > b; ++b)this.addRange(a[b])
        }
      }
    } else {
      if (!(x.isHostMethod(G, "empty") && x.isHostMethod(H, "select") && P && F))return b.fail("No means of selecting a Range or TextRange was found"), !1;
      R.removeAllRanges = function () {
        try {
          if (this.docSelection.empty(), "None" != this.docSelection.type) {
            var a;
            if (this.anchorNode)a = w.getDocument(this.anchorNode); else if (this.docSelection.type == C) {
              var b = this.docSelection.createRange();
              b.length && (a = w.getDocument(b.item(0)).body.createTextRange())
            }
            if (a) {
              var c = a.body.createTextRange();
              c.select(), this.docSelection.empty()
            }
          }
        } catch (d) {
        }
        g(this)
      }, R.addRange = function (a) {
        this.docSelection.type == C ? n(this, a) : (z.rangeToTextRange(a).select(), this._ranges[0] = a, this.rangeCount = 1, this.isCollapsed = this._ranges[0].collapsed, e(this, a, !1))
      }, R.setRanges = function (a) {
        this.removeAllRanges();
        var b = a.length;
        b > 1 ? p(this, a) : b && this.addRange(a[0])
      }
    }
    R.getRangeAt = function (a) {
      if (0 > a || a >= this.rangeCount)throw new A("INDEX_SIZE_ERR");
      return this._ranges[a]
    };
    var T;
    if (F)T = function (b) {
      var c;
      a.isSelectionValid(b.win) ? c = b.docSelection.createRange() : (c = w.getBody(b.win.document).createTextRange(), c.collapse(!0)), b.docSelection.type == C ? m(b) : k(c) ? l(b, c) : g(b)
    }; else if (x.isHostMethod(G, "getRangeAt") && "number" == typeof G.rangeCount)T = function (b) {
      if (P && E && b.docSelection.type == C)m(b); else if (b._ranges.length = b.rangeCount = b.nativeSelection.rangeCount, b.rangeCount) {
        for (var c = 0, d = b.rangeCount; d > c; ++c)b._ranges[c] = new a.WrappedRange(b.nativeSelection.getRangeAt(c));
        e(b, b._ranges[b.rangeCount - 1], V(b.nativeSelection)), b.isCollapsed = t(b)
      } else g(b)
    }; else {
      if (!J || typeof G.isCollapsed != u || typeof H.collapsed != u || !a.features.implementsDomRange)return b.fail("No means of obtaining a Range or TextRange from the user's selection was found"), !1;
      T = function (a) {
        var b, c = a.nativeSelection;
        c.anchorNode ? (b = Q(c, 0), a._ranges = [b], a.rangeCount = 1, f(a), a.isCollapsed = t(a)) : g(a)
      }
    }
    R.refresh = function (a) {
      var b = a ? this._ranges.slice(0) : null;
      if (T(this), a) {
        var c = b.length;
        if (c != this._ranges.length)return !1;
        for (; c--;)if (!y.rangesEqual(b[c], this._ranges[c]))return !1;
        return !0
      }
    };
    var U = function (a, b) {
      var c = a.getAllRanges(), d = !1;
      a.removeAllRanges();
      for (var e = 0, f = c.length; f > e; ++e)d || b !== c[e] ? a.addRange(c[e]) : d = !0;
      a.rangeCount || g(a)
    };
    R.removeRange = P ? function (a) {
      if (this.docSelection.type == C) {
        for (var b, c = this.docSelection.createRange(), d = j(a), e = w.getDocument(c.item(0)), f = w.getBody(e).createControlRange(), g = !1, h = 0, i = c.length; i > h; ++h)b = c.item(h), b !== d || g ? f.add(c.item(h)) : g = !0;
        f.select(), m(this)
      } else U(this, a)
    } : function (a) {
      U(this, a)
    };
    var V;
    !F && J && a.features.implementsDomRange ? (V = function (a) {
      var b = !1;
      return a.anchorNode && (b = 1 == w.comparePoints(a.anchorNode, a.anchorOffset, a.focusNode, a.focusOffset)), b
    }, R.isBackwards = function () {
      return V(this)
    }) : V = R.isBackwards = function () {
      return !1
    }, R.toString = function () {
      for (var a = [], b = 0, c = this.rangeCount; c > b; ++b)a[b] = "" + this._ranges[b];
      return a.join("")
    }, R.collapse = function (b, c) {
      q(this, b);
      var d = a.createRange(w.getDocument(b));
      d.collapseToPoint(b, c), this.removeAllRanges(), this.addRange(d), this.isCollapsed = !0
    }, R.collapseToStart = function () {
      if (!this.rangeCount)throw new A("INVALID_STATE_ERR");
      var a = this._ranges[0];
      this.collapse(a.startContainer, a.startOffset)
    }, R.collapseToEnd = function () {
      if (!this.rangeCount)throw new A("INVALID_STATE_ERR");
      var a = this._ranges[this.rangeCount - 1];
      this.collapse(a.endContainer, a.endOffset)
    }, R.selectAllChildren = function (b) {
      q(this, b);
      var c = a.createRange(w.getDocument(b));
      c.selectNodeContents(b), this.removeAllRanges(), this.addRange(c)
    }, R.deleteFromDocument = function () {
      if (P && E && this.docSelection.type == C) {
        for (var a, b = this.docSelection.createRange(); b.length;)a = b.item(0), b.remove(a), a.parentNode.removeChild(a);
        this.refresh()
      } else if (this.rangeCount) {
        var c = this.getAllRanges();
        this.removeAllRanges();
        for (var d = 0, e = c.length; e > d; ++d)c[d].deleteContents();
        this.addRange(c[e - 1])
      }
    }, R.getAllRanges = function () {
      return this._ranges.slice(0)
    }, R.setSingleRange = function (a) {
      this.setRanges([a])
    }, R.containsNode = function (a, b) {
      for (var c = 0, d = this._ranges.length; d > c; ++c)if (this._ranges[c].containsNode(a, b))return !0;
      return !1
    }, R.toHtml = function () {
      var a = "";
      if (this.rangeCount) {
        for (var b = y.getRangeDocument(this._ranges[0]).createElement("div"), c = 0, d = this._ranges.length; d > c; ++c)b.appendChild(this._ranges[c].cloneContents());
        a = b.innerHTML
      }
      return a
    }, R.getName = function () {
      return "WrappedSelection"
    }, R.inspect = function () {
      return r(this)
    }, R.detach = function () {
      this.win[v] = null, this.win = this.anchorNode = this.focusNode = null
    }, o.inspect = r, a.Selection = o, a.selectionPrototype = R, a.addCreateMissingNativeApiListener(function (b) {
      "undefined" == typeof b.getSelection && (b.getSelection = function () {
        return a.getSelection(this)
      }), b = null
    })
  }), /**
   * @license Selection save and restore module for Rangy.
   * Saves and restores user selections using marker invisible elements in the DOM.
   *
   * Part of Rangy, a cross-browser JavaScript range and selection library
   * http://code.google.com/p/rangy/
   *
   * Depends on Rangy core.
   *
   * Copyright 2012, Tim Down
   * Licensed under the MIT license.
   * Version: 1.2.3
   * Build date: 26 February 2012
   */
    rangy.createModule("SaveRestore", function (a, b) {
      function c(a, b) {
        return (b || document).getElementById(a)
      }

      function d(a, b) {
        var c, d = "selectionBoundary_" + +new Date + "_" + ("" + Math.random()).slice(2), e = k.getDocument(a.startContainer), f = a.cloneRange();
        return f.collapse(b), c = e.createElement("span"), c.id = d, c.style.lineHeight = "0", c.style.display = "none", c.className = "rangySelectionBoundary", c.appendChild(e.createTextNode(l)), f.insertNode(c), f.detach(), c
      }

      function e(a, d, e, f) {
        var g = c(e, a);
        g ? (d[f ? "setStartBefore" : "setEndBefore"](g), g.parentNode.removeChild(g)) : b.warn("Marker element has been removed. Cannot restore selection.")
      }

      function f(a, b) {
        return b.compareBoundaryPoints(a.START_TO_START, a)
      }

      function g(e) {
        e = e || window;
        var g = e.document;
        if (!a.isSelectionValid(e))return void b.warn("Cannot save selection. This usually happens when the selection is collapsed and the selection document has lost focus.");
        var h, i, j, k = a.getSelection(e), l = k.getAllRanges(), m = [];
        l.sort(f);
        for (var n = 0, o = l.length; o > n; ++n)j = l[n], j.collapsed ? (i = d(j, !1), m.push({
          markerId: i.id,
          collapsed: !0
        })) : (i = d(j, !1), h = d(j, !0), m[n] = {
          startMarkerId: h.id,
          endMarkerId: i.id,
          collapsed: !1,
          backwards: 1 == l.length && k.isBackwards()
        });
        for (n = o - 1; n >= 0; --n)j = l[n], j.collapsed ? j.collapseBefore(c(m[n].markerId, g)) : (j.setEndBefore(c(m[n].endMarkerId, g)), j.setStartAfter(c(m[n].startMarkerId, g)));
        return k.setRanges(l), {win: e, doc: g, rangeInfos: m, restored: !1}
      }

      function h(d, f) {
        if (!d.restored) {
          for (var g, h, i = d.rangeInfos, j = a.getSelection(d.win), k = [], l = i.length, m = l - 1; m >= 0; --m) {
            if (g = i[m], h = a.createRange(d.doc), g.collapsed) {
              var n = c(g.markerId, d.doc);
              if (n) {
                n.style.display = "inline";
                var o = n.previousSibling;
                o && 3 == o.nodeType ? (n.parentNode.removeChild(n), h.collapseToPoint(o, o.length)) : (h.collapseBefore(n), n.parentNode.removeChild(n))
              } else b.warn("Marker element has been removed. Cannot restore selection.")
            } else e(d.doc, h, g.startMarkerId, !0), e(d.doc, h, g.endMarkerId, !1);
            1 == l && h.normalizeBoundaries(), k[m] = h
          }
          1 == l && f && a.features.selectionHasExtend && i[0].backwards ? (j.removeAllRanges(), j.addRange(k[0], !0)) : j.setRanges(k), d.restored = !0
        }
      }

      function i(a, b) {
        var d = c(b, a);
        d && d.parentNode.removeChild(d)
      }

      function j(a) {
        for (var b, c = a.rangeInfos, d = 0, e = c.length; e > d; ++d)b = c[d], b.collapsed ? i(a.doc, b.markerId) : (i(a.doc, b.startMarkerId), i(a.doc, b.endMarkerId))
      }

      a.requireModules(["DomUtil", "DomRange", "WrappedRange"]);
      var k = a.dom, l = "﻿";
      a.saveSelection = g, a.restoreSelection = h, a.removeMarkerElement = i, a.removeMarkers = j
    })
}({}, function () {
  return this
}());
