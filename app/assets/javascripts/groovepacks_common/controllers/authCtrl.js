groovepacks_controllers.
controller('authCtrl', ['$scope','dashboard', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$q', 
  'auth', '$rootScope', '$interval', '$window', '$modal',
  function ($scope, dashboard, $http, $timeout, $stateParams, $location, $state, $cookies, $q, auth, $rootScope, $interval, $window, $modal) {
  var myscope = {};
  $scope.auth = {}
  $scope.login = function(auth_form) {
    auth.login(auth_form.username, auth_form.password).then(
      function(response){
        auth.check().then(function(){
          target_url = $rootScope.api_url + '/users/get_email?username=' + auth_form.username;
          $http.get(target_url).success(function(data){
            $scope.email = data.email;
            user = JSON.parse($window.localStorage.getItem('current_user'));
            if(data.status == true && ($scope.email == "" || typeof $scope.email == undefined || $scope.email == null)){  
              var notification_modal = $modal.open({
                templateUrl: '/assets/views/modals/settings/user_email_popup.html',
                controller: 'userEmailNotificationCtrl',
                size: 'lg',
                resolve: {
                  settings_data: function() {
                    return {username: auth_form.username};
                  }
                }
              });
              notification_modal.result.then(function () {
                if (user.is_active == false){
                  $scope.sign_out();
                } else {
                  $state.go('home');
                }
                //dashboard.stats.dashboard_stat();
                $http.get($rootScope.api_url + '/home/<USER>');
                // $window.location.reload();
              });
            } else {
              if (user.is_active == false){
                $scope.sign_out();
              } else {
                $state.go('home');
              }
              //dashboard.stats.dashboard_stat();
              $http.get($rootScope.api_url + '/home/<USER>');
              // $window.location.reload();
            }
          });
          
        })
      })
  }
  
  $scope.update_password = function(auth_pass){
    auth_pass["token"] = $location.search().reset_password_token;
    auth_pass["user_id"] = $location.search().user_id
    auth.update_password(auth_pass);
  };

  $scope.forgetpassword = function(){ 
    var modalInstance = $modal.open({
      templateUrl: '/assets/views/modals/settings/user/password.html',
      controller: 'forgetPasswordNotificationCtrl'
    });
  }

  myscope.init = function() {
    // if ($window.location.hash.split("?")[0] == "#/users/password/edit"){
    // } else{  
      if (auth.isLoggedIn()) {
        /*
        On successful login, we need to set interval to refresh token. 
        The refresh token is set at 1 hr interval 
        */
        $rootScope.$broadcast("set-refresh-token-interval");
        $rootScope.$broadcast("connect-to-socket-server");
        $rootScope.$broadcast("connect-dashboard-to-socket-server");
        $state.go('home');
      }
    // }
  }

  $rootScope.$on('set-refresh-token-interval', function() {
    if($rootScope.refresh_token_interval == null || typeof ($rootScope.refresh_token_interval) == "undefined" ) {
      $interval.cancel($rootScope.refresh_token_interval);
      $rootScope.refresh_token_interval = $interval(function () {
        try {
          auth.refresh_token();
        } catch (e) {
          console.error(e);
        }
      }, 3600000) 
    }
  })

    myscope.init();
  }]);
