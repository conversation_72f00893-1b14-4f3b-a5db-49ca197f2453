groovepacks_controllers.controller('packingCamCtrl', ['$rootScope', '$scope', '$http', '$timeout', '$location', '$stateParams', '$state', '$cookies', '$modal', '$q', 'notification', 'packing_cam', 'Lightbox', '$window', '$filter',
  function ($rootScope, $scope, $http, $timeout, $location, $stateParams, $state, $cookies, $modal, $q, notification, packing_cam, Lightbox, $window, $filter) {
    var myscope = {};

    myscope.init = function () {
      $scope.packing_cam_data = null;
      $scope.loading = true;
      fetch_data();
    }

    var fetch_data = function () {
      packing_cam.single.get($stateParams).then(function(response) {
        if (response.data.status) {
          $scope.packing_cam_data = response.data;
          $scope.lightbox_images = $scope.packing_cam_data.packing_cams;
          var orderNumber = $scope.packing_cam_data.order.increment_id;
          var newCustomerPageMessage = $scope.packing_cam_data.setting.customer_page_message;
          var replacedHtml = newCustomerPageMessage.replace(/\[\[ORDER-NUMBER\]\]/g, orderNumber);
          $scope.customerPageMessage = replacedHtml;
          $scope.filter_box_data = [];
          angular.forEach($scope.packing_cam_data.boxinfo.boxes, function(value, key){
            angular.forEach($scope.packing_cam_data.boxinfo.items, function(i_value, i_key){
              var data = $filter('filter')($scope.packing_cam_data.boxinfo.order_item_boxes, { order_item_id: i_value.iteminfo.id, box_id: value.id});
              if(data.length > 0){
                if(i_value.productinfo.is_kit == 1 && i_value.productinfo.kit_parsing != 'single'){
                  angular.forEach($scope.packing_cam_data.boxinfo.list, function(v, k){
                    if (value.id == k ) {
                      angular.forEach(v, function(vi, ki){
                        $scope.filter_box_data.push({box: value.name, name:  vi.product_name, sku: i_value.sku, qty: vi.qty});
                      });
                    }
                  });
                }
                else{
                  $scope.filter_box_data.push({box: value.name, name:  i_value.productinfo.name, sku: i_value.sku, qty: data[0].item_qty});
                }
              }
            })
          });
          $scope.filter_box_data.sort(function (next, prev) {
            if (next.box !== prev.box) {
              return next.box.localeCompare(prev.box);
            } else {
              return next.name.localeCompare(prev.name);
            }
          });
          $scope.filteredData = $scope.packing_cam_data && $scope.packing_cam_data.order_items ? $scope.packing_cam_data.order_items.slice() : [];
          $scope.filteredBoxData = $scope.filter_box_data && $scope.filter_box_data.length > 0 ? $scope.filter_box_data.slice() : [];
        }
        $timeout( function(){
          $scope.loading = false;
        }, 500);
      });
    };

    $scope.filterData = function () {
      if ($scope.searchQuery) {
          var query = $scope.searchQuery.toLowerCase();
          if ($scope.filter_box_data && $scope.filter_box_data.length > 0) {
              $scope.filteredBoxData = $scope.filter_box_data.filter(function(item) {
                  return item.name.toLowerCase().includes(query) || 
                         item.sku.toLowerCase().includes(query);
              });
          } else if ($scope.packing_cam_data && $scope.packing_cam_data.order_items) {
              $scope.filteredData = $scope.packing_cam_data.order_items.filter(function(item) {
                  return item.name.toLowerCase().includes(query) || 
                         item.sku.toLowerCase().includes(query);
              });
          }
      } else {
          if ($scope.filter_box_data && $scope.filter_box_data.length > 0) {
              $scope.filteredBoxData = $scope.filter_box_data.slice();
          } else if ($scope.packing_cam_data && $scope.packing_cam_data.order_items) {
              $scope.filteredData = $scope.packing_cam_data.order_items.slice();
          }
      }
  };

    $scope.openLightboxModal = function (index) {
      Lightbox.openModal($scope.lightbox_images, index);
    };

    myscope.init();
  }
]);
