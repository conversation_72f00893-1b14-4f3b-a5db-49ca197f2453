groovepacks_controllers.controller('bigcommerceCtrl', ['$rootScope', '$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$modal', '$q', 'notification', '$window',
  function ($rootScope, $scope, $http, $timeout, $location, $state, $cookies, $modal, $q, notification, $window) {
    var myscope = {};

    myscope.init = function() { 
    }

    $scope.go_to_login = function(){
    	$state.go('existing_customer', {plan_id: "", shop_name: ""})
    }
    myscope.init();
  }
]);