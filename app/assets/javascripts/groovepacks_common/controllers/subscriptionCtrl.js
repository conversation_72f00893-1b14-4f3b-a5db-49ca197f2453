groovepacks_controllers.controller('subscriptionCtrl', ['$rootScope', '$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$modal', '$q', 'notification', 'subscription', '$window',
  function ($rootScope, $scope, $http, $timeout, $location, $state, $cookies, $modal, $q, notification, subscription, $window) {
    var myscope = {};

    myscope.init = function() {
      a = $location.$$absUrl
      if ($location.$$absUrl.split('#')[1].split('#')[1] == undefined && $location.$$absUrl.split('?')[1] != undefined) {
        $location.$$absUrl = a.split('?')[0] + '#/' + a.split('#/')[1] + '?' + a.split('?')[1].split('#')[0];
      }
      try{
        if(a.split('&')[1].split('=')[1] == 'true'){
          setTimeout(function(){
            close();
          },3000);
          window.opener.call_parent();
        }
      }catch(e){}
      $scope.shop_name = "";
      $scope.shop_type = "";
      $scope.subscription = {};
      $scope.subscription.no_of_users = $('.no_of_users').val();
      $scope.subscription.checked = $(".bill_annually:checked").length;
      try{
        $scope.subscription.email = email;
      } catch(e){}
      try{
        shop = $rootScope.location.split('&')[1].split('=')[1];
        $scope.shop_name = shop.split('.')[0];
        // $scope.shop_type = "BigCommerce"
        $rootScope.shop_type = shop.includes("shopify") ? "Shopify" : ""
        $scope.shop_type = shop.includes("shopify") ? "Shopify" : ""
      } catch(e){}
      try{
        $scope.subscription.transaction_id = transaction_id;
        $scope.subscription.notice = notice;
        $scope.subscription.next_date = next_date;
        $scope.subscription.store = store;
        $scope.subscription.show_coupon_block = false;
      } catch(e){}
      $scope.success = null;
    }

    $scope.new_subscription = function(){
      $scope.subscription.no_of_users = $('.no_of_users').val();
	    $scope.subscription.checked = $(".bill_annually:checked").length;
	    $scope.subscription.subscription_type = $scope.subscription.checked>0 ? "annually" : "monthly";
	    $scope.subscription = {no_of_users: $scope.subscription.no_of_users, checked: $scope.subscription.checked, subscription_type: $scope.subscription.subscription_type};
      $state.go('create_subscriptions', {no_of_users: $scope.subscription.no_of_users});
    }

    $scope.validate_tenant_name = function(){
      tenant_name = $scope.subscription.shop_name;
    	if (tenant_name == null || tenant_name == "" || tenant_name.length < 4) {
    		$scope.subscription.valid_shop_name = false;
        $scope.subscription.message = "Site name must be at least 4 characters long";
    	} else {
    		subscription.validate_shop_name(tenant_name).success(function (response) {
          if (!response.valid){
            $scope.subscription.message = response.message;
          } else {
            $scope.subscription.valid_shop_name = true;
            $scope.subscription.message = ""
          }
        })
    	};
    }

    $scope.validate_email = function(){
      email = $scope.subscription.email;
      try{
        atpos = email.indexOf("@");
        dotpos = email.lastIndexOf(".");
      } catch(e){
        atpos = null;
        dotpos = null;
      }
      $scope.subscription.valid_email = false;
      if (email == null || email == "") {
        $scope.subscription.email_message = "email must be filled out";
      } else if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
        $scope.subscription.email_message = "email is not valid";
      } else {
        subscription.validate_subsc_email(email).success(function (response) {
          if (!response.valid) {
            $scope.subscription.email_message = email + " email already exists";
          } else {
            $scope.subscription.valid_email = true;
            $scope.subscription.email_message = "";
          };
        });
      }
    };

    $scope.validate_user_name = function(){
      user_name = $scope.subscription.user_name;
      $scope.subscription.valid_user_name = false;
      try{
        name = user_name.replace(/[^\w\s]/gi, '')
      }catch(e){
        name = user_name
      }
      if (user_name == null || user_name == "") {
        $scope.subscription.user_name_message = "username must be filled out";
      } else if (name != user_name || name.indexOf('_') == 0){
        $scope.subscription.user_name_message = "username must not contains special charecters";
      } else {
        $scope.subscription.valid_user_name = true;
        $scope.subscription.user_name_message = "";
      };
    };

    $scope.validate_password= function(){
      password = $scope.subscription.password;
      $scope.subscription.valid_password = false;
      if($scope.subscription.valid_user_name){
        if (password == null || password == "") {
          $scope.subscription.password_message = "password must be filled out";
          $scope.subscription.password_conf_message = "";
        } else if(password.length < 6){
          $scope.subscription.password_message = "Password should be at least 6 characters";
          $scope.subscription.password_conf_message = "";
        } else {
          $scope.subscription.valid_password = true;
          $scope.subscription.password_message = "";
          $scope.subscription.password_conf_message = "";
        }
      }
    };

    $scope.validate_password_conf = function(){
      password_conf = $scope.subscription.password_conf;
      password = $scope.subscription.password;
      $scope.subscription.valid_password_conf = false;
      if (password_conf == null || password_conf == "" || password_conf != password) {
        $scope.subscription.password_message = "password and password confirmation must match"
        $scope.subscription.password_conf_message = "password and password confirmation must match"
      } else {
        $scope.subscription.valid_password_conf = true;
        $scope.subscription.password_message = "";
        $scope.subscription.password_conf_message = "";
      }
    };

    $scope.validate_tos_checkbox = function(){
      tos = $scope.subscription.tos_checkbox;
      $scope.subscription.valid_tos_checkbox = false;
      if (tos == null || tos == "" || tos == false) {
        $scope.subscription.tos_checkbox_message = "Please click I agree for TOS and Privacy Policy";
      } else {
        $scope.subscription.valid_tos_checkbox = true ;
        $scope.subscription.tos_checkbox_message = "";
      }
    }

    $scope.apply = function(){
      myscope.check_one_time_payment(true);
    }

    myscope.check_one_time_payment = function(show_coupon_block){
      attrs = {shop_name: $scope.shop_name,  shop_type: $scope.shop_type};
      subscription.get_one_time_payment(attrs).success(function(response){
        $scope.subscription.one_time_payment = response["one_time_payment"];
        $scope.subscription.stripe_public_key = response["stripe_public_key"];
      });
      coupon_id = $scope.subscription.coupon_id;
      if (coupon_id != '') {
        subscription.apply(coupon_id).success(function(response){
          $scope.subscription.discount_amount = response.discount_amount;
          if (response.status == false) {
            coupon_id = null;
            one_time_payment = $scope.subscription.one_time_payment;
            if (show_coupon_block) {
              $scope.subscription.show_coupon_block = show_coupon_block;
            }
            $scope.subscription.error_coupon = response.messages[0];
          }else{
            one_time_payment = $scope.subscription.one_time_payment - response.discount_amount;
            if (show_coupon_block) {
              $scope.subscription.show_coupon_block = false;
              $scope.subscription.success_coupon = response.messages[0];
            }
          }
        });
      };
    };

    $scope.custom_button = function(flag){
      // HACK: Temporary Changes to account Creation https://groovepacker.atlassian.net/browse/GROOV-3587
      if ($scope.subscription.no_of_users == undefined) $scope.subscription.no_of_users = 3

      $scope.subscription.plan_id = 'GROOV-' + Number($scope.subscription.no_of_users)*50;
      if ($("input[name='radio_subscription']:checked").length > 0) {
        radio_button_value = $('input:radio[name=radio_subscription]:checked').val();
        if (radio_button_value == "annually" || $scope.subscription.checked){
          $("[type='radio'][value='annually']").prop('checked', true);
          $scope.subscription.plan_id = 'an-' + $scope.subscription.plan_id;
        }
        if(($scope.subscription.valid_shop_name && $scope.subscription.valid_email && $scope.subscription.valid_user_name && $scope.subscription.valid_password && $scope.subscription.valid_password_conf && $scope.subscription.valid_tos_checkbox) == true){
          myscope.open_handler($scope.subscription.plan_id, flag);
        };
      }
    };

    $scope.continue_button = function(){
      $rootScope.location = $location.$$absUrl;
      // $rootScope.location = $location.$$url;
      email = $scope.subscription.email;
      subscription.validate_subsc_email().success(function (response) {
        if(response.valid){
          $state.go('subscriptions', {email: email});
        }
      });
    };

    $scope.auth_button = function(){
      plan_id = "GROOV-" + Number($scope.subscription.no_of_users)*50
      event.preventDefault();
      email = $scope.subscription.email;
      shop_name = $scope.shop_name;
      data = {email: $scope.subscription.email, tenant_name: $scope.subscription.shop_name,
              user_name: $scope.subscription.user_name, shop_name: $scope.shop_name, shop_type: $scope.shop_type,
              radio_subscription: $('input:radio[name=radio_subscription]:checked').val(),
              password: $scope.subscription.password, plan_id: plan_id}

      if (($scope.subscription.valid_shop_name  && $scope.subscription.valid_user_name && $scope.subscription.valid_password && $scope.subscription.valid_password_conf && $scope.subscription.valid_tos_checkbox) == true){
        subscription.store_shopify_subscription_data(data).success(function(){
          subscription.shopify_auth(shop_name, plan_id, email).success(function(response){
            popup = window.open('',"Ratting","width=1250,height=1000,0,status=0");
            popup.location = response.permission_url;
            // window.open(response.permission_url,"Ratting","width=1250,height=1000,0,status=0");
          });
        });
      }
    };

    // $scope.call_parent = function(){
    window.call_parent = function(){
      subscription.get_store_data().success(function(data){
        subscription.request_tenant_creation('', data).success(function(response){
          if (response.valid == true) {
            transaction_id = response.transaction_id;
            notice = response.notice;
            email = response.email;
            next_date = response.next_date;
            store = response.store;
            $state.go('show', {notice: notice, transaction_id: transaction_id, email: email, next_date: next_date, store: store, store_type: "Shopify"});
          } else {
            $scope.subscription.processing_alert_error = response.errors;
          }
        }).error(function () {
          $scope.subscription.processing_alert_error = "Your subscription could not be processed. Please try again.";
        });
      });
    };

    $scope.existing_customer = function(){
      $state.go('existing_customer', {plan_id: "", shop_name: ""})
    }

    $scope.login_button = function(){
      if (tenant_name != undefined){
        $window.open('https://' + $scope.subscription.tenant_name + '.' + 'groovepacker.com');
      }
    };

    myscope.open_handler = function(plan_id, channel){
      attrs = {shop_name: $scope.shop_name,  shop_type: $scope.shop_type};
      subscription.get_one_time_payment(attrs).success(function(response){
        $scope.subscription.one_time_payment = response["one_time_payment"];
        $scope.subscription.stripe_public_key = response["stripe_public_key"];
      });
      subscription.plan_info(plan_id, channel).success(function(response){
        if (response.status == true) {
          amount = response.plan_info.amount;
        }
        if (channel == 'Shopify' || channel == '  ') {
          // amount = 0;
          amount = Number($scope.subscription.one_time_payment / 100).toFixed(2);
          description = 'Add a card to your account';
        } else {
          amount = $scope.subscription.one_time_payment;
          if($scope.subscription.discount_amount != undefined){
            amount = amount - $scope.subscription.discount_amount;
            if (amount == 0){
              amount = amount + 50;
            }
          }
          description = 'Initialization fee ($' + Number((amount) / 100).toFixed(2) + ')';
        }

        subscription.create_payment_intent(amount).success(function (response) {
          response['shop_name'] = $scope.subscription.shop_name;
          response['plan_id'] = plan_id;
          response['amount'] = amount;

          var stripe_payment_modal = $modal.open({
            templateUrl: '/assets/views/modals/stripe/checkout_form.html',
            controller: 'stripeCheckoutCtrl',
            backdrop: 'static',
            size: 'small',
            keyboard: false,
            resolve: {
              data: function () { return response; }
            }
          });

          stripe_payment_modal.result.then(function (response) {
            $scope.success = true;
            data = {tenant_name: $scope.subscription.shop_name,
                    stripe_user_token: response.id || 0,
                    email: $scope.subscription.email,
                    amount: amount,
                    plan_id: plan_id,
                    user_name: $scope.subscription.user_name,
                    password: $scope.subscription.password,
                    radio_subscription: $('input:radio[name=radio_subscription]:checked').val(),
                    coupon_id: $scope.subscription.coupon_id,
                    shop_name: $scope.shop_name,
                    shop_type: $scope.shop_type}
            fbq('init', '2941492595962136');
            fbq('track', 'StartTrial', {value: '500.00', currency: 'USD', predicted_ltv: '100.00'});
            subscription.request_tenant_creation(response.id, data).then(function(response){
              response = response.data
              if (response.valid == true) {
                transaction_id = response.transaction_id;
                notice = response.notice;
                email = response.email;
                next_date = response.next_date;
                store = response.store;
                $state.go('show', {notice: notice});
              } else {
                $scope.success = false;
                notification.notify("Tenant creation failed")
                $scope.subscription.processing_alert_error = response.errors;
              }
            }).error(function () {
              $scope.success = false;
              $scope.subscription.processing_alert_error = "Your subscription could not be processed. Please try again.";
            });
          });
        });
      });
    };

    myscope.init();
  }
]);
