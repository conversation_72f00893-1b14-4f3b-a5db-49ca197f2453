groovepacks_directives.directive('groovDashboard', ['$http', '$window', '$document', '$sce', '$rootScope',
  '$timeout', '$interval', '$state', 'groovIO', 'orders', 'stores', 'notification', 'dashboard','generalsettings', 'dashboard_calculator', 'users',
  function ($http, $window, $document, $sce, $rootScope, $timeout, $interval, $state, groovIO, orders, stores,
            notification, dashboard, generalsettings, dashboard_calculator, users) {
    return {
      restrict: "A",
      templateUrl: "/assets/views/directives/dashboard.html",
      scope: {},
      link: function (scope, el, attrs) {
        scope.dashbord_detail_open = false;
        scope.dash_tabs = [
          {
            "heading": "Home",
            "templateUrl": "/assets/views/directives/dashboard/home.html"
          },
          {
            "heading": "Most Recent Exceptions",
            "templateUrl": "/assets/views/directives/dashboard/most_recent_exceptions.html"
          },
          {
            "heading": "Exceptions by Frequency",
            "templateUrl": "/assets/views/directives/dashboard/most_recent_exceptions.html"
          },
          {
            "heading": "Leader Board",
            "templateUrl": "/assets/views/directives/dashboard/leader_board.html"
          },
          // {
          //   "heading": "Daily Packed %",
          //   "templateUrl": "/assets/views/directives/dashboard/daily_packed_percentage.html"
          // }
        ];
        scope.toggle_dashboard_detail = function () {
          $('#dashboard').toggleClass('pdash-open');
          scope.dashbord_detail_open = !scope.dashbord_detail_open;
          // if (scope.dashbord_detail_open) {
          //   scope.charts.init();
          // }
        };

        scope.init = function () {
          scope.charts.type = 'packing_stats';
          scope.dashboard = dashboard.model.get();
          scope.dash_data = {};
          scope.exceptions.init_all();
          scope.select_toggle = false;
          scope.general_settings = generalsettings.model.get();
          generalsettings.single.get(scope.general_settings).then(function(data){
            scope.check = data.data.data.settings;
            if (data.data.data.settings.daily_packed_toggle == true) {
              scope.dash_tabs.push(
                {
                  "heading": "Daily Packed %",
                  "templateUrl": "/assets/views/directives/dashboard/daily_packed_percentage.html"
                }
              );
            }
            if (scope.check.groovelytic_stat == true) {
              setTimeout(function(){
                dashboard.stats.dashboard_stat('0');
              }, 300);
            }
          });

          $http.get($rootScope.api_url + '/exportsettings/get_export_settings').success(function(response){
            scope.export = response.data.settings
            scope.dashboard.email = response.data.settings.stat_export_email;
            scope.dashboard.processing_time = response.data.settings.processing_time;
          });
          $http.get($rootScope.api_url + '/settings/get_settings').success(function(response){
            scope.dashboard.stat_message = response.data.settings.stat_status;
          });
          $rootScope.$broadcast('connect-dashboard-to-socket-server');
        };

        $rootScope.$on('connect-dashboard-to-socket-server', function(){
          groovIO.on('dashboard_update_stat', function (response) {
            scope.dashboard.percentage = response.data.percentage;
            scope.dashboard.stat_status = response.data.status;
            $http.get($rootScope.api_url + '/settings/update_stat_status.json?percentage=' + scope.dashboard.percentage).success(function (data) {
              scope.dashboard.stat_message = data.stat_status;
            });
            if (scope.dashboard.percentage == 100 && scope.dashboard.stat_status == true) {
              $timeout(function () { scope.dashboard.percentage = null; }, 5000);
            }
            if (scope.dashboard.stat_status == false) {
              scope.dashboard.type = 'failed';
            } else {
              scope.dashboard.type = 'success';
            }
          });

          groovIO.on('dashboard_update', function (message) {
            days = scope.charts.days_filters[scope.charts.current_filter_idx].days;
            scope.dash_data = message.data;
            scope.build_dash_data();
          });
        });
        scope.switch_tab = function (tab) {
          if (tab.heading === "Most Recent Exceptions") {
            scope.exceptions.type = "most_recent";
            scope.exceptions.retrieve.most_recent_exceptions();
          } else if (tab.heading === "Exceptions by Frequency") {
            scope.exceptions.type = "by_frequency";
            scope.exceptions.retrieve.exceptions_by_frequency();
          }
          else if(tab.heading === "Daily Packed %"){
            scope.get_daily_packed_percentage();
          }
        }


        scope.update_settings = function(){
          scope.export.processing_time = scope.dashboard.processing_time
          dashboard.stats.update_export_settings(scope.export);
          dashboard.stats.daily_packed_percentage().then(function(response){
            scope.daily_packed_percentage.list = response.data;
          });
        }

        scope.get_daily_packed_percentage = function(){
          dashboard.stats.daily_packed_percentage().then(function(response){
            scope.daily_packed_percentage.list = response.data;
          });
        }

        scope.download_daily_packed_csv = function(){
          dashboard.stats.download_daily_packed_csv(scope.daily_packed_percentage.list);
        }

        scope.update_max = function () {
          dashboard.model.update_max(scope.dashboard.max_time_per_item).then(function(){
            scope.build_dash_data();
          });
        }

        scope.get_stat = function(event){
          if(event.ctrlKey){
            dashboard.stats.process_missing_data();
          }else{
            dashboard.stats.stat_stream();
          }
          scope.dashboard.stat_status = true;
          scope.dashboard.percentage = 0;
          scope.dashboard.stat_message = "preparing to update";
        }

        scope.generate_stat = function(){
          dashboard.stats.stat_generate(scope.dashboard);
        }

        scope.handle_click_fn = function (row, event) {
          if (typeof event !== 'undefined') {
            event.stopPropagation();
          }
          var toState = 'orders.filter.page.single';
          var toParams = {};
          for (var key in $state.params) {
            if (['filter', 'page'].indexOf(key) !== -1) {
              toParams[key] = $state.params[key];
            }
          }
          orders.single.get_id(row['increment_id']).then(function(response) {
            toParams.order_id = response.data;
            scope.toggle_dashboard_detail();
            $state.go(toState, toParams);
          });
        };



        scope.charts = {
          type: 'packing_stats',
          current_filter_idx: 1,
          days_filters: [
            {id: 1, name: '7 days', days: '7'},
            {id: 2, name: '30 days', days: '30'},
            {id: 3, name: '90 days', days: '90'},
            {id: 4, name: '180 days', days: '180'},
            {id: 5, name: '365 days', days: '365'}
          ],
          change_days_filter: function (index) {
            this.current_filter_idx = index;
            days = scope.charts.days_filters[scope.charts.current_filter_idx].days;
            setTimeout(function(){
              dashboard.stats.dashboard_stat(this.days);
            }, 300);
            // scope.build_dash_data();
          },
          // init: function () {
          //   dashboard.stats.dashboard_stat();
          // },
          set_type: function (chart_mode) {
            if ((scope.charts.type == 'packing_error' && chart_mode == 'packing_stats') ||
              (scope.charts.type == 'packing_time_stats' && chart_mode == 'packing_speed_stats') ||
              (scope.charts.type == 'packed_order_stats' && chart_mode == 'packed_item_stats')) {
              return 0;
            } else {
              scope.charts.type = chart_mode;
            }
          },
          alter_type: function (chart_mode) {
            scope.charts.type = chart_mode;
          }
        };

        scope.leader_board = {
          list: [],
          options: {
            functions: {
              ordernum: scope.handle_click_fn
            },
            all_fields: {
              '': {
                name: "",
                hideable: false,
                editable: false,
                sortable: false,
                transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
              },
              order_items_count: {
                name: "Order Items",
                editable: false
              },
              user_name: {
                name: "Name",
                editable: false
              },
              record_date: {
                name: "Record Date",
                editable: false,
                transclude: "<span>{{row[field] | date:'EEEE MM/dd/yyyy'}}</span>"
              },
              increment_id: {
                name: "Order Number",
                editable: false,
                transclude: '<a href="" ng-click="options.functions.ordernum(row,$event)" >{{row[field]}}</a>'
              },
              packing_time: {
                name: "Packing Time",
                editable: false
              }
            }
          }
        };

        scope.select_all_toggle = function(val){
          scope.daily_packed_percentage.options.select_all = !scope.select_toggle;
          for (var value = 0; value <= scope.daily_packed_percentage.list.length - 1; value++) {
            scope.daily_packed_percentage.list[value].checked = scope.daily_packed_percentage.options.select_all;
          }

          scope.daily_packed_percentage.options.setup.select_all = !scope.select_toggle;
          scope.select_toggle = !scope.select_toggle;
        }

        scope.daily_packed_percentage = {
          list: [],
          options: {
            functions: {
              ordernum: scope.handle_click_fn
            },
            show_hide: true,
            draggable: true,
            setup:{
              select_all: scope.select_toggle
            },
            select_all: scope.select_all_toggle,
            selectable: true,
            all_fields: {
              '': {
                name: "",
                hideable: false,
                editable: false,
                sortable: false,
                transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
              },
              day: {
                name: "Day",
                editable: false
              },
              date: {
                name: "Ship Date",
                editable: false
              },
              scanned: {
                name: "Scanned %",
                editable: false
              },
              imported: {
                name: "Total Imported",
                editable: false
              },
              unscanned: {
                name: "Unscanned",
                editable: false
              }
            }
          }
        };


        scope.exceptions = {
          type: 'most_recent',
          current_user_idx: '0',
          users: [],
          init_all: function () {
            this.init.most_recent_exceptions();
            this.init.exception_by_frequency();
            this.init.users();
          },
          init: {
            users: function () {
              scope.exceptions.users.push({id: '-1', username: 'All Users'});
              users.list.get(null).then(function (response) {
                if (!response.data.users) { return; }

                response.data.users.forEach(function(element) {
                  if (element.active) {
                    scope.exceptions.users.push(element);
                  } else{
                    return;
                  }
                });
              });
            },
            exception_by_frequency: function () {
              scope.exceptions_by_frequency = {
                list: [],
                options: {
                  functions: {
                    ordernum: scope.handle_click_fn
                  },
                  all_fields: {
                    '': {
                      name: "",
                      hideable: false,
                      editable: false,
                      sortable: false,
                      transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
                    },
                    recorded_at: {
                      name: "Date Recorded",
                      editable: false,
                      transclude: "<span>{{row[field] | date:'EEEE MM/dd/yyyy'}}</span>"
                    },
                    created_at: {
                      name: "Order Date",
                      editable: false,
                      transclude: "<span>{{row[field] | date:'EEEE MM/dd/yyyy'}}</span>"
                    },
                    increment_id: {
                      name: "Order Number",
                      editable: false,
                      transclude: '<a href="" ng-click="options.functions.ordernum(row,$event)" >{{row[field]}}</a>'
                    },
                    reason: {
                      name: "Exception Reason",
                      editable: false
                    },
                    description: {
                      name: "Exception Description",
                      editable: false
                    },
                    associated_user: {
                      name: "Associated User",
                      editable: false
                    },
                    frequency: {
                      name: "Frequency",
                      editable: false,
                      transclude: "<span>{{row[field]}} %</span>"
                    }
                  }
                }
              };
            },
            most_recent_exceptions: function () {
              scope.most_recent_exceptions = {
                list: [],
                options: {
                  functions: {
                    ordernum: scope.handle_click_fn
                  },
                  all_fields: {
                    '': {
                      name: "",
                      hideable: false,
                      editable: false,
                      sortable: false,
                      transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
                    },
                    recorded_at: {
                      name: "Date Recorded",
                      editable: false,
                      transclude: "<span>{{row[field] | date:'EEEE MM/dd/yyyy'}}</span>"
                    },
                    created_at: {
                      name: "Order Date",
                      editable: false,
                      transclude: "<span>{{row[field] | date:'EEEE MM/dd/yyyy'}}</span>"
                    },
                    increment_id: {
                      name: "Order Number",
                      editable: false,
                      transclude: '<a href="" ng-click="options.functions.ordernum(row,$event)" >{{row[field]}}</a>'
                    },
                    reason: {
                      name: "Exception Reason",
                      editable: false
                    },
                    description: {
                      name: "Exception Description",
                      editable: false
                    },
                    associated_user: {
                      name: "Associated User",
                      editable: false
                    },
                    frequency: {
                      name: "Frequency",
                      editable: false,
                      transclude: "<span>{{row[field]}} %</span>"
                    }
                  }
                }
              };
            }
          },
          change_user: function (user_idx) {
            this.current_user_idx = user_idx;
            if (scope.exceptions.type === "most_recent") {
              scope.exceptions.retrieve.most_recent_exceptions();
            } else if (scope.exceptions.type === "by_frequency") {
              scope.exceptions.retrieve.exceptions_by_frequency();
            }
          },
          retrieve: {
            most_recent_exceptions: function () {
              dashboard.stats.exceptions(
                scope.exceptions.users[scope.exceptions.current_user_idx].id,
                scope.exceptions.type).then(
                function (response) {
                  scope.most_recent_exceptions.list = response.data;
                });
            },
            exceptions_by_frequency: function () {
              dashboard.stats.exceptions(
                scope.exceptions.users[scope.exceptions.current_user_idx].id,
                scope.exceptions.type).then(
                function (response) {
                  scope.exceptions_by_frequency.list = response.data;
                });
            }
          }
        };

        scope.xAxisTickValuesFunction = function () {
          return function(d){
            var tickVals = [];
            dates = [];
            dlen = d.length;
            for (var i = dlen - 1; i >= 0; i--) {
              ilen = d[i].values.length;
              for (var j = ilen - 1; j >= 0; j--) {
                if (d[i].disabled) {
                  break;
                } else {
                  dates.push(d[i].values[j][0]);
                }
              };
            };
            var max = Math.max.apply(Math,dates)
            var min = Math.min.apply(Math,dates)
            tickVals.push(min);
            if (parseInt((min + max) / 2) > (min + 86400)) {
              tickVals.push(parseInt(min + max) / 2);
            }
            tickVals.push(max);

            return tickVals;
          };
        };

        scope.yAxisTickValuesFunction = function (floating) {
          return function(d){
            var tickVals = [];
            var vals = [];
            dlen = d.length;
            for (var i = dlen - 1; i >= 0; i--) {
              ilen = d[i].values.length;
              for (var j = ilen - 1; j >= 0; j--) {
                if (d[i].disabled) {
                  break;
                } else {
                  vals.push(d[i].values[j][1]);
                }
              };
            };
            vals.sort();
            max = Math.max.apply(Math,vals);
            min = Math.min.apply(Math,vals);
            div_range = (max - min) / 5;
            tickVals.push(min);
            temp = min;
            while (temp + div_range < max) {
              temp += div_range;
              if (temp.toFixed(2) % 1 == '0.00' || !floating) {
                tickVals.push(Math.round( temp ));
              } else{
                tickVals.push(temp.toFixed(2));
              };
            };
            tickVals.push(max);
            return tickVals;
          };
        };

        scope.xAxisTickFormatFunction = function () {
          return function (d) {
            time_zone = localStorage.getItem('current_time_zone');
            var date = time_zone ? d3.time.format('%b %e, %Y')(moment.unix(d).tz(time_zone).startOf('day').toDate()) : d3.time.format('%b %e, %Y')(moment.unix(d).toDate());
            return date;
          };
        };

        scope.yAxisTickFormatFunction = function () {
          return function (d) {
            return d;
          };
        };
        scope.legendColorFunction = function () {
          return function (d) {
            return d.color;
          };
        };

        scope.toolTipContentFunction = function () {
          return function (key, x, y, e, graph) {
            time_zone = localStorage.getItem('current_time_zone')
            date = time_zone ? moment.tz(x, 'MMM D, YYYY', time_zone).unix() : Date.parse(x)/1000
            data = dashboard.stats.points_data(scope.dashboard, scope.charts, date, y);

            $("body").on("mousemove", "nvd3-line-chart", function(e){
              var x = (e.clientX + 20) + 'px !important',
                  y = (e.clientY - $('.nvtooltip').height() - 20) + 'px !important';
              if(e.clientX > (window.screen.width/2)){
                x = window.screen.width - e.clientX + 20 + "px";
                $('.nvtooltip').attr("style", "top:" + y + ";right:" + x + ";position: fixed");
              }else{
                $('.nvtooltip').attr("style", "top:" + y + ";left:" + x + ";position: fixed");
              }
            });
            return dashboard.stats.tooltip(data, scope.charts, scope.dashboard);
          };
        };

        scope.get_avg_time = function(time, items) {
          if (parseInt(items) === 0) {
            return 0;
          } else {
            return (parseInt(time) / parseFloat(items)).toFixed(2);
          }
        };

        scope.build_dash_data = function() {
          scope.dashboard.max_time_per_item = 0;
          dashboard.model.get_max(scope.dashboard).then(function(response) {
            days = scope.charts.days_filters[scope.charts.current_filter_idx].days;
            scope.leader_board.list = scope.dash_data.leader_board.list;
            for (var i = 0; i <= scope.dash_data.dashboard.length - 1; i++) {
              if (parseInt(scope.dash_data.dashboard[i].duration, 10) === parseInt(days)) {
                scope.dashboard.main_summary = scope.dash_data.dashboard[i].main_summary;
                scope.dashboard.packing_stats = scope.dash_data.dashboard[i].daily_user_data.packing_stats;
                scope.dashboard.packed_item_stats = scope.dash_data.dashboard[i].daily_user_data.packed_item_stats;
                scope.dashboard.packing_speed_stats = scope.dash_data.dashboard[i].daily_user_data.packing_speed_stats;
                scope.dashboard.avg_packing_accuracy_stats = scope.dash_data.dashboard[i].avg_user_data.packing_stats;
                scope.dashboard.avg_packing_speed_stats = scope.dash_data.dashboard[i].avg_user_data.packing_speed_stats;
                var current_period_avg = scope.get_avg_time(scope.dashboard.main_summary.packing_time_summary.current_period, scope.dashboard.main_summary.packed_items_summary.current_period);
                var previous_period_avg = scope.get_avg_time(scope.dashboard.main_summary.packing_time_summary.previous_period, scope.dashboard.main_summary.packed_items_summary.previous_period);
                scope.dashboard.packing_time_summary.current_period = current_period_avg
                scope.dashboard.packing_time_summary.previous_period = previous_period_avg
                scope.dashboard.packing_time_summary.delta = (current_period_avg - previous_period_avg).toFixed(2);
                scope.dashboard.packing_speed_summary.current_period = dashboard.stats.speed(current_period_avg, scope.dashboard);
                scope.dashboard.packing_speed_summary.previous_period = dashboard.stats.speed(previous_period_avg, scope.dashboard);
                scope.dashboard.packing_speed_summary.delta =
                  (scope.dashboard.packing_speed_summary.current_period - scope.dashboard.packing_speed_summary.previous_period).toFixed(2);
              }
            }
            scope.set_manager_data();
          })
        };

        scope.set_manager_data = function(){
          scope.dashboard.manager_data = { accuracy: "", speed: "", packed: "", cu_order: 0, cu_order_item: 0, leader_order: 0, leader_order_item: 0, leader_name: ""};
          var current_date = d3.time.format('%b %e, %Y')(new Date());
          var total_packed = 0;
          var current_leader = { orders: 0, order_item: 0, username: ""};
          angular.forEach(scope.dashboard.packing_stats, function(obj, obj_key) {
            angular.forEach(obj.values, function(value, key) {
              var date = d3.time.format('%b %e, %Y')(moment.unix(value[0]).toDate());
              if(date == current_date){
                if(value[3] >= current_leader.order_item){
                  current_leader = { orders: value[2], order_item: value[3], username: obj.key };
                }

                if(obj.key == $rootScope.current_user_data.username){
                  scope.dashboard.manager_data.cu_order = value[2];
                  scope.dashboard.manager_data.cu_order_item = value[3];
                  // if($rootScope.current_user_data.view_dashboard == "packer_dashboard" || $rootScope.current_user_data.view_dashboard == "admin_dashboard_with_packer_stats"){
                  //   scope.dashboard.manager_data.accuracy = value[1];
                  // }
                }
              }
              total_packed += value[3];
            });
          });

          scope.dashboard.manager_data.packed = total_packed;
          set_current_leader_data(current_leader);

          // if($rootScope.current_user_data.view_dashboard == "packer_dashboard" || $rootScope.current_user_data.view_dashboard == "admin_dashboard_with_packer_stats"){
          //   angular.forEach(scope.dashboard.packing_speed_stats, function(obj, obj_key) {
          //     if(obj.key == $rootScope.current_user_data.username){
          //       angular.forEach(obj.values, function(value, key) {
          //         var date = d3.time.format('%b %e, %Y')(moment.unix(value[0]).toDate());
          //         if(date == current_date){
          //           scope.dashboard.manager_data.speed = dashboard.stats.speed(value[1], scope.dashboard);
          //         }
          //       });
          //     }
          //   });
          // }
          // else{
          //   scope.dashboard.manager_data.accuracy = scope.dashboard.main_summary.packing_accuracy_summary.current_period;
          //   scope.dashboard.manager_data.speed = scope.dashboard.packing_speed_summary.current_period;
          // }
        }

        var set_current_leader_data = function(current_leader){
          scope.dashboard.manager_data.leader_order = current_leader.orders;
          scope.dashboard.manager_data.leader_order_item = current_leader.order_item;
          scope.dashboard.manager_data.leader_name = current_leader.username;
        }

        scope.init();
      }
    };
  }]);

