groovepacks_directives.directive('groovNotification',["stores", "$rootScope", function (stores, $rootScope) {
  return {
    restrict: "A",
    templateUrl: "/assets/views/directives/notification.html",
    scope: {},
    link: function (scope, el, attrs) {
      scope.notifs = {};
      scope.scan_pack_location = location.href.match(/scan.*pack/)
      //Notification related calls
      scope.$on('notification', function (event, args) {
        scope.notifs = args.data;
      });

      scope.close = function(notif){
        notif.show = false;
      };

      scope.reimport_from_scratch = function() {
        csv = {};
        csv.current = $rootScope.csv;
        if ($rootScope.csv != undefined){
          csv.current.reimport_from_scratch = true;
          stores.csv.do_import(csv);
        }
      };

      //import All Orders function
      //scope.groovImport = function () {
      //$('#importOrders').modal('show');
      //importOrders.do_import(scope);
      //}
    }
  };
}]);
