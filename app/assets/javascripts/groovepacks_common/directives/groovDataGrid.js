groovepacks_directives.directive('groovDataGrid', ['$timeout', '$http', '$sce', 'settings', 'hotkeys' , 'editable', 'notification', function ($timeout, $http, $sce, settings, hotkeys, editable, notification) {
  $sce.editable_loaded = false;
  var default_options = function () {
    return {
      identifier: 'datagrid',
      select_all: false,
      invert: false,
      selectable: false,
      scrollbar: false,
      col_length: 25,
      no_of_lines: 1,
      copyable: false,
      selections: {
        single_callback: function () {
        },
        multi_page: function () {
        },
        show_dropdown: false,
        selected_count: 0,
        unbind: false,
        show: function () {
        }
      },
      dynamic_width: false,
      show_hide: false,
      editable: false,
      disable_global_edit: false,
      enable_edit: false,
      sortable: false,
      paginate: {
        show: false,
        total_items: 0,
        max_size: 12,
        current_page: 1,
        items_per_page: 10,
        callback: function () {
        }
      },
      sort_func: function () {
      },
      setup: {},
      all_fields: {}
    }
  };

  var default_field_options = function () {
    return {
      name: "field",
      class: "col-xs-2",
      hideable: true,
      hidden: false,
      transclude: '',
      model: 'row'
    }
  };

  return {
    restrict: "A",
    scope: {
      groovDataGrid: "=",
      rows: "=groovList"
    },
    templateUrl: "/assets/views/directives/datagrid.html",
    link: function (scope, el, attrs) {
      var myscope = {};
      scope.editable_modal = editable;
      scope.context_menu_event = function (event) {
        if (scope.options.show_hide) {
          if (typeof event == 'undefined' || typeof event['pointerType'] == 'undefined') {
            scope.context_menu.shown = !scope.context_menu.shown;
          }
          if (typeof event != "undefined") {
            event.preventDefault();
            if (typeof event['pointerType'] != 'undefined') {
              event = event.srcEvent;
            }
            var offset = el.offset();
            scope.context_menu.style = {left: event.pageX - offset.left, top: event.pageY - offset.top}
          }
        }
      };

      scope.show_hide = function (field) {
        field.hidden = !field.hidden;
        scope.update();
      };

      scope.set_position = function (row, rows) {
        return rows.indexOf(row) === 0 ? 'bottom' : 'top'
      }

      scope.check_uncheck = function (row, index, event) {
        //If target is a link

        if(event.target.id == 'copy-text'){return};

        if(event.target.tagName == 'A'){return};

        if (scope.options.selectable) {
          scope.options.setup.select_all = false;
          row.checked = !row.checked;
          scope.options.selections.single_callback(row);
          if (event.shiftKey && myscope.last_clicked !== null) {
            event.preventDefault();
            var start = index;
            var end = myscope.last_clicked.index;
            if (myscope.last_clicked.page == scope.options.paginate.current_page) {
              if (index > myscope.last_clicked.index) {
                start = end;
                end = index;
              }
            } else {

              if (scope.options.paginate.current_page > myscope.last_clicked.page) {
                start = 0;
                end = index;
                scope.options.selections.multi_page(myscope.last_clicked, {
                  page: scope.options.paginate.current_page,
                  index: index
                }, row.checked);
              } else if (myscope.last_clicked.page > scope.options.paginate.current_page) {
                start = index;
                end = scope.rows.length - 1;
                scope.options.selections.multi_page({
                  page: scope.options.paginate.current_page,
                  index: index
                }, myscope.last_clicked, row.checked);
              }
            }
            for (var i = start; i <= end; i++) {
              scope.rows[i].checked = row.checked;
              scope.options.selections.single_callback(scope.rows[i]);
            }

          }
          myscope.last_clicked = {page: scope.options.paginate.current_page, index: index};
        }
      };
      scope.show_dropdown = function () {
        scope.dropdown.show = false;
        if (scope.options.selections.show_dropdown && !(scope.options.setup.select_all || scope.options.setup.inverted)) {
          $timeout.cancel(myscope.dropdown_promise);
          myscope.dropdown_promise = null;
          scope.dropdown.show = true;
        }
      };

      scope.copied = function(msg){
        char = msg.includes(' (S') ? ' (S' : (msg.includes(' (C') ? ' (C' : (msg.includes(' (D') ? ' (D' : false))
        if (msg.includes(char)) {
          msg = msg.split(char);
          msg.pop();
          msg = msg.join();
        }
        $('#copy')[0].value = msg;
        selecter = document.querySelector('#copy');
        selecter.select();
        document.execCommand('copy');
        notification.notify('copied ' + msg + ' to clipboard', 1);
      }

      scope.update = function () {
        myscope.make_theads(scope.theads);
        settings.column_preferences.save(scope.options.identifier, scope.theads);
      };

      scope.load_editable = function (ind, field) {
        if (typeof scope.editable[field] == "undefined") {
          if($sce.editable_loaded==true) return;
          scope.compile(ind, field);
          $sce.editable_loaded = true;
          window.setTimeout(function() {
            editable.force_exit();
          }, 1000);
        }
      }

      scope.compile = function (ind, field) {
        if (typeof scope.editable[field] == "undefined") {
          scope.editable[field] = {};
        }

        if (typeof scope.editable[field][ind] == "undefined") {
          scope.editable[field][ind] = $sce.trustAsHtml(
            '<div ng-class="{\'grid-editable-field\': !editable_modal.status(),' +
                           ' \'grid-editing-mode\': editable_modal.status()}" ' +
                'ng-style="{\'min-width\' : (row[field].length > (options.all_fields[field].col_length || options.col_length) ? ((options.all_fields[field].col_length || options.col_length) + 1) : (row[field].length || 5) + 1) + \'rem\'}"' +
                //'ng-mouseover="row.show_action_icon ? (row.show_action_icon[field]=true) : (row.show_action_icon={})"' +
                //'ng-mouseleave="row.show_action_icon ? (row.show_action_icon[field]=false) : (row.show_action_icon={})" ' +
                'groov-editable="options.editable" prop="{{field}}" ng-model="' +
                scope.options.all_fields[field].model + '" identifier="' +
                scope.options.identifier + '_list-' + field + '-' + ind +'"'+ 'position="bottom"' + '>' +
              '<span class="pull-right fa datagrid-pencil fa-pencil pointer" ' +
                'ng-show="row.show_action_icon[field]" groov-click="compile(' + ind + ',\'' +field+ '\')">' +
              '</span>' +
              scope.options.all_fields[field].transclude +
            '</div>'
          );
        }

        $timeout(function () {
          scope.$broadcast(scope.options.identifier + '_list-' + field + '-' + ind);
        }, 30);
      };

      // scope.add_style = function(field, ind) {
      //   style =  $sce.trustAsHtml(
      //     '<style>
      //       .grid-editable-field-' + field + $parent.$parent.$index + '{
      //         padding: 0.8rem 0.3rem;
      //         min-height: 3rem;
      //         &:hover{
      //           background-color: rgb(230, 228, 228);
      //           border-color: #C5C4C4;
      //           border-style: solid;
      //           border-width: thin;
      //           border-radius: 3px;
      //           position: absolute;
      //           width: ' + scope.options.all_fields[field].col_length + 3 + 'rem;
      //         }
      //       }
      //     </style>
      //   ')
      // }

      myscope.make_theads = function (theads) {
        var shown = [];
        for (var i in scope.options.all_fields) {
          if (scope.options.all_fields.hasOwnProperty(i)) {
            if (!scope.options.all_fields[i].hidden) {
              shown.push(i);
            }
          }
        }
        scope.theads = theads.concat(shown).filter(function (elem, idx, arr) {
          return (shown.indexOf(elem) != -1 && arr.indexOf(elem) >= idx);
        });
        scope.dragOptions.reload = true;
      };

      myscope.invert_selection = function () {
        if (scope.options.invert === false) {
          for (var i = 0; i < scope.rows.length; i++) {
            scope.check_uncheck(scope.rows[i], i, {shiftKey: false});
          }
        } else {
          scope.options.invert(!scope.options.setup.inverted);
        }
        myscope.last_clicked = null;
      };

      myscope.update_paginate = function () {
        var options = default_options();
        jQuery.extend(true, options.paginate, scope.groovDataGrid.paginate);
        scope.options.paginate = options.paginate;
        myscope.last_clicked = null;
      };

      myscope.update_selections = function () {
        var options = default_options();
        jQuery.extend(true, options.selections, scope.groovDataGrid.selections);
        scope.options.selections = options.selections;
        $timeout(function () {
          selector_icons = $('.fa-bullseye');
          for (i = 0; i < selector_icons.length; ++i) {
            if ($('.fa-bullseye')[i].closest('.grid-simple-text') != null) {
              $('.fa-bullseye')[i].closest('.grid-simple-text').style.minWidth = '0px';
            }
          }
          if ($('.fa-bullseye')[0].closest('td') != null) {
            $('table').find('th').eq($('.fa-bullseye')[0].closest('td').cellIndex)[0].style.width = '1%';
          }
        }, 1000);
      };

      scope.start_dropdown_timer = function () {
        myscope.dropdown_promise = $timeout(function () {
          scope.dropdown.show = false
        }, 500);
      };

      myscope.add_double_scrollbar = function () {
        if(!scope.options.scrollbar){return;};
        setTimeout(function() {
          // Remove prev scrollbar
          $('#' + scope.custom_identifier).parents('.table-parent').removeData();
          $('#' + scope.custom_identifier).parents('.container-fluid').find('.suwala-doubleScroll-scroll-wrapper').remove();

          targetWidth = 0;
          $.each($('#' + scope.custom_identifier).parents('.table-parent').find('th'), function(){
            targetWidth += $(this).width()
          });


          if(targetWidth > $('#' + scope.custom_identifier).parents('.table-parent').width()){
            $('#' + scope.custom_identifier).parents('.table-parent').doubleScroll({
              width: targetWidth
            });
          }
        }, 1000);
      }

      myscope.init = function () {
        scope.theads = [];
        myscope.last_clicked = null;

        scope.editable = {};
        var options = default_options();
        jQuery.extend(true, options, scope.groovDataGrid);
        for (var i in scope.groovDataGrid.all_fields) {
          if (scope.groovDataGrid.all_fields.hasOwnProperty(i)) {
            options.all_fields[i] = default_field_options();
            options.all_fields[i].editable = (options.disable_global_edit == false && options.editable != false);
            options.all_fields[i].draggable = (options.draggable != false);
            options.all_fields[i].sortable = (options.sortable != false);
            angular.extend(options.all_fields[i], scope.groovDataGrid.all_fields[i]);
            if (options.all_fields[i].transclude !== '') {
              options.all_fields[i].transclude = $sce.trustAsHtml(scope.groovDataGrid.all_fields[i].transclude);
            }
            if (options.all_fields[i].enable_edit == true) {
              options.all_fields[i].editable = true;
            }
            scope.theads.push(i);
          }
        }
        if (angular.isObject(scope.groovDataGrid['setup'])) {
          options.setup = scope.groovDataGrid.setup;
        }
        scope.context_menu = {
          shown: false,
          style: {}
        };
        scope.options = options;
        scope.dragOptions = {
          update: scope.update,
          enabled: scope.options.draggable,
          reload: false
        };
        scope.dropdown = {
          show: false
        };
        scope.custom_identifier = scope.options.identifier + Math.floor(Math.random() * 1000);

        settings.column_preferences.get(scope.options.identifier).success(function (data) {
          if (data.status) {
            var theads = [];
            if (data.data && typeof data.data['theads'] != "undefined" && data.data.theads) {
              theads = data.data.theads;
              for (var i in scope.options.all_fields) {
                if (scope.options.all_fields.hasOwnProperty(i)) {
                  if (scope.options.all_fields[i].hideable) {
                    scope.options.all_fields[i].hidden = true;
                  }
                  if (theads.indexOf(i) != -1) {
                    scope.options.all_fields[i].hidden = false;
                  }
                }
              }
            }
            myscope.make_theads(theads);
          }
        });
        if (scope.options.selectable && !scope.options.selections.unbind) {
          hotkeys.add({
            combo: 'mod+i',
            callback: myscope.invert_selection
          });
        }
        if (typeof scope.groovDataGrid['paginate'] != "undefined") {
          scope.$watch('groovDataGrid.paginate', myscope.update_paginate, true);
          scope.$watch('options.paginate.current_page', scope.options.paginate.callback);
        }
        if (typeof scope.groovDataGrid['selections'] != "undefined" && scope.groovDataGrid.selections.show_dropdown) {
          scope.$watch('groovDataGrid.selections', myscope.update_selections, true);
        }

        scope.$watch('theads', myscope.add_double_scrollbar);
      };
      myscope.init();

      $(document).ready(function(){
        $(window).on('resize', function(){
          myscope.add_double_scrollbar();
        });
        
        $timeout(function () {
          selector_icons = $('.fa-bullseye');
          for (i = 0; i < selector_icons.length; ++i) {
            if ($('.fa-bullseye')[i].closest('.grid-simple-text') != null) {
              $('.fa-bullseye')[i].closest('.grid-simple-text').style.minWidth = '0px';
            }
          }
          if ($('.fa-bullseye')[0].closest('td') != null) {
            $('table').find('th').eq($('.fa-bullseye')[0].closest('td').cellIndex)[0].style.width = '1%';
          }
        }, 1000);
      });
    }
  };
}]);
