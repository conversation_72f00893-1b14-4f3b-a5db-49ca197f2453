// register the interceptor as a service
groovepacks_services.factory('authInterceptor', ['$q', '$window', '$injector', '$interval', '$rootScope', 
  function($q, $window, $injector, $interval, $rootScope) {
  return {
    // optional method
    'request': function(config) {
      // do something on success
      access_token = $window.localStorage.getItem('access_token');

      if (access_token != null) {
        if (config.url.indexOf("getToken") == -1) {
          config.headers["Authorization"] = "Bearer " + access_token;
        }
      }
      return config;
    },

    // optional method
    'responseError': function(rejection) {
      // redirecting to login page on 401 status
      if (rejection.status === 401) {
        //call sign out
        $window.localStorage.removeItem('access_token');
        $window.localStorage.removeItem('refresh_token');
        $window.localStorage.removeItem('created_at');
        $window.localStorage.removeItem('current_user');
        $interval.cancel($rootScope.refresh_token_interval);
        var stateService = $injector.get('$state');
         stateService.go('login');
      } 
      return $q.reject(rejection);
    }

  };
}]);
