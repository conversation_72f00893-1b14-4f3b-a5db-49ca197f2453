groovepacks_services.factory('dashboard', ['$http', 'notification', 'auth', '$rootScope', '$window', '$modal', function ($http, notification, auth, $rootScope, $window, $modal) {
  var get_default = function () {
    return {
      packing_stats: [],
      packed_item_stats: [],
      packing_speed_stats: [],
      avg_packing_speed_stats: [],
      avg_packing_accuracy_stats: [],
      main_summary: {},
      max_time_per_item: 0,
      packing_time_summary: {},
      packing_speed_summary: {}
    };
  };

  var get_max_time = function(dashboard) {
    return(
      $http.get($rootScope.api_url + '/settings/get_settings').success(function(response){
        if (response.status===true) {
          dashboard.max_time_per_item = response.data.settings.max_time_per_item;
        }
      })
    );
  };

  var update_max_time = function(max_time_per_item) {
    return(
      $http.put($rootScope.api_url + '/settings/update_settings?max_time_per_item=' + max_time_per_item).error(function(){
        notification.notify("Failed to update maximum expected time/item", 0);
      })
    );
  }

  var exceptions = function (user_id, type) {
    return (
      $http.get($rootScope.api_url + '/dashboard/exceptions?user_id=' + user_id + '&exception_type=' + type).error(function (response) {
        notification.notify("Failed to load exception statistics", 0);
      })
    );
  }

  var get_dashboard_data = function(value) {
    try{
      var tenant = document.getElementById('current_tenant').value
      var value = value
    }
    catch(e){
      tenant = null;
    }
    
    if(tenant)
    {
      var domain = document.getElementById('gl_app_url').value;
      var site_host = document.getElementById('site_host').value;
      var access_token = $window.localStorage.getItem('access_token');
      var created_at = $window.localStorage.getItem('created_at');
      var url = document.URL.split('/');
      // d = new Date();
      // if (created_at > parseInt(d.getTime() / 1000) - 5400) {
      //   refresh_access_token(url).then(function(response){
      //     access_token = response;
      //     request_analytic_server(tenant, domain, site_host, access_token, url[0]);
      //   });
      // } else {
        request_analytic_server(tenant, domain, site_host, access_token,url[0], value);
      // }
    }
  };

  var refresh_access_token = function(url) {
    var refresh_token = localStorage.getItem('refresh_token');
    var target_url = url[0] + '//' + url[2] + '/auth/v1/getToken';
    return $http.get(target_url, {headers: {
      "Content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      "Authorization": refresh_token
    }}).then(function (response) {
      if(response.status == 200){
        access_token = response.data.access_token;
        refresh_token = response.data.refresh_token;
        created_at = response.data.created_at;
      } else {
        access_token = null;
        refresh_token = null;
        created_at = null
      }
      localStorage.removeItem('access_token');
      localStorage.setItem('access_token', access_token);
      localStorage.removeItem('refresh_token');
      localStorage.setItem('refresh_token', refresh_token);
      localStorage.removeItem('created_at');
      localStorage.setItem('created_at', created_at);
      return access_token;
    });
  };

  var request_analytic_server = function(tenant, domain, site_host, access_token, protocol, value) {
    $http.get(
      // protocol + '//' + domain +'/dashboard/calculate',
      domain +'/dashboard/calculate',

      {headers: {'Authorization':'Bearer ' + access_token, 'domain':site_host, 'tenant':tenant, 'value':value}}
      ).error(function(response){
      notification.notify("Failed to load dashboard data", 0);
    });
  };

  var get_datapoints_data = function(dashboard, charts, date, y) {
    data_points = {};
    data_points.data = [];
    data_points.user = [];
    dashboard_data = {};
    if (charts.type === 'packing_stats' || charts.type === 'packing_error') {
      dashboard_data = dashboard.packing_stats;
    } else if (charts.type === 'packing_speed_stats' || charts.type === 'packing_time_stats') {
      dashboard_data = dashboard.packing_speed_stats;
    } else if (charts.type === 'packed_item_stats' || charts.type === 'packed_order_stats') {
      dashboard_data = dashboard.packed_item_stats;
    }
    for (var i = dashboard_data.length - 1; i >= 0; i--) {
      for (var j = dashboard_data[i].values.length - 1; j >= 0; j--) {
        if (moment(dashboard_data[i].values[j][0] * 1000).format('L') == moment(date * 1000).format('L') &&
            dashboard_data[i].values[j][1] == y && !$('.nv-series.disabled').find('.nv-legend-text').text().match(RegExp(dashboard_data[i].key))
        ) {
          data_points.data.push(dashboard_data[i].values[j]);
          data_points.user.push([dashboard_data[i].key, dashboard_data[i].color]);
        };
      };
    };
    return data_points;
  }

  var get_tool_tip = function(data_points, charts, dashboard) {
    var tooltipText = '';
    for (var i = data_points.data.length - 1, j=0; i >= 0; i--, j++) {
        
        var settings = JSON.parse(localStorage.getItem('general_settings'));
        var time_zone = null;

        if (settings && settings.new_time_zones && settings.data && settings.data.settings) {
          var newTimeZones = settings.new_time_zones;
          var matchingKey = Object.keys(newTimeZones).find(function(key) {
            return newTimeZones[key] === settings.data.settings.new_time_zone;
          });
          
          if (matchingKey) {
            var match = matchingKey.match(/\(([^)]+)\)$/);
            time_zone = match ? match[1] : null;
          }
        }

        var unixTimestamp = data_points.data[i][0];
        var date = time_zone && moment.tz ? 
          d3.time.format('%b %e, %Y')(moment.unix(unixTimestamp).tz(time_zone).startOf('day').toDate()) : 
          d3.time.format('%b %e, %Y')(moment.unix(unixTimestamp).toDate());
        
        col_sm = Math.floor(12/data_points.data.length);
        col_sm = col_sm < 3 ? 3 : col_sm
        if(j % 4 == 0){ tooltipText += (j == 0 ? '<div class="col-sm-12"> <div class="row">' : '</div></div><hr><div class="col-sm-12"><div class="row">') }
        tooltipText += '<div class="col-sm-' + col_sm + ' col-md-' + col_sm + ' col-lg-' + col_sm + '"><h4 style="text-transform: capitalize; color:' + data_points.user[i][1] +
        '">' + data_points.user[i][0] + '</h4>';
      if (charts.type === 'packing_stats' || charts.type === 'packing_error') {
        tooltipText += 
        '<span><strong>' + date + '</strong></span><br/>' +
        '<span><strong>Period Accuracy: ' + data_points.data[i][5] + '% </strong></span><br/>' +
        '<span><strong>Day\'s Accuracy: ' + data_points.data[i][1] + '% </strong></span><br/>' +
        '<span><strong>' + data_points.data[i][2] + ' Orders Scanned</strong></span><br/>' +
        '<span><strong>' + data_points.data[i][3] + ' Items Packed </strong></span><br/>' +
        '<span><strong>' + data_points.data[i][4] + ' Exceptions Recorded</strong></span><br/>' +
        '<span><strong>' + data_points.data[i][9] + ' Items Scanned(Period)</strong></span><br/>' +
        '<span><strong>' + data_points.data[i][8] + ' Orders Scanned(Period)</strong></span><br/>';

        if (data_points.data[i][6] != null && data_points.data[i][7] != null) {
          var orders = data_points.data[i][6].split(' ')
          var dates = data_points.data[i][7].split(' ')
          tooltipText += '<legend style="border-bottom: 2px solid rgba(0,0,1,.86); margin-bottom: 10px;"></legend>';
          for (var j = 0; j < orders.length; j++) {
            if (dates[j] == '') {
              tooltipText +=
              '<span><span style="margin-bottom: -4px; text-transform: capitalize; color:' + data_points.user[i][1] +
              '"><strong>#' + orders[j] + '</strong></span><br/>' +
              '<span style="margin-top: -4px;"><strong>Recorded: </strong></span></span><br/>';
            } else{
              tooltipText +=
              '<span><span style="margin-bottom: -4px; text-transform: capitalize; color:' + data_points.user[i][1] +
              '"><strong>#' + orders[j] + '</strong></span><br/>' +
              '<span style="margin-top: -4px;"><strong>Recorded: ' + d3.time.format('%b %e, %Y')(moment.unix(dates[j]).toDate()) + '</strong></span></span><br/>';
            };
            
          };
        }
        tooltipText += '</div>';
      } else if (charts.type === 'packing_speed_stats' || charts.type === 'packing_time_stats') {
        $.each(dashboard.packing_stats, function(i, response) {
            if (response.key == data_points.user[0][0]){
              sum = 0
              total = []
              total_sum = []
              $.each(dashboard.packing_speed_stats, function(a, obj) {
                if (obj.key == response.key) {
                  $.each(obj.values, function(q, val) {
                    sum = sum + val[1];
                    period_order_count = val[3];
                    period_item_count = val[4];
                  });
                };
              });
              avg = sum/response.values.length;
              total.push(response.values.length);
              total_sum.push(sum);
              $.each(response.values, function(j, value){
                if (value[0] == data_points.data[0][0]){
                  orders_count = value[2];
                  items_count = value[3]; 
                } 
              });
            }
        });

        clicked = 0
        if (clicked == 0){
          clicked = clicked + 1;
          trigger_click(avg,  data.user[0][0], data.data[0][1], charts,total-1, total_sum);
        };

        tooltipText +=
        '<span><strong>' + date + '</strong></span><br/>' +
        '<span><strong>Period Speed Score: ' + get_speed(data_points.data[i][2], dashboard) + '%</strong></span><br/>' +
        '<span><strong>Period Sec/Item: ' + data_points.data[i][2] + ' sec</strong></span><br/>' +
        '<span><strong>Day\'s Speed Score: ' + get_speed(data_points.data[i][1], dashboard) + '% </strong></span><br/>' +
        '<span><strong>Day\'s Sec/Item: ' + data.data[i][1]  + '</strong></span><br/>' +
        '<span><strong>' + items_count + ' Items Packed</strong></span><br/>' +
        '<span><strong>' + orders_count +' Orders Packed</strong></span><br/>' +
        '<span><strong>' + period_item_count +' Items Scanned(Period)</strong></span><br/>' +
        '<span><strong>' + period_order_count +' Orders Scanned(Period)</strong></span><br/>' +
        '<legend style="border-bottom: 2px solid rgba(0,0,1,.86); margin-bottom: 10px;"></legend>' +
        '</div>';
      } else if (charts.type === 'packed_item_stats' || charts.type === 'packed_order_stats') {
        single_tooltip = data_points.data[i][1] + ' items packed <br/>' + data_points.data[i][2] + ' orders<br/>' + data_points.data[i][4] + ' Items Scanned(Period)<br/>' + data_points.data[i][3] + ' Orders Scanned(Period)';
        tooltipText +=
          '<span><strong>' + date + '</strong></span><br/>' +
          '<span><strong>' + single_tooltip + '<strong></span>' +
          '<legend style="border-bottom: 2px solid rgba(0,0,1,.86); margin-bottom: 10px;"></legend>' +
          '</div>';
      }
    }
    return tooltipText;
  }

  var get_speed = function(avg, dashboard) {
    if (avg === 0) {
      return 0;
    };
    var speed = dashboard.max_time_per_item - avg;
    if (speed < 0) {
      return (100 + speed).toFixed(2);
    } else {
      return 100;
    }
  };

  var get_stat_stream = function(){
    url = $rootScope.api_url + "/dashboard/get_stat_stream_manually.json"
    $http.get(url).success(function(){
      notification.notify("Your request has been queued.", 1);
    });
  };

  var generate_missing_stats = function(){
    url = $rootScope.api_url + "/dashboard/process_missing_data"
    $http.get(url).success(function(){
      notification.notify("Your request has been queued.", 1);
    });
  };

  var stat_generate = function(dashboard){
    $http.get($rootScope.api_url +'/exportsettings/get_export_settings').success(function(response){
      email = response.data.settings.stat_export_email;
      url = $rootScope.api_url + "/dashboard/1/generate_stats.json"
      data = {}
      data["duration"] = dashboard["main_summary"]["duration"]
      data["email"] = email
      if (email == ''){
        notification.notify("Please specify an email address for system notifications in the" + "<a href = '#/settings/system/stats_export'> user stat export.</a>", 0);
      } else {
        notification.notify("Your request has been queued.", 1);
        $http.post(url, data);
      }
    }); 
  }



  var trigger_click = function(avg, username, data_point, charts, total, total_sum){
    clicked = 0
    dashboard_data.avg = avg;
    dashboard_data.data_point = data_point;
    dashboard_data.username = username;
    $(".nv-linesWrap path").click(function(){ 
      if (clicked == 0 && charts.type === 'packing_speed_stats') {
        dashboard_data.avg = (total_sum - dashboard_data.data_point) / total
        clicked = clicked + 1
        var modal_d = $modal.open({
          template:
           '<div id="modal-dialog-box" class="dialog-modal" style="margin-top: 20%;"> \
                <style>.modal.fade.ng-isolate-scope.in {z-index: 99999!important;}</style> \
                <div class="modal-header"> \
                  <h3 class="modal-title">Please Confirm</h3> \
                </div> \
                <div class="modal-body" style="min-height: 140px; text-align: center; padding: 25px;">Extreme data points resulting from unusual events can be averaged to normalize the stats data. <br/>Would you like to average this data point?</div> \
                <div class="modal-footer"> \
                  <button class="btn btn-primary ok_button" ng-click="ok()">Ok</button> \
                  <button class="btn btn-warning cancel_button" ng-click="cancel()">Cancel</button> \
                </div> \
            </div>',
            controller: 'DashboardCtrl'
        });
        dashboard_data.modal_d = modal_d;
      }
    });
  }

  var get_daily_packed_percentage = function(){
    return(
      $http.get($rootScope.api_url + '/dashboard/daily_packed_percentage').error(function (response) {
        notification.notify("Failed to load Scanning Percentage", 0);
      })
    )
  }

  var update_export_settings = function(data){
    $http.put($rootScope.api_url + '/exportsettings/update_export_settings.json',data)
  }  

  var download_daily_packed_csv = function(daily_packed_percentage_list){
    data = [];
    for (var i = 0; i < daily_packed_percentage_list.length; i++) {
      if (daily_packed_percentage_list[i].checked) {
        var date = new Date(daily_packed_percentage_list[i].date);
        var newdate  =  date.getFullYear() + '/' + ('0' + (date.getMonth()+1)).slice(-2) + '/' + ('0' + date.getDate()).slice(-2) ;
        data.push(newdate);
        daily_packed_percentage_list[i].checked = false;
      }
    }
    if (data.length > 0){
      $http.post($rootScope.api_url+'/dashboard/download_daily_packed_csv?',data);
    }
  }

  return {
    model: {
      get: get_default,
      get_max: get_max_time,
      update_max: update_max_time
    },
    stats: {
      exceptions: exceptions,
      dashboard_stat: get_dashboard_data,
      points_data: get_datapoints_data,
      tooltip: get_tool_tip,
      speed: get_speed,
      stat_stream: get_stat_stream,
      stat_generate: stat_generate,
      daily_packed_percentage: get_daily_packed_percentage,
      download_daily_packed_csv: download_daily_packed_csv,
      update_export_settings: update_export_settings,
      process_missing_data: generate_missing_stats
    }
  };
}]);
