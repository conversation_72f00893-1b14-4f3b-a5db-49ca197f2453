groovepacks_services.factory('subscription', ['$http', 'notification', '$filter', '$rootScope', 
  function ($http, notification, $filter, $rootScope) {

    var validate_shop_name = function(shop_name){
      url = $rootScope.api_url + '/subscriptions/valid_tenant_name.json?tenant_name=' + shop_name;
      return $http.get(url);
    };

    var validate_subsc_email = function(email){
      url = $rootScope.api_url + '/subscriptions/valid_email.json?email=' + email;
      return $http.get(url);
    };

    var get_one_time_payment = function(attrs){
      shop_type = attrs["shop_type"]
      shop_name = attrs["shop_name"]
      url = $rootScope.api_url + '/subscriptions/get_one_time_payment_fee.json?shop_type=' + shop_type + '&shop_name=' + shop_name;    
      return $http.get(url);
    };

    var apply = function(coupon_id){
      url = $rootScope.api_url + '/subscriptions/validate_coupon_id?coupon_id=' + coupon_id;
      return $http.get(url);
    }

    var plan_info = function(plan_id, channel){
      url = $rootScope.api_url + '/subscriptions/plan_info.json?plan_id=' + plan_id;
      return $http.get(url);
    }

    var request_tenant_creation = function(token, data){
      url = $rootScope.api_url + '/subscriptions/confirm_payment.json';  
      return $http.post(url, data);
    }

    var shopify_auth = function(shop_name, name, email){
      url = $rootScope.api_url + '/shopify/get_auth.json';  
      params = {shop_name: shop_name, name: name, email: email} 
      return $http.post(url, params);
    }

    var store_shopify_subscription_data = function(data){
      url = $rootScope.api_url + '/shopify/store_subscription_data.json'; 
      return $http.post(url, data);
    }

    var get_store_data = function(){
      url = $rootScope.api_url + '/shopify/get_store_data.json'; 
      return $http.get(url);
    }

    var create_payment_intent = function(amount) {
      url = $rootScope.api_url + '/subscriptions/create_payment_intent.json';
      return $http.post(url, { amount: amount });
    }

    return {
      validate_shop_name: validate_shop_name,
      validate_subsc_email: validate_subsc_email,
      get_one_time_payment: get_one_time_payment,
      apply: apply,
      plan_info: plan_info,
      request_tenant_creation: request_tenant_creation,
      shopify_auth: shopify_auth,
      store_shopify_subscription_data: store_shopify_subscription_data,
      get_store_data: get_store_data,
      create_payment_intent: create_payment_intent
    }
  }
])
