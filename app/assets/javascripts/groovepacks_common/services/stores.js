groovepacks_services.factory('stores', ['$http', 'notification', '$filter', '$rootScope', function ($http, notification, $filter, $rootScope) {

  /**
   * @typedef {object} StoresHash
   * @property {array} list - list of stores.
   * @property {object} single - hash of a single store data.
   * @property {object} ebay - hash of ebay settings.
   * @property {object} import - hash of import settings.
   * @property {object} types - hash of supported store types.
   * @property {number} current - index of currently open store in list.
   * @property {object} setup - settings of stores list setup.
   */

  var success_messages = {
    update_status: "Status updated Successfully",
    delete: "Deleted Successfully",
    duplicate: "Duplicated Successfully"
  };

  var get_default = function () {
    return {
      list: [],
      single: {},
      ebay: {},
      csv: {
        mapping: {},
        maps: {
          order: [],
          product: []
        }
      },
      import: {
        order: {},
        product: {}
      },
      update: {
        products: {}
      },
      types: {},
      current: 0,
      setup: {
        sort: "",
        order: "DESC",
        search: '',
        select_all: false,
        //used for updating only
        status: '',
        storeArray: []
      }
    };
  };

   var select_notification = function () {
    notification.notify("Please select atleast one Store", 0);
  }

  //Setup related function
  var update_setup = function (setup, type, value) {
    if (type == 'sort') {
      if (setup[type] == value) {
        if (setup.order == "DESC") {
          setup.order = "ASC";
        } else {
          setup.order = "DESC";
        }
      } else {
        setup.order = "DESC";
      }
    }
    setup[type] = value;
    return setup;
  };

  //list related functions
  var get_list = function (object) {
    var result = [];
    return $http.get($rootScope.api_url + '/stores.json').success(
      function (data) {
        result = $filter('filter')(data, object.setup.search);
        result = $filter('orderBy')(result, object.setup.sort, (object.setup.order == 'DESC'));
        object.list = result;
      }
    ).error(notification.server_error);
  };

  var update_list = function (action, stores) {
    if (["update_status", "delete", "duplicate"].indexOf(action) != -1) {
      stores.setup.storeArray = [];
      for (var i = 0; i < stores.list.length; i++) {
        if (stores.list[i].checked == true) {
          stores.setup.storeArray.push({id: stores.list[i].id, status: (stores.setup.status == 'active')});
        }
      }
      var url = '';
      if (action == "delete") {
        url = $rootScope.api_url + '/stores/delete_store.json';
      } else if (action == "duplicate") {
        url = $rootScope.api_url + '/stores/duplicate_store.json';
      } else if (action == "update_status") {
        url = $rootScope.api_url + '/stores/change_store_status.json';
      }

      return $http.post(url, stores.setup.storeArray).success(function (data) {
        if (data.status) {
          stores.setup.select_all = false;
          notification.notify(success_messages[action], 1);
        } else {
          notification.notify(data.messages, 0);
        }
      }).error(notification.server_error);
    }
  };

  var update_list_node = function (obj) {
    return $http.post($rootScope.api_url + '/stores/update_store_list.json', obj).success(function (data) {
      if (data.status) {
        notification.notify("Successfully Updated", 1);
      } else {
        notification.notify(data.error_msg, 0);
      }
    }).error(notification.server_error);
  };

  var amazon_products_import = function (obj) {
    return $http({
                  method: 'POST',
                  headers: {'Content-Type': undefined},
                  url: $rootScope.api_url + '/amazons/products_import.json',
                  transformRequest: function (obj) {
                    var request = new FormData();
                    for (var key in obj) {
                      if (obj.hasOwnProperty(key)) {
                        request.append(key, obj[key]);
                      }
                    }
                    return request;
                  },
                  data: obj
                }).success(function (data) {
                  if (data.status) {
                    notification.notify("Your request has been queued successfully", 1);
                  } else {
                    notification.notify(data.messages, 0);
                  }
                }).error(notification.server_error);
  };



  //single store related functions
  var get_single = function (id, stores) {
    return $http.get($rootScope.api_url + '/stores/'+id+'.json').success(function (data) {
      // stores.single = {};
      stores.import.product.status = "";
      stores.import.order.status = "";
      stores.import.product.status_show = false;
      stores.import.order.status_show = false;
      if (data.status) {
        stores.single.is_fba = data.is_fba;
        stores.single.order_cup_direct_shipping_enabled = data.order_cup_direct_shipping;
        stores.single.custom_product_fields = data.custom_product_fields;
        stores.single.product_ftp_import = data.product_ftp_import;
        // stores.single = data.store;
        for (var key in data.store) {
          stores.single[key] = data.store[key];
        }
        if (data.mapping) {
          stores.csv.mapping = data.mapping;
        }
        if (data.credentials.status == true) {
          stores.single.allow_bc_inv_push = data.access_restrictions.allow_bc_inv_push;
          stores.single.allow_mg_rest_inv_push = data.access_restrictions.allow_mg_rest_inv_push;
          stores.single.allow_shopify_inv_push = data.access_restrictions.allow_shopify_inv_push;
          stores.single.allow_shopline_inv_push = data.access_restrictions.allow_shopline_inv_push;
          stores.single.allow_teapplix_inv_push = data.access_restrictions.allow_teapplix_inv_push;
          stores.single.allow_magento_soap_tracking_no_push = data.access_restrictions.allow_magento_soap_tracking_no_push;
          stores.general_settings = data.general_settings;
          stores.current_tenant = data.current_tenant;
          stores.host_url = data.host_url;
          stores.single.show_originating_store_id = data.show_originating_store_id;
          stores.single.packing_cam = data.packing_cam;
          if (data.store.store_type == 'Magento') {
            stores.single.host = data.credentials.magento_credentials.host;
            stores.single.username = data.credentials.magento_credentials.username;
            stores.single.password = data.credentials.magento_credentials.password;
            stores.single.api_key = data.credentials.magento_credentials.api_key;

            stores.single.shall_import_processing = data.credentials.magento_credentials.shall_import_processing;
            stores.single.shall_import_pending = data.credentials.magento_credentials.shall_import_pending;
            stores.single.shall_import_closed = data.credentials.magento_credentials.shall_import_closed;
            stores.single.shall_import_complete = data.credentials.magento_credentials.shall_import_complete;
            stores.single.shall_import_fraud = data.credentials.magento_credentials.shall_import_fraud;
            stores.single.enable_status_update = data.credentials.magento_credentials.enable_status_update;
            stores.single.status_to_update = data.credentials.magento_credentials.status_to_update;
            stores.single.push_tracking_number = data.credentials.magento_credentials.push_tracking_number;

            stores.single.producthost = data.credentials.magento_credentials.producthost;
            stores.single.productusername = data.credentials.magento_credentials.productusername;
            stores.single.productpassword = data.credentials.magento_credentials.productpassword;
            stores.single.productapi_key = data.credentials.magento_credentials.productapi_key;
            stores.single.import_products = data.credentials.magento_credentials.import_products;
            stores.single.import_images = data.credentials.magento_credentials.import_images;
            stores.single.updated_patch = data.credentials.magento_credentials.updated_patch;
          } else if (data.store.store_type == 'Magento API 2') {
            stores.single.store_version = data.credentials.magento_rest_credential.store_version;
            stores.single.store_token = data.credentials.magento_rest_credential.store_token;
            stores.single.host = data.credentials.magento_rest_credential.host;
            stores.single.store_admin_url = data.credentials.magento_rest_credential.store_admin_url;
            stores.single.api_key = data.credentials.magento_rest_credential.api_key;
            stores.single.api_secret = data.credentials.magento_rest_credential.api_secret;
            stores.single.access_token = data.credentials.magento_rest_credential.access_token;
            stores.single.oauth_token_secret = data.credentials.magento_rest_credential.oauth_token_secret;
            stores.single.import_images = data.credentials.magento_rest_credential.import_images;
            stores.single.import_categories = data.credentials.magento_rest_credential.import_categories;
            stores.single.gen_barcode_from_sku = data.credentials.magento_rest_credential.gen_barcode_from_sku;
          } else if (data.store.store_type == 'ShippingEasy') {
            stores.single.popup_shipping_label = data.credentials.shipping_easy_credentials.popup_shipping_label;
            stores.single.api_key = data.credentials.shipping_easy_credentials.api_key;
            stores.single.api_secret = data.credentials.shipping_easy_credentials.api_secret;
            stores.single.store_api_key = data.credentials.shipping_easy_credentials.store_api_key;
            stores.single.import_ready_for_shipment = data.credentials.shipping_easy_credentials.import_ready_for_shipment;
            stores.single.ready_to_ship = data.credentials.shipping_easy_credentials.ready_to_ship;
            stores.single.import_shipped = data.credentials.shipping_easy_credentials.import_shipped;
            stores.single.gen_barcode_from_sku = data.credentials.shipping_easy_credentials.gen_barcode_from_sku;
            stores.single.import_upc =  data.credentials.shipping_easy_credentials.import_upc;
            stores.single.large_popup = data.credentials.shipping_easy_credentials.large_popup;
            stores.single.multiple_lines_per_sku_accepted = data.credentials.shipping_easy_credentials.multiple_lines_per_sku_accepted;
            stores.single.allow_duplicate_id =  data.credentials.shipping_easy_credentials.allow_duplicate_id;
            stores.single.use_alternate_id_as_order_num = data.credentials.shipping_easy_credentials.use_alternate_id_as_order_num;
            stores.single.import_shipped_having_tracking = data.credentials.shipping_easy_credentials.import_shipped_having_tracking;
            stores.single.remove_cancelled_orders = data.credentials.shipping_easy_credentials.remove_cancelled_orders;
            stores.single.origin_stores = data.origin_stores;
            stores.single.display_origin_store_name = data.store.display_origin_store_name;
          } else if (data.store.store_type == 'Ebay') {
            stores.single.ebay_auth_token = data.credentials.ebay_credentials.auth_token;
            stores.single.productebay_auth_token = data.credentials.ebay_credentials.productauth_token;
            stores.single.import_products = data.credentials.ebay_credentials.import_products;
            stores.single.import_images = data.credentials.ebay_credentials.import_images;
            stores.single.shipped_status = data.credentials.ebay_credentials.shipped_status;
            stores.single.unshipped_status = data.credentials.ebay_credentials.unshipped_status;
            if (stores.single.ebay_auth_token != '' && stores.single.ebay_auth_token != null) {
              stores.ebay.show_url = false;
            } else {
              stores.ebay.show_url = true;
              ebay_sign_in_url(stores);
            }

          } else if (data.store.store_type == 'Amazon') {
            stores.single.marketplace_id = data.credentials.amazon_credentials.marketplace_id;
            stores.single.merchant_id = data.credentials.amazon_credentials.merchant_id;
            stores.single.mws_auth_token = data.credentials.amazon_credentials.mws_auth_token;

            stores.single.productmarketplace_id = data.credentials.amazon_credentials.productmarketplace_id;
            stores.single.productmerchant_id = data.credentials.amazon_credentials.productmerchant_id;
            stores.single.import_products = data.credentials.amazon_credentials.import_products;
            stores.single.import_images = data.credentials.amazon_credentials.import_images;
            stores.single.show_shipping_weight_only = data.credentials.amazon_credentials.show_shipping_weight_only;
            stores.single.productreport_id = data.credentials.amazon_credentials.productreport_id;
            stores.single.productgenerated_report_id = data.credentials.amazon_credentials.productgenerated_report_id;
            stores.single.shipped_status = data.credentials.amazon_credentials.shipped_status;
            stores.single.unshipped_status = data.credentials.amazon_credentials.unshipped_status;
            stores.single.mfn_fulfillment_channel = data.credentials.amazon_credentials.mfn_fulfillment_channel;
            stores.single.afn_fulfillment_channel = data.credentials.amazon_credentials.afn_fulfillment_channel;
          } else if (data.store.store_type == 'Shipstation') {
            stores.single.username = data.credentials.shipstation_credentials.username;
            stores.single.password = data.credentials.shipstation_credentials.password;
          } else if (data.store.store_type == 'Shipstation API 2') {
            stores.single.use_chrome_extention = data.credentials.shipstation_rest_credentials.use_chrome_extention;
            stores.single.use_api_create_label = data.credentials.shipstation_rest_credentials.use_api_create_label;
            stores.single.ss_api_create_label = data.ss_api_create_label;
            stores.single.switch_back_button = data.credentials.shipstation_rest_credentials.switch_back_button;
            stores.single.return_to_order = data.credentials.shipstation_rest_credentials.return_to_order;
            stores.single.add_gpscanned_tag = data.credentials.shipstation_rest_credentials.add_gpscanned_tag;
            stores.single.auto_click_create_label = data.credentials.shipstation_rest_credentials.auto_click_create_label;
            stores.single.api_key = data.credentials.shipstation_rest_credentials.api_key;
            stores.single.api_secret = data.credentials.shipstation_rest_credentials.api_secret;
            stores.single.shall_import_awaiting_shipment = data.credentials.shipstation_rest_credentials.shall_import_awaiting_shipment;
            stores.single.shall_import_shipped = data.credentials.shipstation_rest_credentials.shall_import_shipped;
            stores.single.shall_import_pending_fulfillment = data.credentials.shipstation_rest_credentials.shall_import_pending_fulfillment;
            stores.single.shall_import_customer_notes =
              data.credentials.shipstation_rest_credentials.shall_import_customer_notes;
            stores.single.shall_import_internal_notes =
              data.credentials.shipstation_rest_credentials.shall_import_internal_notes;
            stores.single.hex_barcode = data.credentials.shipstation_rest_credentials.hex_barcode;
            stores.single.regular_import_range =
              data.credentials.shipstation_rest_credentials.regular_import_range;
            stores.single.import_days = ["0", "1", "2", "3", "4", "5", "6"];
            stores.single.warehouse_location_update = data.credentials.shipstation_rest_credentials.warehouse_location_update;
            stores.single.gp_ready_tag_name = data.credentials.shipstation_rest_credentials.gp_ready_tag_name;
            stores.single.gp_imported_tag_name = data.credentials.shipstation_rest_credentials.gp_imported_tag_name;
            stores.single.gen_barcode_from_sku = data.credentials.shipstation_rest_credentials.gen_barcode_from_sku;
            stores.single.import_upc =  data.credentials.shipstation_rest_credentials.import_upc;
            stores.single.allow_duplicate_order = data.credentials.shipstation_rest_credentials.allow_duplicate_order;
            stores.single.import_discounts_option = data.credentials.shipstation_rest_credentials.import_discounts_option;
            stores.single.set_coupons_to_intangible = data.credentials.shipstation_rest_credentials.set_coupons_to_intangible;
            stores.single.tag_import_option = data.credentials.shipstation_rest_credentials.tag_import_option;
            stores.single.order_import_range_days = data.credentials.shipstation_rest_credentials.order_import_range_days;
            stores.single.import_tracking_info = data.credentials.shipstation_rest_credentials.import_tracking_info;
            stores.single.import_shipped_having_tracking = data.credentials.shipstation_rest_credentials.import_shipped_having_tracking;
            stores.single.remove_cancelled_orders = data.credentials.shipstation_rest_credentials.remove_cancelled_orders;
            stores.single.postcode = data.credentials.shipstation_rest_credentials.postcode;
            stores.single.skip_ss_label_confirmation = data.credentials.shipstation_rest_credentials.skip_ss_label_confirmation;
            stores.single.enabled_status = data.enabled_status;
            stores.single.quick_import_last_modified_v2 = data.credentials.shipstation_rest_credentials.quick_import_last_modified_v2;
            stores.single.origin_stores = data.origin_stores;
            stores.single.display_origin_store_name = data.store.display_origin_store_name;
            stores.single.full_name = data.credentials.shipstation_rest_credentials.full_name;
            stores.single.street1 = data.credentials.shipstation_rest_credentials.street1;
            stores.single.street2 = data.credentials.shipstation_rest_credentials.street2;
            stores.single.city = data.credentials.shipstation_rest_credentials.city;
            stores.single.state = data.credentials.shipstation_rest_credentials.state;
            stores.single.country = data.credentials.shipstation_rest_credentials.country;
            stores.single.webhook_secret = data.credentials.shipstation_rest_credentials.webhook_secret;
            stores.single.webhook_endpoint = data.credentials.shipstation_rest_credentials.webhook_endpoint;
            stores.single.use_shopify_as_product_source_switch = data.credentials.shipstation_rest_credentials.use_shopify_as_product_source_switch;
            stores.single.product_source_shopify_store_id = data.credentials.shipstation_rest_credentials.product_source_shopify_store_id;
            stores.single.shopify_active_stores = data.credentials.shopify_active_stores;
            //shall_import_customer_notes
          }else if (data.store.store_type == 'Veeqo') {
            stores.single.api_key = data.credentials.veeqo_credentials.api_key;
            stores.single.shipped_status = data.credentials.veeqo_credentials.shipped_status;
            stores.single.awaiting_fulfillment_status = data.credentials.veeqo_credentials.awaiting_fulfillment_status;
            stores.single.awaiting_amazon_fulfillment_status = data.credentials.veeqo_credentials.awaiting_amazon_fulfillment_status;
            stores.single.shall_import_customer_notes = data.credentials.veeqo_credentials.shall_import_customer_notes;
            stores.single.shall_import_internal_notes = data.credentials.veeqo_credentials.shall_import_internal_notes;
            stores.single.gen_barcode_from_sku = data.credentials.veeqo_credentials.gen_barcode_from_sku;
            stores.single.use_shopify_as_product_source_switch = data.credentials.veeqo_credentials.use_shopify_as_product_source_switch;
            stores.single.allow_duplicate_order = data.credentials.veeqo_credentials.allow_duplicate_order;
            stores.single.order_import_range_days = data.credentials.veeqo_credentials.order_import_range_days;
            stores.single.import_shipped_having_tracking = data.credentials.veeqo_credentials.import_shipped_having_tracking;
            stores.single.product_source_shopify_store_id = data.credentials.veeqo_credentials.product_source_shopify_store_id;
            stores.single.use_veeqo_order_id = data.credentials.veeqo_credentials.use_veeqo_order_id;
            stores.single.shopify_active_stores = data.credentials.shopify_active_stores;
          } else if (data.store.store_type == 'Shipworks') {
            stores.single.auth_token = data.credentials.shipworks_credentials.auth_token;
            stores.single.import_store_order_number = data.credentials.shipworks_credentials.import_store_order_number
            stores.single.shall_import_in_process = data.credentials.shipworks_credentials.shall_import_in_process;
            stores.single.shall_import_new_order = data.credentials.shipworks_credentials.shall_import_new_order;
            stores.single.shall_import_not_shipped = data.credentials.shipworks_credentials.shall_import_not_shipped;
            stores.single.shall_import_shipped = data.credentials.shipworks_credentials.shall_import_shipped;
            stores.single.shall_import_no_status = data.credentials.shipworks_credentials.shall_import_no_status;
            stores.single.shall_import_ignore_local = data.credentials.shipworks_credentials.shall_import_ignore_local;
            stores.single.gen_barcode_from_sku = data.credentials.shipworks_credentials.gen_barcode_from_sku;
            stores.single.request_url = data.credentials.shipworks_hook_url;
          } else if (data.store.store_type == 'Shopify') {
            stores.single.shop_name = data.credentials.shopify_credentials.shop_name;
            stores.single.access_token = data.credentials.shopify_credentials.access_token;
            stores.single.shopify_permission_url = data.credentials.shopify_permission_url;
            stores.single.shopify_status = data.credentials.shopify_credentials.shopify_status;
            stores.single.shipped_status = data.credentials.shopify_credentials.shipped_status;
            stores.single.push_inv_location_id = data.credentials.shopify_credentials.push_inv_location_id;
            stores.single.pull_inv_location_id = data.credentials.shopify_credentials.pull_inv_location_id;
            stores.single.pull_combined_qoh = data.credentials.shopify_credentials.pull_combined_qoh;
            stores.single.shopify_locations = data.credentials.shopify_locations;
            stores.single.shopify_access_scopes = data.credentials.shopify_access_scopes;
            stores.single.unshipped_status = data.credentials.shopify_credentials.unshipped_status;
            stores.single.import_fulfilled_having_tracking = data.credentials.shopify_credentials.import_fulfilled_having_tracking;
            stores.single.partial_status = data.credentials.shopify_credentials.partial_status;
            stores.single.modified_barcode_handling = data.credentials.shopify_credentials.modified_barcode_handling;
            stores.single.generating_barcodes = data.credentials.shopify_credentials.generating_barcodes;
            stores.single.import_inventory_qoh = data.credentials.shopify_credentials.import_inventory_qoh;
            stores.single.permit_shared_barcodes = data.credentials.shopify_credentials.permit_shared_barcodes;
            stores.single.import_updated_sku = data.credentials.shopify_credentials.import_updated_sku;
            stores.single.updated_sku_handling = data.credentials.shopify_credentials.updated_sku_handling;
            stores.single.fix_all_product_images = data.credentials.shopify_credentials.fix_all_product_images;
            stores.single.add_gp_scanned_tag = data.credentials.shopify_credentials.add_gp_scanned_tag;
            stores.single.on_hold_status = data.credentials.shopify_credentials.on_hold_status;
            stores.single.re_associate_shopify_products = data.credentials.shopify_credentials.re_associate_shopify_products;
            stores.single.import_variant_names = data.credentials.shopify_credentials.import_variant_names;
            stores.single.webhook_order_import = data.credentials.shopify_credentials.webhook_order_import;
            stores.single.order_import_range_days = data.credentials.shopify_credentials.order_import_range_days;
          } else if (data.store.store_type == 'Shopline') {
            stores.single.shop_name = data.credentials.shopline_credentials.shop_name;
            stores.single.access_token = data.credentials.shopline_credentials.access_token;
            stores.single.shopline_permission_url = data.credentials.shopline_permission_url;
            stores.single.shopline_status = data.credentials.shopline_credentials.shopline_status;
            stores.single.shipped_status = data.credentials.shopline_credentials.shipped_status;
            stores.single.push_inv_location_id = data.credentials.shopline_credentials.push_inv_location_id;
            stores.single.pull_inv_location_id = data.credentials.shopline_credentials.pull_inv_location_id;
            stores.single.pull_combined_qoh = data.credentials.shopline_credentials.pull_combined_qoh;
            stores.single.unshipped_status = data.credentials.shopline_credentials.unshipped_status;
            stores.single.import_fulfilled_having_tracking = data.credentials.shopline_credentials.import_fulfilled_having_tracking;
            stores.single.partial_status = data.credentials.shopline_credentials.partial_status;
            stores.single.modified_barcode_handling = data.credentials.shopline_credentials.modified_barcode_handling;
            stores.single.generating_barcodes = data.credentials.shopline_credentials.generating_barcodes;
            stores.single.import_inventory_qoh = data.credentials.shopline_credentials.import_inventory_qoh;
            stores.single.import_updated_sku = data.credentials.shopline_credentials.import_updated_sku;
            stores.single.updated_sku_handling = data.credentials.shopline_credentials.updated_sku_handling;
            stores.single.fix_all_product_images = data.credentials.shopline_credentials.fix_all_product_images;
            stores.single.on_hold_status = data.credentials.shopline_credentials.on_hold_status;
            stores.single.import_variant_names = data.credentials.shopline_credentials.import_variant_names;
          }else if (data.store.store_type == 'BigCommerce') {
            stores.single.shop_name = data.credentials.big_commerce_credentials.shop_name;
            stores.single.access_token = data.credentials.big_commerce_credentials.access_token;
            stores.single.store_hash = data.credentials.big_commerce_credentials.store_hash;
            stores.single.bigcommerce_permission_url = data.credentials.bigcommerce_permission_url;
          }else if (data.store.store_type == 'Teapplix') {
            stores.single.account_name = data.credentials.teapplix_credential.account_name;
            stores.single.username = data.credentials.teapplix_credential.username;
            stores.single.password = data.credentials.teapplix_credential.password;
            stores.single.gen_barcode_from_sku = data.credentials.teapplix_credential.gen_barcode_from_sku;
            stores.single.import_shipped = data.credentials.teapplix_credential.import_shipped;
            stores.single.import_open_orders = data.credentials.teapplix_credential.import_open_orders;
            stores.single.import_shipped_having_tracking = data.credentials.teapplix_credential.import_shipped_having_tracking;
          } else if (data.store.store_type == 'CSV') {
            stores.single.host = data.credentials.ftp_credentials.host;
            stores.single.port = data.credentials.ftp_credentials.port;
            stores.single.connection_method = data.credentials.ftp_credentials.connection_method;
            stores.single.connection_established = data.credentials.ftp_credentials.connection_established;
            stores.single.username = data.credentials.ftp_credentials.username;
            stores.single.password = data.credentials.ftp_credentials.password;
            stores.single.fba_import = data.store.fba_import;
            stores.single.use_csv_beta = data.store.csv_beta;
            stores.single.use_ftp_import = data.credentials.ftp_credentials.use_ftp_import;
            stores.single.order_cup_direct_shipping = data.store.order_cup_direct_shipping;
            stores.single.use_product_ftp_import = data.credentials.ftp_credentials.use_product_ftp_import;
            stores.single.product_ftp_host = data.credentials.ftp_credentials.product_ftp_host;
            stores.single.product_ftp_connection_method = data.credentials.ftp_credentials.product_ftp_connection_method;
            stores.single.product_ftp_username = data.credentials.ftp_credentials.product_ftp_username;
            stores.single.product_ftp_password = data.credentials.ftp_credentials.product_ftp_password;
            stores.single.product_ftp_connection_established = data.credentials.ftp_credentials.product_ftp_connection_established;
          } else if (data.store.store_type == 'Shippo') {
            stores.single.api_key = data.credentials.shippo_credentials.api_key;
            stores.single.api_version = data.credentials.shippo_credentials.api_version;
            stores.single.generate_barcode_option = data.credentials.shippo_credentials.generate_barcode_option;
            stores.single.import_paid = data.credentials.shippo_credentials.import_paid;
            stores.single.import_awaitpay = data.credentials.shippo_credentials.import_awaitpay;
            stores.single.import_partially_fulfilled = data.credentials.shippo_credentials.import_partially_fulfilled;
            stores.single.import_shipped = data.credentials.shippo_credentials.import_shipped;
            stores.single.import_any = data.credentials.shippo_credentials.import_any;
            stores.single.import_shipped_having_tracking = data.credentials.shippo_credentials.import_shipped_having_tracking;
            stores.single.last_imported_at = data.credentials.shippo_credentials.last_imported_at;
          }
        }
      }
    }).error(notification.server_error);
  };
  var get_system = function (stores) {
    return $http.get($rootScope.api_url + '/stores/get_system.json').success(function (data) {
      if (data.status) {
        stores.single = data.store;
      }
    }).error(notification.server_error);
  };

  /**
   * Validate if we have all the data to send a store creation request.
   * @param {StoresHash} stores - {@link StoresHash}
   * @returns {boolean} true if the code can proceed to create a store.
   */
  var validate_create_single = function (stores) {
    //Return true if the checks match, if it reaches the end it returns false by default.
    if (stores.single.name && stores.single.store_type) {
      switch (stores.single.store_type) {
        case 'Magento':
          return ((stores.single.host && stores.single.username && stores.single.api_key) || true);
          break;
        case 'Shipstation':
          return (stores.single.username && stores.single.password);
          break;
        // case 'Shipstation API 2':
        //   return (stores.single.api_key && stores.single.api_secret);
        //   break;
        case 'Amazon':
          return ((stores.single.merchant_id && stores.single.marketplace_id) || true);
          break;
        case 'Shopify':
          return (stores.single.shop_name || true);
          break;
        //for any other store types (ebay and csv) just return true
        case 'Shipworks':
        default:
          return true;
      }
    }
    return false;
  };

  var can_create_single = function () {
    return $http.get($rootScope.api_url + '/stores/let_store_be_created.json')
  };

  var create_update_single = function (stores, auto) {
    if (typeof auto !== "boolean") {
      auto = true;
    }
    return $http({
      method: 'POST',
      headers: {'Content-Type': undefined},
      url: $rootScope.api_url + '/stores/create_update_store.json',
      transformRequest: function (data) {
        var request = new FormData();
        for (var key in data) {
          if (data.hasOwnProperty(key)) {
            request.append(key, data[key]);
          }
        }
        return request;
      },
      data: stores.single
    }).success(function (data) {
      if (data.status && data.store_id) {
        if (!auto) {
          if (data.csv_import) {
            notification.notify("Successfully Updated", 1);
          }
          ;
        }
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var connect_ftp_server = function(stores) {
    return $http.get($rootScope.api_url + '/stores/'+stores.single.id+'/connect_and_retrieve.json').success(function (data) {
      if (data.connection.status) {
        notification.notify(data.connection.success_messages, 1);
        stores.single.file_path = data.connection.downloaded_file;
      } else {
        notification.notify(data.connection.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var connect_product_ftp_server = function(stores) {
    return $http.get($rootScope.api_url + '/stores/'+stores.single.id+'/connect_and_retrieve.json?type=product').success(function (data) {
      if (data.connection.status) {
        notification.notify(data.connection.success_messages, 1);
        stores.single.file_path = data.connection.downloaded_file;
      } else {
        notification.notify(data.connection.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var check_imported = function(stores) {
    return $http.get($rootScope.api_url + '/stores/'+stores.single.id+'/check_imported_folder.json').success(function (data) {
      if (data.connection.status) {
        notification.notify(data.connection.success_messages, 1);
        stores.single.file_path = data.connection.downloaded_file;
      } else {
        notification.notify(data.connection.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var check_product_ftp_imported = function(stores) {
    return $http.get($rootScope.api_url + '/stores/'+stores.single.id+'/check_imported_folder.json?type=product').success(function (data) {
      if (data.connection.status) {
        notification.notify(data.connection.success_messages, 1);
        stores.single.file_path = data.connection.downloaded_file;
      } else {
        notification.notify(data.connection.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var create_update_ftp_credentials = function(stores) {

    return $http({
      method: 'POST',
      headers: {'Content-Type': undefined},
      url: $rootScope.api_url + '/stores/'+stores.single.id+'/create_update_ftp_credentials.json',
      transformRequest: function (data) {
        var request = new FormData();
        for (var key in data) {
          if (data.hasOwnProperty(key)) {
            request.append(key, data[key]);
          }
        }
        return request;
      },
      data: stores.single
    }).success(function(data) {
      if(data.status) {
        notification.notify("Successfully Updated", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  }

  //ebay related functions
  var ebay_sign_in_url = function (stores) {
    return $http.get($rootScope.api_url + '/stores/get_ebay_signin_url.json').success(function (data) {
      if (data.ebay_signin_url_status) {
        stores.ebay.signin_url = data.ebay_signin_url;
        stores.ebay.signin_url_status = data.ebay_signin_url_status;
        stores.ebay.sessionid = data.ebay_sessionid;
        stores.ebay.current_tenant = data.current_tenant;
      }
    }).error(function (data) {
      stores.ebay.signin_url_status = false;
      notification.server_error(data);
    });
  };

  var ebay_token_fetch = function (stores) {
    return $http.get($rootScope.api_url + '/stores/ebay_user_fetch_token.json').success(function (data) {
      if (data.status) {
        stores.ebay.show_url = false;
      }
    }).error(notification.server_error);
  };

  var ebay_token_delete = function (stores) {
    return $http.post($rootScope.api_url + '/stores/'+stores.single.id+'/delete_ebay_token.json').success(function (data) {
      if (data.status) {
        ebay_sign_in_url(stores);
      }
    }).error(notification.server_error);
  };

  var ebay_token_update = function (stores, id) {
    return $http.post($rootScope.api_url + '/stores/'+id+'/update_ebay_user_token.json').success(function (data) {
      if (data.status) {
        stores.ebay.show_url = false;
      }
    }).error(notification.server_error);
  };

  //Import related functions
  var import_products = function (stores, report_id) {
    //return $http.get('/products/import_products/' + stores.single.id + '.json?reportid=' + report_id).success(function (data) {
      var add_product_import_range_days = $('#product_import_range_days')[0] == undefined ? '' : '&product_import_range_days=' + $('#product_import_range_days')[0].value;
        return $http.get($rootScope.api_url + '/products/import_products.json?reportid=' + report_id + '&id=' + stores.single.id + add_product_import_range_days + '&product_import_type=' + $('input[name=product_import_type]:checked').val()).success(function (data) {
      if (data.status) {
        stores.import.product.status = "Successfully imported " + data.success_imported + " of " + data.total_imported +
          " products. " + data.previous_imported + " products were previously imported";
        if(stores.general_settings.email_address_for_packer_notes!=undefined && stores.general_settings.email_address_for_packer_notes!=0) {
          notification.notify("Your request has been queued. you will receive an email to " + stores.general_settings.email_address_for_packer_notes + " when products import is complete", 1);
        } else {
          notification.notify("Your request has been queued.", 1);
        }
      } else {
        stores.import.product.status = "";
        for (var j = 0; j < data.messages.length; j++) {
          stores.import.product.status += data.messages[j] + " ";
        }
      }
    }).error(function (data) {
      stores.import.product.status = "Import failed. Please check your credentials";
    });
  };

  var import_orders = function (stores) {
    return $http.get($rootScope.api_url + '/orders/import_orders/' + stores.single.id + '.json').success(function (data) {
      if (data.status) {
        stores.import.order.status = "Successfully imported " + data.success_imported + " of " + data.total_imported +
          " orders. " + data.previous_imported + " orders were previously imported";
      } else {
        stores.import.order.status = "";
        for (var j = 0; j < data.messages.length; j++) {
          stores.import.order.status += data.messages[j] + " ";
        }
      }
    }).error(function (data) {
      stores.import.order.status = "Import failed. Please check your credentials.";
    });
  };

  var import_images = function (stores, report_id) {
    return $http.get($rootScope.api_url + '/products/import_images/' + stores.single.id + '.json').success(function (data) {
      if (data.status) {
        stores.import.image.status = "Successfully imported " + data.success_imported + " of " + data.total_imported +
          " images. " + data.previous_imported + " images were previously imported";
      } else {
        stores.import.image.status = "";
        for (var j = 0; j < data.messages.length; j++) {
          stores.import.image.status += data.messages[j] + " ";
        }
      }
    }).error(function (data) {
      stores.import.image.status = "Import failed. Please check your credentials.";
    });
  };

  var import_amazon_request = function (stores) {
    return $http.get($rootScope.api_url + '/products/requestamazonreport/' + stores.single.id + '.json').success(function (data) {
      if (data.status) {
        stores.import.product.status = "Report for product import has been submitted. " +
          "Please check status in few minutes to import the products";
        stores.single.productgenerated_report_id = '';
        stores.single.productreport_id = data.requestedreport_id;
      } else {
        stores.import.product.status = "Report request failed. Please check your credentials."
      }
    }).error(function (data) {
      stores.import.product.status = "Report request failed. Please check your credentials.";
    });
  };

  var import_amazon_check = function (stores) {
    return $http.get($rootScope.api_url + '/products/checkamazonreportstatus/' + stores.single.id + '.json').success(function (data) {
      if (data.status) {
        stores.import.product.status = data.report_status;
        stores.single.productgenerated_report_id = data.generated_report_id;
      } else {
        stores.import.product.status = "Error checking status."
      }
    }).error(function (data) {
      stores.import.product.status = "Error checking status. Please try again later";
    });
  };

  //csv related functions
  var csv_import_data = function (stores, id) {
    return $http.post($rootScope.api_url + '/stores/'+id+'/csv_import_data.json?&type=' + stores.single.type).
      error(notification.server_error);
  };

  var csv_check_data = function (stores, id, current_map) {
    return $http.post($rootScope.api_url + '/stores/'+id+'/csv_check_data.json?&type=' + stores.single.type + '&store_id=' + id, current_map).success(function (data) {
      if (data.status) {
        notification.notify("All checks passed!", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var csv_do_import = function (csv) {
    $rootScope.csv = csv.current;
    return $http.post($rootScope.api_url + '/stores/'+csv.current.store_id+'/csv_do_import.json', csv.current).success(function (data) {
      if (data.status) {
        notification.notify("CSV import queued successfully.", 1);
        csv.current = {};
        csv.importer = {};
      } else {
        notification.notify(data.messages, 0);
        csv.current.rows = csv.current.rows + data.last_row;
      }
    }).error(notification.server_error);
  };

  var csv_product_import_cancel = function (id) {
    return $http.post($rootScope.api_url + '/stores/'+id+'/csv_product_import_cancel.json').success(function (data) {
      notification.notify(data['error_messages']);
      notification.notify(data['success_messages'], 1);
      notification.notify(data['notice_messages'], 2);
    }).error(notification.server_error);
  };
  var update_csv_map = function (stores, map) {
    return $http.post($rootScope.api_url + '/stores/'+stores.single.id+'/update_csv_map.json', {
      map: map
    }).success(function (data) {
      if (data.status) {
        if (map.kind == 'order') {
          stores.csv.mapping.order_csv_map_id = map.id;
        } else if (map.kind == 'product') {
          stores.csv.mapping.product_csv_map_id = map.id;
        } else {
          stores.csv.mapping.kit_csv_map_id = map.id;
        };

      } else {
        notification.notify(data['messages']);
      };
    }).error(notification.server_error);
  };

  var delete_csv_map = function (stores, kind) {
    return $http.post($rootScope.api_url + '/stores/'+stores.single.id+'/delete_csv_map.json', {
      kind: kind
    }).success(function (data) {
      if (data.status) {
        if (kind == 'order') {
          stores.csv.mapping.order_csv_map_id = null;
        } else if (kind == 'product') {
          stores.csv.mapping.product_csv_map_id = null;
        } else {
          stores.csv.mapping.kit_csv_map_id = null;
        };

      } else {
        notification.notify(data['messages']);
      };
    }).error(notification.server_error);
  };

  var delete_map = function(stores, map){
    $http.post($rootScope.api_url +'/stores/'+stores.single.id+'/delete_map.json', {map: map})
  };

  var get_csv_maps = function (stores) {
    return $http.get($rootScope.api_url + '/stores/csv_map_data.json').success(function (data) {
      stores.csv.maps = data;
    }).error(notification.server_error);
  };

  var update_products = function (store_id) {
    return $http.put($rootScope.api_url + '/stores/' + store_id + '/update_products.json', null).success(
      function (data) {
        if (data.status) {
          notification.notify("CSV imported successfully", 1);
          csv.current = {};
          csv.importer = {};
        } else {
          notification.notify(data.messages, 0);
          csv.current.rows = csv.current.rows + data.last_row;
        }
      }).error(notification.server_error);
  };

  var verify_tags = function (store_id) {
    return $http.get($rootScope.api_url + '/stores/' + store_id + '/verify_tags.json').success(
      function (data) {
      }).error(notification.server_error);
  }

  var verify_awaiting_tags = function (store_id) {
    return $http.get($rootScope.api_url + '/stores/' + store_id + '/verify_awaiting_tags.json').success(
      function (data) {
      }).error(notification.server_error);
  }

  var update_all_locations = function (store_id) {
    return $http.put($rootScope.api_url + '/stores/' + store_id + '/update_all_locations.json', null).success(
      function (data) {
      }).error(notification.server_error);
  }

  var fix_import_dates = function (store_id) {
    return $http.put($rootScope.api_url + '/shipstation_rest_credentials/' + store_id + '/fix_import_dates.json', null).success(function(data) {
      if (data.status) {
        notification.notify(data.messages, 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var update_product_image = function(store_id){
    return $http.put($rootScope.api_url + '/shipstation_rest_credentials/' + store_id + '/update_product_image.json', null).success(function(data) {
      if (data.status) {
        notification.notify(data.messages, 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  }

  var shopfiy_disconnect = function (store_id) {
    return $http.put($rootScope.api_url + '/shopify/' + store_id + '/disconnect.json', null).error(
      notification.server_error
    );
  }

  var big_commerce_disconnect = function (store_id) {
    return $http.put($rootScope.api_url + '/big_commerce/' + store_id + '/disconnect.json', null).error(
      notification.server_error
    );
  }

  var disconnect_magento = function (store_id) {
    return $http.put($rootScope.api_url + '/magento_rest/' + store_id + '/disconnect.json', null).error(
      notification.server_error
    );
  }

  var pull_store_inventory = function (store_id) {
    return $http.get($rootScope.api_url + '/stores/' + store_id + '/pull_store_inventory.json', null).success(
      function (data) {
      }).error(notification.server_error);
  }

  var push_store_inventory = function (store_id) {
    return $http.get($rootScope.api_url + '/stores/' + store_id + '/push_store_inventory.json', null).success(
      function (data) {
      }).error(notification.server_error);
  }

  var get_magento_aurthorize_url = function (store_id) {
    return $http.get($rootScope.api_url + '/magento_rest/' + store_id + '/magento_authorize_url.json', null).success(
      function (data) {
      }).error(notification.server_error);
  }

  var get_magento_access_token = function (store) {
    return $http.get($rootScope.api_url + '/magento_rest/' + store.id + '/get_access_token.json?oauth_varifier='+store.oauth_varifier , null).success(
      function (data) {
      }).error(notification.server_error);
  }

  var check_connection = function (store_type, store_id) {
    var url = "";
    if (store_type=="BigCommerce") {
      url = $rootScope.api_url + '/big_commerce/' + store_id + '/check_connection.json';
    } else if (store_type=="Magento API 2"){
      url = $rootScope.api_url + '/magento_rest/' + store_id + '/check_connection.json';
    }
    return $http.get(url, null).success(
      function (data) {
        return data;
      }
    ).error(notification.server_error);
  }

  //Public facing API
  return {
    model: {
      get: get_default
    },
    setup: {
      update: update_setup
    },
    list: {
      get: get_list,
      update: update_list,
      update_node: update_list_node,
      select_notification: select_notification
    },
    single: {
      get: get_single,
      get_system: get_system,
      can_create: can_create_single,
      validate_create: validate_create_single,
      update: create_update_single,
      update_ftp: create_update_ftp_credentials,
      connect: connect_ftp_server,
      connect_product_ftp: connect_product_ftp_server,
      pull_inventory: pull_store_inventory,
      push_inventory: push_store_inventory,
      amazon_products_import: amazon_products_import,
      check_imported: check_imported,
      check_product_ftp_imported: check_product_ftp_imported
    },
    ebay: {
      sign_in_url: {
        get: ebay_sign_in_url
      },
      user_token: {
        fetch: ebay_token_fetch,
        delete: ebay_token_delete,
        update: ebay_token_update
      }
    },
    import: {
      products: import_products,
      orders: import_orders,
      images: import_images,
      amazon: {
        request: import_amazon_request,
        check: import_amazon_check
      }
    },
    csv: {
      import: csv_import_data,
      do_import: csv_do_import,
      do_check: csv_check_data,
      cancel_product_import: csv_product_import_cancel,
      map: {
        get: get_csv_maps,
        update: update_csv_map,
        delete: delete_csv_map,
        delete_map: delete_map
      }
    },
    update: {
      products: update_products
    },
    shipstation: {
      verify_tags: verify_tags,
      verify_awaiting_tags: verify_awaiting_tags,
      update_all_locations: update_all_locations,
      fix_dates: fix_import_dates,
      update_product_image: update_product_image
    },
    shopify: {
      disconnect: shopfiy_disconnect
    },
    big_commerce: {
      check_connection: check_connection,
      disconnect: big_commerce_disconnect
    },
    magento: {
      get_aurthorize_url: get_magento_aurthorize_url,
      get_access_token: get_magento_access_token,
      disconnect: disconnect_magento,
      check_connection: check_connection,
    }
  };
}]);
