groovepacks_services.factory('orders', ['$http', '$window', '$modal', 'notification', '$q', 'printing_service', '$rootScope', function ($http,
  $window, $modal, notification, $q, printing_service, $rootScope) {

  var success_messages = {
    update_status: "Status updated Successfully",
    delete: "Deleted Queued Successfully",
    duplicate: "Duplicated Queued Successfully"
  };

  var get_default = function () {
    return {
      list: [],
      selected: [],
      single: {},
      load_new: true,
      current: 0,
      setup: {
        sort: "",
        order: "DESC",
        filter: "awaiting",
        search: '',
        select_all: false,
        inverted: false,
        limit: 20,
        offset: 0,
        //used for updating only
        status: '',
        reallocate_inventory: false,
        orderArray: []
      },
      orders_count: {}
    };
  };

  //Setup related function
  var update_setup = function (setup, type, value) {
    if (type == 'sort') {
      if (setup[type] == value) {
        if (setup.order == "DESC") {
          setup.order = "ASC";
        } else {
          setup.order = "DESC";
        }
      } else {
        setup.order = "DESC";
      }
    }
    setup[type] = value;
    return setup;
  };

  var update_items_setup = function (items, type, value) {
    var ascending = true;
    //var i = 0, length = items.length;
    var i = 0;
    while (i < items.length - 1) {
      if (items[i].category.toLowerCase() > items[++i].category.toLowerCase()) {
        ascending &= false;
        break;
      }
      ;
    }
    ;
    if (!ascending) {
      items.sort(sort_by_category_ascend);
    } else {
      items.sort(sort_by_category_descend);
    }
    ;
    return items;
  };

  var sort_by_category_ascend = function (a, b) {
    var aName = a.category.toLowerCase();
    var bName = b.category.toLowerCase();
    return ((aName < bName) ? -1 : ((aName > bName) ? 1 : 0));
  };

  var sort_by_category_descend = function (a, b) {
    var aName = a.category.toLowerCase();
    var bName = b.category.toLowerCase();
    return ((aName < bName) ? 1 : ((aName > bName) ? -1 : 0));
  };

  //list related functions
  var get_list = function (object, page, product_search_toggle) {
    var url = '';
    var setup = object.setup;
    if (typeof page != 'undefined' && page > 0) {
      page = page - 1;
    } else {
      page = 0;
    }
    try{
      object.ctrlKey = event.ctrlKey;
    } catch(e){}
    object.setup.offset = page * object.setup.limit;
    if (setup.search == '') {
      url = $rootScope.api_url + '/orders.json?' + $.param({filter: setup.filter, sort: setup.sort, order: setup.order});
    } else {
      url = $rootScope.api_url + '/orders/search.json?' + $.param({search: setup.search, sort: setup.sort, order: setup.order});
    }
    url += '&limit=' + setup.limit + '&offset=' + setup.offset + '&product_search_toggle=' + product_search_toggle;
    return $http.get(url).success(
      function (data) {
        if (data.status) {
          object.load_new = (data.orders.length > 0);
          object.orders_count = data.orders_count;
          object.list = data.orders;
          for (var i = 0; i < object.list.length; i++) {
            if (object.list[i].order_date!= null) {
              object.list[i].order_date = object.list[i].order_date.replace('Z', '').slice(0, -6);
            }
            if (object.list[i].last_modified!= null) {
              object.list[i].last_modified = object.list[i].last_modified.replace('Z', '').slice(0, -6);
            }
          }

          object.current = false;
          if (object.setup.select_all) {
            object.selected = [];
          }
          for (var i = 0; i < object.list.length; i++) {
            if (object.ctrlKey == true){
              object.list[i].checked =  true;
            }
            if (object.single && typeof object.single['basicinfo'] != "undefined") {
              if (object.list[i].id == object.single.basicinfo.id) {
                object.current = i;
              }
            }
            if (object.setup.select_all) {
              object.list[i].checked = object.setup.select_all;
              select_single(object, object.list[i]);
            } else {
              for (var j = 0; j < object.selected.length; j++) {
                if (object.list[i].id == object.selected[j].id) {
                  object.list[i].checked = object.selected[j].checked;
                  break;
                }
              }
            }
          }
        } else {
          notification.notify("Can't load list of orders", 0);
        }
      }
    ).error(notification.server_error);
  };

  var search_by_product_toggle = function(product_search_toggle){
    url = $rootScope.api_url +  "/settings/search_by_product.json"
    return $http.post(url, product_search_toggle);
  }

  var generate_list = function (action, orders) {
    if(typeof $window.order_modified == 'undefined'){$window.order_modified = []};
    orders.setup.orderArray = [];


    for (var i = 0; i < orders.selected.length; i++) {

      if (orders.selected[i].checked == true) {

        orders.setup.orderArray.push({id: orders.selected[i].id});
        $window.order_modified.push(orders.selected[i].ordernum);
      }
    }
    var url = '';
    var myscope = {};
    var interval = null;
    //set url for each action.
    if (action == "pick_list") {
      url = $rootScope.api_url + '/orders/generate_pick_list.json';
    }
    else if (action == "packing_slip") {
      if (orders.setup.select_all == true && orders.html_print == true){
        if(orders.setup.search != null && orders.setup.search != ""){
          // var ids = [];
          // angular.forEach(orders.selected, function(value, key) {
          //   ids.push(value.id)
          // });
          slip_url = $rootScope.api_url + '/orders/generate_all_packing_slip?filter='+orders.setup.filter+'&limit='+orders.setup.limit+'&select_all='+orders.setup.select_all+'&sort='+orders.setup.sort+'&product_search_toggle='+ orders.product_search_toggle +'&offset=' + orders.setup.offset+'&search='+orders.setup.search+'&order='+orders.setup.order;
        }else{
          slip_url = $rootScope.api_url + '/orders/generate_all_packing_slip?filter='+orders.setup.filter+'&sort='+orders.setup.sort+'&order='+orders.setup.order;
        }
        $window.open(slip_url);
      } else {
        url = $rootScope.api_url + '/orders/generate_packing_slip.json';
      }
    } else if (action == 'items_list') {
      if (orders.setup.select_all == true || orders.setup.orderArray.length > 20){
        url = $rootScope.api_url + '/orders/order_items_export.json?product_search_toggle='+orders.product_search_toggle;
      } else {
        url = $rootScope.api_url + '/orders/order_items_export.json?product_search_toggle='+orders.product_search_toggle;
      }
    } else if (action == 'barcode_slip') {
      if (orders.setup.select_all == true){
        url = $rootScope.api_url + '/products/bulk_barcode_generation?ids=all&status=' + orders.setup.filter;
        barcode_url =  null
      } else {
        ids = ""
        angular.forEach(orders.setup.orderArray, function(item){
          ids = ids + "," + item.id
        })

        if (orders.setup.orderArray.length > 20) {
          url = $rootScope.api_url + '/products/bulk_barcode_generation?ids=' + ids;
          barcode_url = null
        } else if (orders.setup.orderArray.length > 0){
          barcode_url = $rootScope.api_url + '/products/bulk_barcode_pdf.pdf?ids=' + ids;
        } else {
          notification.notify('No orders selected', 0);
        }
      }
      if (barcode_url != null) {
        $window.open(barcode_url);
      }
    }
    //send post http request and catch the response to display the pdfs.
    if (url != ""){
      return $http.post(url, orders.setup)
        .success(function (response) {
          if (action == "pick_list") {
            if (response['status']) {
              $window.open(response.url);
            } else {
              notification.notify(response['messages'], 0);
            }
          } else if (action == 'items_list') {
            if (response['status']) {
              if (response.filename != '') {
                $window.open(response.filename);
              }
            } else {
              notification.notify(response['messages']);
            }
          }
          if (action == 'barcode_slip' ) {
            notification.notify(response['messages'], 1);
          }else if (action == 'packing_slip' && !response['status']){
            notification.notify(response['messages'], 0);
          }
        }).error(notification.server_error);
    }


  };
  var cancel_pdf_gen = function (id) {
    return $http.post($rootScope.api_url + '/orders/cancel_packing_slip.json', {id: id}).success(function (data) {
      notification.notify(data['error_messages']);
      notification.notify(data['success_messages'], 1);
      notification.notify(data['notice_messages'], 2);
    }).error(notification.server_error);
  };

  var update_list = function (action, orders, search_by_product) {
    if (["update_status", "delete", "duplicate", "clear_assigned_tote"].indexOf(action) != -1) {
      orders.setup.orderArray = [];
      for (var i = 0; i < orders.selected.length; i++) {
        if (orders.selected[i].checked == true) {
          orders.setup.orderArray.push({id: orders.selected[i].id});
        }
      }
      if (orders.setup.orderArray.length<1 && !orders.setup.select_all) {
        notification.notify("Please select orders to perform this action.", 0);
        return;
      }

      var url = '';
      if (action == "delete") {
        url = $rootScope.api_url + '/orders/delete_orders.json';
      } else if (action == "duplicate") {
        url = $rootScope.api_url + '/orders/duplicate_orders.json';
      } else if (action == "update_status") {
        url = $rootScope.api_url + '/orders/change_orders_status.json';
      } else if (action == "clear_assigned_tote") {
        url = $rootScope.api_url + '/orders/clear_assigned_tote.json';
      }

      try{
        orders.setup.product_search_toggle = search_by_product.toString();
      } catch(e){
        console.log(e);
      }

      return $http.post(url, orders.setup).success(function (data) {
        orders.selected = [];
        if (data.status) {
          orders.setup.select_all = false;
          orders.setup.inverted = false;
          notification.notify(success_messages[action], 1);
          notification.notify(data.notice_messages, 2);
        } else {
          notification.notify(data.error_messages, 0);
        }
      }).error(notification.server_error);
    }
  };

  var update_list_by_option = function (option, orders) {
    orders.setup.orderArray = [];
    orders.setup.option = option;
    for (var i = 0; i < orders.selected.length; i++) {
      if (orders.selected[i].checked == true) {
        orders.setup.orderArray.push({id: orders.selected[i].id});
      }
    }

    return $http.post($rootScope.api_url + '/orders/changeorderstatus.json', orders.setup).success(function (data) {
      orders.selected = [];
      if (data.status) {
        orders.setup.select_all = false;
        orders.setup.inverted = false;
        notification.notify("order status updated successfully", 1);
        notification.notify(data.notice_messages, 2);
      } else {
        notification.notify(data.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var select_list = function (orders, from, to, state) {
    var url = '';
    var setup = orders.setup;
    var from_page = 0;
    var to_page = 0;

    if (typeof from.page != 'undefined' && from.page > 0) {
      from_page = from.page - 1;
    }
    if (typeof to.page != 'undefined' && to.page > 0) {
      to_page = to.page - 1;
    }
    var from_offset = from_page * setup.limit + from.index;
    var to_limit = to_page * setup.limit + to.index + 1 - from_offset;

    if (setup.search == '') {
      url = $rootScope.api_url + '/orders/getorders.json?filter=' + setup.filter + '&sort=' + setup.sort + '&order=' + setup.order;
    } else {
      url = $rootScope.api_url + '/orders/search.json?search=' + setup.search
    }
    url += '&is_kit=' + setup.is_kit + '&limit=' + to_limit + '&offset=' + from_offset;
    return $http.get(url).success(function (data) {
      if (data.status) {
        for (var i = 0; i < data.orders.length; i++) {
          data.orders[i].checked = state;
          select_single(orders, data.orders[i]);
        }
      } else {
        notification.notify("Some error occurred in loading the selection.");
      }
    });

  };


  var total_items_list = function (orders) {
    var total_items;
    if (orders.setup.search != "") {
      total_items = orders.orders_count['search'];
    } else {
      total_items = orders.orders_count[orders.setup['filter']];
    }
    if (typeof total_items == 'undefined') {
      total_items = 0;
    }
    return total_items;
  };

  var update_list_node = function (obj) {
    if (obj["var"] == "order_date"){
      t = obj["value"].toString()
      obj["value"]  = new Date(t).toLocaleString('en-GB');
    }
    return $http.post($rootScope.api_url + '/orders/update_order_list.json', obj).success(function (data) {
      if (data.status) {
        notification.notify("Successfully Updated", 1);
      } else {
        notification.notify(data.error_msg, 0);
      }
    }).error(notification.server_error);
  };

  //single order related functions
  var get_single = function (id, orders) {
    return $http.get($rootScope.api_url + '/orders/' + id + '.json').success(function (data) {
      orders.single = {};
      if (data.order) {
        //data.order.basicinfo.order_placed_time = new Date(data.order.basicinfo.order_placed_time);
        if (data.order.basicinfo.order_placed_time != null) {
          data.order.basicinfo.order_placed_time = data.order.basicinfo.order_placed_time.replace('Z', '').slice(0, -6);
        }

        orders.single = data.order;
      }
    }).error(notification.server_error);
  };

  var update_single = function (orders, auto) {
    if (typeof auto !== "boolean") {
      auto = true;
    }
    var order_data = {};
    for (var i in orders.single.basicinfo) {
      if (orders.single.basicinfo.hasOwnProperty(i) && i != 'id' && i != 'created_at' && i != 'updated_at') {
        order_data[i] = orders.single.basicinfo[i];
      }
    }

    try{
      t = order_data.order_placed_time.toString()
      order_data.order_placed_time  = new Date(t).toLocaleString('en-GB')
    }catch(e){
      order_data.order_placed_time = new Date('July 04, 1776 00:00:00').toLocaleString('en-GB');
    }
    return $http.put($rootScope.api_url + "/orders/" + orders.single.basicinfo.id + ".json",{order: order_data}).success(
      function (data) {
        if (data.status) {
          if (!auto) {
            notification.notify("Successfully Updated", 1);
          }
        } else {
          notification.notify(data.messages, 0);
        }
      }
    ).error(notification.server_error);
  };

  var select_single = function (orders, row) {
    var found = false;
    for (var i = 0; i < orders.selected.length; i++) {
      if (orders.selected[i].id == row.id) {
        found = i;
        break;
      }
    }

    if (found !== false) {
      if (!row.checked) {
        orders.selected.splice(found, 1);
      }
    } else {
      if (row.checked) {
        orders.selected.push(row);
      }
    }
  };

  var rollback_single = function (single) {
    return $http.post($rootScope.api_url + "/orders/rollback.json", {single: single}).success(
      function (data) {
        if (data.status) {
          //notification.notify("Successfully Updated",1);
        } else {
          notification.notify(data.messages, 0);
        }
      }
    ).error(notification.server_error);
  };
  var single_add_item = function (orders, ids) {
    return $http.post($rootScope.api_url + "/orders/" + orders.single.basicinfo.id + "/add_item_to_order.json", {productids: ids, qty: 1}).success(
      function (data) {
        if (data.status) {
          notification.notify("Item Successfully Added", 1);
        } else {
          notification.notify("Error adding", 0);
        }
      }
    ).error(notification.server_error);
  };

  var single_remove_item = function (ids) {
    return $http.post($rootScope.api_url + "/orders/remove_item_from_order.json", {orderitem: ids}).success(
      function (data) {
        if (data.status) {
          notification.notify("Item Successfully Removed", 1);
        } else {
          notification.notify(data.messages, 0);
        }
      }
    ).error(notification.server_error);
  };

  var single_record_exception = function (orders) {
    if(orders.single.exception.assoc==undefined) {
      orders.single.exception.assoc = orders.single.users[0];
    }
    return $http.post($rootScope.api_url +
      '/orders/'+orders.single.basicinfo.id+'/record_exception.json',
      {
        reason: orders.single.exception.reason,
        description: orders.single.exception.description,
        assoc: orders.single.exception.assoc
      }
    ).success(function (data) {
        if (data.status) {
          notification.notify("Exception successfully recorded", 1);
        } else {
          notification.notify(data.messages, 0);
        }
      }).error(notification.server_error);
  };

  var single_clear_exception = function (orders) {
    return $http.post($rootScope.api_url + '/orders/'+orders.single.basicinfo.id+'/clear_exception.json').success(function (data) {
      if (data.status) {
        notification.notify("Exception successfully cleared", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var single_update_item_qty = function (item) {
    return $http.post($rootScope.api_url + '/orders/update_item_in_order.json', {orderitem: item.id, qty: item.qty}).success(function (data) {
      if (data.status) {
        notification.notify("Item updated", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var single_update_print_status = function (item) {
    var result = $q.defer();
    return $http.post($rootScope.api_url + '/orders/update_item_in_order.json', {orderitem: item.id}).success(function (data) {
      if (data.status) {
        if (data.messages.length > 0) {
          alert(data.messages[0]);
          result.resolve();
        }
        ;
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
    return result.promise;
  };

  var single_print_barcode = function (item) {
    $window.open($rootScope.api_url + '/products/' + item.id + '/generate_barcode_slip.pdf?item_id=' + item.item_id);
  };

  var acknowledge_activity = function (activity_id) {
    return $http.put($rootScope.api_url + '/order_activities/' + activity_id + '/acknowledge/', null).success(function (data) {
      if (data.status) {
        notification.notify("Activity Acknowledged.", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var select_notification = function (message) {
    notification.notify(message, 0);
  }

  var get_order_id = function (increment_id) {
    return $http.post($rootScope.api_url + '/orders/get_id.json',{increment_id: increment_id}).success(function(response) {
      return response;
    }).error(notification.server_error);
  }

  var generate_box_slip = function(order, box_ids, type){
    if(type == undefined){ type = null };
    if(typeof $window.order_modified == 'undefined'){$window.order_modified = []};
    $window.order_modified.push(order.increment_id);
    var orderArray = [{id: order.id}];
    return $http.post($rootScope.api_url + '/orders/generate_packing_slip.json', { orderArray: orderArray, box_ids: box_ids, packing_type: type });
  }

  var next_split_order = function(order){
    return $http.get($rootScope.api_url + '/orders/next_split_order?id='+order.increment_id);
  }

  var print_shipping_label = function(scope_orders){
    if (!scope_orders.setup.select_all && scope_orders.selected.length == 1) {
      selected_order = scope_orders.selected[0];
      $http.get($rootScope.api_url + '/orders/' + selected_order.id + '/print_shipping_label.json').then(
        function (data) {
          if (data.data) {
            if (data.data.status) {
              var settings = {};
              settings = JSON.parse(localStorage.getItem('general_settings'));
              if (settings.direct_printing_options && settings.data.settings.print_ss_shipping_labels) {
                printing_service.print_now({ url: data.data.url, dimensions: data.data.dimensions}, 'user_selected_printer_ss_shipping_label_' + settings.data.settings.packing_slip_size);
              } else {
                $window.open(data.data.url);
              }
            } else {
              error = data.data.error_messages != undefined ? data.data.error_messages : data.data.error;
              if (error != undefined) { notification.notify(error, 0); }
              if (data.data.ss_label_order_data) {
                launch_ss_label_popup(data.data.ss_label_order_data);
              }
            }
          }
          });
    } else {
      notification.notify("Please select single order", 0);
    }

  }

  var launch_ss_label_popup = function (ss_label_data) {
    var ss_label_modal = $modal.open({
      templateUrl: '/assets/views/modals/shipstation_label.html',
      controller: 'shipstationlabelCtrl',
      size: 'lg',
      backdrop: 'static',
      keyboard: true,
      resolve: {
        ss_label_data: function () { return ss_label_data; }
      }
    });
  }

  var delete_box = function(id){
    return $http.put($rootScope.api_url + '/box/delete_box.json', { id: id });
  }

  var create_single = function (orders) {
    return $http.post($rootScope.api_url + '/orders').success(function (data) {
      orders.single = {};
      if (!data.status) {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  // OrderCup Direct Shipping
  var launch_order_cup_direct_shipping_popup = function (order, general_settings) {
    try {
      var custom_field_key_values = { custom_field_one: general_settings.custom_field_one, custom_field_two: general_settings.custom_field_two };
      var key = Object.keys(custom_field_key_values).filter(function(key) { return custom_field_key_values[key] === 'UOID'; })[0];
      if (key != undefined && order[key]) {
        url = 'https://ship.ordercup.com/pick_and_pack/ship?uoid=' + order[key];
        $window.open(url, 'Groovepacker - OrderCup Direct Shipping', "width=" + screen.availWidth + ", height=" + screen.availHeight);
      }
    } catch(e) {}
  };

  // Shopify Direct Shipping label
  var launch_shopify_shipping_popup = function (order_id, store_name) {
    try {
      if (order_id != undefined) {
        url = 'https://admin.shopify.com/store/' + store_name + '/orders/' + order_id;
        $window.open(url, 'Groovepacker - Shopify Direct Shipping', "width=" + screen.availWidth + ", height=" + screen.availHeight);
      }
    } catch(e) {}
  };

  return {
    model: {
      get: get_default
    },
    setup: {
      update: update_setup,
      update_items: update_items_setup
    },
    list: {
      search_by_product_toggle: search_by_product_toggle,
      get: get_list,
      update: update_list,
      select: select_list,
      total_items: total_items_list,
      update_node: update_list_node,
      generate: generate_list,
      cancel_pdf_gen: cancel_pdf_gen,
      update_with_option: update_list_by_option,
      select_notification: select_notification,
      generate_box_slip: generate_box_slip
    },
    single: {
      get: get_single,
      delete_box: delete_box,
      update: update_single,
      select: select_single,
      rollback: rollback_single,
      get_id: get_order_id,
      create: create_single,
      next_split_order: next_split_order,
      print_shipping_label: print_shipping_label,
      launch_order_cup_direct_shipping_popup: launch_order_cup_direct_shipping_popup,
      launch_shopify_shipping_popup: launch_shopify_shipping_popup,
      item: {
        add: single_add_item,
        remove: single_remove_item,
        update: single_update_item_qty,
        print_status: single_update_print_status,
        print_barcode: single_print_barcode
      },
      exception: {
        record: single_record_exception,
        clear: single_clear_exception
      },
      activity: {
        acknowledge: acknowledge_activity
      }
    }
  }
}]);
