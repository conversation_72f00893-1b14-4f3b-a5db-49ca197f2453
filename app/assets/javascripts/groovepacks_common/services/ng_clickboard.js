groovepacks_services.factory('ngClipboard',['$compile', '$rootScope', '$document', function($compile,$rootScope,$document) {
  return {
    toClipboard: function(element){
      var copyElement = angular.element('<span id="ngClipboardCopyId">'+element+'</span>');
      var body = $document.find('body').eq(0);
      body.append($compile(copyElement)($rootScope));
      var ngClipboardElement = angular.element(document.getElementById('ngClipboardCopyId'));
      var range = document.createRange();
      range.selectNode(ngClipboardElement[0]);
      window.getSelection().removeAllRanges();
      window.getSelection().addRange(range);
      var successful = document.execCommand('copy');
      var msg = successful ? 'successful' : 'unsuccessful';
      window.getSelection().removeAllRanges();
      copyElement.remove();
    }
  }
}])