groovepacks_services.factory('generalsettings', ['$http', 'notification', '$rootScope', 'Idle', function ($http, notification, $rootScope, Idle) {


  //default object
  var get_default = function () {
    return {
      single: {}
    };
  };

  var get_settings = function (settings) {
    time_in_zone = moment().format("Z");
    var url = $rootScope.api_url + '/settings/get_settings.json';
    return $http.get(url).success(
      function (data) {
        if (data.status) {
          localStorage.setItem('general_settings', JSON.stringify(data));
          var matchingKey = Object.keys(data.new_time_zones).find(function(key) {
              return data.new_time_zones[key] === data.data.settings.new_time_zone;
          });
          if (matchingKey) {
            match = matchingKey.match(/\(([^)]+)\)$/);
            time_zone = match ? match[1] : null;
            localStorage.setItem('current_time_zone', time_zone);
          }
          settings.single = data.data.settings;
          settings.single.is_multi_box = data.is_multi_box
          settings.single.direct_printing_options = data.direct_printing_options
          settings.single.order_cup_direct_shipping = data.order_cup_direct_shipping
          settings.single.ss_api_create_label = data.ss_api_create_label
          settings.single.show_external_logs_button = data.show_external_logs_button
          settings.single.loggly_sw_imports = data.loggly_sw_imports
          settings.single.loggly_shopify_imports = data.loggly_shopify_imports
          settings.single.loggly_se_imports = data.loggly_se_imports
          settings.single.loggly_veeqo_imports = data.loggly_veeqo_imports
          settings.single.loggly_shipstation_imports = data.loggly_shipstation_imports
          settings.single.loggly_gpx_order_scan = data.loggly_gpx_order_scan
          settings.single.api_call = data.api_call
          settings.single.allow_rts = data.allow_rts
          settings.single.groovelytic_stat = data.groovelytic_stat
          settings.single.packing_cam = data.packing_cam
          settings.single.product_activity = data.product_activity
          settings.single.custom_product_fields = data.custom_product_fields
          settings.single.enable_developer_tools = data.enable_developer_tools
          settings.single.email_address_for_billing_notification = data.email_address_for_billing_notification;
          settings.single.time_zones = data.time_zone
          settings.single.current_time = data.current_time
          settings.single.new_time_zones = data.new_time_zones
          data.data.settings.time_to_send_email = data.data.settings.time_to_send_email.replace("T", " ").replace("Z", "").slice(0, -6);
          try{
            data.data.settings.time_to_import_orders = settings.single.time_to_import_orders.replace("T", " ").replace("Z", "").slice(0, -6);
          }catch(e){
            data.data.settings.time_to_import_orders = new Date(settings.single.time_to_import_orders).toString().split("GMT")[0];
          }
          try{
            data.data.settings.to_import = settings.single.to_import == undefined ? new Date() : settings.single.to_import.replace("T", " ").replace("Z", "").slice(0, -6);
          }catch(e){
            data.data.settings.to_import = settings.single.to_import == undefined ? new Date() : settings.single.to_import.toString().split("GMT")[0];
          }
          try{
            data.data.settings.from_import = settings.single.from_import == undefined ? new Date() : settings.single.from_import.replace("T", " ").replace("Z", "").slice(0, -6);
          }catch(e){
            data.data.settings.from_import = settings.single.from_import == undefined ? new Date() : settings.single.from_import.toString().split("GMT")[0];
          }
          settings.single.scheduled_import_toggle = data.scheduled_import_toggle
          settings.single.scan_pack_workflow = data.scan_pack_workflow
          settings.single.daily_packed_toggle = data.daily_packed_toggle;
          // settings.single.time_to_send_email = new Date(data.data.settings.time_to_send_email);
          if (data.is_active == true) {
            setIdleTimeout(data.data.settings.idle_timeout);
          }else{
            logoutuser();
          }

          // if(data.user_sign_in_count<2 && data.data.settings.time_zone == null) {
          //   time_zone = {}
          //   time_zone["add_time_zone"] = time_in_zone;
          //   time_zone["auto_detect"] = "true";
          //   add_time_zone(time_zone);
          // }
        } else {
          notification.notify(data.error_messages, 0);
        }
      }
    ).error(notification.server_error);
  };

  var fix_times = function (settings) {
    var today = null;
    var all = ['time_to_import_orders', 'time_to_send_email'];
    var config_date = null;
    for (var i = 0; i < all.length; i++) {
      config_date = new Date(settings.single[all[i]]);
      today = new Date();
      today.setHours(config_date.getHours());
      today.setMinutes(config_date.getMinutes());
      today.setSeconds(0);
      settings.single[all[i]] = today;
    }

  };

  var update_settings = function (settings) {
    var url = $rootScope.api_url + '/settings/update_settings.json';
    fix_times(settings);
    settings.single.time_to_import_orders = settings.single.time_to_import_orders.toString().split("GMT")[0];
    try{
      settings.single.to_import = settings.single.to_import == undefined ? new Date() : settings.single.to_import.replace("T", " ").replace("Z", "");
    }catch(e){
      settings.single.to_import = settings.single.to_import == undefined ? new Date() : settings.single.to_import.toString().split("GMT")[0];
    }
    try{
      settings.single.from_import = settings.single.from_import == undefined ? new Date() : settings.single.from_import.toString().split("GMT")[0];
    }catch(e){
      settings.single.from_import = settings.single.from_import == undefined ? new Date() : settings.single.from_import.replace("T", " ").replace("Z", "");
    }
    settings.single.time_to_send_email = settings.single.time_to_send_email.toString().split("GMT")[0];
    return $http.put(url, settings.single).success(
      function (data) {
        if (data.status) {
          get_settings(settings);
          notification.notify(data.success_messages, 1);
        } else {
          notification.notify(data.error_messages, 0);
        }
      }
    ).error(notification.server_error);
  };

  var add_time_zone = function (time_zone, settings) {
    var url = $rootScope.api_url + '/settings/fetch_and_update_time_zone.json';
    return $http.post(url, time_zone).success(function (data) {
      settings.single.current_time = data.current_time;
    });
  }

  var update_auto_time_zone = function (time_zone, settings) {
    var url = $rootScope.api_url + '/settings/update_auto_time_zone.json';
    return $http.post(url, time_zone).success(function (data) {
      if(data.status) {
        settings.single.current_time = data.time;
        settings.single.new_time_zone = data.zone.name;
      }
    });
  }

  var setIdleTimeout = function(time) {
    if(time != null && time != 0){
      Idle.setIdle(time*60);
    }else{
      Idle.setIdle(365*24*60*60);
    }
  }

  var logoutuser = function(){
    Idle.setIdle(1);
  }

  var auto_complete = function(value, type){
    var url = $rootScope.api_url + '/settings/auto_complete.json?value='+value+'&type='+type;
    return $http.get(url);
  }

  //Public facing API
  return {
    model: {
      get: get_default
    },
    single: {
      update: update_settings,
      get: get_settings,
      add_time_zone: add_time_zone,
      auto_complete: auto_complete,
      update_auto_time_zone: update_auto_time_zone
    }
  };
}]);
