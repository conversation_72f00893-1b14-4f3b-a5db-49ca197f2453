groovepacks_services.factory('websocketLogger', ['$http', '$rootScope', function ($http, $rootScope) {

  // Flag to track if printing operation is active
  var printing_operation_active = false;

  // Store current order ID during printing operations
  var current_order_id = null;

  // Valid print types that should trigger logging
  var valid_print_types = [
    'packing_slip', 'order_barcode', 'bulk_barcode', 'bulk_order_items',
    'shipping_label', 'barcode_label', 'receiving_label', 'print'
  ];

  var log_websocket_event = function(log_type, event_type, service, details) {
    // Only log if printing operation is active
    if (!printing_operation_active) {
      return;
    }

    try {
      // Add order ID to details if available
      var enhanced_details = details || {};
      if (current_order_id) {
        enhanced_details.orderId = current_order_id;
      }

      var payload = {
        log_type: log_type,
        data: {
          event_type: event_type,
          service: service,
          timestamp: new Date().toISOString(),
          details: enhanced_details
        }
      };

      return $http.post($rootScope.api_url + '/print/web_socket_logs', payload)
        .success(function(response) {
          console.log('WebSocket event logged successfully:', response);
        })
        .error(function(error) {
          console.error('Failed to log WebSocket event:', error);
        });
    } catch (e) {
      console.error('Error in WebSocket logging:', e);
    }
  };

  var log_connection = function(service, details) {
    return log_websocket_event('WebSocket Connection', 'connect', service, details);
  };

  var log_reconnection = function(service, details) {
    return log_websocket_event('WebSocket Reconnection', 'reconnect', service, details);
  };

  var log_disconnection = function(service, details) {
    return log_websocket_event('WebSocket Disconnection', 'disconnect', service, details);
  };

  var log_error = function(service, error_details) {
    return log_websocket_event('WebSocket Error', 'error', service, error_details);
  };

  var log_connection_failed = function(service, error_details) {
    return log_websocket_event('WebSocket Connection Failed', 'connection_failed', service, error_details);
  };

  var log_connection_closed = function(service, close_details) {
    return log_websocket_event('WebSocket Connection Closed', 'connection_closed', service, close_details);
  };

  // Functions to control when logging should happen
  var start_printing_operation = function(print_type, order_id) {
    if (valid_print_types.indexOf(print_type) !== -1) {
      printing_operation_active = true;
      current_order_id = order_id || null;
      console.log('WebSocket logging enabled for print operation:', print_type, 'Order ID:', order_id);
    }
  };

  var end_printing_operation = function() {
    printing_operation_active = false;
    current_order_id = null;
    console.log('WebSocket logging disabled - print operation completed');
  };

  // Create global bridge function for non-Angular libraries like QZ Tray
  window.logWebSocketEvent = function(log_type, event_type, service, details) {
    try {
      log_websocket_event(log_type, event_type, service, details);
    } catch (e) {
      console.error('Error in global WebSocket logging bridge:', e);
    }
  };

  // Global functions to control printing operation state
  window.startPrintingOperation = function(print_type, order_id) {
    start_printing_operation(print_type, order_id);
  };

  window.endPrintingOperation = function() {
    end_printing_operation();
  };

  return {
    log_connection: log_connection,
    log_reconnection: log_reconnection,
    log_disconnection: log_disconnection,
    log_error: log_error,
    log_connection_failed: log_connection_failed,
    log_connection_closed: log_connection_closed,
    log_websocket_event: log_websocket_event,
    start_printing_operation: start_printing_operation,
    end_printing_operation: end_printing_operation
  };

}]);
