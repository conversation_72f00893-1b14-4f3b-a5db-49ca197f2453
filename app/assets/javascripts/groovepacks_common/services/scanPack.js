groovepacks_services.factory('scanPack', ['$http', 'notification', '$state', '$window', '$interval','$rootScope', function ($http, notification, $state, $window, $interval, $rootScope) {

  if(typeof $window.order_modified == 'undefined'){
    $window.order_modified = [];
    window.order_modified = $window.order_modified;
  }

  // Used to store temp array of order ids which are scanned in the current tab.
  var set_order_scanned = function(action){
    increment_id = $window.increment_id;
    index = $window.order_modified.indexOf(increment_id);
    if(action == 'push'){
      if(increment_id != null && index == -1){
        $window.order_modified.push(increment_id);
      }
    }
    else{
      if(index > -1){
        $window.order_modified.splice(index, 1);
      }
    }
  }

  var get_state = function () {
    return {
      image: {
        enabled: false,
        time: 0,
        src: ''
      },
      sound: {
        enabled: false
      }
    }
  };

  var get_default = function () {
    return {
      state: 'none',
      scan_states: {
        success: get_state(),
        fail: get_state(),
        order_complete: get_state()
      },
      settings: {}
    };
  };

  var input = function (input, state, id, rem_qty, box_id, store_order_id, order_by_number) {
    if ((scope.scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall' || scope.scan_pack.settings.scan_pack_workflow == 'multi_put_wall' ) && state == null) {
      return $http.post($rootScope.api_url + '/scan_pack/product_first_scan.json', {input: input}).success(function (data) {
        if (data.status == false) {
          if (data.product_error) {
            scope.trigger_scan_message('fail');
            notification.notify(data.notice_messages, 2);
          } else if (data.no_order) {
            notification.notify(data.notice_messages, 2);
          }
        } else if (data.single_item_order) {
          scope.trigger_scan_message('order_complete', true);
        }
      }).error(function(){
        notification.server_error;
      });
    }
    if (input == scope.scan_pack.settings.click_scan_barcode && scope.scan_pack.settings.click_scan) {
      set_order_scanned('push');

      return $http.post($rootScope.api_url + '/scan_pack/click_scan.json', {barcode: scope.data.order.next_item.barcodes[0].barcode, id: id, box_id: box_id}).success(function (data) {
        notification.notify(data.notice_messages, 2);
        notification.notify(data.success_messages, 1);
        notification.notify(data.error_messages, 0);
      }).error(function(){
        notification.server_error;
        set_order_scanned('pop');
      });
    }
    // else if (input == scope.scan_pack.settings.scanned_barcode && scope.scan_pack.settings.scanned) {
    //   return $http.post($rootScope.api_url + '/scan_pack/order_change_into_scanned.json', {id: id}).success(function (data) {
    //     notification.notify(data.notice_messages, 2);
    //     notification.notify(data.success_messages, 1);
    //     notification.notify(data.error_messages, 0);
    //   }).error(function(){
    //     notification.server_error;
    //   });
    //   $scope.$broadcast('reload-scanpack-state');
    // }
    else{
      set_order_scanned('push');
      return $http.post($rootScope.api_url + '/scan_pack/scan_barcode.json', {input: input, state: state, id: id, rem_qty: rem_qty, box_id: box_id, store_order_id: store_order_id, order_by_number: order_by_number}).success(function (data) {      
        if (data.data.serial != undefined) {
          scope.set_last_scanned(data.data.serial.barcode);
        }
        try{
          $rootScope.username = data.data.order.firstname + " " + data.data.order.lastname;
        }catch(e){}
        if (data.notice_messages.length == 0){
          notification.close(2);
          notification.notify(data.notice_messages, 2);
        } else if(data.notice_messages[0].length > 400)  {
          notification.close(1);
          notification.notify(data.notice_messages, 2);
        } else {
          notification.notify(data.notice_messages, 2);
        }
        if (data.on_demand == true) {
          notification.notify("We are requesting that order and it will open as soon as it's available. If you do not wish to wait you can scan another order number." , 1)
        }
        notification.notify(data.success_messages, 1);
        if (data.on_demand != true) {
          notification.notify(data.error_messages, 0);
        }
      }).error(function(){
        notification.server_error;
        set_order_scanned('pop');
      });
    }
  };

  var getshipment = function(store_id, order_id){
    return $http.post($rootScope.api_url + '/scan_pack/get_shipment.json', {store_id: store_id, order_id: order_id}).success(function (data) {
      if (data.shipment_id) {
        data.shipment_id;
      } else {
        notification.notify("Shipment not found!", 0);
      }
    });
  }

  var reset = function (id) {
    return $http.post($rootScope.api_url + '/scan_pack/reset_order_scan.json', {order_id: id}).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
      if (data.status) {
        if (typeof data.data != "undefined") {
          if (typeof data.data.next_state != "undefined") {
            //states[data.data.next_state](data.data);
            if ($state.current.name == data.data.next_state) {
              $state.reload();
            } else {
              $state.go(data.data.next_state, data.data);
            }
          }
        }
      }
    }).error(notification.server_error);
  };

  var add_note = function (id, send_email, note) {
    return $http.post($rootScope.api_url + '/scan_pack/add_note.json', {id: id, email: send_email, note: note}).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
    }).error(notification.server_error);
  };

  var assign_user_tote_set = function (input) {
    return $http.post($rootScope.api_url + '/scan_pack/assign_user_tote_set.json', { input: input }).success(function (data) {
    }).error(notification.server_error);
  };

  var get_settings = function (model) {
    return $http.get($rootScope.api_url + '/settings/get_scan_pack_settings.json').success(function (data) {
      if (data.status) {
        model.settings = data.settings;
      } else {
        notification.notify(data.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var update_settings = function (model) {
    if (model.settings.single_item_order_complete_msg.length == 0) {
      model.settings.single_item_order_complete_msg = 'Labels Printing!';
    }
    if (model.settings.multi_item_order_complete_msg.length == 0) {
      model.settings.multi_item_order_complete_msg = 'Collect all items from the tote!';
    }
    return $http.post($rootScope.api_url + '/settings/update_scan_pack_settings.json', model.settings).success(function (data) {
      if (data.status) {
        get_settings(model);
        notification.notify(data.success_messages, 1);
      } else {
        notification.notify(data.error_messages, 0);
      }
    }).error(notification.server_error);
  };

  var update_intagibleness = function (model) {
    return $http.post($rootScope.api_url + '/products/update_intangibleness.json', model.settings).success(function (data) {
      if (data.status) {
        notification.notify("updating products queued successfully", 1);
      } else {
        notification.notify(data.messages, 0);
      }
    }).error(notification.server_error);
  };

  var code_confirm = function (code) {
    return $http.post($rootScope.api_url + '/scan_pack/confirmation_code.json', {code: code}).success(function (data) {
    }).error(notification.server_error);
  };

  var order_instruction = function (id, code) {
    return code_confirm(code);
  };

  var type_scan = function (id, next_item, count, current_box) {
    if(current_box == undefined){
      var box_id = null;
    }else{
      var box_id = current_box.id;
    }
    return $http.post($rootScope.api_url + '/scan_pack/type_scan.json', {
      id: id,
      next_item: next_item,
      count: count,
      box_id: box_id
    }).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
    }).error(notification.server_error);
  };

  var product_instruction = function (id, next_item, code) {
    return $http.post($rootScope.api_url + '/scan_pack/product_instruction.json', {
      id: id,
      next_item: next_item,
      code: code
    }).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
    }).error(notification.server_error);
  };

  var send_api_request = function(id){
    $http.post($rootScope.api_url + '/scan_pack/send_request_to_api.json', id)
  };

  var click_scan = function (barcode, id, box_id) {
    set_order_scanned('push');

    return $http.post($rootScope.api_url + '/scan_pack/click_scan.json', {barcode: barcode, id: id, box_id: box_id}).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
    }).error(function(){
      notification.server_error;
      set_order_scanned('pop');
    });
  };

  var serial_scan = function (serial) {
    return $http.post($rootScope.api_url + '/scan_pack/serial_scan.json', serial).success(function (data) {
      notification.notify(data.notice_messages, 2);
      notification.notify(data.success_messages, 1);
      notification.notify(data.error_messages, 0);
    }).error(notification.server_error);
  };

  var update_scanned = function(order){
    $http.post($rootScope.api_url + '/scan_pack/update_scanned.json', order);
  }

  var create_box = function(order_id, box){
    return $http.post($rootScope.api_url + '/box.json', { order_id: order_id, name: 'Box '+ box });
  }

  var remove_from_box = function(id, kit_product_id, box_id) {
    return $http.put($rootScope.api_url + '/box/remove_from_box.json', { order_item_id: id, kit_product_id: kit_product_id, box_id: box_id });
  }

  var remove_empty_boxes = function(box_ids,order_id){
    return $http.put($rootScope.api_url + '/box/remove_empty.json', { ids: box_ids, order_id: order_id });
  }

  var fetch_label_related_data = function(id) {
    return $http.post($rootScope.api_url + '/stores/fetch_label_related_data.json', { id: id });
  }

  return {

    input: input,
    getshipment: getshipment,
    reset: reset,
    settings: {
      model: get_default,
      get: get_settings,
      update: update_settings
    },
    add_note: add_note,
    assign_user_tote_set: assign_user_tote_set,
    states: {
      model: get_state
    },
    click_scan: click_scan,
    type_scan: type_scan,
    code_confirm: code_confirm,
    order_instruction: order_instruction,
    product_instruction: product_instruction,
    product_serial: serial_scan,
    update_products: update_intagibleness,
    update_scanned: update_scanned,
    create_box: create_box,
    remove_from_box: remove_from_box,
    remove_empty_boxes: remove_empty_boxes,
    send_api_request: send_api_request,
    fetch_label_related_data: fetch_label_related_data,
  };
}]);
