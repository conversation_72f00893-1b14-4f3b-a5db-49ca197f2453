groovepacks_services.factory('inventory_manager', ['$http', 'notification', '$rootScope', function ($http, notification, $rootScope) {


  //default object
  var get_default = function () {
    return {
      single: {
        method: 'receive'
      }
    };
  };


  var post_receive_or_recount_inventory = function (inventory_manager_obj) {
    var url = $rootScope.api_url + '/products/'+inventory_manager_obj.single.id+'/adjust_available_inventory.json';
    return $http.put(url, inventory_manager_obj.single).success(
      function (data) {
        if (!data.status) {
          notification.notify(data.error_messages, 0);
        }
      }
    ).error(notification.server_error);
  };

  //Public facing API
  return {
    model: {
      get: get_default
    },
    single: {
      update: post_receive_or_recount_inventory
    }
  };


}]);
