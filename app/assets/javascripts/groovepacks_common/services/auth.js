groovepacks_services.factory("auth", ['$http', '$rootScope', 'groovIO', '$window', '$state', 'notification', '$location', 
  function ($http, $rootScope, groovIO, $window, $state, notification, $location) {
    var check = function () {
      return $http.get($rootScope.api_url + '/home/<USER>', {ignoreLoadingBar: true}).success(function (data) {
        if (!jQuery.isEmptyObject(data)) {
          groovIO.connect();
        } else {
          groovIO.disconnect();
        }
        if(data.view_dashboard == "0"){ data.view_dashboard = "none" }
        $rootScope.current_user_data = data;
        if($rootScope.current_user_data.view_dashboard == "0"){ $rootScope.current_user_data.view_dashboard = "none" }
        $window.localStorage.setItem('current_user', JSON.stringify(data))
        $rootScope.$broadcast("user-data-reloaded");
        $rootScope.$broadcast("connect-to-socket-server");
        $rootScope.$broadcast("connect-dashboard-to-socket-server");
      }).error(function(response){
        $rootScope.$broadcast("sign-out");
      });
    };

    var get_current = function () {
      // To get current user information and store it in local storage at login
      $rootScope.current_user_data  = JSON.parse($window.localStorage.getItem('current_user'));
      return JSON.parse($window.localStorage.getItem('current_user'));
    };

    var home = function () {
    //check if access to orders
    if (has_access('orders')) {
      return 'orders';
    } 
    return 'scanpack.rfo';
  };

  var get_current_tenant = function() {
    tenant = $location.host().split('.')[0];
    return tenant
  }

  var prevent = function (name) {
    var to = false;
    var params = {};

    if (name == "home" && (get_current_tenant() == 'admintools' || get_current_tenant() == 'scadmintools')) {
      to = "tools";
    } else if (name == "home" || !has_access(name)) {
      to = home();
    } else if (to == name) {
      to = false;
    }

    return {to: to, params: params};
  };

  var user_can = function (setting) {
    return public_user_can($rootScope.current_user_data, setting);
  };

  //Should always mimic code from app/model/user.rb User::can?
  var public_user_can = function (user, setting) {
    if (user != null) {
      if (typeof user['role'] == 'undefined') return false;
      if (user.role.make_super_admin) return true;

      if (['create_edit_notes', 'change_order_status', 'import_orders'].indexOf(setting) != -1) {
        return (user.role.add_edit_order_items || user.role[setting]);
      }

      if (typeof user.role[setting] == "boolean") {
        return user.role[setting];
      }
      return false;
    }
  };

  var has_access = function (name) {
    // Check if user has access for home or not after login
    if (name == "home") return true;

    if (name.indexOf('.') != -1) {
      name = name.substr(0, name.indexOf('.'))
    }
    return user_can('access_' + name);
  };

  var login = function(username, password) {
    // send request to the server
    // var url = document.URL.split('/');
    // var target_url = url[0] + '//' + url[2] + '/auth/v1/login';
    var target_url = $rootScope.api_url + '/auth/v1/login'
    return ($http.post(target_url, {username: username, password: password}).success(function(response){
      $window.localStorage.setItem('access_token', response.access_token);
      $window.localStorage.setItem('refresh_token', response.refresh_token);
      $window.localStorage.setItem('created_at', response.created_at);
      url = $rootScope.api_url + '/users/update_login_date';
      $http.post(url, {username: username});
    }).error(function(response) {
      // invalid username/password
      notification.notify("Un-authorised User");
    }));
  }

  var user_check = function() {
    // check for user is logged in or not
    user = get_current();
    return(user)? true : false;
  }

  var update_password = function(auth_pass) {
    if (auth_pass["new_password"] == undefined || auth_pass["new_password"] == "" ){
      notification.notify("Password should not be blank or less than 6 character");
    } else if (auth_pass["new_password"] == auth_pass["confirm_password"]) {
      reset_password_token = auth_pass["token"];
      user_id = auth_pass["user_id"];
      new_pass = auth_pass["new_password"];
      confirm_pass = auth_pass["confirm_password"];
      target_url = $rootScope.api_url + '/users/update_password?reset_password_token=' + reset_password_token +'&password='+ new_pass +'&password_confirmation=' + confirm_pass + '&user_id=' + user_id
      return $http.put(target_url).success(function(result){
        notification.notify(result["msg"],result["code"]);
        $state.go('login');
      });
    } else {
      notification.notify("Password Not-matched", 0);
    }
  };

  var forgetpass = function (user) {
    target_url = $rootScope.api_url + '/users/get_user_email?user=' + user
    return $http.get(target_url).success(function(result){
        notification.notify(result["msg"],result["code"]);
        $state.go('login');
      });
  }

  var refresh_access_token = function() {
    // To check refresh token and save it in local storage of browser
    // var url = document.URL.split('/');
    var refresh_token = $window.localStorage.getItem('refresh_token');
    // var target_url = url[0] + '//' + url[2] + '/auth/v1/getToken';
    var target_url = $rootScope.api_url + '/auth/v1/getToken'
    var created_at = $window.localStorage.getItem('created_at');
    d = new Date();
    if (created_at > parseInt(d.getTime() / 1000) - 5400) {
      return $http.get(target_url, {headers: {
        "Content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Authorization": refresh_token
      }}).success(function (response) {
        $window.localStorage.removeItem('access_token');
        $window.localStorage.setItem('access_token', response.access_token);
        $window.localStorage.removeItem('refresh_token');
        $window.localStorage.setItem('refresh_token', response.refresh_token);
        $window.localStorage.removeItem('created_at');
        $window.localStorage.setItem('created_at', response.created_at);
      }).error(function(response) {
        console.error(response);
      });
    }
  };

  return {
    check: check,
    get: get_current,
    prevent: prevent,
    can: user_can,
    user_can: public_user_can,
    access: has_access,
    login: login,
    isLoggedIn: user_check,
    refresh_token: refresh_access_token,
    forgetpass: forgetpass,
    update_password: update_password
  };

}]);
