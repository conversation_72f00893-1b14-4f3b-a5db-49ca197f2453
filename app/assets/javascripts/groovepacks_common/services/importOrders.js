groovepacks_services.factory("importOrders", ['$http', '$window','notification', '$rootScope', function ($http,$window, notification, 
  $rootScope) {
    return {
      do_import: function () {
        $http.get($rootScope.api_url + '/orders/import_all.json', {ignoreLoadingBar: true}).success(function (data) {
          if (data.status) {
            notification.notify(data.success_messages, 1);
          } else {
            notification.notify(data.error_messages);
          }
        }).error(notification.server_error);
      },

      issue_import: function (store_id, no_of_days, import_type) {
        $http.get($rootScope.api_url + "/orders/import.json?store_id=" + store_id + "&days="+ no_of_days +"&import_type=" + import_type,
          {ignoreLoadingBar: true}).success(function (data) {
            if (data.status) {
              notification.notify(data.success_messages, 1);
            } else {
              notification.notify(data.error_messages);
            }
          }).error(notification.server_error);
      },

      issue_import_for_ss: function (store_id, no_of_days, import_type, import_date, start_date, end_date, order_date_type, order_id ) {
        $http.get($rootScope.api_url + "/orders/import_for_ss.json?store_id=" + store_id + "&days="+ no_of_days +"&import_type=" + import_type + "&import_date=" + import_date+"&start_date=" + start_date +"&end_date=" + end_date +"&order_date_type=" + order_date_type +"&order_id=" + order_id,
          {ignoreLoadingBar: true}).success(function (data) {
            if (data.status) {
              notification.notify(data.success_messages, 1);
            } else {
              notification.notify(data.error_messages);
            }
          }).error(notification.server_error);
      },

      cancel_import: function (store_id) {
        $http.put($rootScope.api_url + '/orders/cancel_import.json',
          {store_id: store_id}).success(function (data) {
            if (data.status) {
              notification.notify(data.success_messages, 1);
            } else {
              notification.notify(data.error_messages);
            }
          }).error(notification.server_error);
      },

      cancel_all: function () {
        if (confirm('This will stop all imports, including not started yed and delete Import Summary. Are you sure?'))
        {
          $http.get($rootScope.api_url + '/orders/cancel_all.json').success(function (data) {
            if (data.status) {
              notification.notify(data.messages, 1);
            }
          }).error(notification.server_error);
        }
      },

      update_popup_display_setting: function(flag) {
        $http.put($rootScope.api_url + '/order_import_summary/update_display_setting',
          {flag: flag}).success(function (data) {
            // if (data.status) {
            //   notification.notify(data.success_messages, 1);
            // } else {
            //   notification.notify(data.error_messages);
            // }
          });
      },

      update_order_import: function(not_started_summary){
        if(not_started_summary==0){
          $http.get($rootScope.api_url + '/order_import_summary/update_order_import_summary.json'); 
          return 1;
        };
      },

      fix_imported_at: function(store_id){
        $http.get($rootScope.api_url + '/order_import_summary/fix_imported_at.json?store_id=' + store_id).success(function (data) {
          if (data.status) {
            notification.notify("Successfully Updated!", 1);
          } else {
            notification.notify(data.error_messages);
          }
        });
      },

      delete_import_summary: function(store_id){
        $http.get($rootScope.api_url + '/order_import_summary/delete_import_summary.json?store_id=' + store_id).success(function (data) {
          if (data.status) {
            notification.notify("Successfully Deleted!", 1);
          } else {
            notification.notify(data.error_messages);
          }
        });
      },


      download_summary_details: function(store_id){
        $http.get($rootScope.api_url + '/order_import_summary/download_summary_details.json?store_id=' + store_id).success(function (data) {
         $window.open(data.url);
        });
      }
    }
  }]
);
