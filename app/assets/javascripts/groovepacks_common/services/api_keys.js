groovepacks_services.factory('api_keys', ['$http', 'notification', '$rootScope', function ($http, notification, $rootScope) {

  var regenerate_single = function() {
    return $http.post($rootScope.api_url + '/api_keys.json').success(function (data) {
      if (!data.status) {
        notification.notify(data.error_messages, 0);
      } else {
        notification.notify(data.success_messages, 1);
      }
    }).error(notification.server_error);
  };

  var delete_single = function(id) {
    return $http.delete($rootScope.api_url + '/api_keys/' + id +'.json').success(function (data) {
      if (!data.status) {
        notification.notify(data.error_messages, 0);
      } else {
        notification.notify(data.success_messages, 1);
      }
    }).error(notification.server_error);
  };

  return {
    single: {
      regenerate: regenerate_single,
      delete: delete_single,
    }
  };
}]);
