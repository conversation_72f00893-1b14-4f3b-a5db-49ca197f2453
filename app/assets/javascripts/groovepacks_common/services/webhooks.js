groovepacks_services.factory('webhooks', ['$http', 'notification', '$rootScope', function ($http, notification, $rootScope) {

  var delete_webhooks = function (webhooks) {
    return $http.delete($rootScope.api_url + '/groovepacker_webhooks/delete_webhooks.json', { params: { "webhook_ids[]": webhooks } }).success(function (response) {
      if (response.status) {
        notification.notify(response.message, 1);
      }
    }).error(notification.server_error);
  }

  return {
    list: {
      delete: delete_webhooks
    }
  };
}]);
