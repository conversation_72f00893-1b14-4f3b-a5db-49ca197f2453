groovepacks_services.factory('printing_service', ['$http', 'notification', '$rootScope', 'Idle', '$window', '$modal', 'websocketLogger', function ($http, notification, $rootScope, Idle, $window, $modal, websocketLogger) {
  var myscope = {};

  var print_now = function (message, printer_type) {
    // Enable WebSocket logging for this printing operation
    var order_id = message.current_increment_id || message.id || null;
    websocketLogger.start_printing_operation('print', order_id);

    var dimensions = { width: 3, height: 1 };
    if (message.dimensions != null && message.dimensions.length > 0) { dimensions = { width: message.dimensions.split('x')[0], height: message.dimensions.split('x')[1] };};
    qz.websocket.connect().then(function() {
      qz.printers.find().then(function(data) {
        selected_printer = localStorage.getItem(printer_type);
        if (data.length != 0) {
          printer_available = (selected_printer == null) ? -1 : data.indexOf(selected_printer);
          if (selected_printer == null || printer_available == -1) {
            select_printer(data, message.url, dimensions, printer_type);
          } else {
            var config = qz.configs.create(selected_printer, {
              size: dimensions, units: 'in', 
              colorType: 'grayscale', 
              interpolation: "nearest-neighbor" 
            });
            var data = [{
                  type: 'pixel',
                  format: 'pdf',
                  flavor  : 'file',
                  data: message.url
            }];
            qz.print(config, data).then(function () {
              notification.notify("File Printed using QZ", 1);
              websocketLogger.end_printing_operation();
            }).catch(function(e) {
              console.error(e);
              websocketLogger.end_printing_operation();
              $window.open(message.url);
            });
          }
        } else {
          websocketLogger.end_printing_operation();
          $window.open(message.url);
        }
      }).catch(function(e) {
        console.error(e);
        websocketLogger.end_printing_operation();
      });
    }).catch(function(e) {
      if (e.message == "An open connection with QZ Tray already exists") {
        qz.printers.find().then(function(data) {
          selected_printer = localStorage.getItem(printer_type);
          if (data.length != 0) {
            printer_available = (selected_printer == null) ? -1 : data.indexOf(selected_printer);
            if (selected_printer == null || printer_available == -1) {
              select_printer(data, message.url, dimensions, printer_type);
            } else {
              var config = qz.configs.create(selected_printer, {
                size: dimensions, units: 'in', 
                colorType: 'grayscale', 
                interpolation: "nearest-neighbor" 
              });
              var data = [{
                    type: 'pixel',
                    format: 'pdf',
                    flavor  : 'file',
                    data: message.url
              }];
              qz.print(config, data).then(function() {
                websocketLogger.end_printing_operation();
              }).catch(function(e) {
                console.error(e);
                websocketLogger.end_printing_operation();
              });
              notification.notify("File Printed using QZ", 1);
            }
          } else {
            websocketLogger.end_printing_operation();
            $window.open(message.url);
          }
        }).catch(function(e) {
          console.error(e);
          websocketLogger.end_printing_operation();
        });
      } else {
        websocketLogger.end_printing_operation();
        $window.open(message.url);
      }
    });
  };

  var select_printer = function (printers, url, dimensions, printer_type) {
    myscope.printer_obj = $modal.open({
      templateUrl: '/assets/views/modals/select_printer.html',
      controller: 'selectprinterCtrl',
      size: 'md',
      resolve: {
        printers: function () {
          return printers;
        },
        url: function () {
          return url;
        },
        dimensions: function () {
          return dimensions;
        },
        printer_type: function() {
          return printer_type;
        }
      }
    });
    myscope.printer_obj.result.then(function(){
    }, function(){
      $window.open(url);
    });
  };



  //Public facing API
  return {
    print_now: print_now
  };
}]);
