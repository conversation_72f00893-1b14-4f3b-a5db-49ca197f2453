groovepacks_services.factory('printing_service', ['$http', 'notification', '$rootScope', 'Idle', '$window', '$modal', function ($http, notification, $rootScope, Idle, $window, $modal) {
  var myscope = {};

  var print_now = function (message, printer_type) {
    var dimensions = { width: 3, height: 1 };
    if (message.dimensions != null && message.dimensions.length > 0) { dimensions = { width: message.dimensions.split('x')[0], height: message.dimensions.split('x')[1] };};
    qz.websocket.connect().then(function() {
      qz.printers.find().then(function(data) {
        selected_printer = localStorage.getItem(printer_type);
        if (data.length != 0) {
          printer_available = (selected_printer == null) ? -1 : data.indexOf(selected_printer);
          if (selected_printer == null || printer_available == -1) {
            select_printer(data, message.url, dimensions, printer_type);
          } else {
            var config = qz.configs.create(selected_printer, {
              size: dimensions, units: 'in', 
              colorType: 'grayscale', 
              interpolation: "nearest-neighbor" 
            });
            var data = [{
                  type: 'pixel',
                  format: 'pdf',
                  flavor  : 'file',
                  data: message.url
            }];
            qz.print(config, data).then(function () {
              notification.notify("File Printed using QZ", 1);
            }).catch(function(e) {
              console.error(e);
              $window.open(message.url);
            });
          }
        } else {
          $window.open(message.url);
        }
      }).catch(function(e) {
        console.error(e); 
      });
    }).catch(function(e) {
      if (e.message == "An open connection with QZ Tray already exists") {
        qz.printers.find().then(function(data) {
          selected_printer = localStorage.getItem(printer_type);
          if (data.length != 0) {
            printer_available = (selected_printer == null) ? -1 : data.indexOf(selected_printer);
            if (selected_printer == null || printer_available == -1) {
              select_printer(data, message.url, dimensions, printer_type);
            } else {
              var config = qz.configs.create(selected_printer, {
                size: dimensions, units: 'in', 
                colorType: 'grayscale', 
                interpolation: "nearest-neighbor" 
              });
              var data = [{
                    type: 'pixel',
                    format: 'pdf',
                    flavor  : 'file',
                    data: message.url
              }];
              qz.print(config, data).catch(function(e) { console.error(e); });
              notification.notify("File Printed using QZ", 1);
            }
          } else {
            $window.open(message.url);
          }
        }).catch(function(e) {
          console.error(e); 
        });
      } else {
        $window.open(message.url);
      }
    });
  };

  var select_printer = function (printers, url, dimensions, printer_type) {
    myscope.printer_obj = $modal.open({
      templateUrl: '/assets/views/modals/select_printer.html',
      controller: 'selectprinterCtrl',
      size: 'md',
      resolve: {
        printers: function () {
          return printers;
        },
        url: function () {
          return url;
        },
        dimensions: function () {
          return dimensions;
        },
        printer_type: function() {
          return printer_type;
        }
      }
    });
    myscope.printer_obj.result.then(function(){
    }, function(){
      $window.open(url);
    });
  };



  //Public facing API
  return {
    print_now: print_now
  };
}]);
