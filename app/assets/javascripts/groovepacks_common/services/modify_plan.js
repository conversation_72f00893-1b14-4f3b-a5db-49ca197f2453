groovepacks_services.factory('modify_plan', ['$http', '$window', 'notification', '$q', '$rootScope', function ($http, 
  $window, notification, $q, $rootScope) {

var get_default = function () {
    return {
      single: {
        method: 'receive'
      }
  };
};


 var update_plan = function(data,data1,data2){
    $http.get($rootScope.api_url + '/users/modify_plan?users='+ data +'&amount=' + data1 + '&is_annual=' + data2).success(function (data) {
      if (data.status) {
        notification.notify("Successfully Updated!", 1);
        if (data.request_send) {
         notification.notify("A removal request has been sent. The GroovePacker team will make the change and your billing will be updated.When the change is complete it will be reflected below.", 1);
        }
        if (data.annual_request) {
          notification.notify("Thank you for choosing to move to annual billing. We have received your request and will update your account momentarily.", 1);
        }
      } else {
        notification.notify(data.error_messages);
      }
    });
 };


 return {
    model: {
      get: get_default,
      update_plan : update_plan
    }
  }
}]);