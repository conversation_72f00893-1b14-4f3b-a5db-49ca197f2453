groovepacks_admin_controllers.
  controller('tenantsSingleModal', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$modal', '$modalInstance', 'hotkeys', 'tenant_data', 'load_page', 'tenant_id', 'tenants',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $cookies, $modal, $modalInstance, hotkeys, tenant_data, load_page, tenant_id, tenants) {
      //Definitions

      var myscope = {};
      $scope.currentPage = 1;
      $scope.recordsPerPage = 20;
      $scope.totalPages = 0;
      $scope.pages = [];
      $scope.hasMoreRecords = true;
      $scope.searchQuery = '';
      /*
       * Public methods
       */

      $scope.ok = function () {
        $modalInstance.close("ok-button-click");
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.update = function (reason) {
        hotkeys.del('up');
        hotkeys.del('down');
        if (reason == "cancel-button-click" || reason == "ok-button-click") {
          myscope.rollback();
        }
      };

      $scope.update_access_restrictions = function () {
        // if(status==true || $scope.tenants.single.subscription_info.shopify_customer==null){
          tenants.single.update_access($scope.tenants).then(function () {
            myscope.load_item($scope.tenants.current);
          });
        // }
      };

      $scope.modal_tabs = [
        {
          active: true,
          heading: "Tenant Settings",
          templateUrl: '/assets/admin_views/modals/tenants/tenant_settings.html',
          onSelect: function() {
            myscope.init();
          }
        },
        {
          active: false,
          heading: "Tenant Activity V2",
          templateUrl: '/assets/admin_views/modals/tenant_activity_logs.html',
          onSelect: function() {
            myscope.get_tenant_activity_v2();
          }
        }
      ];

      $scope.update_zero_plan = function () {
        if (confirm('Do you want to update zero subscription plan?')) {
          tenants.single.update_zero_plan($scope.tenants).then(function () {
            myscope.load_item($scope.tenants.current);
          });
        }
      };

      $scope.allowinventory_pull_push_all = function () {
        tenants.single.update_access($scope.tenants).then(function () {
          myscope.load_item($scope.tenants.current);
        });
      };

      $scope.update_price_field = function (feature, value) {
        tenants.single.update_price_field($scope.tenants.single.basicinfo.id, feature, value);
      };

      $scope.update_scheduled_import_toggle = function(){
        tenants.single.update_scheduled_import_toggle($scope.tenants.single.basicinfo.id);
      }

      $scope.update_tenant_settings = function (setting) {
        tenants.single.update_tenant_settings($scope.tenants.single.basicinfo.id, setting);
      }

      $scope.update_groovelytic_stat = function () {
        tenants.single.update_groovelytic_stat($scope.tenants.single.basicinfo.id);
      }

      $scope.update_scan_workflow = function (value) {
        tenants.single.update_scan_workflow($scope.tenants.single.basicinfo.id, value).then(function () {
          myscope.tenant_single_details($stateParams.tenant_id);
        });
      };

      $scope.update_store_order_respose_log = function () {
        tenants.single.update_store_order_respose_log($scope.tenants.single.basicinfo.id);
      }

      $scope.delete_orders = function () {
        $scope.delete('orders');
      };

      $scope.delete_products = function () {
        $scope.delete('products');
      };

      $scope.delete_orders_and_products = function () {
        $scope.delete('both');
      };

      $scope.delete_all = function () {
        $scope.delete('all');
      };

      $scope.reset_inventory = function () {
        $scope.delete('inventory');
      }

      $scope.delete = function (type) {
        myscope.tenant_obj = $modal.open({
          templateUrl: '/assets/admin_views/modals/tenants/delete.html',
          controller: 'tenantsDeleteModal',
          size: 'md',
          resolve: {
            tenant_data: function () {
              return $scope.tenants
            },
            load_page: function () {
              return function () {
                var req = $q.defer();
                req.reject();
                return req.promise;
              };
            },
            deletion_type: function () {
              return type;
            }
          }
        });
        myscope.tenant_obj.result.finally(function () {
          myscope.load_item($scope.tenants.current);
        });
      };

      myscope.rollback = function () {
        myscope.single = {};
        angular.copy($scope.tenants.single, myscope.single);
      };

      myscope.tenant_single_details = function (id) {
        for (var i = 0; i < $scope.tenants.list.length; i++) {
          if ($scope.tenants.list[i].id == id) {
            $scope.tenants.current = parseInt(i);
            break;
          }
        }

        tenants.single.get(id, $scope.tenants).success(function (data) {
        });
      };

      myscope.get_tenant_activity_v2 = function () {
        const page = $scope.currentPage;
        const offset = (page - 1) * $scope.recordsPerPage;

        tenants.single.get_activity_logs($stateParams.tenant_id, $scope.tenants, offset, $scope.recordsPerPage, $scope.searchQuery).success(function (response) {
          
          $scope.totalPages = Math.ceil(response.tenant.total_activity_log / $scope.recordsPerPage);
          $scope.updatePagination();
          $scope.hasMoreRecords = page < $scope.totalPages;
        });
      };
    
      $scope.nextPage = function() {
        if ($scope.hasMoreRecords) {
          $scope.currentPage++;
          myscope.get_tenant_activity_v2();
        }
      };
    
      $scope.prevPage = function() {
        if ($scope.currentPage > 1) {
          $scope.currentPage--;
          myscope.get_tenant_activity_v2();
        }
      };

      $scope.goToPage = function(page) {
        if (page === '...') return;
        $scope.currentPage = page;
        myscope.get_tenant_activity_v2();
      };

      $scope.triggerSearchOnEnter = function(event) {
        $scope.searchQuery = event.target.value;
        $scope.currentPage = 1;
        myscope.get_tenant_activity_v2();
      };

      $scope.updatePagination = function() {
        const pages = [];
        const maxPagesToShow = 12;
        const currentBlock = Math.floor(($scope.currentPage - 1) / maxPagesToShow);
        const totalBlocks = Math.ceil($scope.totalPages / maxPagesToShow);
        
        if (currentBlock == 0) {
          pages.push(1);
        }
    
        if (currentBlock > 0) {
          pages.push('...');
        }
    
        const startPage = currentBlock * maxPagesToShow + 1;
        const endPage = Math.min(startPage + maxPagesToShow - 1, $scope.totalPages);
    
        for (var i = startPage; i <= endPage; i++) {
          if (i > 1 && i <= $scope.totalPages) {
            pages.push(i);
          }
        }
    
        if (currentBlock < totalBlocks - 1 && endPage < $scope.totalPages - 1) {
          pages.push('...');
        }
        
        $scope.pages = pages;
      };

      myscope.down_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if ($scope.tenants.current < $scope.tenants.list.length - 1) {
          myscope.load_item($scope.tenants.current + 1);
        } else {
          load_page('next').then(function () {
            myscope.load_item(0);
          }, function () {
            alert("Already at the bottom of the list");
          });
        }
      };
      myscope.up_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if ($scope.tenants.current > 0) {
          myscope.load_item($scope.tenants.current - 1);
        } else {
          load_page('previous').then(function () {
            myscope.load_item($scope.tenants.list.length - 1);
          }, function () {
            alert("Already at the top of the list");
          });
        }
      };

      myscope.add_hotkeys = function () {
        hotkeys.del('up');
        hotkeys.del('down');
        hotkeys.del('esc');
        $timeout(function () {
          hotkeys.bindTo($scope).add({
            combo: 'up',
            description: 'Previous tenant',
            callback: myscope.up_key
          })
            .add({
              combo: 'down',
              description: 'Next tenant',
              callback: myscope.down_key
            }).add({
              combo: 'esc',
              description: 'Save and close modal',
              callback: function () {
              }
            });
        }, 2000);
      };

      myscope.load_item = function (id) {
        myscope.tenant_single_details($scope.tenants.list[id].id);
        if (myscope.update_state) {
          var newStateParams = angular.copy($stateParams);
          newStateParams.tenant_id = "" + $scope.tenants.list[id].id;
          $state.go($state.current.name, newStateParams);
        }
      };

      myscope.init = function () {
        $scope.searchQuery = '';
        $scope.currentPage = 1;
        $('.activity-search').val('');

        $scope.tenants = tenant_data;

        myscope.add_hotkeys();
        if (tenant_id) {
          myscope.update_state = false;
          myscope.tenant_single_details(tenant_id);
        } else {
          myscope.update_state = true;
          myscope.tenant_single_details($stateParams.tenant_id);
        }
        $modalInstance.result.then($scope.update, $scope.update);
      };

      myscope.init();

    }]);
