groovepacks_controllers.
  controller('selectprinterCtrl', ['$scope', '$http', 'notification', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$modal', '$modalInstance', 'printers', 'url', 'dimensions', 'printer_type',
    function ($scope, $http, notification, $timeout, $stateParams, $location, $state, $cookies, $modal, $modalInstance, printers, url, dimensions, printer_type) {
      //Definitions

      var myscope = {};
      /*
       * Public methods
       */

      $scope.ok = function () {
        if ($scope.default_printer_name != null) {
          $scope.print();
          localStorage.setItem($scope.printer_type, $scope.default_printer_name);
        } else {
          $modalInstance.dismiss("cancel-button-click");
        }
        $modalInstance.close("ok-button-click");
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.change_printer = function(val) {
        $scope.default_printer_name = val;
      };

      $scope.print = function() {

        var config = qz.configs.create($scope.default_printer_name, {
          size: $scope.dimensions, units: 'in',
          colorType: 'grayscale', 
          interpolation: "nearest-neighbor" 
        });
        var data = [{
              type: 'pixel',
              format: 'pdf',
              flavor  : 'file',
              data: $scope.print_url
        }];
        qz.print(config, data).catch(function(e) { console.error(e); });
        notification.notify("File Printed using QZ", 1);
      }

      myscope.init = function () {
        $scope.default_printer_name = null;
        $scope.printers_name = printers;
        $scope.print_url = url;
        $scope.dimensions = dimensions;
        $scope.printer_type = printer_type;
      };

      myscope.init();

    }]);
