groovepacks_controllers.
  run(['Idle', '$rootScope', function(Idle, $rootScope) {
    Idle.watch();
    $rootScope.$on('IdleTimeout', function() {
      // end their session and redirect to login
      if($rootScope.current_user.isLoggedIn()){
        $rootScope.$emit('sign_out');
      }
    });
  }]).
  controller('appCtrl', ['$http', '$rootScope', '$modal','$scope', '$timeout', '$modalStack', '$state', '$filter', '$document', '$window',
    'hotkeys', 'auth', 'notification', 'importOrders', 'groovIO', 'editable', 'stores', '$interval', 'ngClipboard', 'printing_service', '$sanitize', '$location',
    function ($http, $rootScope, $modal,$scope, $timeout, $modalStack, $state, $filter, $document, $window,
      hotkeys, auth, notification, importOrders, groovIO, editable, stores, $interval, ngClipboard, printing_service, $sanitize, $location) {
      $scope.import_store_id = null;
      $scope.bc_deep_import_days = 1;
      $scope.se_deep_import_days = 1;
      $scope.ss_deep_import_days = 1;
      $scope.tp_deep_import_days = 1;
      $scope.mg_deep_import_days = 1;
      $scope.import_progress = 1;
      $scope.check_fix_it = false;
      $scope.not_started_summary = 0;
      $scope.countdown_progressbar = 0;
      $scope.import_item_id = 0;
      $scope.search_order = null
      $scope.total_imported = null;
      $scope.remaining_items = null;
      $scope.run_by = null;
      $scope.import_start = null;
      $scope.import_end = null;
      $scope.processed_orders = null;
      $scope.completed = null;
      $scope.in_progess = null;
      $scope.elapsed_time = null;
      $scope.search_order = null
      $scope.elapsed_time_remaining = null;
      $scope.view_status = false;
      $scope.no_new_order =false;
      $scope.last_imported_data = null;
      $scope.add_popup_summary_on_load = true;
      $scope.modalOpen = false;
      $rootScope.current_user = auth;
      $scope.$on("user-data-reloaded", function () {
        $rootScope.current_user = auth;
      });

      $rootScope.$on("sign_out", function(){
        $scope.sign_out();
      })

      $rootScope.$on("connect-to-socket-server", function(){
        if (auth.isLoggedIn()) {
          // groovIO.on('countdown_update', function (progress_info) {
          //   if($scope.import_item_id==0){ $scope.import_item_id = progress_info.progress_info.id;}
          //   if($scope.import_item_id!=progress_info.progress_info.id) {
          //     $scope.import_progress = 0 ;
          //     $scope.countdown_progressbar = 0;
          //     $scope.import_item_id = progress_info.progress_info.id;
          //   }
          //   $scope.import_progress = $scope.import_progress + 1
          //   $scope.countdown_progressbar = ($scope.import_progress)*10
          //   if (($scope.import_progress == 11) ) {
          //     $scope.import_progress = 1 ;
          //     $scope.countdown_progressbar = 0;
          //   }
          //   $scope.countdown_progressbar
          // });

          groovIO.on('import_status_update', function (message) {

            if (typeof(message) !== 'undefined') {
              $scope.import_summary = angular.copy(message);
              $scope.import_groov_popover = {title: '', content: '', data: []};
              var get_import_type = function () {
                if ($scope.import_summary.import_info.import_summary_type === 'update_locations') {
                  return ("Update");
                } else {
                  return (" Import");
                }
              };

              var get_import_type_past = function () {
                if ($scope.import_summary.import_info.import_summary_type === 'update_locations') {
                  return ("Updated");
                } else {
                  return ("Imported");
                }
              };

              var get_last_imported_type = function () {
                if ($scope.import_summary.import_info.import_summary_type === 'update_locations') {
                  return ("Updated");
                } else {
                  return ("Checking");
                }
              };

              if ($scope.import_summary.import_info.status === 'completed') {
                try{
                  $scope.import_summary.import_info.updated_at = $scope.import_summary.import_info.updated_at.replace("T", " ").replace("Z", "").slice(0, -6);
                }catch(e){
                  $scope.import_summary.import_info.updated_at = new Date($scope.import_summary.import_info.updated_at).toString().split("GMT")[0];
                }
                date = Date.parse($scope.import_summary.import_info.updated_at)
                $scope.import_groov_popover.title = 'Last ' + get_import_type() + ': ' +
                  $filter('date')(date,
                    'EEE MM/dd/yy hh:mm a');
              } else if ($scope.import_summary.import_info.status === 'in_progress') {
                $scope.import_groov_popover.title = get_import_type() + ' in Progress';
              } else if ($scope.import_summary.import_info.status === 'not_started') {
            $scope.not_started_summary = importOrders.update_order_import($scope.not_started_summary);
                $scope.import_groov_popover.title = 'Waking up the ' + get_import_type() + ' Hamster...';
              } else if ($scope.import_summary.import_info.status === 'cancelled') {
                $scope.import_groov_popover.title = get_import_type() + ' cancelled';
              }

              var logos = {
                Ebay: {
                  alt: "Ebay",
                  src: "https://s3.amazonaws.com/groovepacker/EBAY-BUTTON.png"
                },
                Amazon: {
                  alt: "Amazon",
                  src: "https://s3.amazonaws.com/groovepacker/amazonLogo.jpg"
                },
                Magento: {
                  alt: "Magento",
                  src: "https://s3.amazonaws.com/groovepacker/MagentoLogo.jpg"
                },
                "Magento API 2": {
                  alt: "Magento API 2",
                  src: "/assets/images/MagentoLogo.jpg"
                },
                "Shipstation API 2": {
                  alt: "ShipStation",
                  src: "/assets/images/ShipStation_logo.png"
                },
                Shipworks: {
                  alt: "ShipWorks",
                  src: "/assets/images/shipworks_logo.png"
                },
                CSV: {
                  alt: "CSV",
                  src: "/assets/images/csv_logo.png"
                },
                Shopify: {
                  alt: "CSV",
                  src: "/assets/images/shopify_import.png"
                },
                Shopline: {
                  alt: "CSV",
                  src: "/assets/images/shopline_logo.svg"
                },
                "BigCommerce": {
                  alt: "BigCommerce",
                  src: "/assets/images/bigcommerce-logo.png"
                },
                "ShippingEasy": {
                  alt: "ShippingEasy",
                  src: "/assets/images/shipping_easy.png"
                },
                "Teapplix": {
                  alt: "Teapplix",
                  src: "/assets/images/teapplix-logo.png"
                },
                "Shippo": {
                  alt: "Shippo",
                  src: "/assets/images/shippo-icon.png"
                },
                "Veeqo": {
                  alt: "Veeqo",
                  src: "/assets/images/veeqo-new-logo.png"
                }

              };
              for (var i = 0; i < $scope.import_summary.import_items.length; i++) {
                var import_item = $scope.import_summary.import_items[i];
                if (import_item && import_item.store_info) {
                  var single_data = {progress: {}, progress_product: {}, name: ''};
                  single_data.logo = logos[import_item.store_info.store_type];
                  single_data.name = import_item.store_info.name;
                  single_data.id = import_item.store_info.id;
                  single_data.store_type = import_item.store_info.store_type;
                  single_data.troubleshooter_option = import_item.store_info.troubleshooter_option;
                  single_data.status = import_item.store_info.status;
                  single_data.progress.type = import_item.import_info.status;
                  error_mesage = "Message: " + import_item.import_info.message + "\n";
                  error_mesage = error_mesage + "Error Trace: " + import_item.import_info.import_error + "\n";
                  try{
                    single_data.progress.error = $sanitize(error_mesage);
                  }catch(e){
                    single_data.progress.error = error_mesage
                  }
                  single_data.progress.summary = $scope.import_summary.summary
                  single_data.progress.value = 0;
                  single_data.progress.message = '';
                  single_data.progress_product.show = false;
                  single_data.progress_product.value = 0;
                  single_data.progress_product.message = '';
                  single_data.progress_product.type = 'in_progress';

                  if (import_item.import_info.status === 'completed' || import_item.import_info.status === 'cancelled') {
                    try{
                      import_item.import_info.updated_at = import_item.import_info.updated_at.replace("T", " ").replace("Z", "").slice(0, -6);
                    }catch(e){
                      import_item.import_info.updated_at = new Date(import_item.import_info.updated_at).toString().split("GMT")[0];
                    }
                    date = Date.parse(import_item.import_info.updated_at)
                    single_data.progress.message += 'Last ' + import_item.import_info.status + ' at ' + $filter('date')(date, 'dd MMM hh:mm a ');
                    single_data.progress.value = 100;
                    if (import_item.store_info.store_type === 'CSV') {
                      if(import_item.store_info.csv_beta == true) {
                        //single_data.progress.message += 'Last ' + get_import_type_past() + ' at ' + $filter('date')(date, 'dd MMM hh:mm a');
                      } else {
                        single_data.progress.message += 'Last ' + get_import_type_past() + ' Order #' + import_item.import_info.current_increment_id + ' at ' + $filter('date')(date, 'dd MMM hh:mm a');
                      }
                    } else if (import_item.store_info.store_type === 'Shipworks') {
                      //single_data.progress.message += 'Last ' + get_import_type_past() + ' Order #' + import_item.import_info.current_increment_id + ' at ' + $filter('date')(date, 'dd MMM hh:mm a');
                    } else if (import_item.import_info.success_imported <= 0) {
                      if ($scope.import_summary.import_info.import_summary_type === 'update_locations') {
                        single_data.progress.message += ' No updates made. Locations are upto date.';
                      } else if(import_item.import_info.updated_orders_import > 0){
                        single_data.progress.message += import_item.import_info.updated_orders_import + ' Orders were updated.';
                      } else {
                        single_data.progress.message += ' No new orders found.';
                      }
                    } else {
                      if ($scope.import_summary.import_info.import_summary_type === 'update_locations') {
                        single_data.progress.message += import_item.import_info.success_imported + ' Orders were updated.';
                      } else if(import_item.import_info.updated_orders_import > 0){
                        single_data.progress.message += import_item.import_info.success_imported + ' New Orders Imported and ';
                        single_data.progress.message += import_item.import_info.updated_orders_import + ' Orders were updated.';
                      } else {
                        single_data.progress.message += import_item.import_info.success_imported + ' New Orders Imported.';
                      }
                    }
                    if (import_item.import_info.status === 'cancelled') {
                      single_data.progress.message += ' The import was cancelled.';
                    }
                  } else if (import_item.import_info.status === 'not_started') {
                    single_data.progress.message += 'Import has been queued';
                  } else if (import_item.import_info.status === 'in_progress') {
                    $scope.import_summary.import_info.status = 'in_progress';
                    try{
                      import_item.import_info.updated_at = import_item.import_info.updated_at.replace("T", " ").replace("Z", "").slice(0, -6);
                    }catch(e){
                      import_item.import_info.updated_at = new Date(import_item.import_info.updated_at).toString().split("GMT")[0];
                    }
                    date = Date.parse(import_item.import_info.created_at)
                    single_data.progress.message += 'Current import started at ' + $filter('date')(date, 'dd MMM hh:mm a');
                    if (import_item.import_info.to_import > 0) {
                      //single_data.progress.value = (((import_item.import_info.success_imported + import_item.import_info.previous_imported) / import_item.import_info.to_import) * 100);
                      //single_data.progress.message += get_last_imported_type() + ' ' +
                        //(import_item.import_info.success_imported + import_item.import_info.previous_imported) + '/' + import_item.import_info.to_import + ' Orders ';
                      // if (import_item.import_info.current_increment_id !== '') {
                      //   single_data.progress.message += 'Current #' + import_item.import_info.current_increment_id + ' ';
                      // }
                      if(import_item.store_info.csv_beta == false){
                        single_data.progress_product.show = true;
                      }
                      if (import_item.import_info.current_order_items > 0) {
                        single_data.progress_product.show = true;
                        single_data.progress_product.value = (import_item.import_info.current_order_imported_item / import_item.import_info.current_order_items) * 100;
                        single_data.progress_product.message += get_last_imported_type() + ' ' +
                          import_item.import_info.current_order_imported_item + '/' + import_item.import_info.current_order_items + ' Products';
                      } else {
                        single_data.progress_product.value = 0;
                      }
                      if (single_data.progress_product.value === 0) {
                        if (import_item.import_info.current_order_items <= 0) {
                          single_data.progress_product.type = 'not_started';
                          single_data.progress_product.message = 'waiting on source...';
                        }
                      } else if (single_data.progress_product.value === 100) {
                        single_data.progress_product.type = 'completed';
                      }
                    } else {
                      single_data.progress.message += get_import_type() + ' in progress.';
                    }
                  } else if (import_item.import_info.status === 'failed') {
                    single_data.progress.value = 100;
                    if (import_item.import_info.message !== '') {
                      single_data.progress.message = import_item.import_info.message;
                    }
                  } else if (import_item.import_info.status === 'cancelled') {
                    single_data.progress.value = 100;
                  } else {
                    single_data.progress.value = 100;
                    single_data.progress.type = 'completed';
                    if (import_item.import_info.message !== '') {
                      single_data.progress.message = import_item.import_info.message;
                    }
                  }
                  $scope.import_groov_popover.data.push(single_data);
                }
              }
              $scope.import_groov_popover.content =
                '<table style="font-size: 12px;width:100%;">' +
                  '<tr ng-repeat="store in import_groov_popover.data" ng-hide="!store.status">' +
                    '<td width="60px;" style="white-space: nowrap;">' +
                      '<a class="btn" title="{{store.name}}" href="#/settings/stores/{{store.id}}"><img ng-src="{{store.logo.src}}" width="60px" alt="{{store.logo.alt}}"/></a>' +
                    '</td>' +
                    '<td style="width:62%;padding:3px;">' +
                     '<progressbar type="{{store.progress.type}}" value="store.progress.value"> {{store.progress.message| limitTo: 86}} <button style="background: #339900; text-transform: capitalize;border-radius: 4px; border: 0; position: initial;" ng-show="import_summary.import_info.status==\'in_progress\' && store.progress.type==\'in_progress\'" ng-click="get_import_details(store.id)">Check Status</button></progressbar>' +
                     '<table style="width: 100%; font-size:14px; color: #fff;background-color: #339900;  "ng-show="(import_summary.import_info.status==\'in_progress\' && store.progress.type==\'in_progress\' && no_new_order == \'no_new_order_{{store.id}}\')">' +
                        '<tr><td colspan="2" align="center" style="font-weight:bold;">Checking for Available Orders</td></tr>'+
                      '</table>'+
                      '<table class="store_import_status_{{store.id}}" style="width: 100%; font-size:14px; color: #fff;background-color: #339900;  "ng-show="(import_summary.import_info.status==\'in_progress\' && store.progress.type==\'in_progress\' && view_status == \'view_status_{{store.id}}\')">' +
                        '<tr><td colspan="2" align="center" style="font-weight:bold;">Import Status</td></tr>'+
                        '<tr>'+
                          '<td style="text-align: left; padding: 0 10px;">'+
                            '<div>Elapsed Time:<span>{{elapsed_time}}</span></div>'+
                            '<div>Orders Processed:<span style="color: #fff; text-decoration: underline;" ng-click="show_processed_orders(processed_orders)">{{total_imported}}</span></div>'+
                            '<div ng-hide= "store.store_type==\'CSV\'" >Completed:<span>{{completed}}</span></div>'+
                          '</td>'+
                          '<td style="text-align: right; padding: 0 10px;">'+
                            '<div>Elapsed Time Remaining:<span>{{elapsed_time_remaining}}</span></div>'+
                            '<div>Orders Remaining:<span style="color: #fff; text-decoration: underline;" ng-click="show_remaining_orders(run_by, import_start, import_end)">{{remaining_items}}</span></div>'+
                            '<div ng-hide= "store.store_type==\'CSV\'">In Progress:<span>{{in_progess}}</span></div>'+
                          '</td>'+
                        '</tr>'+
                        '<tr>' +
                          '<td colspan="2" align="center">' +
                             '<div>Last Import Completed at  :<span>{{last_imported_data}}</span></div>'+
                          '</td>' +
                        '</tr>'+
                      '</table>'+
                       '<p style="display:flex;white-space:nowrap;line-height:35px;" ng-show="(import_summary.import_info.status!=\'in_progress\' && (store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.store_type==\'Shopify\' || store.store_type==\'Shopline\' || store.store_type==\'Shippo\' || store.store_type==\'Veeqo\'))">' +' Missing Orders? Scan or enter order number here:' + '<input type="text" style="height:25px;margin:6px 0 0 5px;" ng-show="(import_summary.import_info.status!=\'in_progress\' && (store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.store_type==\'Shopify\' || store.store_type==\'Shopline\' || store.store_type==\'Shippo\' || store.store_type==\'Veeqo\'))" ng-model= "search_order" ng-keydown="send_order_no($event, search_order,store.id)" />' +
                      '</p>' +
                    '</td>' +
                    // '<td ng-if="store.progress.type == \'failed\'" style="width:30px; cursor:pointer;" >' +
                    //   '<span class= "set_btn" ng-click="trigger(store.progress.error,store.progress.summary,$event, store.id)" tooltip="Click to copy the error details to your clipboard">' +
                    //     '<i class="fa fa-files-o" aria-hidden="true"></i>' +
                    //     '<a class="btn set-paddings" title="Delete summary" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="delete_import_summary(store.id)" style=""><i class="fas fa-trash del_btn"></i></a>' +
                    //   '</span>'+
                    // '</td>' +
                    '<td ng-if="store.progress.type != \'failed\'" style="width:30px; cursor:pointer;" >' +
                      '<span class= "set_btn" >' +
                      '<a class="btn set-paddings" title="Copy summary" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="check(store.progress.summary,$event,store.id)" tooltip="Click to copy the import details to your clipboard" style="text-decoration: none;"><i class="fa fa-files-o"></i></a>' +
                        '<a class="btn set-paddings" title="Delete summary" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="delete_import_summary(store.id)" tooltip="Delete Import Summary" style=""><i class="fas fa-trash del_btn"></i></a>' +
                      '</span>'+
                    '</td>' +
                    '<td style="text-align:left;width:38%;padding:3px;">' +
                      '<div class="btn-group">' +
                        '<div ng-show="store.store_type==\'BigCommerce\'" style="display: flex;">' +
                          '<a class="btn" ng-hide="import_summary.import_info.status==\'in_progress\'" title="Regular Import" ng-click="issue_import(store.id, 4, \'regular\')"><img class="icons" src="/assets/images/reg_import.png"></img></a>' +
                          '<div ng-hide="import_summary.import_info.status==\'in_progress\'" ng-mouseover="show_days_select(store, true)" ng-mouseleave="show_days_select(store, false)" style="width: 120px;">' +
                            '<a class="btn" title="Deep Import" ng-click="issue_import(store.id, store.days, \'deep\')" style="float: left;"><img class="icons" src="/assets/images/deep_import.png"></img></a>' +
                            '<input type="number" ng-model="store.days" ng-value="{{bc_deep_import_days}}" data-import="{{store.id}}" ng-mouseleave="check_days_value(store)" max="30" style="font-size: 15px;height: 30px;width: 50px;"/>' +
                          '</div>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'ShippingEasy\'" style="display: flex;">' +
                          '<a class="btn" ng-hide="import_summary.import_info.status==\'in_progress\'" title="Regular Import" ng-click="issue_import(store.id, 4, \'regular\')"><img class="icons" src="/assets/images/reg_import.png"></img></a>' +
                          '<a class="btn" title="Find Missing Orders" ng-show="store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.troubleshooter_option == true" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                          '<div  ng-hide="import_summary.import_info.status==\'in_progress\'" ng-mouseover="show_days_select(store, true)" ng-mouseleave="show_days_select(store, false)" style="width: 120px;">' +
                            '<a class="btn" title="Deep Import" ng-hide="store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.troubleshooter_option == true" ng-click="issue_import(store.id, store.days, \'deep\')" style="float: left;"><img class="icons" src="/assets/images/deep_import.png"></img></a>' +
                            '<input ng-hide="store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.troubleshooter_option == true" type="number" ng-model="store.days" ng-value="{{se_deep_import_days}}" data-import="{{store.id}}" ng-mouseleave="check_days_value(store)" max="30" style="font-size: 15px;height: 30px;width: 50px;"/>' +
                          '</div>' +
                          '<a class="btn" title="Fix It" ng-hide="store.store_type==\'Shipstation API 2\' || store.store_type==\'ShippingEasy\' || store.troubleshooter_option == true" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Magento\'" style="display: flex;">' +
                          '<a class="btn" ng-hide="import_summary.import_info.status==\'in_progress\'" title="Regular Import" ng-click="issue_import(store.id, 4, \'regular\')"><img class="icons" src="/assets/images/reg_import.png"></img></a>' +
                          '<div  ng-hide="import_summary.import_info.status==\'in_progress\'" ng-mouseover="show_days_select(store, true)" ng-mouseleave="show_days_select(store, false)" style="width: 120px;">' +
                            '<a class="btn" title="Deep Import" ng-click="issue_import(store.id, store.days, \'deep\')" style="float: left;"><img class="icons" src="/assets/images/deep_import.png"></img></a>' +
                            '<input type="number" ng-model="store.days" ng-value="{{mg_deep_import_days}}" data-import="{{store.id}}" ng-mouseleave="check_days_value(store)" max="30" style="font-size: 15px;height: 30px;width: 50px;"/>' +
                          '</div>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Shipstation API 2\'" style="display:inline-block;float:left;">' +
                          '<a class="btn" ng-hide="import_summary.import_info.status==\'in_progress\'" title="Tagged Import" ng-click="issue_import(store.id, 7, \'tagged\')"><img class="icons" src="/assets/images/tagged_import.png"></img></a>' +
                           '<a class="btn" title="Find Missing Orders" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Teapplix\'" style="display: flex;">' +
                          '<div  ng-hide="import_summary.import_info.status==\'in_progress\'" ng-mouseover="show_days_select(store, true)" ng-mouseleave="show_days_select(store, false)" style="width: 120px;">' +
                            '<a class="btn" title="Deep Import" ng-click="issue_import(store.id, store.days, \'deep\')" style="float: left;"><img class="icons" src="/assets/images/reg_import.png"></img></a>' +
                            '<input type="number" ng-model="store.days" ng-value="{{tp_deep_import_days}}" data-import="{{store.id}}" ng-mouseleave="check_days_value(store)" max="30" style="font-size: 15px;height: 30px;width: 50px;"/>' +
                          '</div>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Amazon\'" style="display: flex;">' +
                          '<div  ng-hide="import_summary.import_info.status==\'in_progress\'" ng-mouseover="show_days_select(store, true)" ng-mouseleave="show_days_select(store, false)" style="display: flex;">' +
                            '<a class="btn" title="Deep Import" ng-click="issue_import(store.id, store.days, \'deep\')" style="float: left;"><img class="icons" src="/assets/images/reg_import.png"></img></a>' +
                            '<input type="number" ng-model="store.days" ng-value="{{am_deep_import_days}}" data-import="{{store.id}}" ng-mouseleave="check_days_value(store)" max="30" style="font-size: 15px;height: 30px;width: 50px;"/>' +
                          '</div>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>'+
                        '<div ng-show="store.store_type==\'Shopify\'" style="display: flex;">' +
                          '<a class="btn" title="Find Missing Orders" ng-show="store.store_type==\'Shopify\'" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="open_lro_updater(store.id, store.store_type)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Shopline\'" style="display: flex;">' +
                          '<a class="btn" title="Find Missing Orders" ng-show="store.store_type==\'Shopline\'" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="open_lro_updater(store.id, store.store_type)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Shippo\'" style="display: flex;">' +
                           '<a class="btn" title="Find Missing Orders" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                           '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="open_lro_updater(store.id, store.store_type)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Veeqo\'" style="display:inline-block;float:left;">' +
                           '<a class="btn" title="Find Missing Orders" ng-click="open_troubleshooter(store.id, store.store_type)"><img class="icons" src="/assets/images/magnifying_glass_icon.png"></img></a>' +
                        '</div>' +
                        '<div ng-show="store.store_type==\'Magento API 2\'" style="display: flex;">' +
                          '<a class="btn" title="Fix It" ng-hide="import_summary.import_info.status==\'in_progress\'" ng-click="fix_imported_at(store.id)" style="float: left;"><img class="icons" src="/assets/images/importsummary_fix_it_b.png"></img></a>' +
                        '</div>' +
                        '<a class="btn" ng-show="import_summary.import_info.status==\'in_progress\' && import_summary.import_info.import_summary_type != \'update_locations\'" title="Cancel Import" ng-click="cancel_import(store.id)"><img class="icons" src="/assets/images/cancel_import.png"></img></a>' +
                      '</div>' +
                    '</td>' +
                  '</tr>' +
                '</table>';
            }
          });
          groovIO.on('popup_display_for_on_demand_import', function (resp) {
            notification.notify(resp.message);
          });


          groovIO.on('access_token_message', function (resp) {
            notification.notify(resp.message);
          });

          $scope.shown_force_logout = false;
          groovIO.on('force_logout', function (resp) {
            if ($scope.shown_force_logout == false && resp.username == $scope.current_user_data.username) {
              $scope.shown_force_logout = true;
              notification.notify('Detected login from another location', 0);
              $timeout(function () {
                groovIO.log_out({message: ''});
              }, 1000);
            }
          });

          $scope.show_logout_box = false;
          groovIO.on('ask_logout', function (msg) {
            if (!$scope.show_logout_box) {
              notification.notify(msg.message);
              $scope.show_logout_box = true;
            }
          });

          groovIO.on('hide_logout', function (msg) {
            if ($scope.show_logout_box) {
              notification.notify(msg.message, 1);
              $scope.show_logout_box = false;
            }
          });

          groovIO.on('csv_already_imported_warning', function (resp) {
            notification.notify(resp.message);
          });

          groovIO.on('notification_all_status_disabled', function (resp, event) {
            if ($scope.current_user_data.id == resp.user_id){
              notification.notify(resp.message);
            }
          });

          $scope.show_msg = true;
          groovIO.on('print_lable', function (url) {
            if($scope.show_msg == true){
              notification.notify("Labels generated Successfully", 1);
              $window.open(url);
              $scope.show_msg = false;
            }
          });

          $scope.show_msg = true;
          groovIO.on('barcode_lable', function (data) {
            if($scope.show_msg == true && $scope.current_user_data.username == data['username']){
              notification.notify("Barcode Labels generated successfully", 1);
              $scope.show_msg = false;
              $timeout( function(){
                var settings = {};
                settings = JSON.parse(localStorage.getItem('general_settings'));
                if (settings.direct_printing_options && settings.data.settings.print_product_barcode_labels) {
                  printing_service.print_now({ url: data['url'] }, 'user_selected_printer_product_barcode_labels_' + settings.data.settings.product_barcode_label_size);
                } else {
                  $window.open(data['url']);
                  $scope.show_msg = true;
                }
              }, 1000 );
              if (data['last_batch'] == true){
                $scope.show_msg = false;
              }
            }
          });

          groovIO.on('notification_default_import_date', function (resp) {
            console.log(resp)
            if($scope.current_user_data.id == resp.user_id) {
              var alertModal = $modal.open({
                templateUrl: '/assets/views/modals/alert_modal.html',
                controller: 'alertModalCtrl',
                size: 'small',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                  // message: function() { return resp.message }
                  message: function() {
                    return { message: resp.message, store_id: resp.store_id, store_type: resp.store_type }
                  }
                }
              });

              alertModal.opened.then(function () {
                  if ($scope.modalOpen == false){
                    $scope.modalOpen = true;
                  }
              });

              alertModal.result.then(function(){
                $scope.open_troubleshooter(resp.store_id, resp.store_type);
                $scope.modalOpen = false;
              });
            }
          });
        }
      });

      $scope.send_order_no = function(event, search_order, store_id) {
        if (event.which != '13') return;
        if (search_order != "") {
          $http.get($rootScope.api_url + '/stores/get_order_details?order_no=' + encodeURIComponent(search_order)  + '&store_id=' + encodeURIComponent(store_id)).success(function (data) {
              if (data.status) {
                $scope.open_popup(data.store_id, data.store_type, data.createDate,data.modifyDate, data.orderStatus, data.gp_ready_status, data.gp_order_found, data.id, data.order_no, data.range_start_date, data.range_end_date, data.range_created_start_date, data.range_created_end_date, data.ss_similar_order_found)
              } else {
                notification.notify("Order is not found!", 0);
              }
          });
        }
      };

      $scope.trigger = function(error,error2,event, store_id){
        if (error == "Message: ---&#10;- - All CSV files on the server appear to have been imported.&#10;&#10;Error Trace: null&#10;") {
          if(event.ctrlKey){
            importOrders.download_summary_details(store_id);
          }
          else{
            ngClipboard.toClipboard(error2);
          }
        }
        else{
          ngClipboard.toClipboard(error);
        }

      };

      $scope.check = function(error,event,store_id){
        if(event.ctrlKey){
          importOrders.download_summary_details(store_id);
        }
        else{
          ngClipboard.toClipboard(error);
        }
      };

      $scope.open_troubleshooter = function(store_id, store_type){
        $scope.open_popup(store_id, store_type, null,null, null, null, null, null, null)
      }

      $scope.show_processed_orders = function(processed_orders){
        if($scope.processed_orders.length > 0) {
          var alertModal = $modal.open({
            templateUrl: '/assets/views/modals/alert_modal.html',
            controller: 'alertModalCtrl',
            size: 'small',
            backdrop: 'static',
            keyboard: false,
            resolve: {
              message: function() { return { processed_orders: processed_orders, type: 'processed_orders' } }
            }
          });
        }
      };

      $scope.show_remaining_orders = function(run_by, import_start, import_end){
        var alertModal = $modal.open({
          templateUrl: '/assets/views/modals/alert_modal.html',
          controller: 'alertModalCtrl',
          size: 'small',
          backdrop: 'static',
          keyboard: false,
          resolve: {
            message: function() { return { run_by: run_by, type: 'remaining_orders', import_start: import_start, import_end: import_end } }
          }
        });
      };

      $scope.open_lro_updater = function (store_id, store_type) {
        var lro_update_modal = $modal.open({
          templateUrl: '/assets/views/modals/lro_updater.html',
          controller: 'lroUpdateCtrl',
          resolve: {
            store_id: function () { return store_id },
            store_type: function () { return store_type },
          }
        });
      };

      $scope.open_popup = function (store_id, store_type, set_created_at, set_modified_at, order_status, tag_order_status, backend_order, set_order_id, set_order_no, set_range_start_date, set_range_end_date, set_range_created_start_date, set_range_created_end_date, ss_similar_order_found) {
        var csv_modal = $modal.open({
          templateUrl: '/assets/views/modals/missing_order.html',
          controller: 'missingorderCtrl',
           size: 'lg',
           resolve: {
            settings_data: function () { return store_id },
            send_store_type: function () { return store_type },
            send_create_at: function () { return set_created_at },
            send_modify_at: function () { return set_modified_at },
            send_order_status: function() {return order_status },
            send_tag_order: function() {return tag_order_status },
            send_backend_order: function() {return backend_order },
            send_ss_similar_order_found: function() {return ss_similar_order_found},
            send_order_id: function() {return set_order_id },
            send_order_no: function() {return set_order_no; },
            send_range_start_date: function() {return set_range_start_date; },
            send_range_end_date: function() {return set_range_end_date; },
            send_range_created_start_date: function() {return set_range_created_start_date; },
            send_range_created_end_date: function() {return set_range_created_end_date; },
           }
         });
       };

      $scope.forcessl = function () {
        if ($location.protocol() !== 'https') {
          if (document.getElementById('rails_env') && document.getElementById('rails_env').value != 'development') {
            $window.location.href = $location.absUrl().replace('http', 'https');
          }
          if ($location.path() == "/settings/cost_calculator"){
            $scope.path = "cost_calculator"
          }
        }
      };
      $scope.forcessl();

      $scope.fix_imported_at = function(store_id){
        importOrders.fix_imported_at(store_id);
      };

      $scope.delete_import_summary = function(store_id){
        importOrders.delete_import_summary(store_id);
      };

      $scope.get_import_details = function(store_id){
        $http.get($rootScope.api_url + '/order_import_summary/get_import_details.json?store_id=' + store_id).success(function (data) {
            if (data.status) {
              $scope.run_by = data.run_by;
              $scope.import_start = data.import_start;
              $scope.import_end = data.import_end;
              $scope.processed_orders = data.processed_orders;
              $scope.total_imported = data.total_imported
              $scope.remaining_items = data.remaining_items
              $scope.completed = data.completed
              $scope.in_progess = data.in_progess
              $scope.elapsed_time = data.elapsed_time
              $scope.elapsed_time_remaining = data.elapsed_time_remaining
              $scope.view_status = 'view_status_' + data.store_id;
              $('.store_import_status_' + data.store_id).show();
              try{
                val = data.last_imported_data.replace("T", " ").replace("Z", "").slice(0, -6);
                }catch(e){
                  val = new Date(data.last_imported_data).toString().split("GMT")[0];
                }
                $scope.last_imported_data = $filter('date')(Date.parse(val), 'dd MMM hh:mm a ');
            }else{
              $scope.no_new_order = 'no_new_order_' + data.store_id;
            }
        });
      };

      $scope.issue_import = function (store_id, days, import_type) {
        importOrders.issue_import(store_id, days, import_type);
      };

      $scope.issue_import_for_ss = function (store_id ,days ,import_type, import_date, start_date, end_date ,order_date_type, order_id) {
        importOrders.issue_import_for_ss(store_id, days, import_type, import_date, start_date, end_date, order_date_type, order_id);
      }

      $scope.update_popup_display_setting = function (flag) {
        importOrders.update_popup_display_setting(flag);
      };

      $scope.cancel_import = function (store_id) {
        //alert("cancel import" + store_id)
        importOrders.cancel_import(store_id);
        $scope.import_summary.import_info.status = "cancelled"
      };

      $scope.sign_out = function () {
        var accessToken = localStorage.getItem('access_token');
        if (accessToken && $rootScope.api_url) {
          var logoutTime = new Date().toISOString();
          var payload = { logout_time: logoutTime };
          
          // Set the authorization header explicitly
          var config = {
            headers: {
              'Authorization': 'Bearer ' + accessToken
            }
          };
          
          $http.post($rootScope.api_url + '/users/update_logout_time.json', payload, config)
            .then(function(response) {
              debugger
              console.log('Logout time saved successfully');
            })
            .catch(function(error) {
              console.log('Error saving logout time', error);
            });
        }
        
        // Always perform these logout actions immediately
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('created_at');
        localStorage.removeItem('current_user');
        
        if ($rootScope.refresh_token_interval) {
          $interval.cancel($rootScope.refresh_token_interval);
        }
        
        if (groovIO) {
          groovIO.disconnect(true);
        }
        
        // Redirect immediately without waiting for API response
        $window.location.href = '/#/';
        $window.location.reload();
      };
      $scope.issue_update = function (store_id) {
        stores.shipstation.update_all_locations(store_id);
      };

      $scope.show_days_select = function (store, show) {
        store.days = $scope.get_deep_import_days_for_store(store);

        // if(store.days ==undefined) {
        //   store.days=1;
        // }
        if(show===true) {
          $('[data-import='+store.id+']').css('display', 'block');
        } else {
          $('[data-import='+store.id+']').css('display', 'none');
        }
      };

      $scope.check_days_value = function (store) {
        var element_val = parseInt(store.days);
        if(element_val>30) {
          store.days=30;
        }
        $scope.set_deep_import_days_for_store(store);
      };

      $scope.get_deep_import_days_for_store = function(store){
        var days = 1;
        if(store.store_type==="BigCommerce"){
          days = $scope.bc_deep_import_days;
        }
        if(store.store_type==="ShippingEasy"){
          days = $scope.se_deep_import_days;
        }
        if(store.store_type==="Shipstation API 2"){
          days = $scope.ss_deep_import_days;
        }
        if(store.store_type==="Teapplix"){
          days = $scope.tp_deep_import_days;
        }
        if(store.store_type==="Magento"){
          days = $scope.mg_deep_import_days;
        }
        if(store.store_type==="Amazon"){
          days = $scope.am_deep_import_days;
        }
        if(store.store_type==="Shippo"){
          days = $scope.sp_deep_import_days;
        }
        return days;
      };

      $scope.set_deep_import_days_for_store = function(store){
        if(store.store_type==="BigCommerce"){
          $scope.bc_deep_import_days = store.days;
        }
        if(store.store_type==="ShippingEasy"){
          $scope.se_deep_import_days = store.days;
        }
        if(store.store_type==="Shipstation API 2"){
          $scope.ss_deep_import_days = store.days;
        }
        if(store.store_type==="Teapplix"){
          $scope.tp_deep_import_days = store.days;
        }
        if(store.store_type==="Magento"){
          $scope.mg_deep_import_days = store.days;
        }
        if(store.store_type==="Amazon"){
          $scope.am_deep_import_days = store.days;
        }
        if(store.store_type==="Shippo"){
          $scope.sp_deep_import_days = store.days;
        }
      };


      $rootScope.$on("editing-a-var", function (event, data) {
        $scope.currently_editing = (data.ident !== false);
      });

      $scope.log_out = function (who) {
        if (who === 'me') {
          $rootScope.$emit('sign_out');
          $window.location.reload();
        } else if (who === 'everyone_else') {
          groovIO.emit('logout_everyone_else');
        }
      };
      $scope.stop_editing = function () {
        editable.force_exit();
      };

      $scope.is_active_tab = function (string) {
        var name = $state.current.name;
        if (name.indexOf('.') !== -1) {
          name = name.substr(0, name.indexOf('.'));
        }
        return (string === name);
      };
      $scope.notify = function (msg, type) {
        notification.notify(msg, type);
      };
      $scope.import_summary = {};
      var myscope = {};

      $scope.import_all_orders = function (event) {
        if (event.ctrlKey) {
          importOrders.cancel_all($scope);
        } else {
          importOrders.do_import($scope);
        }
      };

      $scope.cancel_all_imports = function (event) {
        if (event.ctrlKey) {
          importOrders.cancel_all($scope);
        }
        else{
          notification.notify("Import is already in progress. If you want to import again, delete the import summary from General Settings.", "error");
        }
      };

      $rootScope.focus_search = function (event) {
        var elem;
        if (typeof event !== 'undefined') {
          event.preventDefault();
        }
        //if cheatsheet is open, do nothing;
        if ($document.find('.cfp-hotkeys-container').hasClass('in')) {
          return;
        }
        // If in modal
        if ($document.find('body').hasClass('modal-open')) {
          elem = $document.find('.modal-dialog:last .modal-body .search-box');
        } else {
          elem = $document.find('.search-box');
        }
        elem.focus();
        return elem;
      };
      hotkeys.bindTo($scope).add({
        combo: ['return'],
        description: 'Focus search/scan bar (if present)',
        callback: $rootScope.focus_search
      });
      hotkeys.bindTo($scope).add({
        combo: ['mod+shift+e'],
        description: 'Exit Editing mode',
        callback: $scope.stop_editing
      });

      document.onmouseover = function () {
        if (!$scope.mouse_in_page) {
          $scope.$apply(function () {
            $scope.mouse_in_page = true;
          });
        }
      };
      document.onmouseleave = function () {
        if ($scope.mouse_in_page) {
          $scope.$apply(function () {
            $scope.mouse_in_page = false;
          });
        }
      };

      //myscope.get_status();
      $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
        if ($(".modal").is(':visible') && toState.name !== fromState.name) {
          var modal = $modalStack.getTop();
          if (modal && modal.value.backdrop && modal.value.backdrop !== 'static' && !$scope.mouse_in_page) {
            event.preventDefault();
            $modalStack.dismiss(modal.key, 'browser-back-button');
          }
        }
      });
      $rootScope.$on('$viewContentLoaded', function () {
        $timeout($rootScope.focus_search);
      });
    }]);
