groovepacks_controllers.
  controller('dailyPackedCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$window', 'exportsettings', '$rootScope', '$modal', 'generalsettings',
    function ($scope, $http, $timeout, $location, $state, $cookies, $window, exportsettings, $rootScope, $modal, generalsettings) {
      var myscope = {};
      myscope.defaults = function () {
        return { }
      };

      myscope.init = function () {
        $scope.exports = myscope.defaults();
        $scope.setup_page('backup_restore', 'daily_packed');
        $scope.export_settings = exportsettings.model.get();
        exportsettings.single.get($scope.export_settings);
        $scope.general_settings = {single: {}}
        generalsettings.single.get($scope.general_settings).success(
          function (data) {
            $scope.export_settings.current_time = data.current_time;
        })
      };

      $scope.update_daily_packed_export_settings = function () {
        $scope.show_button = false;
        exportsettings.single.update($scope.export_settings);
      };

      $scope.scrollup = function(){
        $('html, body').animate({ scrollTop: 1100});
      }

      $scope.export_daily_packed = function () {
        if ($scope.export_settings.single.daily_packed_email == ""){
          var notification_modal = $modal.open({
            templateUrl: '/assets/views/modals/settings/daily_packed_popup.html',
            controller: 'exportNotificationCtrl',
            size: 'lg',
            resolve: {
              settings_data: function() {
                return $scope.export_settings.single;
              }
            }
          });
          notification_modal.result.then(function () {
            $http.get($rootScope.api_url +'/exportsettings/daily_packed');
            $scope.notify('daily packed % report export will be emailed to this address.', 1);
          });
        } else {
          $http.get($rootScope.api_url + '/exportsettings/daily_packed');
          $scope.notify('It will be emailed to ' + $scope.export_settings.single.daily_packed_email, 1);
        }
      };
      
      myscope.init();
    }]);
 