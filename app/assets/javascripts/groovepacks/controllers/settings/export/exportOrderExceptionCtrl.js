groovepacks_controllers.controller('exportOrderExceptionCtrl', ['$state', '$scope', '$window', '$http', '$rootScope', function ($state, $scope,
 $window, $http, $rootScope) {
  var myscope = {};
  myscope.defaults = function () {
    return {
      start: {
        open: false,
        time: new Date()
      },
      end: {
        open: false,
        time: new Date()
      }
    }
  };

  myscope.init = function () {
    $scope.exception = myscope.defaults();
    $scope.serial = myscope.defaults();
    $scope.setup_page('backup_restore', $state.current.url.substring(1));
  };

  $scope.open_picker = function (event, object) {
    event.preventDefault();
    event.stopPropagation();
    object.open = true;
  };

  $scope.download_csv = function (which) {
    if (['exception', 'serial'].indexOf(which) != -1) {
      if ($scope[which].start.time <= $scope[which].end.time) {
        var st_time = $scope[which].start.time.toString().split("GMT")[0];
        var ed_time = $scope[which].end.time.toString().split("GMT")[0];
        url = $rootScope.api_url + '/settings/order_' + which + 's.csv?start=' + st_time + '&end=' + ed_time
        $http.get(url)
          .success(function (response) {
            // to download csv from S3
            var downloadLink = angular.element('<a></a>');//create a new  <a> tag element
            downloadLink.attr('href', response.filename.url);
            downloadLink.attr('download', response.filename.filename);
            downloadLink.attr('target','_self');
            downloadLink[0].click();//call click function
          }).error(notification.server_error);

        // $window.open('/settings/order_' + which + 's.csv?start=' + $scope[which].start.time + '&end=' + $scope[which].end.time);
      } else {
        $scope.notify('Start time can not be after End time');
      }
    } else {
      $scope.notify('Unknown csv requested');
    }

  };
  myscope.init();
}]);
