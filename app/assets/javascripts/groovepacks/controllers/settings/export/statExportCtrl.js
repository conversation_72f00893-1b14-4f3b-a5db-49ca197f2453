groovepacks_controllers.
  controller('statExportCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$window', 'exportsettings', '$rootScope', '$modal', 'generalsettings',
    function ($scope, $http, $timeout, $location, $state, $cookies, $window, exportsettings, $rootScope, $modal, generalsettings) {
      var myscope = {};
      myscope.defaults = function () {
        return { }
      };

      myscope.init = function () {
        $scope.exports = myscope.defaults();
        $scope.setup_page('backup_restore', 'stats_export');
        $scope.export_settings = exportsettings.model.get();
        exportsettings.single.get($scope.export_settings);
        $scope.general_settings = {single: {}}
        generalsettings.single.get($scope.general_settings).success(
          function (data) {
            $scope.export_settings.current_time = data.current_time;
        })
      };

      $scope.update_stat_export_settings = function () {
        $scope.show_button = false;
        exportsettings.single.update($scope.export_settings);
      };

      $scope.scrollup = function(){
        $('html, body').animate({ scrollTop: 1100});
      }

      $scope.change_option = function (key, value) {
        $scope.export_settings.single[key] = value;
        $scope.show_button = false;
        exportsettings.single.update($scope.export_settings);
      };

      $scope.export_stat = function () {
        if ($scope.export_settings.single.stat_export_email == ""){
          var notification_modal = $modal.open({
            templateUrl: '/assets/views/modals/settings/export_stat_popup.html',
            controller: 'exportNotificationCtrl',
            size: 'lg',
            resolve: {
              settings_data: function() {
                return $scope.export_settings.single;
              }
            }
          });
          notification_modal.result.then(function () {
            $http.get($rootScope.api_url +'/exportsettings/email_stats');
            $scope.notify('stats export will be emailed to this address.', 1);
          });
        } else {
          $http.get($rootScope.api_url + '/exportsettings/email_stats');
          $scope.notify('It will be emailed to ' + $scope.export_settings.single.stat_export_email, 1);
        }
      };
      
      myscope.init();
    }]);
 