groovepacks_controllers.
  controller('orderExportCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$window', 'exportsettings', '$rootScope', '$modal', 'generalsettings',
    function ($scope, $http, $timeout, $location, $state, $cookies, $window, exportsettings, $rootScope, $modal, generalsettings) {
      var myscope = {};
      myscope.defaults = function (start_time, end_time) {
        return {
          start: {
            open: false,
            time: start_time
          },
          end: {
            open: false,
            time: end_time
          }
        }
      };

      myscope.init = function () {
        $scope.setup_page('backup_restore', 'order_export');
        $scope.export_settings = exportsettings.model.get();
        exportsettings.single.get($scope.export_settings).success(
          function (data) {
            // try {
            //   start_time = $scope.export_settings.single.start_time.replace("T", " ").replace("Z", "").slice(0, -6);
            //   start_time = start_time.length ? new Date(start_time) : new Date();
            // } catch(e) {
            //   start_time = new Date();
            // }
            start_time = new Date();
            // try {
            //   end_time = $scope.export_settings.single.end_time.replace("T", " ").replace("Z", "").slice(0, -6);
            //   end_time = end_time.length ? new Date(end_time) : new Date();
            // } catch(e) {
            //   end_time = new Date();
            // }
            end_time = new Date();
            $scope.exports = myscope.defaults(start_time, end_time);
            $scope.general_settings = {single: {}};
            generalsettings.single.get($scope.general_settings).success(
              function (data) {
                $scope.export_settings.current_time = data.current_time;
            })
          }
        )
      };

      $scope.open_picker = function (event, object) {
        event.preventDefault();
        event.stopPropagation();
        object.open = true;
      };

      $scope.update_export_settings = function () {
        $scope.show_button = false;
        exportsettings.single.update($scope.export_settings);
      };

      $scope.scrollup = function(){
        $('html, body').animate({ scrollTop: 1100});
      }

      $scope.change_option = function (key, value) {
        $scope.export_settings.single[key] = value;
        $scope.update_export_settings();
      };

      $scope.download_csv = function () {
        if ($scope.export_settings.single.order_export_email == ""){
          var notification_modal = $modal.open({
            templateUrl: '/assets/views/modals/settings/export_popup.html',
            controller: 'exportNotificationCtrl',
            size: 'lg',
            resolve: {
              settings_data: function() {
                return $scope.export_settings.single;
              }
            }
          });
          notification_modal.result.then(function () {
            if ($scope.exports.start.time <= $scope.exports.end.time) {
              $http.get($rootScope.api_url +'/exportsettings/order_exports?start=' + $scope.exports.start.time + '&end=' + $scope.exports.end.time);
              $scope.notify('export will be emailed to this address.', 1);
            } else {
              $scope.notify('Start time can not be after End time');
            }
          });
        } else {
          if ($scope.exports.start.time <= $scope.exports.end.time) {
            // $window.open('/exportsettings/order_exports?start=' + $scope.exports.start.time + '&end=' + $scope.exports.end.time);
            $http.get($rootScope.api_url + '/exportsettings/order_exports?start=' + $scope.exports.start.time + '&end=' + $scope.exports.end.time);
            $scope.notify('It will be emailed to ' + $scope.export_settings.single.order_export_email, 1);
          } else {
            $scope.notify('Start time can not be after End time');
          }
        }
      };

      myscope.init();
    }]);
