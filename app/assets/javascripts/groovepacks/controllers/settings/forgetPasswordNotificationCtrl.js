groovepacks_controllers.
  controller('forgetPasswordNotificationCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', 'generalsettings', '$rootScope', 'auth',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, generalsettings, $rootScope, auth) {
    var myscope = {};

  $scope.cancel = function () {
    $modalInstance.dismiss("cancel-button-click");
  };


  $scope.forgetpass = function(auth_form){
    if (auth_form != undefined && auth_form != ""){
      auth.forgetpass(auth_form.user_name);
      $modalInstance.close("ok-button-click");
    } else {
      notification.notify("please enter valid username");
    }
  }
  }]);
