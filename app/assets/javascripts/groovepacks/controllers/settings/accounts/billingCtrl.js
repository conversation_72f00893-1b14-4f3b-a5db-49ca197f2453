groovepacks_controllers.
  controller('billingCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$modal', '$rootScope', 'groov_translator',
    function ($scope, $http, $timeout, $location, $state, $cookies, $modal, $rootScope, groov_translator) {
      var myscope = {};

      myscope.init = function () {
        $scope.translations = {
          "headings": {
            "invoices": ""
          },
          "labels": {
          },
          "tooltips": {
          }
        };
        $scope.current_page = '';
        $scope.setup_page('show_card_details', 'billing');
        groov_translator.translate('settings.accounts', $scope.translations);

        $http.get($rootScope.api_url + '/users/invoices.json').success(function(data){
          $scope.invoice_data = data.invoice_data;
          $scope.status = data.status;
          $scope.error_message = data.error;
        });
      };


      myscope.init();
    }]);
