groovepacks_controllers.
  controller('modifyPlanCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$modal', '$rootScope', 'notification', 'payments', 'groov_translator', '$sanitize','modify_plan',
    function ($scope, $http, $timeout, $location, $state, $cookies, $modal, $rootScope, notification, payments, groov_translator, $sanitize, modify_plan) {
      var myscope = {};

      myscope.init = function () {
        $http.get($rootScope.api_url + '/users/get_subscription_info.json').success(function(data){
          $scope.modify = { } 
          $scope.modify.no_of_users = data["no_of_users"]
          $scope.modify.users  = data["no_of_users"]
          $scope.modify.price = data["amount"]
          $scope.modify.total_users = data["total_users"]
          $scope.modify.added_through_ui = data["added_through_ui"]
          $scope.modify.discount = ($scope.modify.price * 12)/10
          $scope.modify.is_annual = false
          $scope.modify.calender = "month"
          $scope.modify.allow_delete = false
          $scope.modify.fix_amount = data["amount"]
        });
        $scope.current_page = '';
        $scope.setup_page('show_card_details', 'modify_plan');
      };

      
      $scope.calculate = function(sign){
        var no_of_users = $scope.modify.no_of_users
        var ui_user = $scope.modify.added_through_ui
        var users = $scope.modify.users
        var total_users = $scope.modify.total_users

        if(sign=="+") {
          no_of_users = no_of_users+1;
        } else {
          if(no_of_users>2) {
            if (total_users <= no_of_users) {
               no_of_users = no_of_users-1;
            } else{
              $scope.notify('The number of users in the user list exceeds the plan total.');
              $scope.notify('Please delete the user(s) that are no longer needed before removing them from the plan.');
            }
          }
        }

        if (no_of_users == 2) {
          $scope.notify('The minimum plan is 2 users.');
        }

        if ($scope.modify.is_annual) {
          $scope.modify.no_of_users = no_of_users
          price_calculate()
          var month_price = $scope.modify.price
          $scope.modify.discount = ($scope.modify.price * 12)/10
          var discount = $scope.modify.discount
          annual_price = (month_price * 12) - discount
          $scope.modify.price = annual_price
          $scope.modify.is_annual = true
          $scope.modify.calender = "year"
        }else{
          $scope.modify.no_of_users = no_of_users
          price_calculate()
          $scope.modify.discount = ($scope.modify.price * 12)/10
          $scope.modify.is_annual = false
          $scope.modify.calender = "month"
        }

        var allow_delete = ($scope.modify.users ) - $scope.modify.added_through_ui  
        if (allow_delete > $scope.modify.no_of_users) {
          $scope.modify.allow_delete = true
        }else{
          $scope.modify.allow_delete = false
        }

      }

      $scope.annual = function(value){
        if (!value) {
          var month_price = $scope.modify.price
          var discount = $scope.modify.discount
          annual_price = (month_price * 12) - discount
          $scope.modify.price = annual_price
          $scope.modify.is_annual = true
          $scope.modify.calender = "year"
        }else{
          var no_of_users = $scope.modify.no_of_users
          price_calculate()
          $scope.modify.is_annual = false
          $scope.modify.calender = "month"
        }

      }

      var price_calculate = function(){
        var fix_amount = $scope.modify.fix_amount
        if ($scope.modify.no_of_users > $scope.modify.users) {
          var added_user = $scope.modify.no_of_users - $scope.modify.users
          $scope.modify.price  = fix_amount + 50* added_user
        } else if ($scope.modify.no_of_users < $scope.modify.users){
          var remove_user = $scope.modify.users - $scope.modify.no_of_users
          $scope.modify.price  = fix_amount - 50* remove_user
        }else if($scope.modify.no_of_users == $scope.modify.users){
          $scope.modify.price  = fix_amount
        }
      }

      $scope.update_plan = function(){
        modify_plan.model.update_plan($scope.modify.no_of_users, $scope.modify.price ,$scope.modify.is_annual);
      }
      myscope.init();
    }]);
