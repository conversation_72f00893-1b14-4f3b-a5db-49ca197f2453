groovepacks_controllers.
  controller('costCalculatorCtrl', ['$scope', '$http', '$timeout', '$location', '$state', '$cookies', '$modal', '$rootScope', 'notification', 'payments', 'groov_translator', '$sanitize',
    function ($scope, $http, $timeout, $location, $state, $cookies, $modal, $rootScope, notification, payments, groov_translator, $sanitize) {

      var myscope = {};
      myscope.init = function () {
        $http.get($rootScope.api_url + '/cost_calculator.json').success(function(data){
          $scope.cost = data;
          cost = $scope.cost;
          if($location.$$url.length>100){
            $scope.cost = getUrlParameter($location.$$url);
            $scope.cost.cost_header = decodeURIComponent($scope.cost.cost_header).replace(/\+/g, ' ');
          } 
          $scope.cost.monthly_shipping = $("#monthly_shipping").val();
          try{
            if ($scope.cost.email_text != null){
              text = decodeURIComponent($scope.cost.email_text).replace(/\+/g, ' ');
            } else {text = null} 
          }catch(e){ text = null; }
          try{
            from_app = $location.$$url.split('?')[1].split('=')
            if(from_app[0] == "from_app"){
              $scope.cost[from_app[0]] = from_app[1]
            }
          }
          catch(e){$scope.cost.from_app = "false"}

          $scope.cost.email_text = text || "Hi Guys, Looks like we are spending about $" + $scope.cost.monthly_shipping + " each month on shipping errors. Please follow the link below to see the details of this estimate. You can adjust any of the inputs to refine the calculation."
          if ($scope.cost.from_app == true || $location.$$url.search('/settings/accounts/cost_calculator')==0){
            $http.get($rootScope.api_url + '/settings/get_settings').success(function(response){
              try{ $scope.setup_page('show_card_details', 'cost_calculator');}catch(e){}
              $scope.cost_calculator_url = response.data.settings.cost_calculator_url
              $scope.cost = getUrlParameter($scope.cost_calculator_url); 
              $scope.cost.total_cot_per_error = $scope.cost.total_cost;
            })
          }
        });
      };

      var getUrlParameter = function getUrlParameter(response) {
         $scope.cost = {}
        // var sPageURL = decodeURIComponent(response.data.settings.cost_calculator_url),
        response = response.split('?')[1]
        var sPageURL = decodeURIComponent(response),
            sURLVariables = sPageURL.replace(/&amp;/g, '&').split('&'),
            sParameterName,
            i;
        for (i = 0; i < sURLVariables.length; i++) {
          sParameterName = sURLVariables[i].split('=');
          $scope.cost[sParameterName[0]] = sParameterName[1];
          // if (sParameterName[0] === sParam) {
          //     return sParameterName[1] === undefined ? true : sParameterName[1];
          // }
        }
        return $scope.cost
      };

      $scope.send_email = function(flag){
        cost_header = $("#cost_header").html();
        $http.get($rootScope.api_url + '/email_calculations.json?'+$.param($scope.cost)+'&gp_cost='+$("#gp_cost").val()+'&monthly_shipping='+$("#monthly_shipping").val()+'&error_cost_per_day='+$("#error_cost_per_day").val()+'&total_cost='+$("#total_cost").val()+'&recipient_one='+$scope.cost.recipient_one+'&recipient_two='+$scope.cost.recipient_two+'&monthly_saving='+$('#monthly_saving').val()+'&monthly_shipping='+$("#monthly_shipping").val()+'&recipient_name='+$scope.cost.recipient_name+'&recipient_three='+$scope.cost.recipient_three+'&follow_up_email='+$('#follow_up_email').is(':checked')+'&email_text='+ $scope.cost.email_text+ '&cost_header='+cost_header+'&only_save='+flag).success(function(response){
          $scope.remove_message = true;
          $('.notice').html(response.message);
          if (response["only_save"] == "true"){
            $('#message').hide();
          } else {
            if(($scope.cost.recipient_two || $scope.cost.recipient_one || $scope.cost.recipient_three) != undefined){
              $('#message').show();
            }
          }
        });
      };
    
      myscope.init();
    }]);
