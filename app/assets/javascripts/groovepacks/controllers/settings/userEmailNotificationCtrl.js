groovepacks_controllers.
  controller('userEmailNotificationCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', 'settings_data', '$rootScope', '$window',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, settings_data, $rootScope, $window) {
    var myscope = {};

    myscope.init = function () {
      $scope.settings_data = settings_data;
      $scope.user_email_address = "";
    };

    $scope.update_user_email_address = function(email){
      if (email != "" && email.includes("@")){
        $scope.settings_data.email = email;
        $http.post($rootScope.api_url + '/users/update_email.json', $scope.settings_data);
        notification.notify("Email is succesfully updated.", 1);
        $modalInstance.close("ok-button-click");
      } else {
        notification.notify("Please update email", 0);
        $modalInstance.dismiss("cancel-button-click");  
      }
    }

    $scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
      $window.location.reload();
    };

    myscope.init();
  }]);
