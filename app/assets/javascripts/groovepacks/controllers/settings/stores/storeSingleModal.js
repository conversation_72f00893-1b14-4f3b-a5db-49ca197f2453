groovepacks_controllers.controller('storeSingleModal', ['$rootScope', '$http', '$scope', 'store_data', '$window', '$sce', '$interval', '$state', '$stateParams', '$modal',
  '$modalInstance', '$timeout', 'hotkeys', 'stores', 'warehouses', 'notification', '$q', 'groov_translator', 'Lightbox', 'ngClipboard',
  function ($rootScope, $http, scope, store_data, $window, $sce, $interval, $state, $stateParams, $modal, $modalInstance, $timeout, hotkeys, stores, warehouses, notification, $q, groov_translator, Lightbox, ngClipboard) {
    var myscope = {};
    scope.change_store_name = false;
    scope.split_order_options = [{ name: "Disabled", value: "disabled"}, { name: "Verify Together", value: "verify_together"}, { name: "Verify Separately", value: "verify_separately"}, { name: "Shipment Handling V2", value: "shipment_handling_v2"}];

    /**
     * Public methods
     */

    scope.ok = function () {
      $modalInstance.close("ok-button-click");
    };
    scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    scope.update = function (reason) {
      if (reason == "cancel-button-click") {
        myscope.rollback();
      } else if (reason == "csv-modal-closed") {
        scope.update_single_store(true);
      } else if (typeof scope.stores.single.id != "undefined") {
        // scope.update_single_store(false);
        scope.update_single_store(true);
      }
    };

    scope.lightbox_images = [
      {
        'url': '/assets/images/magento-2-1.png',
        'caption': 'Image-1'
      },
      {
        'url': '/assets/images/magento-2-2.png',
        'caption': 'Image-2'
      }
    ];

    scope.openLightboxModal = function (index) {
      Lightbox.openModal(scope.lightbox_images, index);
    };

    scope.disconnect_ebay_seller = function () {
      stores.ebay.user_token.delete(scope.stores).then(function () {
        myscope.store_single_details(scope.stores.single.id, true);
      });
    };

    scope.disconnect_shopify = function () {
      stores.shopify.disconnect(scope.stores.single.id).then(function () {
        myscope.store_single_details(scope.stores.single.id, true);
      });
    }

    scope.check_bigcommerce_connection = function () {
      stores.big_commerce.check_connection(scope.stores.single.store_type, scope.stores.single.id).then(function (response) {
        scope.stores.single.message = response.data.message;
      });
    }

    scope.check_magento_connection = function () {
      stores.magento.check_connection(scope.stores.single.store_type, scope.stores.single.id).then(function (response) {
        scope.stores.single.message = response.data.message;
      });
    }

    scope.disconnect_bigcommerce_connection = function () {
      stores.big_commerce.disconnect(scope.stores.single.id).then(function (response) {
        myscope.store_single_details(scope.stores.single.id, true);
      });
    }

    scope.disconnect_magento_connection = function(){
      stores.magento.disconnect(scope.stores.single.id).then(function (response) {
        scope.stores.single.message='';
        myscope.store_single_details(scope.stores.single.id, true);
      });
    }

    scope.fix_import_dates = function(){
      stores.shipstation.fix_dates(scope.stores.single.id).then(function (response) {
        scope.stores.single.message='';
        //myscope.store_single_details(scope.stores.single.id, true);
      });
    }

    scope.update_product_image = function(){
      stores.shipstation.update_product_image(scope.stores.single.id);
    }

    scope.show_hide_images = function(element_class, link_class) {
      $('.'+element_class).toggle('slow');
      if($('.'+link_class).hasClass('fa-caret-down')) {
        $('.'+link_class).removeClass('fa-caret-down');
        $('.'+link_class).addClass('fa-caret-up');
      } else {
        $('.'+link_class).removeClass('fa-caret-up');
        $('.'+link_class).addClass('fa-caret-down');
      }
    }

    scope.import_orders = function (report_id) {
      scope.stores.import.order.status = "Import in progress";
      scope.stores.import.order.status_show = true;
      scope.update_single_store(false).then(function () {
        stores.import.orders(scope.stores);
      });

    };

    scope.copy_acknowledgement = function (msg) {
      $("#clipboard2")[0].value = msg;
      selecter = document.querySelector("#clipboard2");
      selecter.select();
      document.execCommand('copy');
      scope.copy_text.text = 'Copied to clipboard';
      scope.copy_text.class = 'label label-success';
      $timeout(function () {
        scope.copy_text.text = 'Click Here to copy to clipboard';
        scope.copy_text.class = '';
      }, 2000);
    };

    scope.copy_callback_url = function () {
      scope.copy_text.mgcb_text = 'Copied to clipboard';
      scope.copy_text.mgcb_class = 'label label-success';
      $timeout(function () {
        scope.copy_text.mgcb_text = 'Click Here to copy to clipboard';
        scope.copy_text.mgcb_class = '';
      }, 2000);
    };

    scope.copy_success_url = function () {
      scope.copy_text.mgsc_text = 'Copied to clipboard';
      scope.copy_text.mgsc_class = 'label label-success';
      $timeout(function () {
        scope.copy_text.mgsc_text = 'Click Here to copy to clipboard';
        scope.copy_text.mgsc_class = '';
      }, 2000);
    };

    scope.import_images = function (report_id) {
      scope.stores.import.image.status = "Import in progress";
      scope.stores.import.image.status_show = true;
      scope.update_single_store(false).then(function () {
        stores.import.images(scope.stores, report_id);
      });

    };

    scope.copied = function (value) {
      ngClipboard.toClipboard(value);
      notification.notify("Successfully Copied", 1);
    }

    scope.update_products = function () {
      scope.stores.update.products.status = "Update in progress";
      scope.stores.update.products.status_show = true;
      stores.update.products($stateParams.storeid).then(function () {
        scope.stores.update.products.status = "Update completed";
      });
    };

    scope.import_product_catalog = function(){
      scope.stores.single.import_type = 'bc_products_import';
      scope.import_products();
    }

    scope.re_associate_all_products = function(store_id){
      return $http.post($rootScope.api_url + '/products/re_associate_all_products_with_shopify?store_id=' + store_id).success(function (data) {
        if (data.status) {
          notification.notify('Re-Association of All Products will take time.', 1);
        };
      });
    }

    scope.fix_broken_images = function(store_id){
      return $http.post($rootScope.api_url + '/products/fix_shopify_broken_images?store_id=' + store_id).success(function (data) {
        if (data.status) {
          notification.notify('Fix broken images will take time, you will get an email after completion.', 1);
        };
      });
   }

    scope.cancel_shopify_product_imports = function(){
      return $http.post($rootScope.api_url + '/products/cancel_shopify_product_imports').success(function (data) {
        if (data.status) {
          notification.notify('Shopify Product Imports Cancelled.', 1);
        };
      });
    }

    scope.import_products = function (report_id) {
      if (scope.stores.general_settings.email_address_for_packer_notes == ""){
        scope.stores.general_settings
        var notification_modal = $modal.open({
          templateUrl: '/assets/views/modals/settings/stores/bc_notification.html',
          controller: 'bcNotificationCtrl',
          size: 'lg',
          resolve: {
            store_data: function() {
             return scope.stores;
            }
          }
        });
        notification_modal.result.then(
          function () {
            scope.update_single_store(false).then(function () {
              stores.import.products(scope.stores, scope.stores.single.productgenerated_report_id);
            });
          }
        );
      } else {
        scope.stores.import.product.status = "Import in progress";
        scope.stores.import.product.status_show = true;
        scope.update_single_store(false).then(function () {
          stores.import.products(scope.stores, scope.stores.single.productgenerated_report_id);
        });
      }
    };

    scope.request_import_products = function () {
      scope.stores.import.product.status = "Import request in progress";
      scope.stores.import.product.status_show = true;
      stores.import.amazon.request(scope.stores);
    };

    scope.check_request_import_products = function () {
      scope.stores.import.product.status = "Checking status of the request";
      scope.stores.import.product.status_show = true;
      stores.import.amazon.check(scope.stores);
    };

    scope.copydata = function (event) {
      if (event) {
        if (scope.stores.single.store_type == 'Magento') {
          scope.stores.single.producthost = scope.stores.single.host;
          scope.stores.single.productusername = scope.stores.single.username;
          scope.stores.single.productpassword = scope.stores.single.password;
          scope.stores.single.productapi_key = scope.stores.single.api_key;
        } else if (scope.stores.single.store_type == 'Ebay') {
          scope.stores.single.productebay_auth_token = scope.stores.single.ebay_auth_token;
        } else if (scope.stores.single.store_type == 'Amazon') {
          scope.stores.single.productmarketplace_id = scope.stores.single.marketplace_id;
          scope.stores.single.productmerchant_id = scope.stores.single.merchant_id;
        } else if (scope.stores.single.store_type == 'Shipstation') {
          scope.stores.single.productusername = scope.stores.single.username;
          scope.stores.single.productpassword = scope.stores.single.password;
        }
      } else {
        if (scope.stores.single.store_type == 'Magento') {
          scope.stores.single.producthost = "";
          scope.stores.single.productusername = "";
          scope.stores.single.productpassword = "";
          scope.stores.single.productapi_key = "";
        } else if (scope.stores.single.store_type == 'Ebay') {
          scope.stores.single.productebay_auth_token = "";
        } else if (scope.stores.single.store_type == 'Amazon') {
          scope.stores.single.productmarketplace_id = "";
          scope.stores.single.productmerchant_id = "";
        } else if (scope.stores.single.store_type == 'Shipstation') {
          scope.stores.single.productusername = "";
          scope.stores.single.productpassword = "";
        }
      }
    };

    scope.hide_if_mg_rest = function (store_type) {
      if((store_type=="Magento API 2") && location.host.includes("groovepacker.com")) {
        return false;
      } else {
        return true;
      }
    };

    scope.change_opt = function (id, value) {
      scope.stores.single[id] = value;
      scope.change_store_name = true;
      scope.update_single_store(true);
    };

    scope.change_magento_store_version = function (value) {
      scope.stores.single.store_version = value;
      scope.update_single_store(true);
    };

    scope.change_value_of_status_to_update = function (value) {
      scope.stores.single.status_to_update = value;
      scope.update_single_store(true);
    };

    myscope.store_single_details = function (id, new_rollback) {
      for (var i = 0; i < scope.stores.list.length; i++) {
        if (scope.stores.list[i].id == id) {
          scope.stores.current = parseInt(i);
          break;
        }
      }
      return stores.single.get(id, scope.stores).then(function (response) {
        if (response.data.status) {
          scope.edit_status = true;
          if (typeof new_rollback == 'boolean' && new_rollback) {
            myscope.single = {};
            angular.copy(scope.stores.single, myscope.single);
          }
        }
      });
    };

    scope.update_ftp_credentials = function () {
      var connection_stat = scope.stores.single.connection_established

      stores.single.update_ftp(scope.stores).then(function(data) {
        if (connection_stat === true) {
          myscope.init();
        };
      });
    };

    scope.check_ftp_input_is_disable = function(){
      if($rootScope.current_user_data.username == 'gpadmin' || $rootScope.current_user_data.role.name == 'Super Admin')
      {
        return false;
      }
      return true;
    };

    myscope.check_empty_credentials = function (data) {
      var credentials = ['host', 'username', 'password']
      for (var key in data) {
        if ($.inArray(key, credentials) > -1 && (data[key] == null || data[key] == '')) {
          return true;
        }
      }
      return false;
    }

    myscope.check_product_ftp_empty_credentials = function (data) {
      var credentials = ['product_ftp_host', 'product_ftp_username', 'product_ftp_password']
      for (var key in data) {
        if ($.inArray(key, credentials) > -1 && (data[key] == null || data[key] == '')) {
          return true;
        }
      }
      return false;
    }

    scope.establish_connection = function() {
      var empty_credentials = myscope.check_empty_credentials(scope.stores.single);
      if (empty_credentials) {
        notification.notify("Please fillout all the credentials for the ftp store");
      } else{
        stores.single.update_ftp(scope.stores).then(function(data) {
          stores.single.connect(scope.stores);
          // stores.single.connect(scope.stores).then(function(data) {
          //   myscope.init();
          // });
        });
      };
    };

    scope.establish_product_ftp_connection = function() {
      var empty_credentials = myscope.check_product_ftp_empty_credentials(scope.stores.single);
      if (empty_credentials) {
        notification.notify("Please fillout all the credentials for the ftp store");
      } else{
        stores.single.update_ftp(scope.stores).then(function(data) {
          stores.single.connect_product_ftp(scope.stores);
          // stores.single.connect_product_ftp(scope.stores).then(function(data) {
          //   myscope.init();
          // });
        });
      };
    };

    scope.check_product_ftp_imported_folder = function() {
      var empty_credentials = myscope.check_product_ftp_empty_credentials(scope.stores.single);
      if (empty_credentials) {
        notification.notify("Please fillout all the credentials for the ftp store");
      } else{
        stores.single.update_ftp(scope.stores).then(function(data) {
          stores.single.check_product_ftp_imported(scope.stores);
          // stores.single.check_product_ftp_imported(scope.stores).then(function(data) {
          //   myscope.init();
          // });
        });
      };
    };

    scope.check_imported_folder = function() {
      var empty_credentials = myscope.check_empty_credentials(scope.stores.single);
      if (empty_credentials) {
        notification.notify("Please fillout all the credentials for the ftp store");
      } else{
        stores.single.update_ftp(scope.stores).then(function(data) {
          stores.single.check_imported(scope.stores);
          // stores.single.check_imported(scope.stores).then(function(data) {
          //   myscope.init();
          // });
        });
      };
    };

    scope.pull_store_inventory = function () {
      stores.single.pull_inventory(scope.stores.single.id).then(function (response) {
        if(response.data.status==false){
          notification.notify(response.data.message);
        } else {
          notification.notify("Your request has been queued", 1);
        }
      });
    }

    scope.push_store_inventory = function () {
      stores.single.push_inventory(scope.stores.single.id).then(function (response) {
        if(response.data.status==false){
          notification.notify(response.data.message);
        } else {
          notification.notify("Your request has been queued", 1);
        }
      });
    }

    scope.toggle_shopify_sync = function (type) {
      $http.post($rootScope.api_url + '/stores/' + scope.stores.single.id + '/toggle_shopify_sync.json', { type: type }).success(function (data) {
        if (data.status) {
          notification.notify('Successfully Updated', 1);
          scope.update_single_store(true);
        } else {
          notification.notify(data.error_messages, 0);
        }
      });
    }

    scope.update_view_dahboard_opt = function(value){
      scope.stores.single.split_order = value;
      scope.update_single_store();
    }

    scope.v2_toggle = function() {
      if (scope.stores.single.regular_import_v2){
        scope.stores.single.troubleshooter_option = true;
      }
    }

    scope.change_order_import_range_days = function() {
      if (scope.stores.single.order_import_range_days != undefined && scope.stores.single.order_import_range_days >= 0) {
        scope.update_single_store(true);
      } else {
        notification.notify("Please enter valid number of days", 0);
        $("input[name='order_import_range_days']").focus();
      }
    }

    scope.update_screct_key_ss = function (create) {
      return $http.post($rootScope.api_url + '/stores/create_update_screct_key_ss.json?id=' + scope.stores.single.id + '&create=' + create).success(function (data) {
        if (data.status) {
          notification.notify(data.message, 1);
          myscope.store_single_details(data.store_id);
        };
      });
    }

    scope.update_single_store = function (auto) {
      if (scope.stores.single.store_type && !scope.stores.single.id && !scope.stores.single.name) {
        scope.stores.single.name= scope.stores.single.store_type+'-'+scope.stores.list.length;
      } else if (scope.stores.single.store_type && scope.stores.single.id && scope.stores.single.name && scope.change_store_name) {
        scope.stores.single.name= scope.stores.single.store_type+'-'+scope.stores.list.length;
      };

      if (scope.edit_status || stores.single.validate_create(scope.stores)) {
        return stores.single.update(scope.stores, auto).success(function (data) {
          if (data.status && data.store_id) {
            if (scope.stores.single['id'] != 0) {
              myscope.store_single_details(data.store_id);
            }
            if (typeof scope.stores.single['id'] == "undefined") {
              myscope.store_single_details(data.store_id);
            }
            if (!auto) {
              //Use FileReader API here if it exists (post prototype feature)
              if (data.csv_import && data.store_id) {
                if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] && scope.start_checking_map) {
                  scope.start_checking_map = false;
                  var result = $q.defer();
                  for (var i = 0; i < scope.stores.csv.maps[scope.stores.single.type].length; i++) {
                    if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] == scope.stores.csv.maps[scope.stores.single.type][i].id) {
                      var current_map = jQuery.extend(true, {}, scope.stores.csv.maps[scope.stores.single.type][i]);
                      break;
                    }
                  }

                  current_map.map.store_id = scope.stores.single.id;
                  current_map.map.type = scope.stores.single.type;
                  current_map.map.name = current_map.name;
                  current_map.map.flag = 'file_upload';
                  if (current_map.map.type == 'order') {
                    if (current_map.map.order_date_time_format == null) {
                      if(confirm("Order Date/Time format has not been set. Would you like to continue using the current Date/Time for each imported order? Click ok to continue the import using the current date/time for all orders or click cancel and edit map to select one.")){
                        current_map.map.order_placed_at = new Date();
                        stores.csv.do_check(scope.stores, scope.stores.single.id, current_map.map)
                        result.resolve();
                      } else {
                        result.resolve();
                      };
                    } else {
                      var not_found = true
                      for (var i = 0; i < Object.keys(current_map.map.map).length; i++) {
                        if (current_map.map.map[i].name == "Order Date/Time") {
                          not_found &= false
                          break;
                        } else {
                          continue;
                        }
                        ;
                      }
                      if (not_found) {
                        if (confirm("An Order Date/Time has not been mapped. Would you like to continue using the current Date/Time for each imported order?")) {
                          current_map.map.order_placed_at = new Date();
                          stores.csv.do_check(scope.stores, scope.stores.single.id, current_map.map)
                          result.resolve();
                        };
                      } else {
                        current_map.map.order_placed_at = null;
                        stores.csv.do_check(scope.stores, scope.stores.single.id, current_map.map)
                        result.resolve();
                      };
                    };

                  } else {
                    stores.csv.do_check(scope.stores, scope.stores.single.id, current_map.map)
                    result.resolve();
                  };
                  // stores.csv.do_import({current:current_map.map});
                  // $modalInstance.close("csv-modal-closed");
                  return result.promise;
                } else if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] && !scope.start_editing_map) {
                  var result = $q.defer();
                  for (var i = 0; i < scope.stores.csv.maps[scope.stores.single.type].length; i++) {
                    if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] == scope.stores.csv.maps[scope.stores.single.type][i].id) {
                      var current_map = jQuery.extend(true, {}, scope.stores.csv.maps[scope.stores.single.type][i]);
                      break;
                    }
                  }


                  current_map.map.store_id = scope.stores.single.id;
                  current_map.map.type = scope.stores.single.type;
                  current_map.map.name = current_map.name;
                  current_map.map.flag = 'file_upload';
                  if (current_map.map.type == 'order') {
                    if (current_map.map.order_date_time_format == null) {
                      if(confirm("Order Date/Time format has not been set. Would you like to continue using the current Date/Time for each imported order? Click ok to continue the import using the current date/time for all orders or click cancel and edit map to select one.")){
                        current_map.map.order_placed_at = new Date();
                        stores.csv.do_import({current: current_map.map});
                        myscope.reset_choose_file();
                        $modalInstance.close("csv-modal-closed");
                        result.resolve();
                      } else {
                        result.resolve();
                      };
                    } else {
                      var not_found = true
                      for (var i = 0; i < Object.keys(current_map.map.map).length; i++) {
                        if (current_map.map.map[i].name == "Order Date/Time") {
                          not_found &= false
                          break;
                        } else {
                          continue;
                        }
                        ;
                      }
                      if (not_found) {
                        if (confirm("An Order Date/Time has not been mapped. Would you like to continue using the current Date/Time for each imported order?")) {
                          current_map.map.order_placed_at = new Date();
                          stores.csv.do_import({current: current_map.map});
                          myscope.reset_choose_file();
                          $modalInstance.close("csv-modal-closed");
                          result.resolve();
                        };
                      } else {
                        current_map.map.order_placed_at = null;
                        stores.csv.do_import({current: current_map.map});
                        myscope.reset_choose_file();
                        $modalInstance.close("csv-modal-closed");
                        result.resolve();
                      };
                    };

                  } else {
                    stores.csv.do_import({current: current_map.map});
                    myscope.reset_choose_file();
                    $modalInstance.close("csv-modal-closed");
                    result.resolve();
                  };
                  // stores.csv.do_import({current:current_map.map});
                  // $modalInstance.close("csv-modal-closed");
                  return result.promise;
                } else {
                  var csv_modal;
                  if (scope.stores.single.type == 'order' || scope.stores.single.type == 'kit') {
                    csv_modal = $modal.open({
                      templateUrl: '/assets/views/modals/settings/stores/csv_import.html',
                      controller: 'csvSingleModal',
                      size: 'lg',
                      resolve: {
                        store_data: function () {
                          return scope.stores;
                        }
                      }
                    });
                  } else if (scope.stores.single.type == 'product') {
                    csv_modal = $modal.open({
                      templateUrl: '/assets/views/modals/settings/stores/csv_import_detailed.html',
                      controller: 'csvDetailedModal',
                      size: 'lg',
                      resolve: {
                        store_data: function () {
                          return scope.stores;
                        }
                      }
                    });
                  };
                  csv_modal.result.finally(function () {
                    myscope.reset_choose_file();
                    $modalInstance.close("csv-modal-closed");
                  });
                };
              } else {
                if(scope.stores.single.import_type != 'bc_products_import'){
                  notification.notify("Please choose a file to import first",0);
                }
              };
            }
          }

          scope.start_editing_map = false;
          scope.change_store_name = false;
        });
      }
    };
    
    scope.toggleStatus = function (type) {
      var current = scope.stores.single.shopify_status;
      var isOpen = current === 'open' || current === 'any';
      var isClosed = current === 'closed' || current === 'any';
    
      if (type === 'open') {
        isOpen = !isOpen;
      } else if (type === 'closed') {
        isClosed = !isClosed;
      }
    
      if (isOpen && isClosed) {
        scope.stores.single.shopify_status = 'any';
      } else if (isOpen) {
        scope.stores.single.shopify_status = 'open';
      } else if (isClosed) {
        scope.stores.single.shopify_status = 'closed';
      } else {
        scope.stores.single.shopify_status = 'open';
      }
    
      scope.update_single_store(true);
    };

    
    myscope.reset_choose_file = function() {
      delete scope.stores.single['orderfile'];
      delete scope.stores.single['productfile'];
      delete scope.stores.single['kitfile'];
    };

    scope.clipboard = function(msg){
      $("#clipboard")[0].value = msg;
      selecter = document.querySelector("#clipboard");
      selecter.select();
      document.execCommand('copy');
    }

    scope.import_map = function () {
      scope.update_single_store(false);
    };

    scope.amazon_products_import = function () {
      stores.single.amazon_products_import(scope.stores.single);
    };

    scope.popup_shipping_label = function(){
      store_id = scope.stores.single.id
      url = $rootScope.api_url + '/stores/popup_shipping_label.json?store_id=' + store_id
      $http.get(url).success(function (data) {
        stores.single.popup_shipping_label = data.popup_shipping_label
      });
    };

    scope.use_chrome_extention = function(){
      store_id = scope.stores.single.id
      $http.get($rootScope.api_url + '/shipstation_rest_credentials/use_chrome_extention.json?store_id=' + store_id).success(function (data) {
        stores.single.use_chrome_extention = data.use_chrome_extention
      });
    }

    scope.use_api_create_label = function(){
      store_id = scope.stores.single.id
      $http.get($rootScope.api_url + '/shipstation_rest_credentials/use_api_create_label.json?store_id=' + store_id).success(function (data) {
        stores.single.use_api_create_label = data.use_api_create_label
      });
    }

    scope.switch_back_button = function(){
      store_id = scope.stores.single.id
      $http.get($rootScope.api_url + '/shipstation_rest_credentials/switch_back_button.json?store_id=' + store_id).success(function (data) {
        stores.single.switch_back_button = data.switch_back_button
      });
    }

    scope.auto_click_create_label = function(){
      store_id = scope.stores.single.id
      $http.get($rootScope.api_url + '/shipstation_rest_credentials/auto_click_create_label.json?store_id=' + store_id).success(function (data) {
        stores.single.auto_click_create_label = data.auto_click_create_label
      });
    }

    scope.expand_beta_option = function () {
      if (scope.beta_option == true) {
        scope.beta_option = false;
      } else {
        scope.beta_option = true
      }
    }

    scope.update_fba = function(){
      store_id = scope.stores.single.id
      $http.get($rootScope.api_url + '/stores/amazon_fba.json?store_id=' + store_id).success(function (data) {
        stores.single.amazon_fba = data.amazon_fba
      });
    }

    scope.import_ftp = function() {
      if (scope.stores.csv.mapping.order_csv_map_id) {
        scope.stores.single.type = 'order';
        if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] && !scope.start_editing_map) {
          var result = $q.defer();
          for (var i = 0; i < scope.stores.csv.maps[scope.stores.single.type].length; i++) {
            if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] == scope.stores.csv.maps[scope.stores.single.type][i].id) {
              var current_map = jQuery.extend(true, {}, scope.stores.csv.maps[scope.stores.single.type][i]);
              break;
            }
          }
          current_map.map.store_id = scope.stores.single.id;
          current_map.map.type = scope.stores.single.type;
          current_map.map.name = current_map.name;
          current_map.map.flag = 'ftp_download';

          if (current_map.map.order_date_time_format == null || current_map.map.order_date_time_format == 'Default') {
            current_map.map.order_placed_at = new Date();
            stores.csv.do_import({current: current_map.map});
            $modalInstance.close("csv-modal-closed");
            result.resolve();
          } else {
            var not_found = true
            for (var i = 0; i < Object.keys(current_map.map.map).length; i++) {
              if (current_map.map.map[i].name == "Order Date/Time") {
                not_found &= false
                break;
              } else {
                continue;
              };
            }
            if (not_found) {
              current_map.map.order_placed_at = new Date();
              stores.csv.do_import({current: current_map.map});
              $modalInstance.close("csv-modal-closed");
              result.resolve();
            } else {
              current_map.map.order_placed_at = null;
              stores.csv.do_import({current: current_map.map});
              $modalInstance.close("csv-modal-closed");
              result.resolve();
            };
          };
          return result.promise;
        }
      } else {
        alert('Please choose a saved import map to use for the FTP import. If you do not have a saved map you can import an order file with the same format from your computer and save a map that can be used for FTP imports.');
      }
    }

    scope.import_product_ftp = function() {
      if (scope.stores.csv.mapping.product_csv_map_id) {
        scope.stores.single.type = 'product';
        if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] && !scope.start_editing_map) {
          var result = $q.defer();
          for (var i = 0; i < scope.stores.csv.maps[scope.stores.single.type].length; i++) {
            if (scope.stores.csv.mapping[scope.stores.single.type + '_csv_map_id'] == scope.stores.csv.maps[scope.stores.single.type][i].id) {
              var current_map = jQuery.extend(true, {}, scope.stores.csv.maps[scope.stores.single.type][i]);
              break;
            }
          }
          current_map.map.store_id = scope.stores.single.id;
          current_map.map.type = scope.stores.single.type;
          current_map.map.name = current_map.name;
          current_map.map.flag = 'ftp_download';
          stores.csv.do_import({current: current_map.map});
          $modalInstance.close("csv-modal-closed");
          result.resolve();

          return result.promise;
        }
      } else {
        alert('Please choose a saved import map to use for the FTP import. If you do not have a saved map you can import an order file with the same format from your computer and save a map that can be used for FTP imports.');
      }
    }

    scope.edit_map = function () {
      scope.start_editing_map = true;
      scope.update_single_store(false);
    };

    scope.check_map = function () {
      scope.start_checking_map = true;
      scope.update_single_store(false);
    };

    scope.select_map = function (map) {
      stores.csv.map.update(scope.stores, map);
    };

    scope.delete_map = function (map) {
      if(confirm("are you sure, you want to delete mapping?")){
        stores.csv.map.delete_map(scope.stores, map);
        stores.csv.map.get(scope.stores)
      }
    };

    scope.clear_map = function (kind) {
      stores.csv.map.delete(scope.stores, kind);
    };

    scope.shipstation_verify_tags = function (store_id) {
      stores.shipstation.verify_tags(store_id).then(function (response) {
        scope.verification_tags = response.data.data;
      });
    };

    scope.shipstation_verify_awaiting = function (store_id) {
      stores.shipstation.verify_awaiting_tags(store_id).then(function (response) {
        if (response.data.data.orders.length > 0){
          notification.notify("GP ready awaiting orders found for this store", 1);
        } else {
          notification.notify("GP ready awaiting orders not found for this store", 0);
        }
      });
    };

    scope.shipstation_update_all_locations = function (store_id) {
      scope.shipstation_loc_update_response = {}
      stores.shipstation.update_all_locations(store_id).then(function (response) {
        scope.shipstation_loc_update_response = response.data;
      });
    };

    myscope.open_popup = function (url) {
      var w = 1000;
      var h = 400;
      if(scope.stores.single.store_type=="BigCommerce") {
        w = 1300;
        h = 600;
      }
      var left_adjust = angular.isDefined($window.screenLeft) ? $window.screenLeft : $window.screen.left;
      var top_adjust = angular.isDefined($window.screenTop) ? $window.screenTop : $window.screen.top;
      var width = $window.innerWidth ? $window.innerWidth : $window.document.documentElement.clientWidth ? $window.document.documentElement.clientWidth : $window.screen.width;
      var height = $window.innerHeight ? $window.innerHeight : $window.document.documentElement.clientHeight ? $window.document.documentElement.clientHeight : $window.screen.height;

      var left = ((width / 2) - (w / 2)) + left_adjust;
      var top = ((height / 2) - (h / 2)) + top_adjust;

      var popup = $window.open(url, '', "top=" + top + ", left=" + left + ", width=" + w + ", height=" + h);
      var interval = 1000;

      var i = $interval(function () {
        try {
          if (popup == null || popup.closed) {
            $interval.cancel(i);
            myscope.store_single_details(scope.stores.single.id, true);
          }
        } catch (e) {
          console.error(e);
        }
      }, interval);
    };
    scope.launch_ebay_popup = function () {
      var ebay_url = $sce.trustAsResourceUrl(scope.stores.ebay.signin_url + '&ruparams=redirect%3Dtrue%26editstatus%3D' + scope.edit_status + '%26name%3D' +
        scope.stores.single.name + '%26status%3D' + scope.stores.single.status + '%26storetype%3D' +
        scope.stores.single.store_type + '%26storeid%3D' + scope.stores.single.id + '%26inventorywarehouseid%3D' + scope.stores.single.inventory_warehouse_id + '%26importimages%3D' + scope.stores.single.import_images +
        '%26importproducts%3D' + scope.stores.single.import_products + '%26messagetocustomer%3D' + scope.stores.single.thank_you_message_to_customer + '%26tenantname%3D' + scope.stores.ebay.current_tenant);

      myscope.open_popup(ebay_url);
    };

    scope.launch_shopify_popup = function (ev) {
      copy_paste_token = ev.ctrlKey;
      $timeout(function () {
        var shopify_url = $sce.trustAsResourceUrl(scope.stores.single.shopify_permission_url);
        if (shopify_url == null) {
          if (typeof scope.stores.single.shop_name == 'undefined') {
            notification.notify("Please enter your store name first.");
          } else if (copy_paste_token) {
            if (scope.stores.single.access_token != null || scope.stores.single.access_token != 'null') {
              $('#copy')[0].value = scope.stores.single.access_token;
              selecter = document.querySelector('#copy');
              selecter.select();
              document.execCommand('copy');
              notification.notify('Shopify Token copied to clipboard', 1);
            }
          }
        } else {
          if (copy_paste_token) {
            if (confirm('Are you sure to enter shopify access token manually?')) {
              token_input = prompt('Enter Token');
              if (token_input == '') {
                notification.notify('Please enter valid Shopify token', 0);
              } else {
                $http.post($rootScope.api_url + '/stores/update_shopify_token', { id: scope.stores.single.id, token: token_input }).success(function (data) {
                  if (data.status) {
                    notification.notify('Shopify Token Updated', 1);
                    scope.update_single_store(true);
                  } else {
                    notification.notify(data.error_messages, 0);
                  }
                });
              }
            }
          } else {
            myscope.open_popup(shopify_url);
          }
        }
      }, 500);
    };

    scope.launch_shopline_popup = function (ev) {
      copy_paste_token = ev.ctrlKey;
      $timeout(function () {
        var shopline_url = $sce.trustAsResourceUrl(scope.stores.single.shopline_permission_url);
        if (shopline_url == null) {
          if (typeof scope.stores.single.shop_name == 'undefined') {
            notification.notify("Please enter your store name first.");
          } else if (copy_paste_token) {
            if (scope.stores.single.access_token != null || scope.stores.single.access_token != 'null') {
              $('#copy')[0].value = scope.stores.single.access_token;
              selecter = document.querySelector('#copy');
              selecter.select();
              document.execCommand('copy');
              notification.notify('Shopline Token copied to clipboard', 1);
            }
          }
        } else {
          if (copy_paste_token) {
            if (confirm('Are you sure to enter shopline access token manually?')) {
              token_input = prompt('Enter Token');
              if (token_input == '') {
                notification.notify('Please enter valid Shopline token', 0);
              } else {
                $http.post($rootScope.api_url + '/stores/update_shopline_token', { id: scope.stores.single.id, token: token_input }).success(function (data) {
                  if (data.status) {
                    notification.notify('Shopline Token Updated', 1);
                    scope.update_single_store(true);
                  } else {
                    notification.notify(data.error_messages, 0);
                  }
                });
              }
            }
          } else {
            myscope.open_popup(shopline_url);
          }
        }
      }, 500);
    };

    scope.launch_big_commerce_popup = function () {
      $timeout(function () {
        var bigcommerce_url = $sce.trustAsResourceUrl(scope.stores.single.bigcommerce_permission_url);
        if (bigcommerce_url == null) {
          if (typeof scope.stores.single.shop_name == 'undefined') {
            notification.notify("Please enter your store name first.");
          }
        } else {
          myscope.open_popup(bigcommerce_url);
        }
      }, 500);
    };

    scope.launch_magento_aurthorize_popup = function () {
      var magento_authorize_url;
      stores.magento.get_aurthorize_url(scope.stores.single.id).then(function (response) {
        if(response.data.status==false) {
          notification.notify(response.data.message);
          return;
        } else{
          $timeout(function () {
            var magento_url = $sce.trustAsResourceUrl(response.data.authorized_url);
            if (magento_url == null) {
              if (typeof response.data.authorized_url == 'undefined') {
                notification.notify("Please enter correct URL, API Key and API Secret.");
              }
            } else {
              myscope.open_popup(response.data.authorized_url);
            }
          }, 500);
        }
      })
    };

    scope.get_magento_access_token = function () {
      stores.magento.get_access_token(scope.stores.single).then(function (response) {
        scope.stores.single.message='';
        myscope.store_single_details(scope.stores.single.id, true);
      })
    }

    myscope.rollback = function () {
      if (typeof myscope.single == "undefined" || typeof myscope.single.id == "undefined") {
        if (typeof scope.stores.single['id'] != "undefined") {
          stores.list.update('delete', {setup: {status: ''}, list: [{id: scope.stores.single.id, checked: true}]});
        }
      } else {
        scope.stores.single = {};
        angular.copy(myscope.single, scope.stores.single);
        scope.update_single_store(true);
      }
    };

    myscope.up_key = function (event) {
      event.preventDefault();
      event.stopPropagation();
      if ($state.includes('settings.stores.single')) {
        if (scope.stores.current > 0) {
          myscope.load_item(scope.stores.current - 1);
        } else {
          alert("Already at the top of the list");
        }
      }
    };

    myscope.down_key = function (event) {
      event.preventDefault();
      event.stopPropagation();
      if ($state.includes('settings.stores.single')) {
        if (scope.stores.current < scope.stores.list.length - 1) {
          myscope.load_item(scope.stores.current + 1);
        } else {
          alert("Already at the bottom of the list");
        }
      }
    };

    myscope.load_item = function (id) {
      var newStateParams = angular.copy($stateParams);
      newStateParams.storeid = "" + scope.stores.list[id].id;
      myscope.store_single_details(scope.stores.list[id].id, true);
      $state.go($state.current.name, newStateParams);
    };

    scope.export_active_products = function () {
      url = $rootScope.api_url + '/stores/export_active_products'
      $http.get(url)
        .success(function (response) {
          var downloadLink = angular.element('<a></a>');//create a new  <a> tag element
          downloadLink.attr('href', response.url);
          downloadLink.attr('download', response.filename);
          downloadLink.attr('target','_self');
          downloadLink[0].click();//call click function
      }).error(notification.server_error);
    };

    scope.bin_location_api_push = function (store_id) {
      url = $rootScope.api_url + '/stores/bin_location_api_push?id=' + store_id;
      $http.get(url)
        .success(function (response) {
          notification.notify('Bin Location will be pushed.', 1);
      }).error(notification.server_error);
    };

    scope.bin_location_api_pull = function (store_id) {
      url = $rootScope.api_url + '/stores/bin_location_api_pull?id=' + store_id;
      $http.get(url)
        .success(function (response) {
          notification.notify('Bin Location will be pulled.', 1);
      }).error(notification.server_error);
    };

    scope.updateOriginStore = function (originStoreId, newName) {
      var url = $rootScope.api_url + '/origin_stores/' + originStoreId;
      var data = { origin_store: { store_name: newName } };

      $http({
        method: 'PATCH', // I Use 'PUT' for full updates
        url: url,
        data: data
      })
      .then(function(response) {
        notification.notify('Origin Store updated successfully.', 1);
      })
      .catch(function(error) {
        notification.server_error(error);
      });
    };

    scope.update_product_import_type = function (type) {
      if (type == 'refresh_catalog') {
        scope.show_product_import_range_days = true;
      } else {
        scope.show_product_import_range_days = false;
      }
    }

    myscope.init = function () {
      scope.translations = {
        "tooltips": {
          "ftp_address": "",
          "import_from_ftp": "",
          "pull_combined_qoh": "",
          "shopify_inventory_sync": ""
        }
      };
      groov_translator.translate('settings.csv_modal', scope.translations);
      scope.stores = store_data;
      scope.stores.single = {};
      scope.stores.ebay = {};
      scope.stores.csv = {};
      scope.stores.csv.maps = {order: [], product: []};
      scope.stores.csv.mapping = {};
      scope.start_editing_map = false;
      scope.stores.single.file_path = '';
      scope.beta_option = false;
      scope.stores.import = {
        order: {},
        product: {},
        image: {}
      };

      scope.show_product_import_range_days = false;

      scope.copy_text = {
        text: 'Click Here to copy to clipboard',
        class: '',
        mgcb_text: 'Click Here to copy to clipboard',
        mgcb_class: '',
        mgsc_text: 'Click Here to copy to clipboard',
        mgsc_class: ''
      };

      scope.stores.types = {};
      scope.warehouses = warehouses.model.get();
      warehouses.list.get(scope.warehouses).then(function () {
        if (typeof scope.stores.single['inventory_warehouse_id'] != "number") {
          for (var i = 0; i < scope.warehouses.list.length; i++) {
            if (scope.warehouses.list[i].info.is_default) {
              scope.stores.single.inventory_warehouse_id = scope.warehouses.list[i].info.id;
              break;
            }
          }
        }
      });

      stores.csv.map.get(scope.stores);


      scope.stores.types = {
        Magento: {
          name: "Magento SOAP",
          file: "/assets/views/modals/settings/stores/magento.html"
        },
        "Magento API 2": {
          name: "Magento REST",
          file: "/assets/views/modals/settings/stores/magento_rest.html"
        },
        Ebay: {
          name: "Ebay",
          file: "/assets/views/modals/settings/stores/ebay.html"
        },
        Amazon: {
          name: "Amazon",
          file: "/assets/views/modals/settings/stores/amazon.html"
        },
        CSV: {
          name: "CSV",
          file: "/assets/views/modals/settings/stores/csv.html"
        },
        "Shipstation API 2": {
          name: "Shipstation",
          file: "/assets/views/modals/settings/stores/shipstation_rest.html"
        },
        Shippo: {
          name: "Shippo",
          file: "/assets/views/modals/settings/stores/shippo.html"
        },
        Shipworks: {
          name: "Shipworks",
          file: "/assets/views/modals/settings/stores/shipworks.html"
        },
        Shopify: {
          name: "Shopify",
          file: "/assets/views/modals/settings/stores/shopify.html"
        },
        Shopline: {
          name: "Shopline",
          file: "/assets/views/modals/settings/stores/shopline.html"
        },
        "BigCommerce": {
          name: "BigCommerce",
          file: "/assets/views/modals/settings/stores/big_commerce.html"
        },
        "ShippingEasy": {
          name: "Shipping Easy",
          file: "/assets/views/modals/settings/stores/shipping_easy.html"
        },
        "Teapplix": {
          name: "Teapplix",
          file: "/assets/views/modals/settings/stores/teapplix.html"
        },
        Veeqo: {
          name: "Veeqo",
          file: "/assets/views/modals/settings/stores/veeqo.html"
        },
      };


      //Determine create/ edit/ redirect call

      scope.stores.import.order.type = 'apiimport';
      scope.stores.import.product.type = 'apiimport';
      scope.stores.ebay.show_url = true;
      scope.stores.ebay.signin_url_status = true;
      if ($state.includes('settings.stores.create')) {
        scope.edit_status = false;
        scope.redirect = false;
        scope.stores.single.status = 1;
        scope.stores.ebay.show_url = true;
        stores.ebay.sign_in_url.get(scope.stores);
        scope.stores.single.import_images = true;
        scope.stores.single.import_products = true;
        scope.stores.single.shall_import_awaiting_shipment = true;
      } else {
        scope.edit_status = true;
        scope.redirect = ($stateParams.redirect || ($stateParams.action == "create"));
        if (scope.redirect) {
          if (typeof $stateParams['editstatus'] != 'undefined' && $stateParams.editstatus == 'true') {
            scope.edit_status = $stateParams.editstatus;
            stores.ebay.user_token.update(scope.stores, $stateParams.storeid);

            scope.stores.single.id = $stateParams.storeid;

            scope.stores.single.name = $stateParams.name;

            scope.stores.single.status = ($stateParams.status == 'true');
            scope.stores.single.store_type = $stateParams.storetype;

            scope.stores.single.inventory_warehouse_id = parseInt($stateParams.inventorywarehouseid);
            scope.stores.single.import_images = ($stateParams.importimages == 'true');
            scope.stores.single.import_products = ($stateParams.importproducts == 'true');
            scope.stores.single.thank_you_message_to_customer = $stateParams.messagetocustomer;
          } else {
            scope.stores.single.name = $stateParams.name;
            scope.stores.single.status = ($stateParams.status == true);
            scope.stores.single.store_type = $stateParams.storetype;

            scope.stores.single.inventory_warehouse_id = parseInt($stateParams.inventorywarehouseid);
            scope.stores.single.import_images = ($stateParams.importimages == 'true');
            scope.stores.single.import_products = ($stateParams.importproducts == 'true');
            scope.stores.single.thank_you_message_to_customer = $stateParams.messagetocustomer;
            stores.ebay.user_token.fetch(scope.stores).then(function (response) {
              if (response.data.status) {
                scope.update_single_store(true);
              }
            });
          }
          if (typeof scope.stores.single.status == "undefined") {
            scope.stores.single.status = 1;
          }
        } else {
          myscope.store_single_details($stateParams.storeid, true);
        }

      }


      scope.$on("fileSelected", function (event, args) {
        if (args.name == 'orderfile' || args.name == 'productfile' || args.name == 'kitfile') {
          scope.$apply(function () {
            scope.stores.single[args.name] = args.file;
          });
          // $("input[type='file']").val('');
          if (args.name == 'orderfile') {
            scope.stores.single.type = 'order';
          } else if (args.name == 'productfile') {
            scope.stores.single.type = 'product';
          } else if (args.name == 'kitfile') {
            scope.stores.single.type = 'kit';
          }
          //scope.update_single_store(false);
        }
      });
      $modalInstance.result.then(scope.update, scope.update);
      hotkeys.bindTo(scope).add({
        combo: 'up',
        description: 'Previous user',
        callback: myscope.up_key
      }).add({
        combo: 'down',
        description: 'Next user',
        callback: myscope.down_key
      })

    };

    myscope.init();

  }]);
