groovepacks_controllers.
  controller('exportNotificationCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', 'settings_data', '$rootScope',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, settings_data, $rootScope) {
    var myscope = {};

    myscope.init = function () {
      $scope.settings_data = settings_data;
      $scope.export_address = "";
    };

    $scope.update_export_address = function(email, type){
      if (email != "" && email.includes("@")){
        if (type == 'stat'){
          $scope.settings_data.stat_export_email = email;
        }else if (type == 'daily') {
            $scope.settings_data.daily_packed_email = email;
        }else{
          $scope.settings_data.order_export_email = email;
        } 
        $http.put($rootScope.api_url + '/exportsettings/update_export_settings.json', $scope.settings_data);
        notification.notify("Email is succesfully updated.", 1);
        $modalInstance.close("ok-button-click");
      } else {
        notification.notify("Please update email", 0);
        $modalInstance.dismiss("cancel-button-click");  
      }
    }

    $scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    myscope.init();
  }]);
