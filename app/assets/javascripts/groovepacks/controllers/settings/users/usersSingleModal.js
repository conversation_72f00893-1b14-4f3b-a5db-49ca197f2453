groovepacks_controllers.
  controller('usersSingleModal', ['$scope', 'user_data', '$state', '$stateParams', '$modal', '$modalInstance', '$timeout', 'hotkeys', 
    'users', 'auth', 'notification', 'groov_translator', '$http', '$rootScope', '$window', 'generalsettings',
    function (scope, user_data, $state, $stateParams, $modal, $modalInstance, $timeout, hotkeys, users, auth, notification, 
      groov_translator, $http, $rootScope, $window, generalsettings) {

      var myscope = {};
      scope.dashboard_options = [
        {name: "None", value: "none"},
        {name: "Admin Dashboard", value: "admin_dashboard"},
        {name: "Packer Stats", value: "packer_dashboard"},
        {name: "Admin dashboard with Packer Stats", value: "admin_dashboard_with_packer_stats"}
      ]

      /**
       * Public methods
       */
      scope.print_confirmation_code_pdf = function (id) {
        return $http.get($rootScope.api_url + '/users/' + id + '/print_confirmation_code.pdf')
        .success(function (data) {
          $window.open(data.url);
        }).error(function(response) {
        });
      }

      scope.ok = function () {
        if (scope.users.single.username && (!scope.show_password || (scope.users.single.password && scope.users.single.conf_password)) && scope.users.single.confirmation_code) {
          $modalInstance.close("ok-button-click");
        } else {
          if (!users.single.validate(scope.users, true, scope.edit_status)) {
            notification.notify("Oops, looks like some additional info is required");
          } else if (scope.show_password && (typeof scope.users.single.password == "undefined" || typeof scope.users.single.conf_password == "undefined")) {
            notification.notify("Both password and confirm password must be at least 8 characters");
          }
        }
      };
      scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      scope.update = function (reason) {
        if (reason == "cancel-button-click") {
          myscope.rollback();
        } else if (typeof scope.users.single.id != "undefined") {
          scope.update_single_user(false);
        }
      };

      scope.user_single_details = function (id, new_rollback) {
        for (var i = 0; i < scope.users.list.length; i++) {
          if (scope.users.list[i].id == id) {
            scope.users.current = parseInt(i);
            break;
          }
        }
        return users.single.get(id, scope.users).then(function (data) {
          // Set originalRole immediately when user data is loaded
          if (scope.users.single.role) {
            scope.originalRole = angular.copy(scope.users.single.role);
          }

          myscope.load_roles();
          scope.edit_status = true;
          scope.show_password = false;
          if (typeof new_rollback == 'boolean' && new_rollback) {
            myscope.single = {};
            angular.copy(scope.users.single, myscope.single);
            if(scope.users.single.view_dashboard == '0'){
              scope.users.single.view_dashboard = 'none';
            }
          }
        });
      };

      scope.update_single_user = function (auto) {
        if (users.single.validate(scope.users, auto, scope.edit_status)) {
          return users.single.update(scope.users, auto).then(function (data) {
            myscope.load_roles();
            if (data.data.status) {
              var wasCreateMode = !scope.edit_status;
              scope.edit_status = true;
              scope.show_password = false;
              scope.ready_for_auto_complete = false;

              // If this was a create operation that just became an edit operation,
              // we need to set the originalRole
              if (wasCreateMode && scope.users.single.role && !scope.originalRole) {
                scope.originalRole = angular.copy(scope.users.single.role);
              }
            }
          });
        }
      };

      scope.make_new_role = function () {
        return users.roles.create(scope.users).then(myscope.load_roles);
      };

      scope.delete_role = function () {
        if (confirm("Are you sure you want to delete this role? All users with current role will be changed to Scan & Pack users")) {
          users.roles.delete(scope.users).then(myscope.load_roles);
        }
      };


      scope.set_selected_role = function (event) {
        if (!scope.checkAdminOnlySelection(event)) {
          return;
        }

        scope.roles_data.showSelectBaseRole = false;
        if (scope.roles_data.selectedRole != null) {
          if (confirm("Are you sure?")) {
            $timeout(function () {
              scope.users.single.role = scope.roles_data.selectedRole;
              if(scope.roles_data.selectedRole.name === 'Super Admin'){
                scope.users.single.override_pass_scanning = true;
              }
              scope.update_single_user();
            });
          }
        } else {
          scope.roles_data.showSelectBaseRole = true;
          scope.users.single.role = {};
        }
      };

      scope.change_opt = function (key, value) {
        scope.users.single[key] = value;
        scope.update_single_user();
      };

      scope.set_base_role = function (role) {
        scope.users.single.role = {};
        for (var i in role) {
          if (role.hasOwnProperty(i) && i != "id" && i != "name") {
            scope.users.single.role[i] = role[i];
          }
        }
        scope.roles_data.showSelectBaseRole = false;
        notification.notify("Permissions from " + role.name + " applied", 1);        
      };

      scope.change_password = function () {
        scope.show_password = true;
      };

      myscope.rollback = function () {
        if (typeof myscope.single == 'undefined' || typeof myscope.single['id'] == "undefined") {
          if (typeof scope.users.single['id'] != "undefined") {
            users.list.update('delete', {setup: {status: ''}, list: [{id: scope.users.single.id, checked: true}]});
          }
        } else {
          scope.users.single = {};
          angular.copy(myscope.single, scope.users.single);
          scope.update_single_user();
        }
      };
      /**
       * private properties
       */
      myscope.load_roles = function () {
        return users.roles.get(scope.users).then(myscope.reset_selected_role);
      };

      myscope.up_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if (scope.edit_status) {
          if (scope.users.current > 0) {
            myscope.load_item(scope.users.current - 1);
          } else {
            alert("Already at the top of the list");
          }
        }
      };

      myscope.down_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if (scope.edit_status) {
          if (scope.users.current < scope.users.list.length - 1) {
            myscope.load_item(scope.users.current + 1);
          } else {
            alert("Already at the bottom of the list");
          }
        }
      };

      myscope.left_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        var tabs_len = scope.modal_tabs.length - 1;
        for (var i = 0; i <= tabs_len; i++) {
          if (scope.modal_tabs[i].active) {
            //scope.modal_tabs[i].active = false;
            scope.modal_tabs[((i == 0) ? tabs_len : (i - 1))].active = true;
            break;
          }
        }
      };

      myscope.right_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        var tabs_len = scope.modal_tabs.length - 1;
        for (var i = 0; i <= tabs_len; i++) {
          if (scope.modal_tabs[i].active) {
            //scope.modal_tabs[i].active = false;
            scope.modal_tabs[((i == tabs_len) ? 0 : (i + 1))].active = true;
            break;
          }
        }
      };

      myscope.load_item = function (id) {
        var newStateParams = angular.copy($stateParams);
        newStateParams.user_id = "" + scope.users.list[id].id;
        scope.user_single_details(scope.users.list[id].id, true);
        $state.go($state.current.name, newStateParams);
      };

      myscope.reset_selected_role = function () {
        scope.roles_data.selectedRole = null;
        //set role by reference for modal
        for (var i = 0; i < scope.users.roles.length; i++) {
          if (scope.users.single.role.id === scope.users.roles[i].id) {
            scope.roles_data.selectedRole = scope.users.roles[i];
            auth.check();
          }
        }
      };

      scope.auto_complete = function(event, type){
        if(event.keyCode != 8 && event.key != "Backspace"){
          var value = event.target.value;
          generalsettings.single.auto_complete(value, 'users').success(function(response){
            scope.set_auto_complete_fields_data(response, type);
          });
        }
      }

      scope.update_custom_fields_onchange = function(type){
        setTimeout(function(){ scope.update_single_user(); }, 1000)
      }

      scope.clear_auto_complete = function(type){
        scope.update_single_user();
        scope.set_auto_complete_fields_data(null, type);
      }

      scope.set_auto_complete_fields_data = function(value, type){
        scope.ready_for_auto_complete =  value == null ? false : true
        switch (type) {
          case 'custom_user_field_one':
            scope.custom_user_field_one_auto_complete_data = value;
            break;
          case 'custom_user_field_two':
            scope.custom_user_field_two_auto_complete_data = value;
            break;
          default:
        }
      }

      scope.check_input_is_disable = function(){
        if($rootScope.current_user_data.username == 'gpadmin' || $rootScope.current_user_data.role.name == 'Super Admin')
        {
          return false;
        }
        return true;
      }

      // Add this function to validate role selection based on slot availability
      scope.checkAdminOnlySelection = function(event) {
        if (!scope.users.user_counts) return true;

        // If Administrative role is selected, ensure it's valid
        if (scope.roles_data.selectedRole && scope.roles_data.selectedRole.name === 'Administrative') {
          var regularSlotsRemaining = scope.users.user_counts.regular_slots_remaining;
          var adminSlotsRemaining = scope.users.user_counts.admin_slots_remaining;

          // Administrative should only be selectable when regular slots are full
          if (regularSlotsRemaining > 0) {
            notification.notify("Cannot create Admin Only user when regular user slots are available.", 0);
            scope.roles_data.selectedRole = null;
            return false;
          }

          // Check if admin slots are available
          if (adminSlotsRemaining <= 0) {
            notification.notify("No Admin Only slots available.", 0);
            scope.roles_data.selectedRole = null;
            return false;
          }
        }
        return true;
      };

      // Add this function to filter roles based on slot availability
      scope.getAvailableRoles = function() {
        if (scope.edit_status) {
          if (!scope.originalRole) {
            return scope.users.roles;
          }

          if (scope.originalRole.name === 'Administrative') {
            return scope.users.roles.filter(function(role) {
              return role.name === 'Administrative';
            });
          }
          else {
            return scope.users.roles.filter(function(role) {
              return role.name !== 'Administrative';
            });
          }
        }

        if (!scope.users.user_counts) {
          return scope.users.roles.filter(function(role) {
            return role.name !== 'Administrative';
          });
        }

        var regularSlotsRemaining = scope.users.user_counts.regular_slots_remaining;
        var adminSlotsRemaining = scope.users.user_counts.admin_slots_remaining;

        if (regularSlotsRemaining > 0) {
          return scope.users.roles.filter(function(role) {
            return role.name !== 'Administrative';
          });
        }
        else if (adminSlotsRemaining > 0) {
          return scope.users.roles.filter(function(role) {
            return role.name === 'Administrative';
          });
        }
        else {
          return [];
        }
      };

      myscope.init = function () {
        scope.translations = {
          "tooltips": {
            "conf_code": "",
            "name": "",
            "override_pass": "",
            "custom_fields": "",
            role: "",
            "base_role": "",
            "section_access": ""
          }
        };
        groov_translator.translate('settings.users.modal', scope.translations);
        scope.users = user_data;
        scope.general_settings = generalsettings.model.get();
        generalsettings.single.get(scope.general_settings);

        scope.modal_tabs = [
          {
            active: true,
            heading: "User Info",
            templateUrl: '/assets/views/modals/settings/user/info.html'
          },
          {
            active: false,
            heading: "Products",
            templateUrl: '/assets/views/modals/settings/user/products.html'
          },
          {
            active: false,
            heading: "Orders",
            templateUrl: '/assets/views/modals/settings/user/orders.html'
          },
          {
            active: false,
            heading: "User",
            templateUrl: '/assets/views/modals/settings/user/user.html'
          },
          {
            active: false,
            heading: "System",
            templateUrl: '/assets/views/modals/settings/user/system.html'
          }
        ];
        $modalInstance.result.then(scope.update, scope.update);
        /**
         * Public properties
         */
        scope.roles_data = {};
        if (typeof $stateParams['user_id'] === 'undefined') {
          scope.edit_status = false;
          scope.show_password = true;
          scope.users.single = {};
          scope.users.single.active = true

          $http.get($rootScope.api_url + '/users/get_super_admin_email.json').success(function(data) {
              scope.users.single.email = data.email;
          });
          scope.users.single.role = {};
          scope.roles_data.showSelectBaseRole = true;
          myscope.load_roles().then(function () {
            // Check slot availability to determine default role
            var regularSlotsRemaining = scope.users.user_counts ? scope.users.user_counts.regular_slots_remaining : 1;
            var adminSlotsRemaining = scope.users.user_counts ? scope.users.user_counts.admin_slots_remaining : 0;

            var defaultRoleName = 'Scan & Pack User'; // Default for regular users

            // If regular slots full but admin slots available, default to Administrative
            if (regularSlotsRemaining <= 0 && adminSlotsRemaining > 0) {
              defaultRoleName = 'Administrative';
            }

            for (var i = 0; i < scope.users.roles.length; i++) {
              if (scope.users.roles[i].name == defaultRoleName) {
                scope.users.single.role = scope.users.roles[i];
                scope.roles_data.selectedRole = scope.users.roles[i]; // Also set selected role
                scope.roles_data.showSelectBaseRole = false;
                myscope.reset_selected_role();
                break;
              }
            }
          });
        } else {
          scope.edit_status = true;
          scope.show_password = false;
          scope.roles_data.showSelectBaseRole = false;
          scope.user_single_details($stateParams['user_id'], true);

          // Set selected role when editing existing user
          myscope.load_roles().then(function() {
            if (scope.users.single.role) {
              // originalRole is already set in user_single_details, just set the selected role
              scope.roles_data.selectedRole = scope.users.single.role;
              scope.roles_data.showSelectBaseRole = false;
            }
          });
        }

        hotkeys.bindTo(scope).add({
          combo: 'up',
          description: 'Previous user',
          callback: myscope.up_key
        }).add({
          combo: 'down',
          description: 'Next user',
          callback: myscope.down_key
        }).add({
          combo: 'left',
          description: 'Previous tab',
          callback: myscope.left_key
        }).add({
          combo: 'right',
          description: 'Next tab',
          callback: myscope.right_key
        }).add({
          combo: 'esc',
          description: 'Save and close modal',
          callback: function () {
          }
        });
      };
      myscope.init();
    }]);
