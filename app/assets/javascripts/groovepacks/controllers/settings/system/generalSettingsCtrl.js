groovepacks_controllers.
  controller('generalSettingsCtrl', ['$scope', '$modal', '$http', '$timeout', '$location', '$state', '$cookies', 'ngClipboard', 'generalsettings', 'groov_translator', '$rootScope', 'api_keys', 'webhooks',
    function ($scope, $modal, $http, $timeout, $location, $state, $cookies, ngClipboard, generalsettings, groov_translator, $rootScope, api_keys, webhooks) {

      var myscope = {};


      myscope.init = function () {
        switch ($location.url()) {
          case '/settings/system/printing':
            var current_page = 'printing';
            break;
          case '/settings/system/developer_tools':
            var current_page = 'developer_tools';
            break;
          default:
            var current_page = 'general';
        }
        $scope.setup_page('system', current_page);
        $scope.translations = {
          "headings": {
            "inventory": "",
            "conf_notif": "",
            "packing_slips": "",
            "product_labels": "",
            "direct_printing_options": "",
            "generate_numeric_barcode": "",
            "miscellaneous_settings": "",
            "flash_setting": "",
            "time_zone": "",
            "adjust_for_daylight": "",
            "order_item_export": "",
            "order_tracking_no_order": "",
            "order_tracking_no_tracking": "",
            "product_weight": "",
            "product_dimension": "",
            "pre_day_order": "",
            "pre_day_import": "",
            "import_orders": "",
            "recurring_ftp": "",
            "new_time_zone": "",
            "api_keys": "",
            "create_webhook": "",
          },
          "labels": {
            "inventory_tracking": "",
            "low_inventory_alert_email": "",
            "time_to_send_email": "",
            "send_email_on": "",
            "mon": "",
            "tue": "",
            "wed": "",
            "thu": "",
            "fri": "",
            "sat": "",
            "sun": "",
            "strict_cc": "",
            "conf_code_product_instruction": "",
            "default_low_inventory_alert_limit": "",
            "conf_req_on_notes_to_packer": "",
            "send_email_for_packer_notes": "",
            "email_address": "",
            "system_notifications": "System Notifications",
            "billing_notifications": "Billing Notifications",
            "report_out_of_stock_notifications": "Report Out of Stock",
            "packing_slip_size": "",
            "packing_slip_orientation": "",
            "portrait": "",
            "landscape": "",
            "packing_slip_message_to_customer": "",
            "show_primary_bin_loc_in_barcodeslip": "",
            "show_sku_in_barcodeslip": "",
            "inventory_auto_allocation": "",
            "custom_field_one": "Custom Field One",
            "custom_order_one": "Custom Order One",
            "barcode_length": "Barcode Length",
            "starting_value": "Starting Value",
            "custom_field_two": "Custom Field Two",
            "custom_order_two": "Custom Order Two",
            "html_print": "",
            "inactive_logout_time": "",
            "hex_barcode": "",
            "print_post_scanning_barcodes": "",
            "print_product_barcode_labels": "",
            "print_product_receiving_labels": "",
            "print_packing_slips": "",
            "print_ss_shipping_labels": "",
            "custom_user_field_one": "Custom User Field One",
            "custom_user_field_two": "Custom User Field Two",
            "custom_user_order_one": "Custom User Order One",
            "custom_user_order_two": "Custom User Order Two",
            "display_kit_parts": "Display Kit Parts on Packing Slips",
            "remove_order_items": "Remove order items with quantity of 0",
            "show_imported_skus": "Display Imported SKUs in Order Items", 
            "delete_import_summary": "Delete Import Summary",
            "display_sku_total": "Display sku total order by order",
            "display_one_total": "Display one total for each sku",
            "standard_order_export": "Standard Order Export",
            "product_barcode_label_size": "",
            "truncated_string": "",
            "truncate_order_number_in_packing_slip": "",
            "api_key": "",
            "generate_api_key": "",
            "delete_api_key": "",
            "regenerate_api_key": "",
          },
          "tooltips": {
            "inventory_tracking": "",
            "low_inventory_alert_email": "",
            "default_low_inventory_alert_limit": "",
            "conf_req_on_notes_to_packer": "",
            "strict_cc": "",
            "conf_code_product_instruction": "",
            "send_email_for_packer_notes": "",
            "packing_slip_size": "",
            "packing_slip_message_to_customer": "",
            "show_primary_bin_loc_in_barcodeslip": "",
            "order_item_export_message": "",
            "html_print": "",
            "inactive_logout_time": "",
            "hex_barcode": "",
            "custom_fields": "",
            "pre_day_order_import_schedule": "",
            "recurring_ftp_order_import_range": "",
            "display_kit_parts": "",
            "remove_order_items": "",
            "show_imported_skus": "", 
            "delete_import_summary": "",
            "starting_value": "",
            "direct_printing_tooltip": "",
            "product_barcode_label_size": "",
            "truncate_order_number_in_packing_slip": "",
            "api_key": "",
            "generate_api_key": "",
            "delete_api_key": "",
            "regenerate_api_key": "",
          }
        };

        $scope.stepsArray = [
          { value: 0, legend: '0' }, { value: 1, legend: '1 AM' }, { value: 2, legend: '2 AM' }, { value: 3, legend: '3 AM' },
          { value: 4, legend: '4 AM' }, { value: 5, legend: '5 AM' }, { value: 6, legend: '6 AM' },
          { value: 7, legend: '7 AM' }, { value: 8, legend: '8 AM' }, { value: 9, legend: '9 AM' },
          { value: 10, legend: '10 AM' }, { value: 11, legend: '11 AM' }, { value: 12, legend: '12 Noon' },
          { value: 13, legend: '1 PM' }, { value: 14, legend: '2 PM' }, { value: 15, legend: '3 PM' },
          { value: 16, legend: '4 PM' }, { value: 17, legend: '5 PM' }, { value: 18, legend: '6 PM' },
          { value: 19, legend: '7 PM' }, { value: 20, legend: '8 PM' }, { value: 21, legend: '9 PM' },
          { value: 22, legend: '10 PM' }, { value: 23, legend: '11 PM' }, { value: 24, legend: '12 Mid' }
        ]

        $scope.slider = {
          min: 1,
          max: 24,
          options: {
            floor: 0,
            ceil: 24,
            onChange: function () {
              $scope.show_button = true;
            },
            translate: function (value) {
              var data = null;
              angular.forEach($scope.stepsArray, function (svalue, skey) {
                if (value == svalue.value) {
                  data = svalue.legend;
                }
              });
              return data;
            },
          },
        }
        groov_translator.translate('settings.system.general', $scope.translations);

        $scope.show_button = false;
        $scope.general_settings = generalsettings.model.get();
        $scope.confirmation_hash = {};
        myscope.reload_settings();
        generalsettings.single.get($scope.general_settings).then(function () {
          // var flash_exist = (function(){
          //   var b=new function(){var n=this;n.c=!1;var a="ShockwaveFlash.ShockwaveFlash",r=[{name:a+".7",version:function(n){return e(n)}},{name:a+".6",version:function(n){var a="6,0,21";try{n.AllowScriptAccess="always",a=e(n)}catch(r){}return a}},{name:a,version:function(n){return e(n)}}],e=function(n){var a=-1;try{a=n.GetVariable("$version")}catch(r){}return a},i=function(n){var a=-1;try{a=new ActiveXObject(n)}catch(r){a={activeXError:!0}}return a};n.b=function(){if(navigator.plugins&&navigator.plugins.length>0){var a="application/x-shockwave-flash",e=navigator.mimeTypes;e&&e[a]&&e[a].enabledPlugin&&e[a].enabledPlugin.description&&(n.c=!0)}else if(-1==navigator.appVersion.indexOf("Mac")&&window.execScript)for(var t=-1,c=0;c<r.length&&-1==t;c++){var o=i(r[c].name);o.activeXError||(n.c=!0)}}()};
          //     return b.c;
          //   })();
          // if (flash_exist == false){
          //   $scope.general_settings.single.flash = "off"
          // } else {
          //   $scope.general_settings.single.flash = "on"
          // }
          // $scope.general_settings.single.time_zones["time_zones"]["(AUTO DETECT)"] = $scope.general_settings.single.auto_detect
          var from_import = new Date($scope.general_settings.single.from_import).getHours();
          var to_import = new Date($scope.general_settings.single.to_import).getHours();
          var minutes = new Date($scope.general_settings.single.to_import).getMinutes();

          if (to_import == 23 && minutes == 59) {
            to_import = 24;
          }

          if (from_import <= to_import) {
            $scope.slider.min = from_import;
            $scope.slider.max = to_import;
          }
          else {
            $scope.slider.min = to_import;
            $scope.slider.max = from_import;
          }
        });
        $scope.starting_value_disabled = true;
        $rootScope.$on('bulk_action_finished', myscope.reload_settings);
      };

      myscope.reload_settings = function () {
        generalsettings.single.get($scope.general_settings).then(function () {
          $scope.confirmation_hash.inventory_tracking = $scope.general_settings.single.inventory_tracking;
        });
      };


      $scope.confirm_and_update = function (key) {
        if (key == 'inventory_tracking' && $scope.confirmation_hash[key] == false) {
          if (confirm("Are you sure? Turning inventory off will remove all inventory warehouse related data, including Quantity On Hand (QOH).")) {
            $scope.general_settings.single[key] = $scope.confirmation_hash[key];
          } else {
            $scope.confirmation_hash[key] = $scope.general_settings.single[key];
          }
        } else {
          $scope.general_settings.single[key] = $scope.confirmation_hash[key];
        }
        $scope.update_settings();
      };

      $scope.change_opt = function (key, value) {
        $scope.general_settings.single[key] = value;
        $scope.update_settings();
      };

      $scope.update_numeric_bacode_settings = function () {
        if ($scope.general_settings.single.barcode_length == undefined) {
          $scope.notify('Please Enter Valid Barcode Length', 0);
          angular.element('[ng-model="general_settings.single.barcode_length"]').focus();
          $scope.starting_value_disabled = false;
          return false;
        }
        else if ($scope.general_settings.single.starting_value == undefined || !$scope.general_settings.single.starting_value.length || $scope.general_settings.single.starting_value.startsWith(0)) {
          $scope.notify('Please Enter Valid Starting Value ', 0);
          angular.element('[ng-model="general_settings.single.starting_value"]').focus();
          $scope.starting_value_disabled = false;
          return false;
        }
        else if ($scope.general_settings.single.starting_value.length < $scope.general_settings.single.barcode_length) {
          $scope.general_settings.single.starting_value = padLeadingZeros($scope.general_settings.single.starting_value, $scope.general_settings.single.barcode_length);
          $scope.starting_value_disabled = true;
          return true;
        }
        else if ($scope.general_settings.single.starting_value.length > $scope.general_settings.single.barcode_length) {
          $scope.notify('Length of starting value should be equal to Barcode Length', 0);
          angular.element('[ng-model="general_settings.single.starting_value"]').focus();
          $scope.starting_value_disabled = false;
          return false;
        }
        $scope.starting_value_disabled = true;
        return true;
      }

      function padLeadingZeros(num, size) {
        while (num.length < size) num = num + "0";
        return num;
      }

      $scope.update_settings = function () {
        if ($scope.update_numeric_bacode_settings()) {
          $scope.show_button = false;
          generalsettings.single.update($scope.general_settings).then(function () {
            myscope.reload_settings();
            $scope.ready_for_auto_complete = false;
          }
          );
          custom_fields = [$scope.general_settings.single.custom_field_one, $scope.general_settings.single.custom_field_two];
        }
      };
      
      $scope.delete_import_summary = function () {
        $http.put($rootScope.api_url + '/orders/cancel_import.json').success(function (data) {
            if (data.status) {
              notification.notify(data.success_messages, 1);
            } else {
              notification.notify(data.error_messages);
            }
          }).error(notification.server_error);
      };
    
      $scope.scrollup = function () {
        $('html, body').animate({ scrollTop: 1100 });
      }

      $scope.update_auto_time_zone = function () {
        try {
          offset = jstz.determine().offsets[0];
        } catch (e) {
          offset = 0;
        }
        generalsettings.single.update_auto_time_zone({ offset: offset }, $scope.general_settings);
      };

      $scope.fetch_time_zone = function () {
        generalsettings.single.add_time_zone({ new_time_zone: $scope.general_settings.single.new_time_zone }, $scope.general_settings);
      };

      $scope.update_import_time = function () {
        var from_import = new Date($scope.general_settings.single.from_import);
        var to_import = new Date($scope.general_settings.single.to_import);
        from_import.setHours($scope.slider.min);
        to_import.setHours($scope.slider.max);
        if ($scope.slider.max == 24) {
          to_import.setHours(23);
          to_import.setMinutes(59);
        };
        $scope.general_settings.single.from_import = from_import;
        $scope.general_settings.single.to_import = to_import;

        $scope.update_settings();
      }

      $scope.auto_complete = function (event, type) {
        if (event.keyCode != 8 && event.key != "Backspace") {
          var value = event.target.value;
          generalsettings.single.auto_complete(value, 'general_settings').success(function (response) {
            $scope.set_auto_complete_fields_data(response, type);
          });
        }
      }

      $scope.clear_auto_complete = function (type) {
        $scope.update_settings();
        $scope.set_auto_complete_fields_data(null, type);
      }

      $scope.set_auto_complete_fields_data = function (value, type) {
        $scope.ready_for_auto_complete = value == null ? false : true
        switch (type) {
          case 'custom_user_field_one':
            $scope.custom_user_field_one_auto_complete_data = value;
            break;
          case 'custom_user_field_two':
            $scope.custom_user_field_two_auto_complete_data = value;
            break;
          default:
        }
      }

      $scope.remove_localstorage_item = function (type) {
        localStorage.removeItem(type);
        $scope.notify('Local Printer Settings reset successfully', 1);
      };

      $scope.copied = function (value) {
        ngClipboard.toClipboard(value);
      }

      $scope.regenerate_api_key = function () {
        api_keys.single.regenerate().success(function () {
          myscope.reload_settings();
        });
      }

      $scope.delete_api_key = function (id) {
        api_keys.single.delete(id).success(function () {
          myscope.reload_settings();
        });
      }

      $scope.openNewWebhookForm = function () {
        var createModal = $modal.open({
          controller: 'webhooksCtrl',
          templateUrl: 'assets/views/modals/settings/groovepacker_webhooks/new_groovepacker_webhook.html',
          resolve: {
            webhook: function () { return { event: '', createWebhook: true } }
          }
        });
        createModal.result.then(function(){
          myscope.reload_settings();
        });
      }

      $scope.setSelected = function (idSelectedWebhook) {
        var i;
        for (i = 0; i < $scope.general_settings.single.webhooks.length; i++) {
          if ($scope.general_settings.single.webhooks[i].id == idSelectedWebhook) {
            $scope.general_settings.single.webhooks[i].checked = !$scope.general_settings.single.webhooks[i].checked;
          }
        }
      };

      $scope.deleteWebhooks = function () {
        $scope.selectedWebhooks = [];
        $scope.general_settings.single.webhooks.forEach(function (webhook) {
          if (webhook.checked)
            $scope.selectedWebhooks.push(webhook.id);
        });
        if ($scope.selectedWebhooks.length > 0) {
          webhooks.list.delete($scope.selectedWebhooks).then(function () {
            myscope.reload_settings();
          });
        }
        else {
          notification.notify("Select one or more rows to remove");
        }
      };

      $scope.webhook_details = function (data) {
        $modal.open({
          controller: 'webhooksCtrl',
          templateUrl: 'assets/views/modals/settings/groovepacker_webhooks/new_groovepacker_webhook.html',
          resolve: {
            webhook: function () { return data }
          }
        });
      };

      myscope.init();
    }]);
