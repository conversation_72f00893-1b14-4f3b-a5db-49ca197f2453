groovepacks_controllers.
  controller('scanPackSettingsCtrl', ['$scope', '$http', '$timeout', 'generalsettings', '$location', '$state', '$cookies', 'scanPack', 'groov_translator',
    '$modal', '$http', '$rootScope', '$window',
    function ($scope, $http, $timeout, generalsettings, $location, $state, $cookies, scanPack, groov_translator, $modal, $http, $rootScope, $window) {

      var myscope = {};

      /**
       * Public methods
       */
      $scope.cam_options = [
        {name: "Image taken after countdown (Spacebar to skip countdown) ", value: "automatic"},
        {name: "Press spacebar to take the image", value: "Manual"},
        {name: "Do not take an image", value: "do_not_take_image"},
      ]

      $scope.delete_tote_set = function (id) {
        var tote_set_id = id;
        return $http.get($rootScope.api_url + '/settings/delete_tote_set?id=' + id).success(function (data) {
          $scope.notify('Tote set deleted successfully', 1);
          tote_set_index = $scope.scan_pack.settings.tote_sets.findIndex(function(obj){return obj.id == tote_set_id})
          $scope.scan_pack.settings.tote_sets.splice(tote_set_index, 1);
        }).error(function(response) {
        });
      }

      $scope.create_tote_set = function () {
        return $http.get($rootScope.api_url + '/settings/create_tote_set.json').success(function (data) {
          $scope.notify('Tote set created successfully', 1);
          $scope.scan_pack.settings.tote_sets.push(data.tote);
        });
      }

      $scope.reset_totes = function () {
        return $http.get($rootScope.api_url + '/settings/reset_totes.json').success(function (data) {
          $scope.notify('Totes reset successfully.', 1);
        });
      }
      
      $scope.scan_pack_pdf = function (params, id) {
        if (params == 'type_scan') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/type_scan_code.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'skip_code') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/skip_code.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'note_from_packer_code') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/note_from_packer_code.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'service_issue_code') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/service_issue_code.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'restart_code') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/restart_code.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'click_scan_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/click_scan_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'scanned_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/scanned_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'add_next_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/add_next_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'pass_scan_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/pass_scan_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'partial_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/partial_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'remove_barcode') {
          return $http.get($rootScope.api_url + '/settings/print_action_barcode/remove_barcode.pdf')
            .success(function (data) {
              var fileURL = JSON.stringify(data);
              URL = JSON.parse(fileURL);
              $window.open(URL.url)
            }).error(function (response) {
            });
        } else if (params == 'tote_barcodes') {
          return $http.get($rootScope.api_url + '/settings/print_tote_barcodes?id=' + id)
            .success(function (data) {
              if (data.status) {
                var fileURL = JSON.stringify(data.url);
                URL = JSON.parse(fileURL);
                $window.open(URL);
              } else {
                $scope.notify(data.message, 0);
              }
            }).error(function (response) {
            });
        }
      }

      $scope.$on("fileSelected", function (event, args) {
        $("input[type='file']").val('');
        if ((args.name == 'email_logo' || args.name == 'customer_page_logo') && args.file) {
          return $http({
            method: 'POST',
            headers: {'Content-Type': undefined},
            url: $rootScope.api_url + '/settings/update_packing_cam_image.json',
            data: { image: args.file, type: args.name },
            transformRequest: function (data) {
              var request = new FormData();
              for (var key in data) {
                request.append(key, data[key]);
              }
              return request;
            }
          }).success(function (data) {
            if (data.status) {
              $scope.update_settings();
            } else {
              $scope.notify(data.error_message, 0);
            }
          }).error($scope.notify.server_error);
        };
      });

      myscope.init = function () {
        $scope.setup_page('system', 'scan_pack');
        $scope.translations = {
          "headings": {
            "options": "",
            "feedback": "",
            "scan_actions": "",
            "multi_box_option": "",
            "scan_actions_sub_head": "",
            "product_first_scan_to_put_wall": "",
            "js_printmanager": "",
            "camera_options": "",
            "packing_options": "",
            "per_box_packing_slip": "",
            "per_box_shipping_label_creation": ""
          },
          "labels": {
            "tracking_number_validation": "",
            "enable_click_sku": "",
            "ask_tracking_number": "",
            "requires_assigned_orders": "",
            "show_success_image": "",
            "reset_order_barcode_label_printer": "",
            "show_order_complete_image": "",
            "show_fail_image": "",
            "for": "",
            "seconds": "",
            "play_success_sound": "",
            "play_fail_sound": "",
            "play_order_complete_sound": "",
            "scan": "",
            "skip_code": "",
            "remove_skipped": "",
            "note_from_packer_code": "",
            "service_issue_code": "",
            "restart_code": "",
            "click_scan_code": "",
            "scanned_code": "",
            "partial_code": "",
            "remove_code": "",
            "add_next_code": "",
            "type_scan_code": "",
            "escape_string": "",
            "lot_number": "",
            "show_customer_notes": "",
            "show_tags": "",
            "show_internal_notes": "",
            "send_external_logs": "",
            "cue_orders_for_scanpack": "",
            "intangible_setting": "",
            "intangible_string": "",
            "cue_order_for_scan": "",
            "ask_tracking_number_second": "",
            "simple_product": "",
            "scan_all": "",
            "scan_to_cart": "",
            "camera_option": "",
            "capture_event": "",
            "choice_of_resolution": "",
            "packing_cam_enabled": "",
            "email_customer_option": "",
            "capture_image_option": "",
            "email_subject": "",
            "subject": "",
            "email_logo": "",
            "scanning_log": "",
            "email_reply": "",
            "email_insert_dropdown": "",
            "customer_email_insert_dropdown": "",
            "customer_page_logo": "",
            "multi_box_shipment": "",
            "print_when_order_complete": "Print when the order is complete",
            "print_when_new_box": "Print when new boxes are started",
            "manually_print": "Are printed manually",
            "per_box_shipping_label_creation_none": "None",
            "per_box_shipping_label_creation_after_box": "After each box is completed",
            "per_box_shipping_label_creation_after_order": "After order is completed",
            "order_num_esc_str_removal": "",
            "order_num_esc_str_enabled": ""
          },
          "tooltips": {
            "tracking_number_validation": "",
            "enable_click_sku": "",
            "ask_tracking_number": "",
            "requires_assigned_orders": "",
            "feedback": "",
            "skip_code": "",
            "remove_skipped": "",
            "note_from_packer_code": "",
            "service_issue_code": "",
            "restart_code": "",
            "type_scan_code": "",
            "type_in_counts": "",
            "escape_string": "",
            "record_suffix": "",
            "ask_post_scanning_functions": "",
            "show_internal_notes": "",
            "send_external_logs": "",
            "show_customer_notes": "",
            "show_tags": "",
            "cue_orders_optons": "",
            "intangible_setting": "",
            "intangible_setting_gen_barcode_from_sku": "",
            "post_scan_pause": "",
            "string_removal": "",
            "order_verification": "",
            "cue_order_for_scan": "",
            "display_location_on_scanning_page": "",
            "return_to_orders": "",
            "scanning_sequence_tool": "",
            "click_scan_tooltip": "",
            "pass_scan_tooltip": "",
            "scanned_barcode_tooltip": "",
            "partial_barcode_tooltip": "",
            "remove_barcode_tooltip": "",
            "add_next_barcode_tooltip": "",
            "ask_post_scanning_functions_second": "",
            "require_serial": "",
            "replace_gp_code_tootip": "",
            "simple_product_scanning": "",
            "scan_all": "",
            "scan_to_cart": "",
            "email_customer_option": "",
            "capture_image_option": "",
            "packing_cam_enabled": "",
            "scanning_log": "",
            "multi_box_shipment": "",
            "order_num_esc_str_enabled": ""
          }
        };
        groov_translator.translate('settings.system.scan_pack', $scope.translations);
        $scope.scan_pack = scanPack.settings.model();
        scanPack.settings.get($scope.scan_pack);
        $scope.general_settings = generalsettings.model.get();
        generalsettings.single.get($scope.general_settings)
      };


      $scope.per_product_setting = function (key) {
        $modal.open({
          templateUrl: '/assets/views/modals/settings/system/product_list.html',
          controller: 'productListModal',
          size: 'lg',
          resolve: {
            context_data: function () {
              var enabled = false;
              var type = '';
              if (key == 'enable_click_sku') {
                enabled = $scope.scan_pack.settings.enable_click_sku;
                type = 'click_scan_enabled';
              } else if (key == 'type_scan_code') {
                enabled = $scope.scan_pack.settings.type_scan_code_enabled;
                type = 'type_scan_enabled';
              }
              return {
                type: type,
                enabled: enabled
              }
            }
          }
        });
      };

      $scope.change_post_scanning_opt = function (value) {
        $scope.scan_pack.settings.post_scanning_option = value;
        $scope.update_settings();
      };

      $scope.change_post_scanning_opt_second = function (value) {
        $scope.scan_pack.settings.post_scanning_option_second = value;
        $scope.update_settings();
      };

      $scope.change_scanning_seq_opt = function (value) {
        $scope.scan_pack.settings.scanning_sequence = value;
        $scope.update_settings();
      };

      $scope.change_resolution = function (value) {
        $scope.scan_pack.settings.resolution = value;
        $scope.update_settings();
      };

      $scope.update_insert_txt_in_editor = function (value, type) {
        if ((value === 'order_number' && myscope.customerPage && type === 'customer_page') || (value === 'customer_page_url' && myscope.emailPage && type === 'email_page') || (value === 'order_number' && myscope.emailPage && type === 'email_page')) {
          $scope.scan_pack.settings.email_insert_dropdown = value;
          myscope.insertTextAtCursor(value === 'order_number' ? '[[ORDER-NUMBER]]' : '[[CUSTOMER-PAGE-URL]]');
        }
      };

      myscope.insertTextAtCursor = function(textToInsert) {
        if ($scope.clickedElement && $scope.caretPosition ) {
          var selection = window.getSelection();
          var range = selection.getRangeAt(0);
          var textNode = document.createTextNode(textToInsert);
          range.insertNode(textNode);
          
          selection.removeAllRanges();
          selection.addRange(range);
          
          $scope.update_settings();
        }
      }

      $scope.handleClick = function (event) {
        const isInsideEditable = event.target.closest('.ta-editor');
        myscope.emailPage = event.target.closest('#email_page');
        myscope.customerPage = event.target.closest('#customer_page');
        if (!isInsideEditable) {
          $scope.clickedElement = null;
          $scope.caretPosition = null;
        } else {
          $scope.clickedElement = event.target;
          $scope.caretPosition = window.getSelection().getRangeAt(0);
        }
      };

      document.addEventListener('click', function (event) {
        $scope.$apply(function () {
          $scope.handleClick(event);
        });
      });

      $scope.change_pre_scanning_opt = function (value) {
        if (value == "shipping_label_scan") {
          $scope.scan_pack.settings.scan_by_shipping_label = true;
          $scope.scan_pack.settings.scan_by_packing_slip = false;
          $scope.scan_pack.settings.scan_by_packing_slip_or_shipping_label = false;
        } else if (value == 'packing_slip_scan') {
          $scope.scan_pack.settings.scan_by_packing_slip = true;
          $scope.scan_pack.settings.scan_by_shipping_label = false;
          $scope.scan_pack.settings.scan_by_packing_slip_or_shipping_label = false;
        } else if (value == 'scan_by_packing_slip_or_shipping_label') {
          $scope.scan_pack.settings.scan_by_packing_slip_or_shipping_label = true;
          $scope.scan_pack.settings.scan_by_packing_slip = false;
          $scope.scan_pack.settings.scan_by_shipping_label = false;
        }
        $scope.update_settings();
      };

      $scope.update_general_settings = function () {
        generalsettings.single.update($scope.general_settings)
      };

      $scope.update_product_intangibleness = function () {
        scanPack.update_products($scope.scan_pack).then(function (data) {
          $scope.update_settings();
        });
      };

      $scope.update_settings = function () {
        scanPack.settings.update($scope.scan_pack);
      };

      $scope.change_opt = function (key, value) {
        $scope.general_settings.single[key] = value;
        $scope.update_general_settings();
      };

      myscope.init();
    }]);
