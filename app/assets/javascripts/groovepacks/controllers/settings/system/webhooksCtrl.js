groovepacks_controllers.
  controller('webhooksCtrl', ['$http', 'notification', '$rootScope', '$scope', '$modalInstance', 'webhook',
    function ($http, notification, $rootScope, $scope, $modalInstance, webhook) {

      var myscope = {};

      myscope.init = function () {
        $scope.webhook = webhook;
        $scope.formTitle = webhook.createWebhook ? 'Add' : 'Edit';
        $scope.submitButtonText = webhook.createWebhook ? 'Save' : 'Update';
      };

      $scope.close = function () {
        $modalInstance.close("ok-button-click");
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      // Function to handle changing the event
      $scope.change_opt = function (e) {
        $scope.webhook.event = e;
      };

      $scope.createWebhook = function (form) {
        form.$submitted = true;
        if (form.$valid) {
          var formData = {
            webhook: {
              url: form.url.$modelValue,
              secret_key: form.secretKey.$modelValue,
              event: $scope.webhook.event,
            },
          };
          return $http.post($rootScope.api_url + "/groovepacker_webhooks.json", formData).success(function (data) {
            if (data.data) {
              notification.notify(data.message, 1);
            } else {
              notification.notify(data.errors, 0);
            }
            $scope.close();
          }).error(function(data){
            const errors = data.errors
            for (errorKey in errors) {
              const errorMessages = errors[errorKey];
              if (Array.isArray(errorMessages)) {
                errorMessages.forEach(function(errorMessage) {
                  notification.notify(errorKey + " " + errorMessage, 0);
                });                
              }
            }
          });
        }
      };

      $scope.updateWebhook = function (form, id) {
        form.$submitted = true;
        if (form.$valid) {
          var formData = {
            webhook: {
              url: form.url.$modelValue,
              secret_key: form.secretKey.$modelValue,
              event: $scope.webhook.event,
            },
          };
          return $http.put($rootScope.api_url + '/groovepacker_webhooks/' + id, formData).success(function (data) {
            if (data.data) {
              notification.notify(data.message, 1);
            } else {
              notification.notify(data.errors, 0);
            }
            $scope.close();
          }).error(function(data){
            const errors = data.errors
            for (errorKey in errors) {
              const errorMessages = errors[errorKey];
              if (Array.isArray(errorMessages)) {
                errorMessages.forEach(function(errorMessage) {
                  notification.notify(errorKey + " " + errorMessage, 0);
                });                
              }
            }
          });
        }
      };

      myscope.init();
    }]);