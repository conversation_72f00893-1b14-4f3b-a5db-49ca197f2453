groovepacks_controllers.
  controller('shipstationlabelrateCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', '$rootScope','$window', 'ss_label_rate_data',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, $rootScope, $window, ss_label_rate_data) {
      var myscope = {};

      myscope.init = function () {
        if (ss_label_rate_data.length) {
          for(var i=0; i < ss_label_rate_data.length; i++) {
            ss_label_rate_data[i].shipmentCost = parseFloat(ss_label_rate_data[i].shipmentCost).toFixed(2);
            ss_label_rate_data[i].otherCost = parseFloat(ss_label_rate_data[i].otherCost).toFixed(2);
          }
        }
        $scope.ss_label_rate_data = ss_label_rate_data;
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.ok = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      myscope.init();
  }]);
