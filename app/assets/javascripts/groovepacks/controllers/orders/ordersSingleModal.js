groovepacks_controllers.
  controller('ordersSingleModal', ['$scope', 'order_data', 'order_id', 'load_page', '$state', '$stateParams', '$rootScope', '$http', '$modal', '$modalInstance', '$timeout', '$q', 'hotkeys', 'orders', 'products', 'generalsettings', 'auth', 'groov_translator', '$filter', 'notification',
    function (scope, order_data, order_id, load_page, $state, $stateParams, $rootScope, $http, $modal, $modalInstance, $timeout, $q, hotkeys, orders, products, generalsettings, auth, groov_translator, $filter, notification) {

      var myscope = {};
      scope.current_packing_type = null;
      scope.selected_box = [];
      scope.filter_box_data = [];

      /**
       * private properties
       */

      /**
       * Public methods
       */

      scope.ok = function () {
        if (scope.orders.single.basicinfo.increment_id != ""){
          $modalInstance.close("ok-button-click");
        }
        scope.update_single_order(false);
      };
      scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
        scope.update_single_order(false);
      };

      scope.update = function (reason) {
        if (reason == "cancel-button-click") {
          myscope.rollback();
        } else {
          if ($state.params.new_order && reason =="backdrop click" && scope.orders.single.basicinfo.increment_id == ""){
            myscope.rollback();
          }
          else{
            if (scope.orders.single.basicinfo.increment_id != ""){
              scope.update_single_order(false);
            }
          }
        }
      };

      scope.set_selected_box = function(id, event){
        if(event.shiftKey){
          var f_index, l_index;
          angular.forEach(scope.orders.single.boxes, function(value, key){
            if(scope.selected_box[0] == value.id){ f_index = key };
            if(id == value.id){ l_index = key };
          });

          scope.selected_box = [];
          angular.forEach(scope.orders.single.boxes, function(value, key){
            if(f_index <= l_index){
              if(key >= f_index && key <= l_index){ scope.selected_box.push(value.id) };
            }else{
              if(key <= f_index && key >= l_index){ scope.selected_box.push(value.id) };
            }
          });
        }else{
          if(event.currentTarget.classList.value.includes("active") && event.ctrlKey){
            scope.selected_box.splice(scope.selected_box.indexOf(id), 1);
            if(scope.selected_box.length == 0){ scope.selected_box.push(id) };
          }
          else{
            if(!(scope.selected_box.length > 0 && event.ctrlKey)){
              scope.selected_box = [];
            }
            scope.selected_box.push(id);
          }
        }
      }

      scope.$watchCollection('selected_box', function () {
        scope.filter_box_data = [];
        angular.forEach(scope.selected_box, function(value, key){
          angular.forEach(scope.orders.single.items, function(i_value, i_key){
            var data = $filter('filter')(scope.orders.single.order_item_boxes, { order_item_id: i_value.iteminfo.id, box_id: value});
            if(data.length > 0){
              if(i_value.productinfo.is_kit == 1 && i_value.productinfo.kit_parsing != 'single'){
                angular.forEach(scope.orders.single.list, function(v, k){
                  if (value == k ) {
                    angular.forEach(v, function(vi, ki){
                      scope.filter_box_data.push({ name:  vi.product_name, sku: i_value.sku, qty: vi.qty});
                    });
                  }
                });
              }
              else{
                scope.filter_box_data.push({ name:  i_value.productinfo.name, sku: i_value.sku, qty: data[0].item_qty});
              }
            }
          })
        });
      });

      scope.delete_single_box = function (id) {
       if (confirm('Do you wish to remove this box and delete all items in it from the order? If items in this box will remain in the order they should be removed from the box before it is deleted.')) {
          orders.single.delete_box(id).then(function (data) {
            myscope.order_single_details(scope.orders.single.basicinfo.id);
            notification.notify("Box is deleted successfully", 0);
          });
        }
      }

      scope.set_packing_type = function(type){
        scope.current_packing_type = type;
      }

      scope.print_packing_slip = function(all){
        if(scope.current_packing_type == null){
          notification.notify("Please select packing type", 0);
        }else{
          var box_ids = [];
          if(scope.current_packing_type == 'all_boxes'){
            angular.forEach(scope.orders.single.boxes, function(value, key){
              box_ids.push(value.id);
            });
          }else{
            angular.forEach(scope.selected_box, function(value, key){
              box_ids.push(value);
            });
          }
          orders.list.generate_box_slip(scope.orders.single.basicinfo, box_ids, scope.current_packing_type);
        }
      }

      myscope.order_single_details = function (id, new_rollback) {
        for (var i = 0; i < scope.orders.list.length; i++) {
          if (scope.orders.list[i].id == id) {
            scope.orders.current = parseInt(i);
            break;
          }
        }
        orders.single.get(id, scope.orders).then(function (data) {
          var user_found = false;
          var currentuser_idx = -1;
          for (i = 0; i < scope.orders.single.users.length; i++) {
            if (scope.orders.single.exception != null &&
              scope.orders.single.exception.assoc != null &&
              scope.orders.single.users[i].id == scope.orders.single.exception.assoc.id) {
              scope.orders.single.exception.assoc = scope.orders.single.users[i];
              user_found = true;
              break;
            }
          }
          if (scope.orders.single.exception != null && scope.orders.single.exception.assoc == null) {
            scope.orders.single.exception.assoc = scope.orders.single.users[0];
          }
          if(scope.orders.single.boxes.length > 0){
            scope.selected_box.push(scope.orders.single.boxes[0].id);
          }
          if (typeof new_rollback == 'boolean' && new_rollback) {
            myscope.single = {};
            angular.copy(scope.orders.single, myscope.single);
          }
        });
      };

      myscope.rollback = function () {
        if ($state.params.new_order) {
         orders.list.update('delete', {
            selected: [{id: scope.orders.single.basicinfo.id, checked: true}],
            setup: {select_all: false, inverted: false, orderArray: []},
          },scope.general_settings.single.search_by_product).then(function () {
            if ($state.current.name == 'orders.type.filter.page') {
              $state.reload();
            }
          });
        } else {
          scope.orders.single = {};
          angular.copy(myscope.single, scope.orders.single);
          return orders.single.rollback(myscope.single).then(function (response) {
            myscope.order_single_details(scope.orders.single.basicinfo.id);
          })
        }

      };

      scope.update_single_order = function (auto) {
        if ($state.params.new_order && scope.orders.single.basicinfo.increment_id == "") {
          doConfirm("Please click continue to assign an order number and save the order, or click delete to exit and cancel order creation.", function yes(){alert("YEs") }, function no(){ alert("no")
          });
        } else {
          scope.date_picker.show_button = false;
          orders.single.update(scope.orders, auto).then(function (response) {
            myscope.order_single_details(scope.orders.single.basicinfo.id);
            orders.list.get(scope.orders, $state.params.page, product_search_toggle);
          });
        }

      };

      function doConfirm(msg, yesFn, noFn){
        var confirmBox = $("#confirmBox");
        confirmBox.find(".message").text(msg);
        confirmBox.find(".yes,.no").unbind().click(function()
        {
            confirmBox.hide();
        });
        confirmBox.find(".yes").click(function()
        {
          myscope.order_single_details(scope.orders.single.basicinfo.id);
          myscope.add_hotkeys();
        });
        confirmBox.find(".no").click(function()
        {
          myscope.rollback();
          $modalInstance.close("ok-button-click");
        });
        confirmBox.show();
      }

      scope.open_date_picker = function (event) {
        event.stopPropagation();
        event.preventDefault();

        scope.date_picker.opened = true;
      };

      scope.add_item_order = function (args) {
        if (typeof args != 'undefined') {
          orders.single.item.add(scope.orders, args.selected).then(function (response) {
            myscope.order_single_details(scope.orders.single.basicinfo.id);
          });
        }
      };

      scope.item_remove_selected = function () {
        var ids = [];
        for (var i = 0; i < scope.orders.single.items.length; i++) {
          if (scope.orders.single.items[i].checked == true) {
            ids.push(scope.orders.single.items[i].iteminfo.id);
          }
        }

        if(ids.length >0){
          orders.single.item.remove(ids).then(function () {
            myscope.order_single_details(scope.orders.single.basicinfo.id);
          });
        } else {
          orders.list.select_notification("First select items to remove from the list.");
        }
      };

      scope.update_order_exception = function () {
        orders.single.exception.record(scope.orders).then(function () {
          myscope.order_single_details(scope.orders.single.basicinfo.id);
        });
      };

      scope.clear_order_exception = function () {
        orders.single.exception.clear(scope.orders).then(function () {
          myscope.order_single_details(scope.orders.single.basicinfo.id);
        });
      };

      scope.sortOrder = '-activitytime';
      scope.sortActivities = function(order) {
        scope.sortOrder = order === 'asc' ? 'activitytime' : '-activitytime';
      };

      scope.save_item = function (model, prop) {
        if (prop == 'qty') {
          if ((model.skipped_qty != 0) && confirm("If the quantity is modified for this item all skipped pieces will be restored. Are you sure you want to continue?")) {
            orders.single.item.update(model).then(function (response) {
              myscope.order_single_details(scope.orders.single.basicinfo.id);
            });
          } else if (model.skipped_qty == 0) {
            orders.single.item.update(model).then(function (response) {
              myscope.order_single_details(scope.orders.single.basicinfo.id);
            });
          }
        } else {
          var obj = {
            id: (prop == 'name' || prop == 'is_skippable' || prop == 'status') ? model.id : model.iteminfo.product_id,
            order_id: typeof(scope.orders.single.basicinfo.id) == 'number' ? scope.orders.single.basicinfo.id : nil,
            var: prop,
            value: model[prop]
          };
          products.list.update_node(obj).then(function (data) {
            orders.single.get(scope.orders.single.basicinfo.id, scope.orders);
            if (data.data.show_alias_popup) {
              var alias_popup = $modal.open({
                templateUrl: "/assets/views/modals/product/alias_popup.html",
                controller: "aliasPopupEditModal",
                size: "lg",
                resolve: {
                  current_product_data: function () {
                    return data.data.current_product_data;
                  },
                  alias_product_data: function () {
                    return data.data.alias_product_data;
                  },
                  after_alias_product_data: function () {
                    return data.data.after_alias_product_data;
                  },
                  matching_barcode: function () {
                    return data.data.matching_barcode;
                  },
                  shared_bacode_products: function () {
                    return data.data.shared_bacode_products;
                  }
                },
              });
              alias_popup.result.then(function (res) {
                if (res == "consider_different_items") {
                  var permit_shared_barcode_popup = $modal.open({
                    templateUrl: "/assets/views/modals/product/permit_shared_barcode.html",
                    controller: "aliasPopupEditModal",
                    size: "lg",
                    resolve: {
                      current_product_data: function () {
                        return data.data.current_product_data;
                      },
                      alias_product_data: function () {
                        return data.data.alias_product_data;
                      },
                      after_alias_product_data: function () {
                        return data.data.after_alias_product_data;
                      },
                      matching_barcode: function () {
                        return data.data.matching_barcode;
                      },
                      shared_bacode_products: function () {
                        return data.data.shared_bacode_products;
                      }
                    },
                  });
                  permit_shared_barcode_popup.result.then(
                    function (res) {
                      if (res == 'have_unique_barcode') {
                        model[prop] = null;
                      } else if (res == 'allow_shared_barcode') {
                        products.list.update_node({
                          id: obj.id,
                          var: obj.var,
                          value: obj.value,
                          permit_same_barcode: true
                        }).then(function () {
                          myscope.order_single_details(scope.orders.single.basicinfo.id);
                        });
                      }
                    },
                    function () {
                      model[prop] = null;
                    }
                  );
                } else if (res == 'proceed_with_aliasing') {
                    $http.post($rootScope.api_url + "/products/"+data.data.alias_product_data.id+"/set_alias.json", {
                      product_alias_ids: [data.data.current_product_data.id]
                    }).success(
                      function (data) {
                        if (data.status) {
                          notification.notify("Successfully Updated", 1);
                          myscope.order_single_details(scope.orders.single.basicinfo.id);
                        } else {
                          notification.notify(data.messages, 0);
                        }
                        return data;
                      }
                    ).error(notification.server_error);
                }
              },
              function () {
                model[prop] = null;
              });
            }
          });
        }
      };

      scope.update_print_status = function (item, product) {
        orders.single.item.print_status({id: item.id}).then(function (response) {
          myscope.init();
          scope.modal_tabs[0].active = true;
          orders.single.item.print_barcode({id: product.id, item_id: item.id});
        });
      }

      scope.acknowledge_activity = function (activity_id) {
        orders.single.activity.acknowledge(activity_id).then(function (response) {
          myscope.order_single_details(scope.orders.single.basicinfo.id);
        })
      }

      scope.get_order_details = function (increment_id) {
        var toState = 'orders.filter.page.single';
        var toParams = {};
        for (var key in $state.params) {
          if (['filter', 'page'].indexOf(key) !== -1) {
            toParams[key] = $state.params[key];
          }
        }
        orders.single.get_id(increment_id).then(function(response) {
          toParams.order_id = response.data;
          var url = $state.href(toState, toParams);
          window.open(url,'_blank');
        });
      };

      scope.delete_shipment_order = function (id) {
        if (confirm("Are you sure?")) {
          order_id = id;
          $http.post($rootScope.api_url + '/orders/delete_orders.json', {"id": id}).success(function (data) {
            if (data.status) {
              index_element = scope.orders.single.se_old_combined_shipments.map(function (obj) { return obj[0]; }).indexOf(order_id);
              if (index_element != undefined) {
                scope.orders.single.se_old_combined_shipments.splice(index_element, 1);
              }
              notification.notify('Order will be deleted soon', 2);
            } else {
              notification.notify(data.error_messages, 0);
            }
          }).error(notification.server_error);
       }
      };

      scope.handlesort = function (value) {
        myscope.order_items_setup_opt('sort', value);
      };

      myscope.order_items_setup_opt = function (type, value) {
        orders.setup.update_items(scope.orders.single.items, type, value);
      };

      myscope.up_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if (scope.orders.current > 0) {
          myscope.load_item(scope.orders.current - 1);
        } else {
          load_page('previous').then(function () {
            myscope.load_item(scope.orders.list.length - 1);
          }, function () {
            alert("Already at the top of the list");
          });
        }
      };
      myscope.down_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        if (scope.orders.current < scope.orders.list.length - 1) {
          myscope.load_item(scope.orders.current + 1);
        } else {
          load_page('next').then(function () {
            myscope.load_item(0);
          }, function () {
            alert("Already at the bottom of the list");
          });
        }
      };
      myscope.left_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        var tabs_len = scope.modal_tabs.length - 1;
        for (var i = 0; i <= tabs_len; i++) {
          if (scope.modal_tabs[i].active) {
            //scope.modal_tabs[i].active = false;
            scope.modal_tabs[((i == 0) ? tabs_len : (i - 1))].active = true;
            break;
          }
        }
      };
      scope.change_opt = function (key, value) {
        scope.general_settings.single[key] = value;
        generalsettings.single.update(scope.general_settings);
      };
      myscope.right_key = function (event) {
        event.preventDefault();
        event.stopPropagation();
        var tabs_len = scope.modal_tabs.length - 1;
        for (var i = 0; i <= tabs_len; i++) {
          if (scope.modal_tabs[i].active) {
            //scope.modal_tabs[i].active = false;
            scope.modal_tabs[((i == tabs_len) ? 0 : (i + 1))].active = true;
            break;
          }
        }
      };

      myscope.add_hotkeys = function () {
        hotkeys.del('up');
        hotkeys.del('down');
        hotkeys.del('left');
        hotkeys.del('right');
        hotkeys.del('esc');

        $timeout(function () {
          hotkeys.bindTo(scope).add({
            combo: 'up',
            description: 'Previous order',
            callback: myscope.up_key
          }).add({
            combo: 'down',
            description: 'Next order',
            callback: myscope.down_key
          }).add({
            combo: 'left',
            description: 'Previous tab',
            callback: myscope.left_key
          }).add({
            combo: 'right',
            description: 'Next tab',
            callback: myscope.right_key
          }).add({
            combo: 'esc',
            description: 'Save and close modal',
            callback: function () {
            }
          });

        }, 2000);
      };

      myscope.handle_click_fn = function (row, event) {
        if (typeof event != 'undefined') {
          event.preventDefault();
          event.stopPropagation();
        }
        var item_modal = $modal.open({
          templateUrl: '/assets/views/modals/product/main.html',
          controller: 'productsSingleModal',
          size: 'lg',
          resolve: {
            product_data: function () {
              return scope.item_products
            },
            load_page: function () {
              return function () {
                var req = $q.defer();
                req.reject();
                return req.promise;
              };
            },
            product_id: function () {
              return row.productinfo.id;
            }
          }
        });
        item_modal.result.finally(function () {
          myscope.order_single_details(scope.orders.single.basicinfo.id);
          myscope.add_hotkeys();
        });
      };
      scope.item_order = function (type, exceptions, id) {
        var alias_modal = $modal.open({
          templateUrl: '/assets/views/modals/product/alias.html',
          controller: 'aliasModal',
          size: 'lg',
          resolve: {
            type: function () {
              return type
            },
            exceptions: function () {
              return exceptions
            },
            id: function () {
              return id;
            }
          }
        });
        alias_modal.result.then(scope.add_item_order);
      };
      myscope.load_item = function (id) {
        var newStateParams = angular.copy($stateParams);
        newStateParams.order_id = "" + scope.orders.list[id].id;
        myscope.order_single_details(scope.orders.list[id].id, true);
        $state.go($state.current.name, newStateParams);
      };

      var disable_global_edit = function() {
        return ($state.params.filter == 'scanned') ? true : false;
      }

      myscope.init = function () {
        scope.orders = order_data;
        scope.translations = {
          "tooltips": {
            "confirmation": ""
          },
          "labels": {
            "se_old_split_shipments": "",
            "se_old_combined_shipments": "",
            "remove_shipment": "",
            "remove_acknowledge_shipment": ""
          }
        };
        groov_translator.translate('orders.modal', scope.translations);
        scope.confirmation_setting_text = "Ask someone with \"Edit General Preferences\" permission to change the setting in <b>General Settings</b> page if you need to override it per order";
        if (auth.can('edit_general_prefs')) {
          scope.general_settings = generalsettings.model.get();
          generalsettings.single.get(scope.general_settings).success(function(data){
            scope.current_packing_type = data.data.settings.packing_type;
          });
          scope.confirmation_setting_text = "<p><strong>You can change the global setting here</strong></p>" +
            "<div class=\"controls col-sm-offset-4 col-sm-3 \" ng-class=\"{'col-sm-offset-3':general_settings.single.conf_req_on_notes_to_packer=='optional' }\" dropdown>" +
            "<button class=\"dropdown-toggle groove-button label label-default\" ng-class=\"{'label-success':general_settings.single.conf_req_on_notes_to_packer=='always'," +
            " 'label-warning':general_settings.single.conf_req_on_notes_to_packer=='optional'}\">" +
            "<span ng-show=\"general_settings.single.conf_req_on_notes_to_packer=='always'\" translate>common.always</span>" +
            "<span ng-show=\"general_settings.single.conf_req_on_notes_to_packer=='optional'\" translate>common.optional</span>" +
            "<span ng-show=\"general_settings.single.conf_req_on_notes_to_packer=='never'\" translate>common.never</span>" +
            "<span class=\"caret\"></span>" +
            "</button>" +
            "<ul class=\"dropdown-menu\" role=\"menu\">" +
            "<li><a class=\"dropdown-toggle\" ng-click=\"change_opt('conf_req_on_notes_to_packer','always')\" translate>common.always</a></li>" +
            "<li><a class=\"dropdown-toggle\" ng-click=\"change_opt('conf_req_on_notes_to_packer','optional')\" translate>common.optional</a></li>" +
            "<li><a class=\"dropdown-toggle\" ng-click=\"change_opt('conf_req_on_notes_to_packer','never')\" translate>common.never</a></li>" +
            "</ul>" +
            "</div><div class=\"well-main\">&nbsp;</div>";
        }


        //All tabs
        scope.modal_tabs = [
          {
            active: true,
            heading: "Items",
            templateUrl: '/assets/views/modals/order/items.html'
          },
          {
            active: false,
            heading: "Notes",
            templateUrl: '/assets/views/modals/order/notes.html'
          },
          {
            active: false,
            heading: "Information",
            templateUrl: '/assets/views/modals/order/information.html'
          },
          {
            active: false,
            heading: "Activities & Exceptions",
            templateUrl: '/assets/views/modals/order/act_exception.html'
          },
          {
            active: false,
            heading: "Multi-Box Info",
            templateUrl: '/assets/views/modals/order/multi_box_info.html'
          }
        ];
        $modalInstance.result.then(scope.update, scope.update);
        /**
         * Public properties
         */
        scope.date_picker = {};
        scope.date_picker.opened = false;
        scope.date_picker.format = 'dd-MMMM-yyyy';
        scope.date_picker.show_button = false;
        scope.item_products = products.model.get();
        scope.item_products.setup.is_kit = -1;
       scope.$watch('orders.single.items', function () {
        if (typeof scope.orders.single.basicinfo != "undefined") {
          scope.item_products.list = [];
          for (var i = 0; i < scope.orders.single.items.length; i++) {
            scope.item_products.list.push({id: scope.orders.single.items[i].iteminfo.product_id});
            
            if (scope.general_settings && scope.general_settings.single) {
              scope.orders.single.items[i].show_imported_skus = scope.general_settings.single.show_imported_skus;
            }
          }
        }
      });

        scope.disable_global_edit = disable_global_edit();
        scope.gridOptions = {
          identifier: 'orderitems',
          draggable: true,
          disable_global_edit: scope.disable_global_edit,
          show_hide: true,
          selectable: true,
          sort_func: scope.handlesort,
          scrollbar: true,
          no_of_lines: 3,
          editable: {
            update: scope.save_item,
            print_status: scope.update_print_status,
            elements: {
              qty: {type: 'number', min: 0},
              is_skippable: {
                type: 'select',
                options: [
                  {name: "Yes", value: true},
                  {name: "No", value: false}
                ]
              },
              status: {
                type: 'select',
                options: [
                  {name: "Active", value: 'active'},
                  {name: "Inactive", value: 'inactive'},
                  {name: "New", value: 'new'}
                ]
              }
            },
            functions: {
              name: myscope.handle_click_fn
            }
          },
          all_fields: {
            '': {
              name: "",
              hideable: false,
              editable: false,
              sortable: false,
              transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
            },
            image: {
              name: "Primary Image",
              col_length: 15,
              editable: false,
              transclude: "<div ng-show=\"row.productinfo.is_intangible == false || row.productinfo.is_intangible == nil\" " +
              "ng-click=\"options.editable.functions.name(row,$event)\"" +
              " class=\"pointer single-image\"><img class=\"img-responsive\" ng-src=\"{{row.image}}\" /></div>" +
              "<div ng-show=\"row.productinfo.is_intangible\" " +
              "ng-click=\"options.editable.functions.name(row,$event)\" " +
              "class=\"pointer single-image\"><img class=\"img-responsive img-reduced-transparency\" ng-src=\"{{row.image}}\" /></div>"
            },
            name: {
              name: "Product Name",
              hideable: false,
              col_length: 15,
              model: "row.productinfo",
              transclude: '<a href="" ng-click="options.editable.functions.name(row,event)" tooltip="{{row.productinfo.name}}">{{row.productinfo.name | cut:false:(50*no_of_lines)}}</a>'
            },
           sku: {
            name: "Primary SKU",
            model: "row.iteminfo.sku",
            col_length: 15,
            transclude: 
            '<span ng-if="row.show_imported_skus" ng-bind="row.iteminfo.sku"></span>' +
            '<span ng-if="!row.show_imported_skus" ng-bind="row.sku"></span>'
            },
            status: {
              name: "Status",
              col_length: 8,
              model: "row.productinfo",
              transclude: "<span class='label label-default' ng-class=\"{" +
              "'label-success': row.productinfo.status == 'active', " +
              "'label-info': row.productinfo.status == 'new' }\">" +
              "{{row.productinfo.status}}</span>"

            },
            barcode: {
              name: "Primary Barcode",
              col_length: 15
            },
            qty: {
              name: "Qty Ordered",
              model: "row.iteminfo",
              col_length: 7,
              transclude: '<span>{{row.iteminfo.qty}}</span>'
            },
            qty_on_hand: {
              name: "QOH",
              col_length: 7,
              editable: false,
              hidden: true
            },
            location_primary: {
              name: "Primary Location",
              col_length: 20
            },
            available_inv: {
              name: "Available Inv",
              editable: false,
              col_length: 7,
              hidden: true
            },
            is_skippable: {
              name: "Is Skippable",
              model: "row.productinfo",
              editable: false,
              transclude: '<div toggle-switch ng-model="row.productinfo.is_skippable" groov-click="options.editable.update(row.productinfo,\'is_skippable\')"></div>',
              hidden: true
            },
            print_barcode: {
              name: "Print Barcode",
              editable: false,
              hidden: true,
              col_length: 20,
              model: "row.iteminfo",
              transclude: "<a class='groove-button label label-default' ng-class=\"{" +
              "'label-success': row.iteminfo.is_barcode_printed == false, " +
              "'label-default': row.iteminfo.is_barcode_printed == true }\" " +
              " groov-click=\"options.editable.print_status(row.iteminfo,row.productinfo)\" href=\"\">" +
              "&nbsp;&nbsp;<i class=\"glyphicon glyphicon-print icon-large white\"></i>&nbsp;&nbsp;</a>"
            },
            category: {
              name: "Category",
              sortable: true,
              col_length: 20,
              hidden: true
            },
            packing_instructions: {
              name: "Product Instructions",
              col_length: 20,
              hidden: true
            }
          }
        };

        scope.gridOptionsForBox = {
          identifier: 'orderitemsforbox',
          draggable: true,
          show_hide: true,
          selectable: true,
          scrollbar: true,
          no_of_lines: 3,
          all_fields: {
            '': {
              name: "",
              hideable: false,
              editable: false,
              sortable: false,
              transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
            },
            name: {
              name: "Product Name",
              hideable: false,
              col_length: 15,
              model: "row",
              transclude: '<a href="" tooltip="{{row.name}}">{{row.name | cut:false:(50*no_of_lines)}}</a>'
            },
            sku: {
              name: "SKU",
              col_length: 15
            },
            qty: {
              name: "Qty Ordered",
              col_length: 7
            }
          }
        };

        myscope.add_hotkeys();
        if (order_id) {
          myscope.order_single_details(order_id, true);
        } else {
          myscope.order_single_details($stateParams.order_id, true);
        };
      };
      myscope.init();
      //$('.icon-question-sign').popover({trigger: 'hover focus'});
    }
  ]);
