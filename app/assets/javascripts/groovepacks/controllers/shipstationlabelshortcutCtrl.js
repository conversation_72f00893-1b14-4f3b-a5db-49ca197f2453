groovepacks_controllers.
  controller('shipstationlabelshortcutCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', '$rootScope','$window', 'rate',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, $rootScope, $window, rate) {
      var myscope = {};

      myscope.init = function () {
        $scope.ss_label_rate_option = rate;
        $scope.ss_label_rate_shortcut = null;
      };

      var keysPressed = {};

      document.addEventListener('keydown', function(event) {
        if ($scope.ss_label_rate_shortcut != null) { return; }
        keysPressed[event.key] = true;
      });

      document.addEventListener('keyup', function(event) {
        if ($scope.ss_label_rate_shortcut != null) { return; }
        shortcut = Object.keys(keysPressed).reverse().join('+');
        ok_btn.disabled = false;
        restart_btn.style.display = '';
        ss_label_rate_shortcut.innerHTML = shortcut;
        $scope.ss_label_rate_shortcut = shortcut;
      });

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.ok = function () {
        if ($scope.ss_label_rate_shortcut != null) {
          var shortcut_data = { shortcut: {} };
          shortcut_data.shortcut[$scope.ss_label_rate_shortcut] = $scope.ss_label_rate_option.serviceName;
          $modalInstance.close(shortcut_data);
        } else {
          $modalInstance.dismiss("cancel-button-click");
        }
      };

      $scope.restart = function() {
        keysPressed = {};
        ss_label_rate_shortcut.innerHTML = 'Press shortcut keys';
        $scope.ss_label_rate_shortcut = null;
        ok_btn.disabled = true;
        restart_btn.style.display = 'none';
      }

      myscope.init();
  }]);
