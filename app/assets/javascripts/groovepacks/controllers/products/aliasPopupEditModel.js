groovepacks_controllers.
controller('aliasPopupEditModal', ['$scope', '$rootScope', 'current_product_data', 'alias_product_data', 'after_alias_product_data', 'shared_bacode_products', 'matching_barcode', '$state', '$stateParams', '$modalInstance', '$http', '$timeout', '$modal', '$q', 'groov_translator', 'products', 'warehouses', 'generalsettings', 'scanPack', 'notification',
  function (scope, $rootScope, current_product_data, alias_product_data, after_alias_product_data, shared_bacode_products, matching_barcode, $state, $stateParams, $modalInstance, $http, $timeout, $modal, $q, groov_translator, products, warehouses, generalsettings, scanPack, notification) {
    var myscope = {};


    /**
     * Public methods
     */

    scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    myscope.init = function () {
      scope.current_product_data = current_product_data;
      scope.alias_product_data = alias_product_data;
      scope.after_alias_product_data = after_alias_product_data;
      scope.matching_barcode = matching_barcode;
      scope.shared_bacode_products = shared_bacode_products;
      scope.popup_dismissed = false;
    };

    scope.proceed_with_aliasing = function () {
      $modalInstance.close("proceed_with_aliasing");
    }

    scope.consider_different_items = function () {
      $modalInstance.close("consider_different_items");
    }

    scope.allow_shared_barcode = function () {
      $modalInstance.close("allow_shared_barcode");
    }

    scope.have_unique_barcode = function () {
      $modalInstance.close("have_unique_barcode");
    }

    myscope.init();
  }
]);