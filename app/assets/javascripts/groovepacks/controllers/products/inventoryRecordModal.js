groovepacks_controllers.controller('inventoryRecordModal', ['$scope', 'type', 'exceptions', 'id', '$timeout', '$state' ,'$modalInstance', '$rootScope', '$window','$q', 'notification', 'products', 'selected_report',
  function (scope, type, exceptions, id, $timeout, $state, $modalInstance, $rootScope, $window, $q, notification, products, selected_report) {

    var myscope = {};
    //Definitions
    scope.ok = function () {
      $modalInstance.close({selected: myscope.get_selected_products(), select_toggle: scope.products.setup.select_all, report_name: scope.products.setup.report_name, search: scope.products.setup.search, advanced_search: scope.products.setup.advanced_search});
    };
    scope.add_to_report = function () {
      $modalInstance.close({selected: myscope.get_selected_products(), select_toggle: scope.products.setup.select_all, report_name: scope.products.setup.report_name, search: scope.products.setup.search, advanced_search: scope.products.setup.advanced_search});
    };
    scope.cancel = function () {
      $modalInstance.dismiss();
    };
    scope.clear = function(){
      scope.selected_products = [];
      scope.select_all_toggle(false);
      myscope.get_products();
    };

    /*
     * Public methods
     */

    myscope.get_selected_products = function(){
      if (!scope.products.setup.select_all) {
        selected_products = scope.products.selected
        selected_product_ids = []
        for (var i = 0; i < selected_products.length; i++) {
          selected_product_ids.push(selected_products[i].id);
        }
        return selected_product_ids;
      } else {
        return null;
      }
    };

    scope.load_page = function (direction) {
      var page = parseInt($state.params.page, 10);
      page = (typeof direction == 'undefined' || direction != 'previous') ? page + 1 : page - 1;
      return myscope.load_page_number(page);
    };

    scope.select_all_toggle = function (val) {
      scope.products.setup.select_all = !!val;
      myscope.invert(false);
      scope.products.selected = [];
      for (var i = 0; i < scope.products.list.length; i++) {
        scope.products.list[i].checked = scope.products.setup.select_all;
        if (scope.products.setup.select_all) {
          myscope.select_single(scope.products.list[i]);
        }
      }
    };

    /*
     * Public methods
     */

    myscope.get_selected_products = function(){
      if (!scope.products.setup.select_all) {
        selected_products = scope.products.selected
        selected_product_ids = []
        for (var i = 0; i < selected_products.length; i++) {
          selected_product_ids.push(selected_products[i].id);
        }
        return selected_product_ids;
      } else {
        return null;
      }
    };

    scope.load_page = function (direction) {
      var page = parseInt($state.params.page, 10);
      page = (typeof direction == 'undefined' || direction != 'previous') ? page + 1 : page - 1;
      return myscope.load_page_number(page);
    };

    scope.select_all_toggle = function (val) {
      scope.products.setup.select_all = !!val;
      myscope.invert(false);
      scope.products.selected = [];
      for (var i = 0; i < scope.products.list.length; i++) {
        scope.products.list[i].checked = scope.products.setup.select_all;
        if (scope.products.setup.select_all) {
          myscope.select_single(scope.products.list[i]);
        }
      }
    };

    //Setup options
    scope.handlesort = function (value) {
      if(event.shiftKey){
        value = '';
        scope.products.setup.order = 'ASC';
      }
      myscope.common_setup_opt('sort', value, scope.product_type/*'product'*/);
    };

    /*
     * Private methods
     */

    myscope.select_single = function (row) {
      products.single.select(scope.products, row);
    };

    myscope.select_pages = function (from, to, state) {
      products.list.select(scope.products, from, to, state);
    };

    myscope.invert = function (val) {
      scope.products.setup.inverted = !!val;
      if (scope.products.setup.inverted) {
        if (scope.products.setup.select_all) {
          scope.select_all_toggle(false);
        } else if (scope.products.selected.length == 0) {
          scope.select_all_toggle(true);
        }
      }
      myscope.update_selected_count();
    };

    myscope.load_page_number = function (page) {
      scope.inventory_report_page = false;
      if (page > 0 && page <= Math.ceil(scope.gridOptions.paginate.total_items / scope.gridOptions.paginate.items_per_page)) {
        if (scope.products.setup.search == '') {
          var toParams = {};
          for (var key in $state.params) {
            if ($state.params.hasOwnProperty(key) && ['type', 'filter', 'product_id'].indexOf(key) != -1) {
              toParams[key] = $state.params[key];
            }
          }
          toParams['page'] = page;
          $state.go($state.current.name, toParams);
        }
        return myscope.get_products(page);
      } else {
        var req = $q.defer();
        req.reject();
        return req.promise;
      }
    };

    myscope.get_products = function (page) {
      if (typeof page == 'undefined') {
        page = $state.params.page;
      }
      try{
        scope.ctrlKey = event.ctrlKey;
      } catch(e){}
      if (scope._can_load_products) {
        scope._can_load_products = false;
        return products.list.get(scope.products, page).success(function (response) {
          if (scope.ctrlKey == true){
            scope.products.selected.push(scope.products.list);
            scope.products.selected = [].concat.apply([], scope.products.selected);
          }
          scope.gridOptions.paginate.total_items = products.list.total_items(scope.products);
          if (scope.gridOptions.paginate.current_page != page) {
            scope.gridOptions.paginate.current_page = page;
          }
          myscope.update_selected_count();
          scope._can_load_products = true;
        });
      } else {
        myscope.do_load_products = page;
        var req = $q.defer();
        req.resolve();
        return req.promise;
      }
    };

    myscope.common_setup_opt = function (type, value, selector) {
      products.setup.update(scope.products.setup, type, value);
      scope.products.setup.is_kit = (selector == 'kit') ? 1 : 0;
      myscope.get_products();
    };

    myscope.update_selected_count = function () {
      if (scope.products.setup.inverted && scope.gridOptions.paginate.show) {
        scope.gridOptions.selections.selected_count = scope.gridOptions.paginate.total_items - scope.products.selected.length;
      } else {
        scope.gridOptions.selections.selected_count = scope.products.selected.length;
      }
    };

    myscope.update_print_status = function (product) {
      $window.open($rootScope.api_url+'/products/' + product.id + '/generate_barcode_slip.pdf');
    };

    //Watcher ones
    myscope.can_do_load_products = function () {
      if (scope._can_load_products && myscope.do_load_products) {
        myscope.get_products(1);
        myscope.do_load_products = false;
      }
    };

    myscope.search_products = function () {
      if (scope.productFilterTimeout) $timeout.cancel(scope.productFilterTimeout);

      scope.productFilterTimeout = $timeout(function() {
        myscope.get_products(1);
      }, 700); // delay 700 ms
    };

    //Constructor
    myscope.init = function () {
      //Public properties
      scope.products = products.model.get();
      scope.products.setup.limit = 30;
      scope.products.setup.filter = "all";
      scope.custom_identifier = Math.floor(Math.random() * 1000);
      scope.is_kit = false;
      scope.is_order = false;
      if (selected_report != undefined){
        scope.selected_products = selected_report.selected_id;
        scope.selected_report = selected_report;
        scope.products.setup.report_name = selected_report.name;
      } else {
        scope.selected_products = [];
      }

      scope.load_disabled = false;

      //Private properties
      myscope.exceptions = [];
      myscope.do_load_products = false;
      scope._can_load_products = true;
      myscope.accepted_types = ['alias', 'kit', 'order', 'master_alias'];
      scope.gridOptions = {
        identifier: 'products',
        dynamic_width: true,
        select_all: scope.select_all_toggle,
        invert: myscope.invert,
        sort_func: scope.handlesort,
        setup: scope.products.setup,
        scrollbar: true,
        no_of_lines: 3,
        selections: {
          show_dropdown: false,
          single_callback: myscope.select_single,
          multi_page: myscope.select_pages,
          selected_count: 0,
          show: myscope.show_selected
        },
        paginate: {
          show: true,
          //send a large number to prevent resetting page number
          total_items: 50000,
          current_page: $state.params.page,
          items_per_page: scope.products.setup.limit,
          callback: myscope.load_page_number
        },
        show_hide: true,
        selectable: true,
        draggable: true,
        sortable: true,
        editable: {
          print_status: myscope.update_print_status,
          array: false,
          functions: {
            name: myscope.handle_click_fn
          }
        },
        all_fields: {
          '': {
            name: "",
            hideable: false,
            editable: false,
            sortable: false,
            transclude: '<i class="fas fa-bullseye fa-lg" ng-style="{\'color\' : row.checked ? \'#ccff66\' : \'#cccccc\'}"></i>'
          },
          status: {
            name: "Status",
            col_length: 5,
            transclude: "<span class='label label-default' ng-class=\"{" +
            "'label-success': row[field] == 'active', " +
            "'label-info': row[field] == 'new' }\">" +
            "{{row[field]}}</span>",
            editable: false
          },
          name: {
            name: "Item Name",
            hideable: false,
            col_length: 20,
            editable: false
          },
          sku: {
            name: "SKU",
            col_length: 20,
            editable: false
          },
          barcode: {
            col_length: 20,
            name: "Barcode",
            editable: false
          },
          location_primary: {
            name: "Primary Location",
            col_length: 20,
            class: "span3",
            editable: false
          },
          cat: {
            name: "Category",
            col_length: 15,
            editable: false
          },
          qty_on_hand: {
            name: "QoH",
            col_length: 7,
            editable: false,
          },
          store_name: {
            name: "Store",
            editable: false,
            hidden: true
          },
          available_inv: {
            name: "Avbl Inv",
            editable: false,
            hidden: true
          },
          location_secondary: {
            name: "Secondary Location",
            col_length: 20,
            class: "span3",
            editable: false,
            hidden: true
          },
          image: {
            name: "Image",
            col_length: 15,
            transclude: '<div groov-rt-click="copied(row.image)" class="pointer single-image"><img class="img-responsive" ng-src="{{row.image}}" /></div>',
            editable: false,
            hidden: true
          },
          location_tertiary: {
            name: "Tertiary Location",
            col_length: 20,
            class: "span3",
            editable: false,
            hidden: true
          },
          print_barcode: {
            name: "Print Barcode",
            editable: false,
            hidden: true,
            col_length: 20,
            transclude: "<a class='groove-button label label-default' groov-click=\"options.editable.print_status(row)\" href=\"\">" +
            "&nbsp;&nbsp;<i class=\"glyphicon glyphicon-print icon-large white\"></i>&nbsp;&nbsp;</a>"
          },
          location_name: {
            name: "Warehouse Name",
            class: "span3",
            editable: false,
            hidden: true
          },
          custom_product_1: {
            name: "Custom Product data 1",
            class: "span3",
            editable: false,
            hidden: true
          },
          custom_product_2: {
            name: "Custom Product data 2",
            class: "span3",
            editable: false,
            hidden: true
          },
          custom_product_3: {
            name: "Custom Product data 3",
            class: "span3",
            editable: false,
            hidden: true
          }
        }
      };
      //$("#alias-search-query-"+scope.custom_identifier).focus();
      //Register watchers

      if (myscope.accepted_types.indexOf(type) == -1) {
        type = 'alias';
      }
      myscope.exceptions = [];
      scope.is_order = (type == 'order');
      scope.is_kit = (type == 'kit');
      scope.products.setup.is_kit = (scope.is_order || type == 'alias' || type == 'master_alias') ? -1 : scope.products.setup.is_kit;
      if (typeof exceptions != 'undefined') {
        for (var i = 0; i < exceptions.length; i++) {
          myscope.exceptions.push(exceptions[i].id);
        }
      }
      if (typeof id !== 'undefined') {
        myscope.exceptions.push(id);
      }
      myscope.get_products(1);
      $timeout(scope.focus_search, 200);
      scope.$watch('products.selected', myscope.update_selected_count, true);
      scope.$watch('products.setup.search', myscope.search_products);
      // scope.$watch('_can_load_products', myscope.can_do_load_products);
      scope.$watch('paginate.current_page', myscope.get_products);
      $timeout(function () {
        selector_icons = $('.fa-bullseye');
        for (i = 0; i < selector_icons.length; ++i) {
          if ($('.fa-bullseye')[i].closest('.grid-simple-text') != null) {
            $('.fa-bullseye')[i].closest('.grid-simple-text').style.minWidth = '0px';
          }
        }
        tables  = document.querySelectorAll('.table-well');
        for (i = 0; i < tables.length; ++i) {
          if ($('.fa-bullseye')[0].closest('td') != null) {
            $(tables[i]).find('th').eq($('.fa-bullseye')[0].closest('td').cellIndex)[0].style.width = '1%';
          }
        }
      }, 1000);
    };

    //Definitions end above this line
    /*
     * Initialization
     */
    myscope.init();
  }
]);
