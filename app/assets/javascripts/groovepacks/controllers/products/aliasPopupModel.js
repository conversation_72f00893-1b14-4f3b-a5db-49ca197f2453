groovepacks_controllers.
controller('aliasPopupModal', ['$scope', '$rootScope', 'current_product_data', 'alias_product_data', 'after_alias_product_data', 'shared_bacode_products', 'scope_products', 'matching_barcode', '$state', '$stateParams', '$modalInstance', '$http', '$timeout', '$modal', '$q', 'groov_translator', 'products', 'warehouses', 'generalsettings', 'scanPack', 'notification',
  function (scope, $rootScope, current_product_data, alias_product_data, after_alias_product_data, shared_bacode_products, scope_products, matching_barcode, $state, $stateParams, $modalInstance, $http, $timeout, $modal, $q, groov_translator, products, warehouses, generalsettings, scanPack, notification) {
    var myscope = {};


    /**
     * Public methods
     */

    scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    myscope.init = function () {
      scope.current_product_data = current_product_data;
      scope.alias_product_data = alias_product_data;
      scope.after_alias_product_data = after_alias_product_data;
      scope.matching_barcode = matching_barcode;
      scope.shared_bacode_products = shared_bacode_products;
      scope.popup_dismissed = false;
      scope.scope_products = scope_products;
    };

    scope.proceed_with_aliasing = function () {
      products.single.alias(scope_products, [alias_product_data.id]).then(function (data) {
        if(!!data.data.status) {
          $modalInstance.close("" + alias_product_data.id);
        }
      });
    }

    scope.consider_different_items = function () {
      $modalInstance.close("consider_different_items");
    }

    scope.allow_shared_barcode = function () {
      scope_products.single.barcodes[scope_products.single.barcodes.length - 1].permit_same_barcode = true;
      products.single.update(scope_products, true).then(function (data) {
        if (data.data.status) {
          notification.notify("Barcode added successfully", 1)
        }
        $modalInstance.close("allow_shared_barcode");
      });
    }

    scope.have_unique_barcode = function () {
      $modalInstance.close("have_unique_barcode");
    }

    myscope.init();
  }
]);