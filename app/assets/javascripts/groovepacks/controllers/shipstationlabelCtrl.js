groovepacks_controllers.
  controller('shipstationlabelCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', '$rootScope','$window', 'printing_service', 'generalsettings', 'ss_label_data',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, $rootScope, $window, printing_service, generalsettings, ss_label_data) {
      var myscope = {};

      myscope.defaults = function (val ) {
        if (val == null) {val = $scope.get_current_time_in_zone()}
        return {
          current: {
            open: false,
            time: new Date(val)
          }
        }
      };

      $scope.get_current_time_in_zone = function () {
        var date = new Date();
        var dst_offset = 0;
        if ($scope.dst_status == true) {
          dst_offset = 60; // Add 1 hour if DST
        }
        date.setMinutes(date.getMinutes() + date.getTimezoneOffset() + Math.round($scope.gp_timezone/60) + dst_offset);
        var tz_date = new Date(date);
        return tz_date;
      }

      myscope.init = function () {
        $scope.update_rate = false;
        $scope.print_clicked = false;
        $scope.label_rate = null;
        $scope.rate_loading = false;
        $scope.ss_label_data = ss_label_data;
        $scope.general_settings = generalsettings.model.get();
        $scope.service_packages = [];
        generalsettings.single.get($scope.general_settings).success(function(data){
          $scope.carrier = ss_label_data.carrier ? ss_label_data.carrier : null;
          $scope.service = ss_label_data.service ? ss_label_data.service : null;
          $scope.gp_timezone = data.data.settings.time_zone;
          $scope.gp_tz_name = data.time_zone_name;
          $scope.dst_status = data.gp_tz_dst;
          date = new Date
          todays_date = date.setHours(date.getHours() + 6)
          if (ss_label_data.shipDate != undefined && ss_label_data.shipDate.length > 0) {
            try{
              if (moment(ss_label_data.shipDate).isAfter(new Date())) {
                $scope.ship_date = myscope.defaults(moment(ss_label_data.shipDate).format());
              } else {
                $scope.ship_date = myscope.defaults(todays_date);
              }
            } catch(e) {
              console.log('SS Label Date Error: ', e)
              $scope.ship_date = myscope.defaults(todays_date);
            }
          } else{
            $scope.ship_date = myscope.defaults(todays_date);
          }
          $scope.order_number = ss_label_data.order_number ? ss_label_data.order_number : null;
          $scope.order_id = ss_label_data.orderId ? ss_label_data.orderId : null;
          $scope.available_carriers = ss_label_data.available_carriers;
          // $scope.available_services = [];
          // $scope.available_packages = [];
          // $scope.all_available_services = ss_label_data.all_available_services;
          // $scope.all_available_packages = ss_label_data.all_available_packages;
          $scope.package = ss_label_data.package ? ss_label_data.package : null;
          $scope.rate = null;
          if ($scope.carrier != null) {
            $scope.carrier = $scope.available_carriers.find(function(carrier) { if (carrier.name === $scope.carrier.name) { return carrier} });
          }
          // if ($scope.service != null) {
          //   $scope.service = $scope.all_available_services.find(function(service) { if (service.name === $scope.service.name) { return service} else { return $scope.service} });
          // }
          // if ($scope.package != null) {
          //   $scope.package = $scope.all_available_packages.find(function(package) { if (package.name === $scope.package.name) { return package} else { return $scope.package} });
          // }
          $scope.weight = ss_label_data.weight ? (ss_label_data.weight.value ? parseFloat(ss_label_data.weight.value) : null) : null;
          $scope.weight_unit = ss_label_data.weight ? (ss_label_data.weight.units ? ss_label_data.weight.units : null) : null;
          $scope.length = ss_label_data.dimensions ? (ss_label_data.dimensions.length ? parseFloat(ss_label_data.dimensions.length) : null) : null;
          $scope.width = ss_label_data.dimensions ? (ss_label_data.dimensions.width ? parseFloat(ss_label_data.dimensions.width) : null) : null;
          $scope.height = ss_label_data.dimensions ? (ss_label_data.dimensions.height ? parseFloat(ss_label_data.dimensions.height) : null) : null;
          $scope.length_unit = ss_label_data.dimensions ? (ss_label_data.dimensions.units ? ss_label_data.dimensions.units : null) : null;
          $scope.confirmation = ss_label_data.confirmation ? ss_label_data.confirmation : null;
          $scope.fromPostalCode = ss_label_data.fromPostalCode ? ss_label_data.fromPostalCode : null;
          $scope.toState = ss_label_data.toState ? ss_label_data.toState : null;
          $scope.toPostalCode = ss_label_data.toPostalCode ? ss_label_data.toPostalCode : null;
          $scope.toCountry = ss_label_data.toCountry ? ss_label_data.toCountry : null;
          $scope.toName = ss_label_data.toName ? ss_label_data.toName : null;
          $scope.toAddress1 = ss_label_data.toAddress1 ? ss_label_data.toAddress1 : null;
          $scope.toAddress2 = ss_label_data.toAddress2 ? ss_label_data.toAddress2 : null;
          $scope.toCity = ss_label_data.toCity ? ss_label_data.toCity : null;
          $scope.shipping_address = {
            name: $scope.toName, address1: $scope.toAddress1, address2: $scope.toAddress2, state: $scope.toState,
            postal_code: $scope.toPostalCode, city: $scope.toCity, country: $scope.toCountry
          };
          $scope.current_shipping_address = {
            name: angular.copy($scope.shipping_address.name), address1: angular.copy($scope.shipping_address.address1),
            address2: angular.copy($scope.shipping_address.address2), state: angular.copy($scope.shipping_address.state),
            postal_code: angular.copy($scope.shipping_address.postal_code), city: angular.copy($scope.shipping_address.city),
            country: angular.copy($scope.shipping_address.country)
          };
          $scope.weight_dimensions = {
            weight: { value: $scope.weight, units: $scope.weight_unit },
            dimensions: { units: $scope.length_unit, length: $scope.length, width: $scope.width, height: $scope.height }
          }
          $scope.current_weight_dimensions = {
            weight: { value: angular.copy($scope.weight_dimensions.weight.value), units: angular.copy($scope.weight_dimensions.weight.units) },
            dimensions: {
              units: angular.copy($scope.weight_dimensions.dimensions.units), length: angular.copy($scope.weight_dimensions.dimensions.length),
              width: angular.copy($scope.weight_dimensions.dimensions.width), height: angular.copy($scope.weight_dimensions.dimensions.height)
            }
          };
          // if ($scope.carrier != null){
          //   $scope.get_carrier_service_packages(false);
          // }
          // if ($scope.service != null){
          //   $scope.set_service_packages();
          // }
          // $scope.getRealtimeRate();
          $scope.label_shortcuts = ss_label_data.label_shortcuts ? ss_label_data.label_shortcuts : {};
          myscope.set_rate(true);

        });
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      // $scope.fetch_services_packages = function (carrier_code, reset) {
      //   $scope.available_services = null;
      //   $scope.available_packages = null;
      //   if (reset) {
      //     $scope.service = null;
      //     $scope.package = null;
      //   }
      //   $scope.service_packages = [];
      //   $http.get($rootScope.api_url + '/orders/fetch_services_packages?credential_id=' + $scope.ss_label_data.credential_id + '&carrier_code=' + carrier_code).success(function (data) {
      //     if (data.status) {
      //       $scope.all_available_services = data.available_services;
      //       $scope.all_available_packages = data.available_packages;
      //     } else {
      //       notification.notify(data.error_messages, 0);
      //     }
      //   });
      // };

      $scope.updateRealtimeRates = function(){
        if($scope.update_rate){
          $scope.getRealtimeRate(false);
        }
      }

      $scope.ok = function () {
        if ($scope.update_rate) {
          $scope.getRealtimeRate(true);
        } else {
          myscope.print_label();
        }
      };

      myscope.print_label = function() {
        if (myscope.label_data_valid() && $scope.print_clicked == false) {
          $scope.print_clicked = true;
          $http.post($rootScope.api_url + '/orders/create_ss_label', myscope.create_label_data()).success(function (data) {
              if (data.status) {
                var settings = {};
                settings = JSON.parse(localStorage.getItem('general_settings'));
                if (settings.direct_printing_options && settings.data.settings.print_ss_shipping_labels) {
                  printing_service.print_now(data, 'user_selected_printer_ss_shipping_label_' + settings.data.settings.packing_slip_size);
                } else {
                  notification.notify("Label Created", 1);
                  $window.open(data.url);
                }
                $scope.print_clicked = false;
                $scope.cancel();
              } else {
                $scope.print_clicked = false;
                notification.notify(data.error_messages, 0);
              }
          });
        } else if ($scope.print_clicked == false) {
          notification.notify("Please select required fields", 0);
        }
      };

      myscope.label_data_valid = function() {
        return ($scope.ship_date != null && $scope.confirmation != null);
      };

      myscope.label_rates_data_valid = function() {
        // if ($scope.carrier != null && $scope.fromPostalCode != null && $scope.toPostalCode != null  && $scope.toCountry != null && $scope.weight != null && $scope.weight_unit != null) {
        //   return true;
        // } else {
        //   var errors = [];
        //   if ($scope.carrier == null) { errors.push('Carrier Code is missing.') };
        //   if ($scope.fromPostalCode == null) { errors.push('Please set Postal Code in Store Settings.') };
        //   if ($scope.toPostalCode == null) { errors.push('Destination Postal Code missing from order') };
        //   if ($scope.toCountry == null) { errors.push('Country is missing from order') };
        //   if ($scope.weight == null) { errors.push('Weight is missing') };
        //   if ($scope.weight_unit == null) { errors.push('Weight Unit is missing') };
        //   return errors;
        // }
        if ($scope.fromPostalCode != null && $scope.toPostalCode != null  && $scope.toCountry != null && $scope.weight != null && $scope.weight_unit != null) {
          return true;
        } else {
          var errors = [];
          if ($scope.fromPostalCode == null) { errors.push('Please set Postal Code in Store Settings.') };
          if ($scope.toPostalCode == null) { errors.push('Destination Postal Code missing from order') };
          if ($scope.toCountry == null) { errors.push('Country is missing from order') };
          if ($scope.weight == null) { errors.push('Weight is missing') };
          if ($scope.weight_unit == null) { errors.push('Weight Unit is missing') };
          return errors;
        }
      };

      myscope.get_rates_data = function() {
        // var rates_data = { credential_id: $scope.ss_label_data.credential_id, post_data: { carrierCode: $scope.carrier.code, fromPostalCode: $scope.fromPostalCode, toState: $scope.toState, toCountry: $scope.toCountry, toPostalCode: $scope.toPostalCode, weight: { value: $scope.weight, units: $scope.weight_unit } } }
        // if ($scope.weight != null && $scope.weight_unit != null) { rates_data.post_data.weight = { value: $scope.weight, units: $scope.weight_unit }; };
        // if ($scope.length != null && $scope.width != null && $scope.height != null && $scope.length_unit != null) { rates_data.post_data.dimensions = { length: $scope.length, width: $scope.width, height: $scope.height, units: $scope.length_unit}; };
        // // if ($scope.package != null) { rates_data.post_data.packageCode = $scope.package.code; };
        // if ($scope.confirmation != null) { rates_data.post_data.confirmation = $scope.confirmation; };
        // // if ($scope.service != null) { rates_data.post_data.serviceCode = $scope.service.code; };
        // return rates_data;
        var rates_data = { credential_id: $scope.ss_label_data.credential_id, post_data: { fromPostalCode: $scope.fromPostalCode, toState: $scope.toState, toCountry: $scope.toCountry, toPostalCode: $scope.toPostalCode, toCity: $scope.toCity } }
        if ($scope.weight != null && $scope.weight_unit != null) { rates_data.post_data.weight = { value: $scope.weight, units: $scope.weight_unit }; };
        if ($scope.length != null && $scope.width != null && $scope.height != null && $scope.length_unit != null) { rates_data.post_data.dimensions = { length: $scope.length, width: $scope.width, height: $scope.height, units: $scope.length_unit}; };
        // if ($scope.package != null) { rates_data.post_data.packageCode = $scope.package.code; };
        if ($scope.confirmation != null) { rates_data.post_data.confirmation = $scope.confirmation; };
        // if ($scope.service != null) { rates_data.post_data.serviceCode = $scope.service.code; };
        return rates_data;
      };

      myscope.create_label_data = function() {
        var label_data = { credential_id: $scope.ss_label_data.credential_id, post_data: { orderId: $scope.order_id, carrierCode: $scope.rate.carrierCode, serviceCode: $scope.rate.serviceCode, confirmation: $scope.confirmation, shipDate: moment($scope.ship_date.current.time).format('ddd, D MMM YYYY') } }
        // if ($scope.package != null && $scope.carrier.code == 'stamps_com') { label_data.post_data.packageCode = $scope.package.code; };
        if ($scope.rate.packageCode != null) { label_data.post_data.packageCode = $scope.rate.packageCode };
        if ($scope.weight != null && $scope.weight_unit != null) { label_data.post_data.weight = { value: $scope.weight, units: $scope.weight_unit }; };
        if ($scope.toAddress1 != null && $scope.toCity != null && $scope.toCountry != null && $scope.toName != null && $scope.toPostalCode != null && $scope.toState != null) { label_data.post_data.shipTo = { street1: $scope.toAddress1, city: $scope.toCity, country: $scope.toCountry, name: $scope.toName, postalCode: $scope.toPostalCode, state: $scope.toState}}
        if ($scope.length != null && $scope.width != null && $scope.height != null && $scope.length_unit != null) { label_data.post_data.dimensions = { length: $scope.length, width: $scope.width, height: $scope.height, units: $scope.length_unit}; };
        return label_data;
      };

      $scope.open_picker = function (event, object) {
        event.preventDefault();
        event.stopPropagation();
        object.open = true;
      };

      $scope.change_value = function (type, value) {
        var prev_val = null;
        if (type == 'weight') {
          prev_val = $scope.weight;
          $scope.weight = value;
        } else if (type == 'length') {
          prev_val = $scope.length;
          $scope.length = value;
        } else if (type == 'width') {
          prev_val = $scope.width;
          $scope.width = value;
        } else if (type == 'height') {
          prev_val = $scope.height;
          $scope.height = value;
        }
        if (prev_val != value) {
          $scope.update_rate = true;
        }
        $scope.save_ss_label_data(false);
      }

      $scope.change_label_opt = function (type, value) {
        var prev_val = null;
        if (type == 'weight_unit') {
          prev_val = $scope.weight_unit;
          $scope.weight_unit = value;
        } else if (type == 'carrier') {
          prev_val = $scope.carrier;
          $scope.carrier = value;
          // if (prev_val != value) { $scope.get_carrier_service_packages(true); }
          // if (prev_val != value) { $scope.fetch_services_packages(value.code); }
        // } else if (type == 'service') {
        //   prev_val = $scope.service;
        //   $scope.service = value;
        //   if (prev_val != value) { $scope.package = null; }
        // } else if (type == 'package') {
        //   prev_val = $scope.package;
        //   $scope.package = value;
        //   $scope.service = $scope.available_services.find(function(service) { if (service.name === value.serviceName) {return service} });
        } else if (type == 'length_unit') {
          prev_val = $scope.length_unit;
          $scope.length_unit = value;
        } else if (type == 'confirmation') {
          prev_val = $scope.confirmation;
          if (value == '_select_confirmation') {
            $scope.confirmation = null;
          } else {
            $scope.confirmation = value;
          }
        } else if (type == 'rate') {
          prev_val = $scope.rate;
          $scope.rate = value;
        }
        // if (type != 'package' && type != 'service' && type != 'carrier' && prev_val != value) {
        //   // $scope.getRealtimeRate();
        //   $scope.get_carrier_service_packages(false);
        // }
        if (type != 'rate' && prev_val != value) {
          $scope.update_rate = true;
        }
        $scope.save_ss_label_data(false);
      }

      // $scope.set_service_packages = function() {
      //   $scope.selected_package = $scope.package;
      //   $scope.package = null;
      //   $scope.service_packages = [];
      //   var valid_data_for_rates = myscope.label_rates_data_valid();
      //   if (valid_data_for_rates == true) {
      //     $http.post($rootScope.api_url + '/orders/get_rates', myscope.get_rates_data()).success(function (data) {
      //         if (data.status) {
      //           if (data.rates.length) {
      //             package_names = data.rates.map(function(data) {return	{ name: data.serviceName.replace($scope.service.name + ' - ', ''), rate: data.shipmentCost + data.otherCost }});
      //             for(var i=0; i<package_names.length; i++) {
      //               var package_found = $scope.available_packages.find(function(package) { if (package.name === package_names[i].name) { package.rate = parseFloat(package_names[i].rate).toFixed(2); return package; } })
      //               if (package_found != undefined) { $scope.service_packages.push(package_found); }
      //             }
      //             $scope.service_packages.sort(function (a, b) {
      //               return a.rate - b.rate;
      //             });
      //             if ($scope.selected_package != null) {
      //               $scope.package = $scope.service_packages.find(function(package) { if (package.name === $scope.selected_package.name) {return package} });
      //             }
      //           } else {
      //             notification.notify('Sorry, there are no available methods for this combination.' ,0)
      //           }
      //         }
      //     });
      //   }
      // }

      // $scope.get_carrier_service_packages = function(reset_service_packages) {
      //   $scope.prev_service = $scope.service;
      //   var valid_data_for_rates = myscope.label_rates_data_valid();
      //   if (valid_data_for_rates == true) {
      //     if (reset_service_packages) {
      //       $scope.fetch_services_packages($scope.carrier.code, reset_service_packages);
      //     } else {
      //       $scope.available_services = [];
      //       $scope.available_packages = [];
      //     }
      //     $http.post($rootScope.api_url + '/orders/get_rates', myscope.get_rates_data()).success(function (data) {
      //         if (data.status) {
      //           if (data.rates.length) {
      //             if ($scope.carrier.code == 'stamps_com') {
      //               myscope.setStampsServicePackages(data);
      //             } else {
      //               myscope.setUpServicePackages(data);
      //             }
      //             // setUpsServicePackages
      //             // Set Available Services
      //             // var service_names = data.rates.map(function(data) {return	data.serviceCode });
      //             // service_names = service_names.filter(function (x, i, a) {
      //             //   return a.indexOf(x) == i;
      //             // });
      //             // for(var i=0; i<service_names.length; i++) {
      //             //   var service_found = $scope.all_available_services.find(function(service) { if (service.code === service_names[i]) { return service; } })
      //             //   if (service_found != undefined) {
      //             //     service_found.packages = []
      //             //     package_names = data.rates.map(function(data) { if (service_found.code === data.serviceCode) { return { name: data.serviceName.replace(service_found.name + ' - ', ''), serviceName: data.serviceName, rate: data.shipmentCost + data.otherCost }}});
      //             //     package_names = package_names.filter(function(e){ return e;});
      //             //     for(var i=0; i<package_names.length; i++) {
      //             //       var package_found = $scope.all_available_packages.find(function(package) { if (package.name === package_names[i].name) { return package; } })
      //             //       if (package_found != undefined) {
      //             //         package_found = angular.copy(package_found);
      //             //         package_found.serviceName = package_names[i].serviceName.split(' - ')[0];
      //             //         package_found.rate = parseFloat(package_names[i].rate).toFixed(2);
      //             //         service_found.packages.push(package_found);
      //             //       }
      //             //     }
      //             //     service_found.packages.sort(function (a, b) {
      //             //       return a.rate - b.rate;
      //             //     });
      //             //     if ($scope.available_services == null) {
      //             //       $scope.available_services = [];
      //             //     }
      //             //     $scope.available_services.push(service_found);
      //             //   }
      //             // }
      //             // if ($scope.prev_service != null && $scope.available_services != null) {
      //             //   $scope.service = $scope.available_services.find(function(service) { if (service.name === $scope.prev_service.name) {return service} });
      //             // }
      //             // if ($scope.service != null && $scope.package != null) {
      //             //   $scope.package = $scope.service.packages.find(function(package) { if (package.name === $scope.package.name) {return package} });
      //             // }
      //           } else {
      //             notification.notify('Sorry, there are no available methods for this combination.' ,0)
      //           }
      //         } else {
      //           notification.notify(data.error_messages, 0);
      //         }
      //     });
      //   } else {
      //     notification.notify(valid_data_for_rates[0], 0);
      //   }
      // }

      // $scope.getRates = function () {
      //   var valid_data_for_rates = myscope.label_rates_data_valid();
      //   if (valid_data_for_rates == true) {
      //     $http.post($rootScope.api_url + '/orders/get_rates', myscope.get_rates_data()).success(function (data) {
      //         if (data.status) {
      //           if (data.rates.length) {
      //             myscope.show_ss_label_rates_popup(data.rates);
      //           } else {
      //             notification.notify('Sorry, there are no available methods for this combination.' ,0)
      //           }
      //         } else {
      //           notification.notify(data.error_messages, 0);
      //         }
      //     });
      //   } else {
      //     notification.notify(valid_data_for_rates[0], 0);
      //   }
      // }

      $scope.getRealtimeRate = function (print_label) {
        var valid_data_for_rates = myscope.label_rates_data_valid();
        // $scope.rate = null;
        if (valid_data_for_rates == true) {
          var rates_data = myscope.get_rates_data();
          if ($scope.previous_rate_data == rates_data) { return };
          $scope.rate_loading = true;
          $http.post($rootScope.api_url + '/orders/get_realtime_rates', rates_data).success(function (data) {
            $scope.previous_rate_data = rates_data;
            if (data.status) {
              // $scope.service = null;
              // $scope.package = null;
              // $scope.available_carriers = null;
              if (data.ss_label_data.available_carriers.length) {
                $scope.available_carriers = data.ss_label_data.available_carriers;
                myscope.set_rate(false);
              }
            }
            $scope.rate_loading = false;
            notification.notify('Rates Updated', 1);
            if (print_label) { myscope.print_label() };
          });
        } else {
          $scope.service = null;
          $scope.package = null;
          $scope.rate_loading = false;
          notification.notify(valid_data_for_rates[0], 0);
        }
        $scope.update_rate = false;
        // $scope.selected_package = $scope.package;
        // $scope.label_rate = false;
        // $scope.service_packages_new = []
        // var valid_data_for_rates = myscope.label_rates_data_valid();
        // if (valid_data_for_rates == true) {
        //   var rates_data = myscope.get_rates_data();
        //   $http.post($rootScope.api_url + '/orders/get_rates', rates_data).success(function (data) {
        //       if (data.status) {
        //         if (data.rates.length) {
        //           package_names = data.rates.map(function(data) {return	{ name: data.serviceName.replace($scope.service.name + ' - ', ''), rate: data.shipmentCost + data.otherCost }});
        //           for(var i=0; i<package_names.length; i++) {
        //             var package_found = $scope.service_packages.find(function(package) { if (package.name === package_names[i].name) { package.rate = parseFloat(package_names[i].rate).toFixed(2); return package; } })
        //             if (package_found != undefined) { $scope.service_packages_new.push(package_found); }
        //           }
        //           $scope.service_packages = $scope.service_packages_new;
        //           $scope.service_packages.sort(function (a, b) {
        //             return a.rate - b.rate;
        //           });
        //           if ($scope.selected_package != null) {
        //             $scope.package = $scope.service_packages.find(function(package) { if (package.name === $scope.selected_package.name) {return package} });
        //           }
        //         } else {
        //           notification.notify('Sorry, there are no available methods for this combination.' ,0)
        //         }
        //       }
        //   });
        // } else {
        //   $scope.service = null;
        //   $scope.package = null;
        //   $scope.available_services = [];
        //   notification.notify(valid_data_for_rates[0], 0);
        // }
      }


      // myscope.show_ss_label_rates_popup = function(ss_label_rate_data) {
      //   var ss_label_rates_modal = $modal.open({
      //     templateUrl: '/assets/views/modals/shipstation_label_rates.html',
      //     controller: 'shipstationlabelrateCtrl',
      //     resolve: {
      //       ss_label_rate_data: function() {return ss_label_rate_data; }
      //     }
      //   });
      // }

      // myscope.setUpServicePackages = function(data) {
      //   var service_names = data.rates.map(function(data) {return	data.serviceCode });
      //   service_names = service_names.filter(function (x, i, a) { return a.indexOf(x) == i; });
      //   for(var i=0; i<service_names.length; i++) {
      //     var service_found = $scope.all_available_services.find(function(service) { if (service.code === service_names[i]) { return service; } })
      //     if (service_found != undefined) {
      //       var package = {};
      //       package.name = 'Package';
      //       package.serviceName = data.rates[i].serviceName;
      //       package.rate = parseFloat(data.rates[i].shipmentCost + data.rates[i].otherCost).toFixed(2);
      //       package.code = 'package';
      //       service_found.packages = [package];
      //       if ($scope.available_services == null) {
      //         $scope.available_services = [];
      //       }
      //       $scope.available_services.push(service_found);
      //     }
      //   }
      //   if ($scope.prev_service != null && $scope.available_services != null) {
      //     $scope.service = $scope.available_services.find(function(service) { if (service.name === $scope.prev_service.name) {return service} });
      //   }
      //   if ($scope.service != null && $scope.package != null) {
      //     $scope.package = $scope.service.packages.find(function(package) { if (package.name === $scope.package.name) {return package} });
      //   }
      // }

      // myscope.setStampsServicePackages = function(data) {
      //   var service_names = data.rates.map(function(data) {return	data.serviceCode });
      //   service_names = service_names.filter(function (x, i, a) { return a.indexOf(x) == i; });
      //   for(var i=0; i<service_names.length; i++) {
      //     var service_found = $scope.all_available_services.find(function(service) { if (service.code === service_names[i]) { return service; } })
      //     if (service_found != undefined) {
      //       service_found.packages = []
      //       package_names = data.rates.map(function(data) { if (service_found.code === data.serviceCode) { return { name: data.serviceName.replace(service_found.name + ' - ', ''), serviceName: data.serviceName, rate: data.shipmentCost + data.otherCost }}});
      //       package_names = package_names.filter(function(e){ return e;});
      //       for(var i=0; i<package_names.length; i++) {
      //         var package_found = $scope.all_available_packages.find(function(package) { if (package.name === package_names[i].name) { return package; } })
      //         if (package_found != undefined) {
      //           package_found = angular.copy(package_found);
      //           package_found.serviceName = package_names[i].serviceName.split(' - ')[0];
      //           package_found.rate = parseFloat(package_names[i].rate).toFixed(2);
      //           service_found.packages.push(package_found);
      //         }
      //       }
      //       service_found.packages.sort(function (a, b) { return a.rate - b.rate; });
      //       if ($scope.available_services == null) {
      //         $scope.available_services = [];
      //       }
      //       $scope.available_services.push(service_found);
      //     }
      //   }
      //   if ($scope.prev_service != null && $scope.available_services != null) {
      //     $scope.service = $scope.available_services.find(function(service) { if (service.name === $scope.prev_service.name) {return service} });
      //   }
      //   if ($scope.service != null && $scope.package != null) {
      //     $scope.package = $scope.service.packages.find(function(package) { if (package.name === $scope.package.name) {return package} });
      //   }
      // }

      $scope.show_ss_label_advanced_popup = function() {
        var ss_label_advanced_popup = $modal.open({
          templateUrl: '/assets/views/modals/shipstation_label_advanced.html',
          controller: 'shipstationlabeladvancedCtrl',
          backdrop: 'static',
          keyboard: false,
          resolve: {
            credential_id: function() {return $scope.ss_label_data.credential_id; },
            skip_ss_label_confirmation: function() {return $scope.ss_label_data.skip_ss_label_confirmation; }
          }
        });
        ss_label_advanced_popup.result.then(function(response){
          response = response == true;
          var res = {};
          res['credential_id'] = $scope.ss_label_data.credential_id;
          res['skip_ss_label_confirmation'] = response;
          $scope.ss_label_data.skip_ss_label_confirmation = response;
          $http.post($rootScope.api_url + '/shipstation_rest_credentials/set_ss_label_advanced', res).success(function (data) {});
        });
      }

      var keysPressed = {};

      document.addEventListener('keydown', function(event) {
        if (!$('#ss_label_rates_model').is(":visible")) { return };
        if ($('.ssLabelShortcut').hasClass('in')) { return };
        if ( $('input:focus').length > 0 ) {  return; }
        keysPressed[event.key] = true;
        if (keysPressed['Control']) {
          keysPressed[event.key] = true;
        }
      });

      document.addEventListener('keyup', function(event) {
        if (!$('#ss_label_rates_model').is(":visible")) { return };
        if ($('.ssLabelShortcut').hasClass('in')) { return };
        if ( $('input:focus').length > 0 ) {  return; }
        if (event.key == 'Enter' && $scope.update_rate) { $scope.updateRealtimeRates(); return; };
        if(event.key == 'Escape') { $scope.cancel(); return; };
        try {
          shortcut = Object.keys(keysPressed).reverse().join('+');
          if ($scope.label_shortcuts == undefined) { return; }
          rate_service_name = $scope.label_shortcuts[shortcut];
          if (rate_service_name == 'Print Label' && $scope.rate != null) {
            $scope.ok();
          }
          else if (rate_service_name == 'weight') {
            event.preventDefault();
            $('#ss_label_weight')[0].focus();
            $('#ss_label_weight')[0].select();
          } else if (rate_service_name == 'dimension') {
            event.preventDefault();
            $('#ss_label_length')[0].focus();
            $('#ss_label_length')[0].select();
          } else if (rate_service_name != undefined) {
            all_rates = $scope.available_carriers.map(function(carrier) { return carrier.rates }).flat().filter(function(e){ return e !== undefined;})
            rate = all_rates.find(function(rate) { if (rate.serviceName == rate_service_name) {return rate} });
            if (rate != undefined) { $scope.rate = rate; $scope.ok(); };
          }
        }
        catch (e) {
          console.error(e);
        }
        keysPressed = {};
      });

      $scope.set_label_shortcut = function(rate) {
        if (rate == 'weight') {
          rate = { serviceName: rate }
        } else if (rate == 'Print Label') {
          rate = { serviceName: rate }
        } else if (rate == 'dimension') {
          rate = { serviceName: rate }
        }
        var ss_label_shortcuts_modal = $modal.open({
          templateUrl: '/assets/views/modals/shipstation_label_shortcuts.html',
          controller: 'shipstationlabelshortcutCtrl',
          size: 'small',
          backdrop: 'static',
          keyboard: false,
          windowClass : "ssLabelShortcut",
          resolve: {
            rate: function() {return rate; }
          }
        });
        ss_label_shortcuts_modal.result.then(function(res){
          res['credential_id'] = $scope.ss_label_data.credential_id;
          $http.post($rootScope.api_url + '/shipstation_rest_credentials/set_label_shortcut', res).success(function (data) {
            $scope.label_shortcuts = data.label_shortcuts;
          });
        });
      }

      $scope.get_rate_shortcut = function(serviceName) {
        var shortcut_key = '';
        if ($scope.label_shortcuts == undefined) { return; }
        Object.keys($scope.label_shortcuts).some(function(k) {
          if ($scope.label_shortcuts[k] === serviceName) { shortcut_key = k };
        });
        return shortcut_key.replace('Control', 'Ctrl');
      }

      $scope.toggle_carrier_visibility = function(carrier) {
        var res = {};
        $scope.toggled_carrier = carrier;
        res['credential_id'] = $scope.ss_label_data.credential_id;
        res['carrier_code'] = carrier.code;
        $http.post($rootScope.api_url + '/shipstation_rest_credentials/set_carrier_visibility', res).success(function (data) {
          $scope.toggled_carrier.visible = !$scope.toggled_carrier.visible;
          if ($scope.toggled_carrier.visible) { $scope.update_rate = true; };
        });
      }

      $scope.toggle_rate_visibility = function(carrier, type) {
        carrier.edit_rates = true;
      }

      $scope.save_rate_visibility = function(carrier, type) {
        carrier.edit_rates = false;
        all_rates = carrier.rates.map(function (rate) { if (rate.visible == false) { return rate }}).flat().filter(function(e){ return e !== undefined;})
        disable_rates = all_rates.map(function (rate) { return rate.serviceName});
        var res = {};
        res['credential_id'] = $scope.ss_label_data.credential_id;
        res['disable_rates'] = {};
        res['disable_rates'][carrier.code] = disable_rates;
        $http.post($rootScope.api_url + '/shipstation_rest_credentials/set_rate_visibility', res).success(function (data) {
        });
      }

      $scope.save_ss_label_data = function(flag) {
        if (JSON.stringify($scope.shipping_address) == JSON.stringify($scope.current_shipping_address) && JSON.stringify($scope.weight_dimensions) == JSON.stringify($scope.current_weight_dimensions)) {return; }
        res_body = {}
        res_body['order_number'] = $scope.order_number;
        res_body['ss_label_data'] = myscope.current_ss_label_data();

        $http.post($rootScope.api_url + '/orders/update_ss_label_order_data', res_body).success(function (data) {
          if (data.status) {
            var shall_update_rate = myscope.should_update_rate();
            if (shall_update_rate) {
              $scope.update_rate = true;
            }
            $scope.toName = $scope.shipping_address.name;
            $scope.toAddress1 = $scope.shipping_address.address1;
            $scope.toAddress2 = $scope.shipping_address.address2;
            $scope.toState = $scope.shipping_address.state;
            $scope.toCity = $scope.shipping_address.city;
            $scope.toPostalCode = $scope.shipping_address.postal_code;
            $scope.toCountry = $scope.shipping_address.country;
            $scope.weight = $scope.weight_dimensions.weight.value;
            $scope.weight_unit = $scope.weight_dimensions.weight.units;
            $scope.length = $scope.weight_dimensions.dimensions.length;
            $scope.width = $scope.weight_dimensions.dimensions.width;
            $scope.height = $scope.weight_dimensions.dimensions.height;
            $scope.length_unit = $scope.weight_dimensions.dimensions.units;
            myscope.reset_ss_label_address_dimensions();
            if (shall_update_rate) {
              if(flag == 'get_rate') { $scope.updateRealtimeRates(); }
            }
          }
        });
      }

      myscope.reset_ss_label_address_dimensions = function() {
        $scope.current_shipping_address = angular.copy($scope.shipping_address);
        $scope.current_weight_dimensions = angular.copy($scope.weight_dimensions);
      }

      myscope.current_ss_label_data = function() {
        return {
          shipping_address: angular.copy($scope.shipping_address),
          dimensions: angular.copy($scope.weight_dimensions.dimensions),
          weight: angular.copy($scope.weight_dimensions.weight)
        };
      }

      myscope.should_update_rate = function() {
        return $scope.shipping_address.country != $scope.toCountry || $scope.shipping_address.state != $scope.toState || $scope.shipping_address.postal_code != $scope.toPostalCode || $scope.weight != $scope.weight_dimensions.weight.value || $scope.weight_unit != $scope.weight_dimensions.weight.units || $scope.length != $scope.weight_dimensions.dimensions.length || $scope.width != $scope.weight_dimensions.dimensions.width || $scope.height != $scope.weight_dimensions.dimensions.height || $scope.length_unit != $scope.weight_dimensions.dimensions.units;
      }

      $scope.change_weight = function(weight) {
        var should_fetch_rates = $scope.weight != weight
        $('#ss_label_weight').blur();
        $scope.change_value('weight', weight);
        if (should_fetch_rates) {
          $scope.getRealtimeRate(false);
        }
      }

      myscope.set_rate = function(init) {
        if (init) {
          if ($scope.carrier != null && $scope.service != null && $scope.package != null) {
            $scope.rate = $scope.carrier.rates.filter(function(rate) { if (rate.visible) return rate }).find(function(rate) { if (rate.packageCode !=null && rate.packageCode == $scope.package.code && rate.serviceCode == $scope.service.code) { return rate } });
            if ($scope.rate == undefined && $scope.package.code == 'thick_envelope' && $scope.carrier.code == 'stamps_com') {
              $scope.package = $scope.carrier.packages.find(function(package) { if (package.code == 'package') { return package; }});
              $scope.rate = $scope.carrier.rates.filter(function(rate) { if (rate.visible) return rate }).find(function(rate) { if (rate.packageCode !=null && rate.packageCode == $scope.package.code && rate.serviceCode == $scope.service.code) { return rate } });
              if ($scope.rate) { $scope.rate.serviceName += '/Thick Envelope';}
            }
          }
        } else {
          if ($scope.rate != null && $scope.rate != undefined) {
            var prev_rate = $scope.rate;
            if ($scope.carrier != null) {
              $scope.carrier = $scope.available_carriers.find(function(carrier) { if (carrier.name === $scope.carrier.name) { return carrier} });
            }
            $scope.rate = $scope.carrier.rates.filter(function(rate) { if (rate.visible) return rate }).find(function(rate) { if (rate.packageCode !=null && rate.packageCode == prev_rate.packageCode && rate.serviceCode == prev_rate.serviceCode) { return rate } });
          }
          if ($scope.rate == undefined) { $scope.rate = null; }
        }
      }

      myscope.init();
  }]);
