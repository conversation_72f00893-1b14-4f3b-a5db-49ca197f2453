groovepacks_controllers.
  controller('stripeCheckoutCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance', 'notification', '$rootScope', '$window', 'data',
    function ($scope, $http, $timeout, $filter, $stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, $rootScope, $window, data) {
      var myscope = {};
      
      myscope.init = function () {
        $scope.stripe_checkout_data = data;
        $scope.stripe_checkout_data.plan_name = data.plan_id.replace('GROOV', data.shop_name);
        current_tenant_val = document.getElementById('current_tenant').value;
        frontend_host_val = document.getElementById('frontend_host').value;
        $scope.stripe_checkout_data.shop_name += '.' +  frontend_host_val;
        if ($scope.stripe_checkout_data.plan_name.includes('NaN')) {
          window.location.href = "https://" + current_tenant_val + "." + frontend_host_val + '/#/subscriptions';
        }
        $scope.loading = true;

        var stripe = Stripe(data.stripe_public_key);

        $timeout(function () {
          // Disable the button until we have Stripe set up on the page
          document.querySelector(".pay-button").disabled = true;

          var elements = stripe.elements();

          var style = {
            base: {
              color: "#32325d",
              fontFamily: 'Arial, sans-serif',
              fontSmoothing: "antialiased",
              fontSize: "16px",
              "::placeholder": {
                color: "#32325d"
              }
            },
            invalid: {
              fontFamily: 'Arial, sans-serif',
              color: "#fa755a",
              iconColor: "#fa755a"
            }
          };

          var card = elements.create("card", { style: style });
          // Stripe injects an iframe into the DOM
          card.mount("#card-element");
          $scope.loading = false;
          card.on("change", function (event) {
            // Disable the Pay button if there are no card details in the Element
            document.querySelector(".pay-button").disabled = event.empty;
            document.querySelector("#card-error").textContent = event.error ? event.error.message : "";
          });

          var form = document.getElementById("payment-form");
          form.addEventListener("submit", function (event) {
            event.preventDefault();
            // Complete payment when the submit button is clicked
            createCardToken(stripe, card);
          });

          var createCardToken = function (stripe, card) {
            loading(true);
            stripe
              .createToken(card)
              .then(function (result) {
                if (result.error) {
                  // Show error to your customer
                  showError(result.error.message);
                } else {
                  // The payment succeeded!
                  orderComplete(result.token);
                }
              });
          };

          /* ------- UI helpers ------- */

          // Shows a success message when the payment is complete
          var orderComplete = function (token) {
            loading(false);
            document.querySelector(".result-message").classList.remove("hidden");
            document.querySelector(".pay-button").disabled = true;
            document.querySelector(".stripe-card-element").style.display = 'none';
            setTimeout(function () {
              $modalInstance.close(token);
            }, 1000);
          };

          // Show the customer the error from Stripe if their card fails to charge
          var showError = function (errorMsgText) {
            loading(false);
            var errorMsg = document.querySelector("#card-error");
            errorMsg.textContent = errorMsgText;
            setTimeout(function () {
              errorMsg.textContent = "";
            }, 4000);
          };

          // Show a spinner on payment submission
          var loading = function (isLoading) {
            if (isLoading) {
              // Disable the button and show a spinner
              document.querySelector(".pay-button").disabled = true;
              document.querySelector("#spinner").classList.remove("hidden");
              document.querySelector("#button-text").classList.add("hidden");
            } else {
              document.querySelector(".pay-button").disabled = false;
              document.querySelector("#spinner").classList.add("hidden");
              document.querySelector("#button-text").classList.remove("hidden");
            }
          };
        }, 1000);
      };

      myscope.init();
  }]);
