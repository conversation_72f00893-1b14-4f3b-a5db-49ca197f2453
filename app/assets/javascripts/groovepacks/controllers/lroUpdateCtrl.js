groovepacks_controllers.
  controller('lroUpdateCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', 'store_id', 'store_type', '$rootScope', 'stores', 'importOrders','$window', 'generalsettings',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, store_id, store_type, $rootScope, stores, importOrders, $window, generalsettings) {
      var myscope = {};

      myscope.defaults = function (val ) {
        if (val == null) {val = new Date()}
        return {
          start: {
            open: false,
            time: new Date(val)
          },
          end: {
            open: false,
            etime: new Date(val)
          }
        }
      };

      myscope.get_last_modified = function () {
        $http.get($rootScope.api_url + '/order_import_summary/get_last_modified?&store_id=' + $scope.store_id).success(function (data) {
          if (data.last_imported_at) {
            try{
              $scope.last_imported_at = Date.parse(data.last_imported_at);
            }catch(e){
              $scope.last_imported_at = new Date;
            }
            $scope.set_lro = false;
            $scope.store_lro = {};
            try{
              $scope.store_lro.date = new Date($scope.last_imported_at);
            }catch(e){
              $scope.store_lro.date = new Date;
            }
            $scope.store_lro.open = false;
          }
        });
      }

      $scope.get_time_zone = function (date) {
        try{
          new_date = date.replace("T", " ").replace("Z", "");
        }catch(e){
          new_date = date.toString().split("GMT")[0];
        }
        return new_date;
      }

      myscope.init = function () {
        $scope.store_id = store_id;
        $scope.store_type = store_type;
        myscope.get_last_modified();
      };

      $scope.update_lro = function (val) {
        if (val == true) {
          $scope.set_lro = true;
        } else if (val == false) {
          $scope.set_lro = false;
        } else {
          if ($scope.store_type == 'Shippo') {
            var lastDate = new Date(moment($scope.last_imported_at).format("YYYY-MM-DD HH:mm:ss"));
            var selectedDate = new Date(moment(val).format("YYYY-MM-DD HH:mm:ss"));
            var diff = lastDate.getTime() - selectedDate.getTime();
            var differencInDays = diff / (1000 * 60 * 60 * 24);
            if (differencInDays > 15) {
              var alertModal = $modal.open({
                templateUrl: '/assets/views/modals/alert_modal.html',
                controller: 'alertModalCtrl',
                size: 'small',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                  message: function() { return 'Importing orders for a range larger than 15 days may cause instability and system time-outs. If you are missing a specific order you can use the import trouble-shooter to import it directly instead. Do you wish to override this restriction? (not recommended)' }
                }
              });
              alertModal.result.then(function(){
                myscope.update_store_lro()
              });
            }else{
              myscope.update_store_lro()
            }
          }else{
            myscope.update_store_lro()
          }
        }
      };

      myscope.update_store_lro = function() {
        $http.get($rootScope.api_url + '/stores/update_store_lro?lro_date=' + $scope.store_lro.date + '&store_id=' + $scope.store_id).success(function (data) {
          if (data.status) {
            notification.notify("LRO updated successfully", 1);
            $scope.last_imported_at = $scope.store_lro.date;
          } else {
            notification.notify(data.error, 0);
          }
          $scope.set_lro = false;
          $scope.cancel();
        });
      }

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.open_picker = function (event, object) {
        event.preventDefault();
        event.stopPropagation();
        object.open = true;
      };

      myscope.init();
  }]);
