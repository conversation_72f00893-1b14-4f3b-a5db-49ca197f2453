groovepacks_controllers.
  controller('shipstationlabeladvancedCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
    '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', '$rootScope','$window', 'credential_id', 'skip_ss_label_confirmation',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, $rootScope, $window, credential_id, skip_ss_label_confirmation) {
      var myscope = {};
      
      myscope.init = function () {
        $scope.ss_label_advanced_options = {};
        $scope.ss_label_advanced_options['credential_id'] = credential_id;
        $scope.ss_label_advanced_options['skip_ss_label_confirmation'] = skip_ss_label_confirmation;
      };

      $scope.cancel = function () {
        $modalInstance.close($scope.ss_label_advanced_options.skip_ss_label_confirmation);
      };

      myscope.init();
  }]);
