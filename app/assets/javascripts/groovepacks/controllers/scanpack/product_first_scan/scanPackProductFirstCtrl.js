groovepacks_controllers.controller('scanPackProductFirstCtrl', ['$http', '$rootScope', '$scope', '$modal', '$modalInstance', '$timeout', '$sce', '$interval', '$window', 'data', 'scanPack', 'notification', 'scanPack', 'groov_audio', 'orders',
  function ($http, $rootScope, $scope, $modal, $modalInstance, $timeout, $sce, $interval, $window, data, scanPack, notification, scanPack, groov_audio, orders) {
    var myscope = {};

    myscope.init = function () {
      $scope.scan_pack = scanPack.settings.model();
      $scope.scan_pack_state = 'none';

      if (typeof myscope['sounds'] == 'undefined') {
        myscope.sounds = {};
      }
      //$scope.scan_pack_state = 'none';
      scanPack.settings.get($scope.scan_pack).success(function () {
        angular.forEach(['success', 'fail', 'order_complete'], function (i) {
          if ($scope.scan_pack.settings['show_' + i + '_image']) {
            $scope.scan_pack.scan_states[i].image.enabled = $scope.scan_pack.settings['show_' + i + '_image'];
            $scope.scan_pack.scan_states[i].image.src = $scope.scan_pack.settings[i + '_image_src'];
            $scope.scan_pack.scan_states[i].image.time = $scope.scan_pack.settings[i + '_image_time'] * 1000;
          }
          if ($scope.scan_pack.settings['play_' + i + '_sound']) {
            $scope.scan_pack.scan_states[i].sound.enabled = $scope.scan_pack.settings['play_' + i + '_sound'];
            if (typeof myscope.sounds[i] == 'undefined'){
              myscope.sounds[i] = groov_audio.load($scope.scan_pack.settings[i + '_sound_url'], $scope.scan_pack.settings[i + '_sound_vol']);
            }
          }
        });
      });

      if (data.single_item_order) {
        $timeout(function () {
          $modalInstance.close("ok-button-click");
        }, data.single_item_order_message_time * 1000);
      } else if (data.scan_tote_to_completed) {
        $timeout(function () {
          $modalInstance.close("ok-button-click");
          $timeout($scope.focus_search, 500);
        }, data.multi_item_order_message_time * 1000);
        if (data.order_cup_direct_shipping) {
          orders.single.launch_order_cup_direct_shipping_popup(data.order, JSON.parse(localStorage.getItem('general_settings')).data.settings);
        }
      }
      if (data.order_items_unscanned != undefined && data.order_items_scanned != undefined) {
        data.order_qty = {'scanned_qty': 0, 'qty_remaining': 0}
        data.order_items_unscanned.forEach(function(item_data){
          data.order_qty['scanned_qty'] = data.order_qty['scanned_qty'] + item_data.scanned_qty;
          data.order_qty['qty_remaining'] = data.order_qty['qty_remaining'] + item_data.qty_remaining;
        });
        data.order_items_scanned.forEach(function(item_data){
          data.order_qty['scanned_qty'] = data.order_qty['scanned_qty'] + item_data.scanned_qty;
          data.order_qty['qty_remaining'] = data.order_qty['qty_remaining'] + item_data.qty_remaining;
        });
        data.order_items_partial_scanned.forEach(function(item_data){
          data.order_qty['scanned_qty'] = data.order_qty['scanned_qty'] + item_data.scanned_qty;
          data.order_qty['qty_remaining'] = data.order_qty['qty_remaining'] + item_data.qty_remaining;
        });
      }
      if (data.single_item_order_message) {
        data.single_item_order_message = $sce.trustAsHtml(data.single_item_order_message);
      }
      if (data.multi_item_order_message) {
        data.single_item_order_message = $sce.trustAsHtml(data.multi_item_order_message);
      }
      $scope.data = data;
      $timeout($scope.focus_search, 500);
    };

    $scope.ok = function () {
      $modalInstance.close("ok-button-click");
    };
    $scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    myscope.launch_multi_item_order_completed = function (data) {
      myscope.launch_multi_item_order_complete = $modal.open({
        templateUrl: '/assets/views/modals/scanpack/product_first_scan/multi_item_order_complete.html',
        controller: 'scanPackProductFirstCtrl',
        size: 'small',
        resolve: {
          data: function () {
            return data;
          }
        }
      });
    };

    myscope.print_ss_label = function (data) {
      if (data.use_api_create_label) {
        $rootScope.fetching_label_related_data = true;
        scanPack.fetch_label_related_data(data.order.id).then(function (response) {
          $rootScope.fetching_label_related_data = false;
          if (response.data.status) {
            if (response.data.ss_label_data.direct_printed) {
              var settings = {};
              settings = JSON.parse(localStorage.getItem('general_settings'));
              if (settings.direct_printing_options && settings.data.settings.print_ss_shipping_labels) {
                printing_service.print_now({ url: response.data.ss_label_data.direct_printed_url, dimensions: response.data.ss_label_data.dimensions }, 'user_selected_printer_ss_shipping_label_' + settings.data.settings.packing_slip_size);
              } else {
                notification.notify("Label Created", 1);
                $window.open(response.data.ss_label_data.direct_printed_url);
              }
            } else {
              myscope.launch_ss_label_popup(response.data.ss_label_data);
            }
          } else {
            notification.notify(response.data.error, 0);
          }
        })
      }
    }

    myscope.launch_ss_label_popup = function (ss_label_data) {
      var ss_label_modal = $modal.open({
        templateUrl: '/assets/views/modals/shipstation_label.html',
        controller: 'shipstationlabelCtrl',
        size: 'lg',
        backdrop: 'static',
        keyboard: true,
        resolve: {
          ss_label_data: function () { return ss_label_data; }
        }
      });
      ss_label_modal.result.finally(function () {
        $scope.set('input', '');
        $timeout($scope.focus_search, 500);
      });
    }

    myscope.trigger_scan_message = function (type) {
      $scope.scan_pack_state = type;
      if (['success', 'fail', 'order_complete'].indexOf(type) != -1) {
        var object = $scope.scan_pack.scan_states[type];
        if (object.image.enabled) {
          $timeout(function () {
            $scope.scan_pack_state = 'none';
          }, object.image.time);
        }
        if (object.sound.enabled) {
          groov_audio.play(myscope.sounds[type]);
        }
      }
    };

    $scope.confirmClearTote = function(){
      var confirmClearTotePopup = $modal.open({
        templateUrl: 'assets/views/modals/scanpack/product_first_scan/confirm_clear_tote.html',
        controller: 'confirmClearToteCtrl',
        size: 'small',
        backdrop: 'static',
        keyboard: false,
      });
      confirmClearTotePopup.result.then(function () {
        $http.get($rootScope.api_url + '/orders/' + $scope.data.order.id + '/clear_order_tote.json').success(function (data) {
          $scope.cancel();
        }).error(notification.server_error);
      });
    }

    $scope.open_popup = function (url, large_popup) {
      if (large_popup) {
        var popup = $window.open(url, '', "width=" + screen.availWidth + ", height=" + screen.availHeight);
      } else {
        var w = 1240;
        var h = 600;
        var left_adjust = angular.isDefined($window.screenLeft) ? $window.screenLeft : $window.screen.left;
        var top_adjust = angular.isDefined($window.screenTop) ? $window.screenTop : $window.screen.top;
        var width = $window.innerWidth ? $window.innerWidth : $window.document.documentElement.clientWidth ? $window.document.documentElement.clientWidth : $window.screen.width;
        var height = $window.innerHeight ? $window.innerHeight : $window.document.documentElement.clientHeight ? $window.document.documentElement.clientHeight : $window.screen.height;
        var left = ((width / 2) - (w / 2)) + left_adjust;
        var top = ((height / 2) - (h / 2)) + top_adjust;
        var popup = $window.open(url, '', "top=" + top + ", left=" + left + ", width=" + w + ", height=" + h);
      }

      var interval = 1000;
      var i = $interval(function () {
        try {
          if (popup == null || popup.closed) {
            $interval.cancel(i);
            $scope.trigger_scan_message('order_complete');
          }
        } catch (e) {
          console.error(e);
        }
      }, interval);
    };

    $scope.scan_to_tote = function (event, type, tote, order_item_id, tote_barcode, barcode_input) {
      if (event.which != '13') return;
      return $http.post($rootScope.api_url + '/scan_pack/scan_to_tote.json', {type: type, tote: tote, order_item_id: order_item_id, tote_barcode: tote_barcode, barcode_input: barcode_input}).success(function (data) {
        if (data.status) {
          if (data.scan_tote_to_completed) {
            scope.trigger_scan_message('order_complete', true);
            myscope.launch_multi_item_order_completed(data);
            if (data.store_type == 'ShippingEasy' && data.popup_shipping_label == true){
              if(data.order.shipment_id != null){
                var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(data.order.shipment_id) + "/edit");
                $scope.open_popup(shippingeasy_url, data.large_popup);
              } else {
                scanPack.getshipment(data.order.store_id, data.order.store_order_id).success(function (d) {
                  if(d.shipment_id != null){
                    var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(d.shipment_id) + "/edit");
                    $scope.open_popup(shippingeasy_url, data.large_popup);
                  };
                });
              };
            }
          } else {
            scope.trigger_scan_message('success');
          }
          notification.notify(data.success_messages, 1);
          $modalInstance.close("finished");
        } else {
          notification.notify(data.error_messages, 0);
          myscope.trigger_scan_message('fail');
          search_fields = document.querySelectorAll('.search-box')
          search_field = search_fields[search_fields.length - 1]
          if (search_field){
            search_field.value= '';
            search_field.focus();
          }
          var element = angular.element("#pending_tote_input");
          if (element) { $scope.data.tote_input = ''; element.focus(); }
        }
      }).error(function(){
        notification.server_error;
      });
    }

    $scope.assign_user_tote_set = function (event, input) {
      if (event.which != '13') return;
      if (input !=undefined && input != '') {
        scanPack.assign_user_tote_set(input).success( function (data) {
          if (data.status) {
            $scope.current_user.check();
            $scope.data.tote_input = '';
            notification.notify($scope.scan_pack.settings.tote_identifier + ' set successfully assigned.', 1);
            $timeout($scope.focus_search, 500);
            $modalInstance.close("ok-button-click");
          } else {
            $scope.data.tote_input = '';
            $timeout($scope.focus_search, 500);
            notification.notify('Please scan valid ' + $scope.scan_pack.settings.tote_identifier + ' barcode.', 0);
          }
        });
      } else {
        $modalInstance.close("ok-button-click");
      }
    }

    myscope.init();
  }]);
