groovepacks_controllers.
  controller('scanPackCtrl', ['$scope', '$http', '$timeout','groovIO', '$stateParams', '$location', '$state', '$cookies', 'scanPack', 'generalsettings', 'groov_audio', '$window', '$modal', '$sce', '$interval', 'orders', '$filter', 'notification', 'printing_service', 'dashboard',
    function ($scope,$http, $timeout, groovIO, $stateParams, $location, $state, $cookies, scanPack, generalsettings, groov_audio, $window, $modal, $sce, $interval, orders, $filter, notification, printing_service, dashboard) {
      var myscope = {
        gen_setting_loaded: 0,
        scanpack_setting_loaded: 0
      };
      $scope.init = function () {
        if(localStorage.getItem('return_to_order') == "true"){
          $state.go('orders');
        }
        myscope.callback = function () {
          return true;
        };
        if (myscope.check_reload_settings()) {
          var last_scanned_item = myscope.last_scanned_barcode;
          myscope.init();
          myscope.last_scanned_barcode = last_scanned_item;
        }
        myscope.callbacks = {};
        $scope.current_state = $state.current.name;
        if (typeof $scope.data == "undefined") {
          $scope.data = {};
        }
        $scope.data.input = "";
        window.scope = $scope;
        localStorage.setItem('return_to_order', 'nil');
        $scope.is_clicked = false;
      };

      $scope.set = function (key, val) {
        $scope.data[key] = val;
      };

      $scope.trigger_scan_message = function (type, hide_image) {
        if (typeof(hide_image) == 'undefined') {
          $scope.scan_pack_state = type;
        } else if (hide_image == true) {
          $scope.scan_pack_state = 'none';
        }
        if (['success', 'fail', 'order_complete'].indexOf(type) != -1) {
          var object = $scope.scan_pack.scan_states[type];
          if (object.image.enabled) {
            $timeout(function () {
              $scope.scan_pack_state = 'none';
            }, object.image.time);
          }
          if (object.sound.enabled) {
            groov_audio.play(myscope.sounds[type]);
          }
        }
      };

      $scope.show_message = function (type) {
        scope.countDown=3;
        $interval(function(){
          $scope.scan_pack_state = type +'_' +(scope.countDown--);
        },1000, scope.countDown);
        $timeout(function () {
          $scope.scan_pack_state = 'none';
        },3500);
      };

      $scope.get_last_scanned = function () {
        return myscope.last_scanned_barcode;
      };

      $scope.set_last_scanned = function (val) {
        myscope.last_scanned_barcode = val;
      };

      $scope.reg_callback = function (func) {
        if (typeof func == 'function') {
          myscope.callback = func;
        }
      };
      $scope.handle_scan_return = function (data) {
        if ((data.data != "undefined") && (data.data.order!=undefined) && (data.data.order.store_type != undefined)) {
          $scope.store_type = data.data.order.store_type
        }
        $scope.set('raw', data);
        if (typeof data.data != "undefined") {
          if (typeof data.data.order != "undefined") {
            var box = $scope.data.order.box;
            if (typeof(data.data.order.next_item.instruction) != "undefined" && !(data.data.order.next_item.instruction == null) && data.data.order.next_item.instruction != '') {
              data.data.order.next_item_instruction = true;
              data.data.order.next_item.instruction = $sce.trustAsHtml(data.data.order.next_item.instruction);
            }
            $scope.set('order', data.data.order);
            $scope.set('scan_pack_settings', data.data.scan_pack_settings);
            if($scope.data.current_box == undefined && data.data.order.box != undefined){
              $scope.set('current_box', data.data.order.box[0]);
            }else{
              $scope.data.order.box = box;
              $scope.set('box_is_empty', false);
            }
          }
          if (typeof data.data.next_state != "undefined") {
            if ((['scanpack.rfp.verifying', 'scanpack.rfp.no_match'].indexOf($state.current.name) != -1 && data.data.next_state == 'scanpack.rfp.no_match') || ($state.current.name == 'scanpack.rfp.no_tracking_info' && data.data.next_state == 'scanpack.rfp.no_tracking_info')) {
              $scope.trigger_scan_message('fail');
            }
            if ($state.current.name == data.data.next_state) {
              if (data.data.next_state == 'scanpack.rfp.default') {
                $scope.trigger_scan_message((data.status) ? 'success' : 'fail');
                $scope.focus_search();
              }
              $scope.$broadcast('reload-scanpack-state');
            } else {
              if(data.data.next_state == 'scanpack.rfp.recording' && $scope.data.order.store_type == "Shipstation API 2"){
                $scope.ss_label_data();
              }
              if (data.data.order_complete) {
                //destroy empty boxes
                $scope.remove_empty_boxes();

                // Print multi-box packing slips when  order is complete
                if($scope.general_settings.single.multi_box_shipments){
                  var box_ids = [];
                  if($scope.general_settings.single.per_box_packing_slips == 'when_order_is_complete'){
                    angular.forEach($scope.data.order.box, function(value, key){
                      box_ids.push(value.id);
                    });
                  }
                  else if($scope.general_settings.single.per_box_packing_slips == 'when_new_boxes_are_started'){
                    box_ids.push($scope.data.current_box.id);
                  }
                  if(box_ids.length > 0){
                    orders.list.generate_box_slip($scope.data.order, box_ids);
                  }
                }

                // if ($scope.data.order.store_type=="ShippingEasy" && ($scope.data != "undefined") && ($scope.data.order!=undefined) && $scope.data.order.popup_shipping_label==true){
                //   if($scope.data.order.shipment_id != null){
                //     var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt($scope.data.order.shipment_id) + "/edit");
                //     $scope.open_popup(shippingeasy_url);
                //   } else {
                //     scanPack.getshipment($scope.data.order.store_id, $scope.data.order.store_order_id).success(function (d) {
                //       if(d.shipment_id != null){
                //         var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(d.shipment_id) + "/edit");
                //         $scope.open_popup(shippingeasy_url);
                //       };
                //     });
                //   };
                // } else {
                //   $scope.trigger_scan_message('order_complete');
                // }
                $scope.trigger_scan_message('order_complete');
                if ($scope.data.order.store_type == "Shipstation API 2") {
                  $scope.ss_label_data();
                  if( $scope.data.order.use_chrome_extention == true) {
                    $(".content_for_extension").attr("data-switch_back", [$scope.data.order.switch_back_button, $scope.data.order.auto_click_create_label, $scope.data.order.return_to_order]);
                    $(".content_for_extension").text($scope.data.order.increment_id);
                    if ($scope.data.order.switch_back_button && $scope.data.order.return_to_order){
                      localStorage.setItem("return_to_order", true);
                    }
                  }
                }

                // show split next order
                if ($scope.data.order.store_type=="ShippingEasy"){
                  orders.single.next_split_order($scope.data.order).success(function(response){
                    if(response != "null") {
                      $state.go('scanpack.rfp.default', {order_num: response.increment_id, username: response.firstname+' '+response.lastname, order_by_number: true });
                    }
                  });
                }
                // update dashboard data

                if ($scope.general_settings.single.api_call == true) {
                  scanPack.send_api_request($scope.data.order.id)
                }

                if ($scope.general_settings.single.allow_rts == true) {
                  setTimeout(function(){ dashboard.stats.dashboard_stat('0'); }, 5000);
                }
              }
            }

            if($scope.scan_pack.settings.return_to_orders == true && data.data.order_complete ){
              $state.go('orders');
            } else {
              if ((data.data.next_state == "scanpack.rfp.recording" || data.data.order_complete) && $scope.data.order.popup_opened != true && $scope.data.order.store_type=="ShippingEasy" && ($scope.data != "undefined") && ($scope.data.order!=undefined) && $scope.data.order.popup_shipping_label==true) {
                $scope.data.order.popup_opened = true;
                if($scope.data.order.shipment_id != null){
                  $scope.show_complete_order = false;
                  var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt($scope.data.order.shipment_id) + "/edit");
                  $scope.open_popup(shippingeasy_url);
                } else {
                  $scope.show_complete_order = false;
                  scanPack.getshipment($scope.data.order.store_id, $scope.data.order.store_order_id).success(function (d) {
                    if(d.shipment_id != null){
                      var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(d.shipment_id) + "/edit");
                      $scope.open_popup(shippingeasy_url);
                    }
                  });
                };
              }
              if ((data.data.next_state == "scanpack.rfp.recording" || data.data.order_complete) && $scope.data.order.store_type=="CSV" && data.data.order && data.data.order.order_cup_direct_shipping) {
                orders.single.launch_order_cup_direct_shipping_popup(data.data.order, $scope.general_settings.single);
              }

              if ($scope.data.order.store_type =="Shopify" && data.data.order_complete && data.data.order && data.data.order.open_shopify_create_shipping_label) {
                orders.single.launch_shopify_shipping_popup(data.data.order.store_order_id, data.data.order.shopify_store_name);
              }
              //  else if (data.data.order_complete){
              //   $scope.trigger_scan_message('order_complete');
              // }
              if (data.data.next_state == 'scanpack.rfp.default') {data.data.order_by_number = true};
              $state.go(data.data.next_state, data.data);
            }
          }
        }
      };

      $scope.ss_label_data = function(){
        if ($scope.data.order.use_api_create_label == true && $scope.general_settings.single.ss_api_create_label == true) {
          $scope.data_order_box_length = $scope.data.order.box.length;
          $scope.fetching_label_related_data = true;
          scanPack.fetch_label_related_data($scope.data.order.id).then(function(response){
          $scope.fetching_label_related_data = false;
          if (response.data.status) {
              if ($scope.general_settings.single.per_box_shipping_label_creation != 'per_box_shipping_label_creation_after_order' || $scope.data_order_box_length <= 1) {
                if (response.data.ss_label_data.direct_printed) {
                  var settings = {};
                  settings = JSON.parse(localStorage.getItem('general_settings'));
                  if (settings.direct_printing_options && settings.data.settings.print_ss_shipping_labels) {
                    printing_service.print_now({ url: response.data.ss_label_data.direct_printed_url, dimensions: response.data.ss_label_data.dimensions }, 'user_selected_printer_ss_shipping_label_' + settings.data.settings.packing_slip_size);
                  } else {
                    notification.notify("Label Created", 1);
                    $window.open(response.data.ss_label_data.direct_printed_url);
                  }
                } else {
                  myscope.launch_ss_label_popup(response.data.ss_label_data);
                }
              } else if ($scope.general_settings.single.per_box_shipping_label_creation == 'per_box_shipping_label_creation_after_order' && $scope.data_order_box_length > 1) {
                for (var i = 0; i < $scope.data_order_box_length; i++) {
                  myscope.launch_ss_label_popup(response.data.ss_label_data);
                }
              }
            } else {
              notification.notify(response.data.error, 0);
            }
          })
        }
      };

      myscope.launch_product_first_scan_to_put_wall_popups = function (data) {
        myscope.launch_product_first_scan_to_put_wall_popup = $modal.open({
          templateUrl: '/assets/views/modals/scanpack/product_first_scan/' + data.popup_template + '.html',
          controller: 'scanPackProductFirstCtrl',
          size: 'small',
          resolve: {
            data: function () {
              return data;
            }
          }
        });
        myscope.launch_product_first_scan_to_put_wall_popup.result.finally(function () {
          $scope.set('input', '');
          $timeout($scope.focus_search, 500);
        });
      };

      $scope.handle_product_first_scan_to_put_wall = function (data) {
        if (data.single_item_order) {
          data.popup_template = 'single_order_complete';
          myscope.launch_product_first_scan_to_put_wall_popups(data);
          if (data.store_type == 'ShippingEasy' && data.popup_shipping_label == true){
            if(data.order.shipment_id != null){
              var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(data.order.shipment_id) + "/edit");
              $scope.open_popup(shippingeasy_url);
            } else {
              scanPack.getshipment(data.order.store_id, data.order.store_order_id).success(function (d) {
                if(d.shipment_id != null){
                  var shippingeasy_url = $sce.trustAsResourceUrl("http://app.shippingeasy.com/shipments/" + parseInt(d.shipment_id) + "/edit");
                  $scope.open_popup(shippingeasy_url);
                };
              });
            };
          }
          if (data.order_cup_direct_shipping) {
            orders.single.launch_order_cup_direct_shipping_popup(data.order, $scope.general_settings.single);
          }
        } else if (data.scan_tote_to_complete) {
          data.popup_template = 'scan_tote_to_complete';
          myscope.launch_product_first_scan_to_put_wall_popups(data);
          // notification.notify('Scan Tote Number assigned to #' + data.order_number, 2);
        } else if (data.put_in_tote) {
          data.popup_template = 'put_in_tote';
          myscope.launch_product_first_scan_to_put_wall_popups(data);
          // notification.notify('Item ' + data.product + ' assigned to Tote: ' + data.tote + ' for order #' + data.order_number, 2);
        } else if (data.assigned_to_tote) {
          data.popup_template = 'assigned_to_tote';
          myscope.launch_product_first_scan_to_put_wall_popups(data);
          // notification.notify('Scan Tote Number for Tote: ' + data.tote + ' for order #' + data.order_number, 2);
        } else if (data.pending_order) {
          data.popup_template = 'pending_order';
          myscope.launch_product_first_scan_to_put_wall_popups(data);
        }
        $scope.set('input', '');
        $timeout($scope.focus_search, 500);
      };

      groovIO.on('popup_display_for_on_demand_import_v2', function (resp, event) {
        if (scope.current_user_data.id == resp.user_id && typeof($scope.data.order.id) == "undefined"){
          notification.notify(resp.message);
          if ((resp.status == true && $scope.current_state == 'scanpack.rfo')){
            scope.show_message('on_demand');
            $timeout(function () {
              $scope.data.input = resp.id;
              $scope.input_enter({which: 13});
            }, 3700);

          }
        }
      });

      groovIO.on('notification_ondemand_quickfix', function (resp, event) {
        if (scope.current_user_data.id == resp.user_id ){
          notification.notify(resp.message);
        }
      });

      $scope.open_popup = function (url) {
        if ($scope.data.order.large_popup) {
          var popup = $window.open(url, '', "width=" + screen.availWidth + ", height=" + screen.availHeight);
        } else {
          var w = 1240;
          var h = 600;
          var left_adjust = angular.isDefined($window.screenLeft) ? $window.screenLeft : $window.screen.left;
          var top_adjust = angular.isDefined($window.screenTop) ? $window.screenTop : $window.screen.top;
          var width = $window.innerWidth ? $window.innerWidth : $window.document.documentElement.clientWidth ? $window.document.documentElement.clientWidth : $window.screen.width;
          var height = $window.innerHeight ? $window.innerHeight : $window.document.documentElement.clientHeight ? $window.document.documentElement.clientHeight : $window.screen.height;
          var left = ((width / 2) - (w / 2)) + left_adjust;
          var top = ((height / 2) - (h / 2)) + top_adjust;
          var popup = $window.open(url, '', "top=" + top + ", left=" + left + ", width=" + w + ", height=" + h);
        }

        var interval = 1000;
        var i = $interval(function () {
          try {
            if ((popup == null || popup.closed) && $scope.show_complete_order) {
              $interval.cancel(i);
              $scope.trigger_scan_message('order_complete');
            }
          } catch (e) {
            console.error(e);
          }
        }, interval);
      };

      $scope.launch_assign_user_tote_set = function () {
        myscope.launch_assign_user_tote_set = $modal.open({
          templateUrl: '/assets/views/modals/scanpack/assign_user_tote_set.html',
          controller: 'scanPackProductFirstCtrl',
          size: 'small',
          resolve: {
            data: function () {
              return { tote_input: '' };
            }
          }
        });
        myscope.launch_assign_user_tote_set.result.finally(function () {
          $scope.set('input', '');
          $timeout($scope.focus_search, 500);
        });
      };

      $scope.assign_user_tote_set = function (event) {
        if (event.which != '13') return;
        scanPack.assign_user_tote_set($scope.data.input).success( function (data) {
          if (data.status) {
            $scope.current_user.check();
            $scope.set('input', '');
            $timeout($scope.focus_search, 500);
            notification.notify($scope.scan_pack.settings.tote_identifier + ' set successfully assigned.', 1);
          } else {
            notification.notify('Please scan valid ' + $scope.scan_pack.settings.tote_identifier + ' barcode.', 0);
          }
        });
      };

      $scope.input_enter = function (event) {
        if (event.which != '13') return;
        if ($scope.scan_pack.settings.scan_pack_workflow == 'default' || typeof($scope.data.order.id) !== "undefined") {
          if($scope.data.input == "BOXNEXT" || $scope.data.input == "BOXPREV"){
            $scope.change_box_with_input();
          }
          else{
            if ($scope.current_state == 'scanpack.rfp.default' && $scope.scan_pack.settings.post_scan_pause_enabled) {
              window.setTimeout(function() {
                myscope.start_scanning(event);
              }, $scope.scan_pack.settings.post_scan_pause_time*1000);
            } else {
              myscope.start_scanning(event);
            }
          }
        } else if ($scope.scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall' || $scope.scan_pack.settings.scan_pack_workflow == 'multi_put_wall') {
          scanPack.input($scope.data.input, null, null, null, null, null, null).success($scope.handle_product_first_scan_to_put_wall);
        }
      };

      // myscope.start_product_first_scan_to_put_wall_scanning = function(event) {
      //   if ($scope.scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall') {
      //     scanPack.input($scope.data.input, null, null, null, null, null).success($scope.handle_product_first_scan_to_put_wall);
      //   } else if ($scope.scan_pack.settings.scan_pack_workflow == 'default'){
      //     scanPack.input($scope.data.input, null, null, null, null, null).success($scope.handle_scan_return);
      //   }
      // };

      myscope.start_scanning = function(event) {
        if (!myscope.callback()) return;
        var id = null;
        if (typeof $scope.data.order.id !== "undefined") {
          id = $scope.data.order.id;
        }
        // if ($scope.scan_pack.settings.scan_by_hex_number == true && $scope.data.order.id == undefined){
        //   try{
        //     hex = $scope.data.input.split('^')[2];
        //     // if (/^-?[\d.]+(?:e-?\d+)?$/.test(hex)) {
        //       $scope.data.input = String(parseInt(hex,16));
        //     // } else {
        //     //   string = "";
        //     //   for (var i = 0; i < hex.length; i += 2) {
        //     //     string += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
        //     //   }
        //     //   $scope.data.input = string;
        //     // }
        //   } catch(e){}
        // }
        if ($scope.current_state == 'scanpack.rfp.default') {
          scanPack.input_scan_happend = true
        }
        if ($scope.data.input!=undefined && $scope.data.input!='') {
          myscope.last_scanned_barcode = $scope.data.input;
        }
        $window.increment_id = $scope.data.order.increment_id;
        try{
          obj = $scope.data.order.unscanned_items;
          angular.forEach(obj, function(value, key) {
            angular.forEach(value.barcodes, function(v, k) {
              if (v.barcode == $scope.data.input){
                scope.qty_remaining = value.qty_remaining;
              } else if(value.child_items != undefined && value.child_items.length > 0){
                angular.forEach(value.child_items, function(value, key) {
                  angular.forEach(value.barcodes, function(v, k) {
                    if (v.barcode.toLowerCase() == $scope.data.input.toLowerCase()){
                      scope.qty_remaining = value.qty_remaining;
                    }
                  });
                });
              }
            });
          });
        }catch(e){
          scope.qty_remaining = null
        }
        if($scope.data.current_box == undefined){
          scanPack.input($scope.data.input, $scope.current_state, id, scope.qty_remaining, null, null, null).success($scope.handle_scan_return);
        }
        else{
          scanPack.input($scope.data.input, $scope.current_state, id, scope.qty_remaining, $scope.data.current_box.id, null, null).success($scope.handle_scan_return);
        }
          // scanPack.update_chrome_tab();
        // var barcodes = [];
        // if (typeof $scope.data.order.next_item != "undefined" ){
        //   var values = $scope.data.order.next_item.barcodes;
        //   angular.forEach(values, function(value) { this.push(value.barcode.toLowerCase()); }, barcodes);
        // };
        // if (barcodes.includes($scope.data.input.toLowerCase()) || ($scope.current_state != 'scanpack.rfp.default')) {
        //   $window.increment_id = $scope.data.order.increment_id;
        //   scanPack.input($scope.data.input, $scope.current_state, id).success($scope.handle_scan_return);
        // } else {
        //   $scope.trigger_scan_message('fail');
        // }
      };

      myscope.check_reload_settings = function () {
        var cur_time = (new Date).getTime();
        return !(((cur_time - myscope.gen_setting_loaded) < 60000) && ((cur_time - myscope.scanpack_setting_loaded) < 60000));
      };

      myscope.init = function () {
        $scope.current_user.check();
        $scope.scan_pack = scanPack.settings.model();
        $scope.general_settings = generalsettings.model.get();
        myscope.last_scanned_barcode = '';
        generalsettings.single.get($scope.general_settings).success(function () {
          myscope.gen_setting_loaded = (new Date).getTime();
        });
        if (typeof myscope['sounds'] == 'undefined') {
          myscope.sounds = {};
        }
        //$scope.scan_pack_state = 'none';
        scanPack.settings.get($scope.scan_pack).success(function () {
          angular.forEach(['success', 'fail', 'order_complete'], function (i) {
            if ($scope.scan_pack.settings['show_' + i + '_image']) {
              $scope.scan_pack.scan_states[i].image.enabled = $scope.scan_pack.settings['show_' + i + '_image'];
              $scope.scan_pack.scan_states[i].image.src = $scope.scan_pack.settings[i + '_image_src'];
              $scope.scan_pack.scan_states[i].image.time = $scope.scan_pack.settings[i + '_image_time'] * 1000;
            }
            if ($scope.scan_pack.settings['play_' + i + '_sound']) {
              $scope.scan_pack.scan_states[i].sound.enabled = $scope.scan_pack.settings['play_' + i + '_sound'];
              if (typeof myscope.sounds[i] == 'undefined'){
                myscope.sounds[i] = groov_audio.load($scope.scan_pack.settings[i + '_sound_url'], $scope.scan_pack.settings[i + '_sound_vol']);
              }
            }
          });
          myscope.scanpack_setting_loaded = (new Date).getTime();
        });
      }

      $scope.show_video = function(){
        var video_modal_popup = $modal.open({
          template: '<iframe width="560" height="315" src="https://www.youtube.com/embed/GAhFG-CPTJ0?rel=0" frameborder="0" allowfullscreen></iframe>',
          windowClass: 'app-modal-video-window'
        });
      };

      $scope.prev_next_box = function(type) {
        var index = $scope.data.order.box.indexOf($scope.data.current_box);
        if(type == 'next'){
          $scope.set('current_box', $scope.data.order.box[index+1]);
        }else{
          $scope.set('current_box', $scope.data.order.box[index-1]);
        }
      }

      $scope.create_box = function () {
        if ($scope.is_clicked == false){
          var box = $scope.data.current_box;
          $scope.is_clicked = true;
          scanPack.create_box($scope.data.order.id, $scope.data.order.box.length + 1).success(function(data){
            $scope.data.order.box.push(data.box);
            $scope.set('current_box', data.box);
            if($scope.general_settings.single.per_box_shipping_label_creation == 'per_box_shipping_label_creation_after_box' && !angular.equals({}, data.ss_label_data)){
              myscope.launch_ss_label_popup(data.ss_label_data);
            }
          });
        }

        if($scope.general_settings.single.multi_box_shipments && $scope.general_settings.single.per_box_packing_slips == 'when_new_boxes_are_started'
           && box != undefined){
          orders.list.generate_box_slip($scope.data.order, [box.id]);
        }
      }

      myscope.launch_ss_label_popup = function(ss_label_data) {
        var ss_label_modal = $modal.open({
          templateUrl: '/assets/views/modals/shipstation_label.html',
          controller: 'shipstationlabelCtrl',
          size: 'lg',
          backdrop: 'static',
          keyboard: true,
          resolve: {
            ss_label_data: function() {return ss_label_data; }
          }
        });
        ss_label_modal.result.finally(function () {
          $scope.set('input', '');
          $timeout($scope.focus_search, 500);
        });
      }

      $scope.change_box_with_input = function(){
        if($scope.data.input == "BOXNEXT"){
          if($scope.data.next_box){
            $scope.prev_next_box('next');
          }else if(!$scope.data.box_is_empty){
            $scope.create_box();
          }
          else{
            notification.notify("Box is empty so can not create next box", 0);
          }
        }
        else{
          if($scope.data.prev_box){
            $scope.prev_next_box('prev');
          }
        }
        $scope.data.input = null;
      }

      $scope.generate_box_packing_slip = function(){
        if($scope.data.current_box == undefined){
          notification.notify("Box is not Present for print", 0);
        }
        else{
          orders.list.generate_box_slip($scope.data.order, [$scope.data.current_box.id]);
        }
      }

      $scope.show_hide_remove_btn = function(type,id){
        var element = document.getElementById('remove_'+id);
        if(type == 'hide'){
          element.style.background = '#fff';
        }
        else{
          element.style.background =  'rgba(13, 12, 12, 0.14)';
        }
      }

      $scope.remove_empty_boxes = function(){
        var box_ids = [];
        angular.forEach($scope.data.order.box, function(value, key){
          if($filter('filter')($scope.data.order.order_item_boxes, {box_id: value.id}).length == 0){
            box_ids.push(value.id);
          }
        });
        if(box_ids.length > 0){
          scanPack.remove_empty_boxes(box_ids,$scope.data.order.id).success(function(data){
            if(data.status){
              var boxes = $scope.data.order.box;
              angular.forEach(boxes, function(value, key){
                for(var i = 0; i < box_ids.length; i++){
                  if(box_ids[i] == value.id){
                    $scope.data.order.box.splice(key, 1);
                  }
                }
              });
            }
          });;
        }
      }
    }]);
