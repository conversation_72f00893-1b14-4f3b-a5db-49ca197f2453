groovepacks_controllers.
  controller('scanPackRfpDefaultCtrl', ['$scope', '$rootScope', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$modal', 'products', 'orders', 'groov_translator', 'scanPack', 'notification', '$window', '$filter',
    function ($scope, $rootScope,$http, $timeout, $stateParams, $location, $state, $cookies, $modal, products, orders, groov_translator, scanPack, notification, $window, $filter) {
      var myscope = {};

      $scope.reset_order = function () {
        if (confirm("Resetting the order will remove all items that have been packed for this order. Are you sure?")){
          scanPack.reset($scope.data.order.id);
        }
      };

      $scope.add_note = function () {
        myscope.note_obj = $modal.open({
          templateUrl: '/assets/views/modals/scanpack/addnote.html',
          controller: 'scanPackRfpAddNote',
          size: 'lg',
          resolve: {
            order_data: function () {
              return $scope.data.order;
            }
          }
        });
        myscope.note_obj.result.finally(function () {
          $timeout($scope.focus_search, 500);
        });
      };



      $scope.autoscan_barcode = function () {
        if ($scope.scan_pack.settings.enable_click_sku) {
          if ($scope.data.order.next_item.click_scan_enabled == "on_with_confirmation") {
            myscope.show_click_scan_confirm();
          } else if ($scope.data.order.next_item.click_scan_enabled == "on") {
            if($scope.general_settings.single.is_multi_box) {
              if ($scope.check_request == true){
                $scope.check_request = false;
                myscope.do_autoscan();
              }
            }else{
              myscope.do_autoscan();
            }
          } else if ($scope.data.order.next_item.click_scan_enabled == "off") {
            notification.notify('Click Scan is disabled for this product');
          }

        } else {
          notification.notify('Click Scan is disabled');
        }
      };

      $scope.remove_from_box = function(id, kit_product_id){
        if($scope.check_remove_box ==  true) {
          $scope.check_remove_box = false;
          scanPack.remove_from_box(id, kit_product_id, $scope.data.current_box.id).success(function(data){
            if(data.status){
              $scope.data.order.unscanned_items = data.unscanned_items;
              $scope.data.order.scanned_items = data.scanned_items;
              $scope.unscanned_count = data.scanning_count.unscanned;
              $scope.scanned_count = data.scanning_count.scanned;
              $scope.data.order.order_item_boxes = data.order_item_boxes;

              if(data.unscanned_items[0].product_type == "individual"){
                $scope.data.order.next_item = data.unscanned_items[0].child_items[0]
              }else{
                $scope.data.order.next_item = data.unscanned_items[0];
              }
              if($filter('filter')($scope.data.order.order_item_boxes, {box_id: $scope.data.current_box.id}).length == 0){
                $scope.set('box_is_empty', true);
              }
            }
            $scope.check_remove_box = true;
          });
        }
      }

      myscope.type_scan = function () {
        if ($scope.scan_pack.settings.type_scan_code_enabled) {
          var barcode_found = false;
          if ($scope.data.order.next_item.barcodes.length) {
            for (var i = 0; i < $scope.data.order.next_item.barcodes.length; i++) {
              if (angular.lowercase(myscope.last_scanned_barcode) == angular.lowercase($scope.data.order.next_item.barcodes[i].barcode) || angular.lowercase($scope.get_last_scanned()) == angular.lowercase($scope.data.order.next_item.barcodes[i].barcode)) {
                barcode_found = true;
              }
            }
          }
          if (barcode_found) {
            if ($scope.data.order.next_item.type_scan_enabled == "on_with_confirmation") {
              myscope.show_type_scan_confirm();
            } else if ($scope.data.order.next_item.type_scan_enabled == "on") {
              myscope.launch_type_scan();
            } else if ($scope.data.order.next_item.type_scan_enabled == "off") {
              notification.notify('Type-In Scan Count is disabled for this product');
            }
          } else {
            notification.notify('To use Type-In Counts please scan the first item, then scan or enter: ' + $scope.scan_pack.settings.type_scan_code + ' You can then enter the quantity of the item.');
          }
        } else {
          notification.notify('Type-In Scan Count is disabled');
        }
        $scope.set('input', '');
      };

      myscope.launch_type_scan = function () {
        myscope.type_scan_obj = $modal.open({
          templateUrl: '/assets/views/modals/scanpack/typescan.html',
          controller: 'scanPackRfpTypeScan',
          size: 'lg',
          resolve: {
            order_data: function () {
              return $scope.data.order;
            },
            confirm: function () {
              return $scope.handle_scan_return;
            },
            scan_happend: function(){
              return myscope.click_scan_happend || scanPack.input_scan_happend;
            },
            current_box: function () {
              return $scope.data.current_box;
            }
          }
        });
        myscope.type_scan_obj.result.finally(function () {
          $scope.set('input', '');
          myscope.click_scan_happend = scanPack.input_scan_happend = false;
          $timeout($scope.focus_search, 500);
        });
      };

      myscope.do_autoscan = function () {
        myscope.click_scan_happend = true;
        myscope.last_scanned_barcode = $scope.data.order.next_item.barcodes[0].barcode;
        $window.increment_id = $scope.data.order.increment_id;
        if($scope.data.current_box == undefined){
          scanPack.click_scan($scope.data.order.next_item.barcodes[0].barcode, $scope.data.order.id, null).success($scope.handle_scan_return);
        }
        else{
          scanPack.click_scan($scope.data.order.next_item.barcodes[0].barcode, $scope.data.order.id, $scope.data.current_box.id).success($scope.handle_scan_return);
        }
      };

      $scope.hide_se_notification = function () {
        $scope.data.order.se_old_shipments = false;
      }

      $scope.delete_shipment_order = function (id) {
        if (confirm("Are you sure?")) {
          order_id = id;
          $http.post($rootScope.api_url + '/orders/delete_orders.json', {"id": id}).success(function (data) {
            if (data.status) {
              index_element = scope.orders.single.se_old_combined_shipments.map(function (obj) { return obj[0]; }).indexOf(order_id);
              if (index_element != undefined) {
                scope.orders.single.se_old_combined_shipments.splice(index_element, 1);
              }
              notification.notify('Order will be deleted soon', 2);
            } else {
              notification.notify(data.error_messages, 0);
            }
          }).error(notification.server_error);
       }
      };

      $scope.get_order_details = function (increment_id) {
        var toState = 'orders.filter.page.single';
        var toParams = {};
        for (var key in $state.params) {
          if (['filter', 'page'].indexOf(key) !== -1) {
            toParams[key] = $state.params[key];
          }
        }
        orders.single.get_id(increment_id).then(function(response) {
          toParams.order_id = response.data;
          var url = $state.href(toState, toParams);
          window.open(url,'_blank');
        });
      };

      $scope.hide_se_info = function (val) {
        $scope.scan_pack.settings.show_expanded_shipments = !val;
        scanPack.settings.update($scope.scan_pack);
      }

      $scope.product_details = function (id) {
        if ($scope.current_user.can('add_edit_products')) {
          var item_modal = $modal.open({
            templateUrl: '/assets/views/modals/product/main.html',
            controller: 'productsSingleModal',
            size: 'lg',
            resolve: {
              product_data: function () {
                return products.model.get()
              },
              load_page: function () {
                return function () {
                  var req = $q.defer();
                  req.reject();
                  return req.promise;
                }
              },
              product_id: function () {
                return id;
              }
            }
          });
          item_modal.result.finally(myscope.check_reload_compute);
        }
      };

      $scope.order_details = function (id) {
        if ($scope.current_user.can('add_edit_orders')) {
          var item_modal = $modal.open({
            templateUrl: '/assets/views/modals/order/main.html',
            controller: 'ordersSingleModal',
            size: 'lg',
            resolve: {
              order_data: function () {
                return orders.model.get()
              },
              load_page: function () {
                return function () {
                  var req = $q.defer();
                  req.reject();
                  return req.promise;
                }
              },
              order_id: function () {
                return id;
              }
            }
          });
          item_modal.result.finally(myscope.check_reload_compute);
        }
      };

      myscope.show_type_scan_confirm = function () {
        if (myscope.type_scan_confirmed_id != $scope.data.order.next_item.product_id) {
          myscope.type_scan_confirm_obj = $modal.open({
            templateUrl: '/assets/views/modals/scanpack/codeconfirm.html',
            controller: 'scanPackRfpCodeConfirm',
            size: 'lg',
            resolve: {
              order_data: function () {
                return $scope.data.order;
              },
              confirm: function () {
                return function () {
                  myscope.type_scan_confirmed_id = $scope.data.order.next_item.product_id;
                  myscope.launch_type_scan();
                }
              }
            }
          });
          myscope.type_scan_confirm_obj.result.finally(function () {
            $timeout($scope.focus_search, 500);
          });
        } else {
          myscope.type_scan_confirmed_id = 0;
        }
      };

      myscope.show_click_scan_confirm = function () {
        if (myscope.click_scan_confirmed_id != $scope.data.order.next_item.product_id) {
          myscope.click_scan_confirm_obj = $modal.open({
            templateUrl: '/assets/views/modals/scanpack/codeconfirm.html',
            controller: 'scanPackRfpCodeConfirm',
            size: 'lg',
            resolve: {
              order_data: function () {
                return $scope.data.order;
              },
              confirm: function () {
                return function () {
                  myscope.click_scan_confirmed_id = $scope.data.order.next_item.product_id;
                  myscope.do_autoscan();
                }
              }
            }
          });
          myscope.click_scan_confirm_obj.result.finally(function () {
            $timeout($scope.focus_search, 500);
          });
        } else {
          myscope.click_scan_confirmed_id = 0;
        }
      };

      myscope.show_order_instructions = function () {
        if (!myscope.order_instruction_confirmed) {
          myscope.order_instruction_obj = $modal.open({
            templateUrl: '/assets/views/modals/scanpack/orderinstructions.html',
            controller: 'scanPackRfpOrderInstructions',
            size: 'lg',
            resolve: {
              order_data: function () {
                return $scope.data.order;
              },
              scan_pack_settings: function () {
                return $scope.scan_pack.settings;
              },
              confirm: function () {
                return function () {
                  myscope.order_instruction_confirmed = true;
                }
              }
            }
          });
          myscope.order_instruction_obj.result.finally(function () {
            $timeout($scope.focus_search, 500);
            $timeout(myscope.show_order_instructions, 100);
          });
        }
      };

      myscope.show_product_instructions = function () {
        if (myscope.product_instruction_confirmed_id != $scope.data.order.next_item.product_id) {
          myscope.product_instruction_obj = $modal.open({
            templateUrl: '/assets/views/modals/scanpack/productinstructions.html',
            controller: 'scanPackRfpProductInstructions',
            size: 'lg',
            resolve: {
              order_data: function () {
                return $scope.data.order;
              },
              confirm: function () {
                return function () {
                  myscope.product_instruction_confirmed_id = $scope.data.order.next_item.product_id;
                }
              }
            }
          });
          myscope.product_instruction_obj.result.finally(function () {
            $timeout($scope.focus_search, 500);
            $timeout(myscope.show_product_instructions, 100);
          });
        }
      };

      myscope.ask_serial = function (serial) {
        serial.is_scan =  true;
        serial.second_serial = false;
        if (serial.ask && serial.ask_2){
          serial.is_scan =  false;
        }

        if($scope.data.current_box == undefined){
          serial.box_id = null;
        }else{
          serial.box_id = $scope.data.current_box.id;
        }
        myscope.serial_obj = $modal.open({
          templateUrl: '/assets/views/modals/scanpack/productserial.html',
          controller: 'scanPackRfpProductSerial',
          size: 'lg',
          resolve: {
            order_data: function () {
              return $scope.data.order;
            },
            serial_data: function () {
              return serial;
            },
            confirm: function () {
              return $scope.handle_scan_return;
            }
          }
        });
        myscope.serial_obj.result.finally(function () {
          if (serial.ask && serial.ask_2){
            serial.second_serial = true;
            serial.is_scan =  true
            myscope.serial_obj = $modal.open({
              templateUrl: '/assets/views/modals/scanpack/productserial.html',
              controller: 'scanPackRfpProductSerial',
              size: 'lg',
              resolve: {
                order_data: function () {
                  return $scope.data.order;
                },
                serial_data: function () {
                  return serial;
                },
                confirm: function () {
                  return $scope.handle_scan_return;
                }
              }
            });
            myscope.serial_obj.result.finally(function () {
              $timeout($scope.focus_search, 500);
            });
          }
          $timeout($scope.focus_search, 500);
        });


      };

      myscope.compute_counts = function () {
        if (!myscope.order_instruction_confirmed &&
          ($scope.general_settings.single.conf_req_on_notes_to_packer === "always" ||
          ($scope.general_settings.single.conf_req_on_notes_to_packer === "optional" &&
          $scope.data.order.note_confirmation)) &&
          ($scope.data.order.notes_toPacker || ($scope.data.order.notes_internal && $scope.scan_pack.settings.show_internal_notes)
          || ($scope.data.order.customer_comments && $scope.scan_pack.settings.show_customer_notes) || ($scope.data.order.tags && $scope.scan_pack.settings.show_tags))) {
          $timeout(myscope.show_order_instructions);
        }
        try {
          var instruction_present = $scope.data.order['next_item'].instruction.toString().length > 0;
        } catch (e) {
          var instruction_present = false;
        }
        if (typeof $scope.data.order['next_item'] !== 'undefined' && ($scope.general_settings.single.conf_code_product_instruction === "always" || ($scope.general_settings.single.conf_code_product_instruction === "optional" && $scope.data.order.next_item.confirmation)) && (myscope.product_instruction_confirmed_id !== $scope.data.order.next_item.product_id) && $scope.data.order['next_item'].instruction != undefined && instruction_present) {
          $timeout(myscope.show_product_instructions);
        }

        $scope.unscanned_count = $scope.data.order.scanning_count.unscanned;
        $scope.scanned_count = $scope.data.order.scanning_count.scanned;
        $scope.item_image_index = 0;

      };

      myscope.handle_known_codes = function () {
        if ($scope.scan_pack.settings.note_from_packer_code_enabled && $scope.data.input == $scope.scan_pack.settings.note_from_packer_code) {
          $scope.add_note();
          myscope.note_obj.result.finally(function () {
            $scope.set('input', '');
          });
          return false;
        } else if ($scope.scan_pack.settings.service_issue_code_enabled && $scope.data.input == $scope.scan_pack.settings.service_issue_code && !myscope.service_issue_message_saved) {
          $scope.add_note();
          notification.notify("Please add a message with the service issue", 2);
          myscope.note_obj.result.finally(function () {
            $scope.set('input', $scope.scan_pack.settings.service_issue_code);
            myscope.service_issue_message_saved = true;
            $scope.input_enter({which: 13});
          });
          return false;
        } else if ($scope.scan_pack.settings.type_scan_code_enabled && $scope.data.input == $scope.scan_pack.settings.type_scan_code) {
          myscope.type_scan();
          return false;
        } else if ($scope.scan_pack.settings.click_scan && $scope.data.input == $scope.scan_pack.settings.click_scan_barcode) {
          $scope.autoscan_barcode();
          return false;
        } else if ($scope.scan_pack.settings.pass_scan && $scope.data.input == $scope.scan_pack.settings.pass_scan_barcode) {
          $scope.autoscan_barcode();
          return false;
        } else if ($scope.scan_pack.settings.restart_code_enabled && $scope.data.input == $scope.scan_pack.settings.restart_code) {
          $scope.reset_order();
        }
        return true;
      };

      myscope.check_reload_compute = function () {
        $scope.rfpinit().then(function () {
          $scope.set('title', "Ready for Product Scan");
          $scope.check_request = true;
          if (typeof $scope.data.raw.data.serial != 'undefined' && ($scope.data.raw.data.serial.ask || $scope.data.raw.data.serial.ask_2)) {
            myscope.ask_serial($scope.data.raw.data.serial);
          } else{
            $timeout(function () {
              if($('.form-control.search-box').length ==0) {
               $('#ready_product_scan').focus();
             }
           }, 1000);
          }

          if ($scope.data.order.status != "awaiting") {
            $scope.set('order', {});
            $scope.rfpinit().then(myscope.compute_counts);
          } else {
            myscope.compute_counts();
          }
          $scope.reg_callback(myscope.handle_known_codes);
        });
      };

      myscope.init = function () {
        myscope.note_obj = null;
        myscope.serial_obj = null;
        myscope.order_instruction_confirmed = false;
        myscope.order_instruction_obj = null;
        myscope.product_instruction_obj = null;
        myscope.product_instruction_confirmed_id = 0;
        myscope.click_scan_confirm_obj = null;
        myscope.click_scan_confirmed_id = 0;
        myscope.type_scan_confirm_obj = null;
        myscope.type_scan_confirmed_id = 0;
        $scope.confirmation_code = "";
        myscope.service_issue_message_saved = false;
        myscope.check_reload_compute();
        $scope.check_request = true;
        $scope.check_remove_box =  true;
        $scope.translations = {
          "labels": {
            "se_old_split_shipments": "",
            "se_old_combined_shipments": "",
            "se_additional_shipments": "",
            "remove_shipment": "",
            "remove_acknowledge_shipment": "",
          }
        };
        groov_translator.translate('orders.modal', $scope.translations);
      };

      $scope.$on('reload-scanpack-state', myscope.check_reload_compute);
      myscope.init();
    }]);
