groovepacks_controllers.
  controller('scanPackRecordingCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', 'orders', 'scanPack', '$rootScope',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $cookies, orders, scanPack, $rootScope) {
      var myscope = {};
      var title;
      myscope.init = function () {
        $scope.rfpinit().then(function () {
          if ($state.current.name == 'scanpack.rfp.no_match' || $state.current.name == 'scanpack.rfp.no_tracking_info' || $state.current.name == 'scanpack.rfp.verifying') {
            title = "Scan Shipping Label for Order ";
          }
          else {
            title = "Scan Tracking Number for Order ";
          }
          if ($stateParams.username == "" || $stateParams.username == undefined){
            $stateParams.username = $rootScope.username;
          }
          $scope.set('title', title + $stateParams.order_num + " : " + $stateParams.username);
          if ($scope.data.order.status != 'awaiting' || $scope.data.order.unscanned_items.length > 0) {
            $state.go('scanpack.rfp.default', {order_num: $stateParams.order_num, order_by_number: true});
          }
        });

      }
      $scope.$on('reload-scanpack-state', myscope.init);
      myscope.init();
    }]);
