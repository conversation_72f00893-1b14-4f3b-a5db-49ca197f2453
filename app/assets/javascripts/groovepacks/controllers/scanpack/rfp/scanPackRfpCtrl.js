groovepacks_controllers.
  controller('scanPackRfpCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$q', 'scanPack', '$sce', '$filter',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $cookies, $q, scanPack, $sce, $filter) {
      /**
       * Checks if call is on direct url and returns promise to support .then in both cases
       */
      $scope.rfpinit = function () {
        $scope.alternate_orders = [];
        $scope.init();
        var result = $q.defer();
        result.promise.then(function () {
          if (typeof $scope.data.raw.data != "undefined"
            && typeof $scope.data.raw.data.matched_orders != "undefined"
            && $scope.data.raw.data.matched_orders.length > 0) {
            $scope.alternate_orders = $scope.data.raw.data.matched_orders;
          }
        });
        if (typeof $scope.data.order != 'undefined' && typeof $scope.data.order.status != 'undefined') {
          result.resolve();
        } else {
          return scanPack.input($stateParams.order_num, 'scanpack.rfo', null, null, null, $stateParams.store_order_id, $stateParams.order_by_number).then(function (data) {
            $scope.set('raw', data.data);
            if (typeof data.data != 'undefined' && typeof data.data.data != 'undefined') {
              if (typeof data.data.data['next_state'] != 'undefined' && data.data.data['next_state'] != $state.current.name) {
                if (typeof(some_variable) !== "undefined" && some_variable !== null){
                  $stateParams.username = data.data.data.order.firstname
                } 
                if (typeof(some_variable) !== "undefined" && some_variable !== null){
                  $stateParams.username = $stateParams.username + " "  + data.data.data.order.lastname
                }
                $state.go(data.data.data['next_state'], {order_num: $stateParams.order_num, username: $stateParams.username});
              }
              if (typeof(data.data.data.order.next_item.instruction) != "undefined" && !(data.data.data.order.next_item.instruction == null) && data.data.data.order.next_item.instruction != '') {
                data.data.data.order.next_item_instruction = true;
                data.data.data.order.next_item.instruction = $sce.trustAsHtml(data.data.data.order.next_item.instruction);
              }
              $scope.set('order', data.data.data.order);
              $scope.set('scan_pack_settings', data.data.data.scan_pack_settings);
              $scope.set('current_box', data.data.data.order.box[0]);
            }
          }).then(result.resolve);
        }
        return result.promise;
      }

      $scope.$watch('data.current_box', function () {
        if($scope.data.current_box != undefined){
          var index = $scope.data.order.box.indexOf($scope.data.current_box)
          
          if(index < $scope.data.order.box.length-1){
            $scope.set('next_box', true);
          }else{
            $scope.set('next_box', false);
          }

          if(index == 0){
            $scope.set('prev_box', false);
          }
          else{
            $scope.set('prev_box', true);
          }
          
          $scope.set('box_is_empty', false);

          if($filter('filter')($scope.data.order.order_item_boxes, {box_id: $scope.data.current_box.id}).length == 0){
            $scope.set('box_is_empty', true);
          }
          else{
            $scope.set('box_is_empty', false);
          }
        }
      });
    }]);
