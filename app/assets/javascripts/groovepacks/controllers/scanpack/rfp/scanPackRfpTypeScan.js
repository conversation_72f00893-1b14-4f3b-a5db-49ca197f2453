groovepacks_controllers.controller('scanPackRfpTypeScan', ['$scope', '$modalInstance', '$timeout', 'order_data', 'confirm', 'scan_happend', 'scanPack', 'notification', 'current_box',
  function ($scope, $modalInstance, $timeout, order_data, confirm, scan_happend, scanPack, notification, current_box) {
    var myscope = {};

    myscope.init = function () {
      $scope.order = order_data;
      $scope.scan_happend = scan_happend;
      $timeout(function () {
        $scope.focus_search().select();
      }, 200);
      $scope.code = {};
      $scope.code.count = 0;
      $scope.current_box = current_box;
      $scope.type_enter = false;
    };

    $scope.check_confirm = function (event) {
      if (event.which != 13) return;
      $scope.update('ok-enter-key');
      event.preventDefault();
    };

    $scope.update = function (reason) {
      if (reason == 'ok-enter-key' && $scope.type_enter == false ) {
        $scope.type_enter = true;
        if (
          typeof($scope.code.count) != 'number' || $scope.code.count < 1 ||
          ($scope.code.count > ($scope.order.next_item.qty_remaining + ($scope.scan_happend ? 1 : 0)))
          ) {
          notification.notify("Wrong count has been entered. Please try again.");
          $modalInstance.dismiss("wrong-count");
        } else {
          scanPack.type_scan($scope.order.id, $scope.order.next_item, ($scope.code.count - ($scope.scan_happend ? 1 : 0)) || 1, $scope.current_box).success(function (data) {
            $scope.code.count = 0;
            $timeout($scope.focus_search, 200);
            if (data.status) {
              $modalInstance.close("finished");
              confirm(data.data);
            }
          });
        }

      }
    };

    $scope.ok = function () {
      $modalInstance.close("ok-button-click");
    };
    $scope.cancel = function () {
      $modalInstance.dismiss("cancel-button-click");
    };

    myscope.init();
  }]);
