groovepacks_controllers.
  controller('alertModalCtrl', ['$scope', '$http', '$timeout', '$stateParams', '$location', '$state', '$cookies', '$modal', '$modalInstance', 'message','importOrders',
    function ($scope, $http, $timeout, $stateParams, $location, $state, $cookies, $modal, $modalInstance, message, importOrders) {
      var myscope = {};
      $scope.type = null;

      myscope.init = function () {
        $scope.message = message;
        $scope.type = message.type;
        $scope.run_by = message.run_by;
        $scope.import_start = message.import_start;
        $scope.import_end = message.import_end;
        $scope.store_type = message.store_type;
      };

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.ok = function () {
        $modalInstance.close(true);
      };

      $scope.one_day = function (store) {      
        var order_import_type = "range_import";
      
        var startDate = moment().subtract(1, 'days');
        var endDate = moment()

        if ($scope.store_type == 'Shipstation API 2') {
          importOrders.issue_import_for_ss(store, 0, order_import_type, null, startDate.format("YYYY-MM-DD HH:mm:ss"), endDate.format("YYYY-MM-DD HH:mm:ss"), 'modified', null );
        }
      
        $scope.cancel();
        $state.go('home');
      };
            
      myscope.init();
    }]);
