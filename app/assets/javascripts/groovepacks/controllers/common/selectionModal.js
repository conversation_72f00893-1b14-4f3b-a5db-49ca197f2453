groovepacks_controllers.
  controller('selectionModal', ['$scope', 'selected_data', 'selected_table_options', '$modalInstance', '$timeout',
    function (scope, selected_data, selected_table_options, $modalInstance, $timeout) {
      var myscope = {};


      /**
       * Public methods
       */

      scope.ok = function () {
        $modalInstance.close("ok-button-click");
      };
      scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      myscope.init = function () {
        scope.selected = selected_data;

        scope.gridOptions = selected_table_options;

        scope.$watch('selected', function () {
          if (scope.selected.length == 0) {
            scope.ok();
          }
        }, true);
        $timeout(function () {
          selector_icons = $('.fa-bullseye');
          for (i = 0; i < selector_icons.length; ++i) {
            if ($('.fa-bullseye')[i].closest('.grid-simple-text') != null) {
              $('.fa-bullseye')[i].closest('.grid-simple-text').style.minWidth = '0px';
            }
          }
          tables  = document.querySelectorAll('.table-well');
          for (i = 0; i < tables.length; ++i) {
            if ($('.fa-bullseye')[0].closest('td') != null) {
              $(tables[i]).find('th').eq($('.fa-bullseye')[0].closest('td').cellIndex)[0].style.width = '1%';
            }
          }
        }, 1000);
      };
      myscope.init();
    }]);
