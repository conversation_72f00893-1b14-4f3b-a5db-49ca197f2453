groovepacks_controllers.
  controller('missingorderCtrl', ['$scope', '$http', '$timeout', '$filter','$stateParams', '$location', '$state', '$modal',
   '$modalStack', '$previousState', '$cookies', '$modalInstance',  'notification', 'settings_data', 'send_store_type','send_create_at', 'send_modify_at','send_order_status', 'send_tag_order', 'send_backend_order', 'send_order_id', 'send_order_no', 'send_range_start_date', 'send_range_end_date', 'send_range_created_start_date', 'send_range_created_end_date', '$rootScope', 'stores', 'importOrders','$window', 'generalsettings', 'send_ss_similar_order_found',
    function ($scope, $http, $timeout, $filter ,$stateParams, $location, $state, $modal, $modalStack, $previousState, $cookies, $modalInstance, notification, settings_data, send_store_type, send_create_at, send_modify_at, send_order_status, send_tag_order, send_backend_order, send_order_id,send_order_no, send_range_start_date, send_range_end_date, send_range_created_start_date, send_range_created_end_date, $rootScope, stores, importOrders, $window, generalsettings, send_ss_similar_order_found) {
      var myscope = {};

      myscope.defaults = function (val ) {
        if (val == null) {val = $scope.get_current_time_in_zone()}
        return {
          start: {
            open: false,
            time: new Date(val)
          },
          end: {
            open: false,
            etime: new Date(val)
          }
        }
      };

      $scope.getStoreNameForStatus = function (storeType) {
        switch (storeType) {
          case 'ShippingEasy':
            return 'ShippingEasy';
          case 'Shopify':
            return 'Shopify';
          case 'Shopline':
            return 'Shopline';
          case 'Shipstation API 2':
            return 'Shipstation';
          case 'Veeqo':
            return 'Veeqo';
          default:
            return 'Shippo';
        }
      };

      myscope.get_last_modified = function () {
        $http.get($rootScope.api_url + '/order_import_summary/get_last_modified?&store_id=' + $scope.store_id).success(function (data) {
          if (data.last_imported_at) {
            $scope.last_imported_at = Date.parse($scope.get_time_zone(data.last_imported_at));
            $scope.current_time = Date.parse($scope.get_time_zone(data.current_time));
            $scope.set_lro = false;
            $scope.store_lro = {};
            $scope.store_lro.date = new Date($scope.last_imported_at);
            $scope.store_lro.open = false;
          }
        });
      }

      $scope.import_same_order_no = function(same_order_found, search_order, store_id) {
        if (search_order != "") {
          $http.get($rootScope.api_url + '/stores/get_order_details?order_no=' + encodeURIComponent(search_order)  + '&store_id=' + store_id + '&same_order_found=' + same_order_found).success(function (data) {
            if (data.status) {
              notification.notify("Importing Similar Orders..", 1);
            }
          });
        }
      };

      $scope.get_time_zone = function (date) {
        try{
          new_date = date.replace("T", " ").replace("Z", "");
        }catch(e){
          new_date = date.toString().split("GMT")[0];
        }
        return new_date;
      }

      $scope.get_current_time_in_zone = function () {
        var date = new Date();
        var dst_offset = 0;
        var pst_dst_offset = 0;
        if ($scope.dst_status == true) {
          dst_offset = 60; // Add 1 hour if DST
        }
        if ($scope.pst_dst_status == true) {
          pst_dst_offset = 60; // Add 1 hour if DST
        }
        if ($scope.import_type == 'created') {
          date.setMinutes(date.getMinutes() + date.getTimezoneOffset() + Math.round($scope.gp_timezone/60) + dst_offset);
        } else {
          date.setMinutes(date.getMinutes() + date.getTimezoneOffset() + (-480) + pst_dst_offset); //PST Offset = -480
        }
        var tz_date = new Date(date);
        return tz_date;
      }

      myscope.init = function () {
        $scope.user_changed_time = false;
        $scope.general_settings = generalsettings.model.get();
        generalsettings.single.get($scope.general_settings).success(function(data){
          $scope.gp_timezone = data.data.settings.time_zone;
          $scope.gp_tz_name = data.time_zone_name;
          $scope.dst_status = data.gp_tz_dst;
          $scope.pst_dst_status = data.pst_tz_dst;

          $scope.store_id =  settings_data;
          $scope.store_type = send_store_type;
          $scope.start_date = myscope.defaults(send_range_start_date);
          $scope.end_date = myscope.defaults(send_range_end_date);
          $scope.created_at = send_create_at;
          $scope.modifyed_at = send_modify_at;
          $scope.quickfix_import = send_modify_at;
          $scope.range_modify_start_date = new Date(send_range_start_date);
          $scope.range_modify_end_date = new Date(send_range_end_date);
          $scope.range_created_start_date = new Date(send_range_created_start_date);
          $scope.range_created_end_date = new Date(send_range_created_end_date);
          $scope.order_status = send_order_status;
          $scope.gp_ready_status = send_tag_order;
          $scope.order_id = send_order_id;
          $scope.ss_similar_order_found = send_ss_similar_order_found;
          $scope.gp_order = send_backend_order;
          $scope.order_show = send_order_no;
          $scope.import_type = "modified";
          if (send_create_at== null) {
            $scope.show_order = true;
            $scope.show_search = true;
            $scope.import_range = 'last_24hr';
            $scope.set_import_range('last_24hr');
            $timeout( function(){
              angular.element('[ng-model="stores.order_no"]').focus();
            }, 500 );
          } else {
            $scope.created_at = moment.utc(send_create_at).format("YYYY-MM-DD HH:mm:ss A");
            $scope.modifyed_at = moment.utc(send_modify_at).format("YYYY-MM-DD HH:mm:ss A");
            $scope.quickfix_import = send_modify_at;
            $scope.show_order = false;
            $scope.show_search = false;
            $scope.import_range = 'quickfix'
          }

          myscope.get_last_modified();
          $scope.stores = stores.model.get();
          stores.single.get($scope.store_id, $scope.stores)
        });
      };

      $scope.update_lro = function (val) {
        if (val == true) {
          $scope.set_lro = true;
        } else if (val == false) {
          $scope.set_lro = false;
        } else {
          if ($scope.stores.single.store_type == 'Shipstation API 2') {
            try {
              var lastDate = new Date(moment($scope.stores.single.quick_import_last_modified_v2).format("YYYY-MM-DD HH:mm:ss"));
            } catch(e) {
              var lastDate = new Date(moment().format("YYYY-MM-DD HH:mm:ss"));
            }
            var selectedDate = new Date(moment(val).format("YYYY-MM-DD HH:mm:ss"));
            var diff = lastDate.getTime() - selectedDate.getTime();
            var differencInDays = diff / (1000 * 60 * 60 * 24);
            if (differencInDays > 15) {
              var alertModal = $modal.open({
                templateUrl: '/assets/views/modals/alert_modal.html',
                controller: 'alertModalCtrl',
                size: 'small',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                  message: function() { return 'Importing orders for a range larger than 15 days may cause instability and system time-outs. If you are missing a specific order you can use the import trouble-shooter to import it directly instead. Do you wish to override this restriction? (not recommended)' }
                }
              });
              alertModal.result.then(function(){
                myscope.update_store_lro()
              });
            }else{
              myscope.update_store_lro()
            }
          }else{
            myscope.update_store_lro()
          }
        }
      };

      myscope.update_store_lro = function() {
        $http.get($rootScope.api_url + '/stores/update_store_lro?lro_date=' + $scope.store_lro.date + '&store_id=' + $scope.store_id).success(function (data) {
          if (data.status) {
            notification.notify("LRO updated successfully", 1);
            $scope.last_imported_at = $scope.store_lro.date;
          } else {
            notification.notify(data.error, 0);
          }
          $scope.set_lro = false;
        });
      }

      $scope.cancel = function () {
        $modalInstance.dismiss("cancel-button-click");
      };

      $scope.ok = function (order_no, store_id, event) {
        if (event.which != '13') return;
        // order_no = order_no.replaceAll('#', '');
        if (order_no != "") {
          $http.get($rootScope.api_url + '/stores/get_order_details?order_no=' + encodeURIComponent(order_no)  + '&store_id=' + encodeURIComponent(store_id)).success(function (data) {
              if (data.status) {
                myscope.test(data);
              } else {
                  $scope.created_at = null;
                  $scope.modifyed_at = null;
                  $scope.order_status = null;
                  $scope.gp_ready_status = null;
                  $scope.order_id = null;
                  $scope.gp_order = null;
                  $scope.ss_similar_order_found = null;
                  $scope.start_date = myscope.defaults();
                  $scope.end_date = myscope.defaults();
                notification.notify("Order is not found!", 0);
              }
          });
          $scope.order_show = $scope.stores.order_no;
          $scope.stores.order_no = null;
        }
      };


      $scope.open_picker = function (event, object) {
        event.preventDefault();
        event.stopPropagation();
        object.open = true;
      };

      myscope.test = function (data) {
        $scope.user_changed_time = false;
        $scope.general_settings = generalsettings.model.get();
        generalsettings.single.get($scope.general_settings).success(function(setting_data){
          $scope.gp_timezone = setting_data.data.settings.time_zone;
          $scope.gp_tz_name = setting_data.time_zone_name;
          $scope.dst_status = data.gp_tz_dst;
          $scope.pst_dst_status = data.pst_tz_dst;

          $scope.created_at = moment.utc(data.createDate).format("YYYY-MM-DD HH:mm:ss A");
          $scope.modifyed_at = moment.utc(data.modifyDate).format("YYYY-MM-DD HH:mm:ss A");
          $scope.order_status = data.orderStatus;
          $scope.quickfix_import = data.modifyDate;
          $scope.gp_ready_status = data.gp_ready_status;
          $scope.start_date.start.time = new Date(data.range_start_date);
          $scope.end_date.end.etime = new Date(data.range_end_date);
          $scope.range_modify_start_date = new Date(data.range_start_date);
          $scope.range_modify_end_date = new Date(data.range_end_date);
          $scope.range_created_start_date = new Date(data.range_created_start_date);
          $scope.range_created_end_date = new Date(data.range_created_end_date);
          $scope.gp_order = data.gp_order_found;
          $scope.ss_similar_order_found = data.ss_similar_order_found;
          $scope.order_id = data.id;
          $scope.show_order = false;
          $scope.show_search = false;
          $scope.import_range = 'quickfix';
        });
      };

      $scope.send_data = function (auto) {
        stores.single.update($scope.stores, auto);
      }

      $scope.run_quick_fix_import = function (store, order_import_type) {
        if ($scope.stores.single.enabled_status == false) {
          notification.notify("A status import switch must be enabled before running Range or QuickFix imports.", 0);
        } else {
          if (order_import_type == 'quickfix') {
            importOrders.issue_import_for_ss(store, 0, order_import_type, $scope.quickfix_import, null , null, null, $scope.order_show);
            $scope.cancel();
            $state.go('home');
          } else {
            // Range Import
            if ($scope.stores.single.store_type == 'Shipstation API 2') {
              var startDate = new Date(moment($scope.start_date.start.time).format("YYYY-MM-DD HH:mm:ss"));
              var endDate = new Date(moment($scope.end_date.end.etime).format("YYYY-MM-DD HH:mm:ss"));
              var differenceInMilliseconds = endDate.getTime() - startDate.getTime();
              var differenceInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);
              if (differenceInDays > 90) {
                var alertModal = $modal.open({
                  templateUrl: '/assets/views/modals/alert_modal.html',
                  controller: 'alertModalCtrl',
                  size: 'small',
                  backdrop: 'static',
                  keyboard: false,
                  resolve: {
                    message: function() { return 'The range you have selected is very large. The import will take quite a while and may fail to complete. Please reduce the range if possible. If you are looking to import a few specific orders you can provide the order number above to import them directly.' }
                  }
                });
                alertModal.result.then(function(res){
                  importOrders.issue_import_for_ss(store, 0, order_import_type, null , moment($scope.start_date.start.time).format("YYYY-MM-DD HH:mm:ss"), moment($scope.end_date.end.etime).format("YYYY-MM-DD HH:mm:ss"), $scope.import_type, null);
                  $scope.cancel();
                  $state.go('home');
                });
              } else {
                importOrders.issue_import_for_ss(store, 0, order_import_type, null , moment($scope.start_date.start.time).format("YYYY-MM-DD HH:mm:ss"), moment($scope.end_date.end.etime).format("YYYY-MM-DD HH:mm:ss"), $scope.import_type, null);
                $scope.cancel();
                $state.go('home');
              }
            }
            else {
              importOrders.issue_import_for_ss(store, 0, order_import_type, null , moment($scope.start_date.start.time).format("YYYY-MM-DD HH:mm:ss"), moment($scope.end_date.end.etime).format("YYYY-MM-DD HH:mm:ss"), $scope.import_type, null);
              $scope.cancel();
              $state.go('home');
            }
          }
        }
      }

      $scope.clear_details = function () {
        $scope.user_changed_time = false;
        $scope.created_at = null;
        $scope.modifyed_at = null;
        $scope.order_status = null;
        $scope.gp_ready_status = null;
        $scope.order_id = null;
        $scope.gp_order = null;
        $scope.ss_similar_order_found = null;
        $scope.set_created_at = myscope.defaults();
        $scope.set_modified_at = myscope.defaults();
        $scope.order_show = null
        $scope.show_order = true;
        $scope.show_search = true;
        $scope.range_modify_start_date = null;
        $scope.range_modify_end_date = null;
        $scope.range_created_start_date = null;
        $scope.range_created_end_date = null;
        $scope.import_range = 'last_24hr';
        $scope.set_import_range('last_24hr');
        $timeout( function(){
          angular.element('[ng-model="stores.order_no"]').focus();
        }, 500 );
      }

      $scope.change_import_type = function(import_type) {
        if ($scope.user_changed_time == true) {
          return;
        }
        $scope.import_type = import_type;
        if ($scope.show_order == true) {
          $scope.import_range = 'last_24hr';
          $scope.set_import_range('last_24hr');
          $timeout( function(){
            angular.element('[ng-model="stores.order_no"]').focus();
          }, 500 );
        } else {
          $scope.import_range = 'quickfix';
          if (import_type == "modified") {
            $scope.start_date.start.time = $scope.range_modify_start_date;
            $scope.end_date.end.etime = $scope.range_modify_end_date;
          } else if (import_type == "created") {
            $scope.start_date.start.time = $scope.range_created_start_date;
            $scope.end_date.end.etime = $scope.range_created_end_date;
          }
          if ($scope.start_date.start.time == undefined || $scope.end_date.end.etime == undefined) {
            $scope.start_date = myscope.defaults();
            $scope.end_date = myscope.defaults();
          }
        }
      };

      $scope.set_hours = function(val){
        var tz_date = $scope.get_current_time_in_zone();

        tz_date.setHours(tz_date.getHours() - val);
        $scope.start_date.start.time = tz_date;
      }

      $scope.time_changed = function(){
        $scope.user_changed_time = true;
      }

      $scope.set_days = function(val){
        var tz_date = $scope.get_current_time_in_zone();

        tz_date.setDate(tz_date.getDate() - val);
        $scope.start_date.start.time = tz_date;
      }

      $scope.set_qf_hours = function(val){
        qfDate = new Date($scope.quickfix_import);
        qfDate.setHours(qfDate.getHours() - val);
        $scope.start_date.start.time = qfDate;
        qfDate1 = new Date($scope.quickfix_import);
        qfDate1.setHours(qfDate1.getHours() + val);
        $scope.end_date.end.etime = qfDate1;
      }

      $scope.set_import_range =function(val){
        var tz_date = $scope.get_current_time_in_zone();

        $scope.end_date.end.etime = tz_date;
        switch(val) {
          case 'last_2hr':
            $scope.set_hours(2);
            break;
          case 'last_6hr':
            $scope.set_hours(6);
            break;
          case 'last_12hr':
            $scope.set_hours(12);
            break;
          case 'last_24hr':
            $scope.set_hours(24);
            break;
          case 'last_2days':
            $scope.set_days(2);
            break;
          case 'last_3days':
            $scope.set_days(3);
            break;
          case 'last_4days':
            $scope.set_days(4);
            break;
          case 'quickfix':
            qfDate = new Date($scope.quickfix_import);
            $scope.start_date.start.time = qfDate;
            $scope.end_date.end.etime = qfDate;
            break;
          case 'quickfix+2hr':
            $scope.set_qf_hours(2/2);
            break;
          case 'quickfix+6hr':
            $scope.set_qf_hours(6/2);
            break;
          case 'quickfix+12hr':
            $scope.set_qf_hours(12/2);
            break;
          case 'quickfix+24hr':
            $scope.set_qf_hours(24/2);
        }
        $scope.import_range = val;
      }

      myscope.init();
  }]);
