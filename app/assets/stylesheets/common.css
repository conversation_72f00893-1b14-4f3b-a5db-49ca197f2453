/**
 *= require lib
 */
.alert-acknowledge {
  background-color: #3918D3;
  border-color: #3918D3;
  color: #FFFFFF;
}

.alert-note {
  background-color: #eae672;
  color: #333;
  box-shadow: 0 10px 10px 2px rgba(0, 0, 0, 0.1);
  margin-right: 8px;
}

.confirmation-notes {
  position: absolute;
  top: 26px;
  left: 220px;
  z-index: 1;
  right: 20px;
}

.col-box-primary {
  padding: 10px;
  background: #73AE0C;
  color: #ffffff;
  border: 1px solid #dddddd;
  height: 100px;
  font-size: 18px;
  font-family: Play, sans-serif;
  letter-spacing: 1.7px;
  text-align: center;
  box-shadow: inset 1px 2px 49px rgba(19, 88, 22, 0.96);
  border-radius: 20px;
}

.col-box {
  padding: 10px;
  background: #ffffff;
  color: #494949;
  border: 1px solid #dddddd;
  height: 100px;
  font-size: 18px;
  font-family: Play, sans-serif;
  letter-spacing: 1.2px;
  text-align: center;
  border-radius: 20px;
}

.col-md-2.price-column {
  width: 16.66666667%;
  padding-left: 0px;
  padding-right: 0px;
}

.well-header p {
  background-color: #ADACAC;
  color: white;
  padding: 10px;
  font-size: 18px;
  border-radius: 18px;
}

.well-footer p {
  background-color: #ffcccc;
  color: #ff0000;
  margin-top: 10px;
  min-height: 0px;
  font-size: 18px;
  border-radius: 18px;
  padding: 10px;
}

.well.well-notes {
  border: 1px solid #B6B2B2;
  border-radius: 15px;
  margin-top: 10px;
}

.generate_pdf {
  cursor: pointer;
}
