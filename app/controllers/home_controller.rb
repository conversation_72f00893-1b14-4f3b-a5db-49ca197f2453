class HomeController < ApplicationController
  layout 'angular'

  def index
    host = request.host
    tenant = request.host.split(".")[0]
    @status =  true
    if tenant != "admin" && tenant != nil 
      response = HTTParty.get(request.url.split(":")[0] + "://" + tenant + "." + ENV['SITE_HOST'] + "/home/<USER>" + tenant) rescue nil
      @status = response.parsed_response["status"] rescue true
    end   
    tenant = host.to_s.split('.')[0]
    @current_tenant = tenant
    @groovepacks_admin = (@current_tenant == 'admintools' || @current_tenant == 'scadmintools') ? true : false
  end

  def sign_message
    digest   = OpenSSL::Digest.new('sha512')
    pkey     = OpenSSL::PKey::read(File.read(Rails.root.join('config', 'cert', 'signing', 'qz-private-key.pem')))

    signed   = pkey.sign(digest, params[:request])
    encoded  = Base64.encode64(signed)

    render text: encoded
  end
end
