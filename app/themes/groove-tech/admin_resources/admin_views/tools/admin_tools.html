<div groov-notification></div>
<div class="container-fluid">
  <div class="row bottom-well">
    <div class="col-lg-2 col-md-2">
      <div class="row">
        <accordion>
          <accordion-group is-open="true">
            <accordion-heading>
              <a ui-sref="tools">Tools</a>
            </accordion-heading>
            <li ng-class="{active:current_page =='admin_tools'}" ui-sref="tools.type">Admin Tools</li><br/>
            <li ng-class="{active:current_page =='delayed_jobs'}" ng-click="redirect_to_delayed()">Delayed Jobs</li>
            <li ng-click="rollback_new_env()">Rollback New Env</li>
            <li ng-click="clear_all_imports()">Stop all imports</li>
            <li ng-click="download_activity_log()">Email Activity Log</li>
            <li ng-click="download_activity_log_v2()">Email Activity Log v2</li>
            <li ng-click="download_bulk_event_logs()">Download Bulk Event Logs</li>
            <li ng-click="fix_product_data()">Fix Corrupt Product Data</li>
            <li ng-click="get_duplicates_order_info()">Get Duplicates Order Info </li>
            <li ng-click="delete_duplicates_order()">Delete Duplicates Order</li>
            <li ng-click="download_tenant_log()">Email Tenant Billing Report</li>
            <li ng-click="clear_redis()">Clear Redis</li>
          </accordion-group>
        </accordion>
      </div>
    </div>
    <div class="col-lg-10 col-md-10">
      <div class="row">
        <div class="col-lg-7 col-md-7">
          <div class="col-lg-offset-3 col-md-offset-3 col-lg-9 col-md-9 pull-left">
            <input type="text" class="form-control search-box" autofocus="autofocus" id="tenants-search-query" ng-model="tenants.setup.search" placeholder="Search">
          </div>
        </div>
        <div class="col-lg-1 col-md-1" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Edit <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a class="dropdown-toggle" ng-click="delete_selected_tenants()">Delete <span
              class="badge badge-important">{{tenants.setup.select_all? gridOptions.paginate.tenants_count: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="duplicate_selected_tenants()">Duplicate <span
              class="badge badge-important">{{tenants.setup.select_all? gridOptions.paginate.tenants_count: gridOptions.selections.selected_count}}</span></a>
            </li>
          </ul>
        </div>
      <div class="col-lg-4 col-md-4">
        <span>All Tenant Sorting</span>
        <div toggle-switch ng-model="tenants.update_page_sort" ng-click="update_page_sorting()"></div>
      </div>
    </div>

      <div class="row">
        <div class="col-sm-12">
          <div groov-data-grid="gridOptions" groov-list="tenants.list">
            {{tenants.list}}</div>
        </div>
      </div>
      <div ui-view></div>
    </div>
  </div>
</div>
