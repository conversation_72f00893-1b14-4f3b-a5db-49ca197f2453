<div class="container-fluid">
  <div class="row bottom-well">
    <div class="col-lg-2 col-md-2">
      <div class="row">
        <accordion>
          <accordion-group is-open="true">
            <accordion-heading>
              <a ui-sref="tools">Tools</a>
            </accordion-heading>
            <li ng-class="{active:current_page =='admin_tools'}" ui-sref="tools.type">Admin Tools</li><br/>
            <li ng-class="{active:current_page =='delayed_jobs'}" ui-sref="tools.delayed_jobs">Delayed Jobs</li>
          </accordion-group>
        </accordion>
      </div>
    </div>
    <div class="col-lg-10 col-md-10">
			<div class="row">
				<div class="col-lg-7 col-md-7">
				  <div class="col-lg-offset-3 col-md-offset-3 col-lg-9 col-md-9 pull-left">
				    <input type="text" class="form-control search-box" autofocus="autofocus" id="tenants-search-query" ng-model="delayed_jobs.setup.search" placeholder="Search">
				  </div>
				</div>
        <div class="col-lg-1 col-md-1" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Edit <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a ng-click="delete_delayed_jobs()">Delete</a></li>
          </ul>
        </div>
			  <div class="col-sm-12">
			    <div groov-data-grid="gridOptions" groov-list="new_delayed_jobs">
					{{new_delayed_jobs}}
			    </div>
			  </div>
			</div>
		<div ui-view></div>
		</div>
  </div>
</div>