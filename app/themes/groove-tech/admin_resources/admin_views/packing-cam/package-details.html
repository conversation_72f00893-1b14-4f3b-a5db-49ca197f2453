<div class="pricing_page package-details-font">

  <!-- <div class="main-loader">
    <img src="/assets/images/loading.gif" alt="loading..." width="230px">
  </div> -->

  <div class="row col-md-12 setup_pricing">
    <div class="packing-cam-info bottom-well">
      <!-- Loading -->
      <div class="modal-body text-center" ng-show="loading">
        <img src="/assets/images/loading.gif" alt="loading..." width="230px">
      </div>

      <!-- No Data Found -->
      <div ng-show="!loading && !packing_cam_data">
        <div class="row text-center">
          <h3><span>Not Found</span></h1>
        </div>
      </div>

      <!-- Show Details -->
      <div ng-show="!loading && packing_cam_data">
        <div class="row packing-cam-header">
          <div ng-show="packing_cam_data.setting.customer_page_logo" class="col-md-3 packing-cam-logo">
            <img src="{{packing_cam_data.setting.customer_page_logo}}" alt="logo..." width="100% " style="max-width: 220px; max-height: 165px;">
          </div>

          <div class="{{packing_cam_data.setting.customer_page_logo ? 'col-md-9 col-sm-9 col-xs-12' : 'col'}}">
            <div ng-bind-html="customerPageMessage" class="packing-cam-header-content"></div>
          </div>
        </div>

        <!-- Packing Cam Images -->
        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel" ng-show="lightbox_images.length > 0">
          <!-- Indicators -->
          <ol class="carousel-indicators">
            <li data-target="#carousel-example-generic" ng-repeat="image in lightbox_images" data-slide-to="{{$index}}" ng-class="{active:!$index}" ></li>
          </ol>

          <!-- Wrapper for slides -->
          <div class="carousel-inner" role="listbox">
            <div class="item" ng-class="{active:!$index}" ng-click="openLightboxModal($index)" ng-repeat="image in lightbox_images">
              <img src="{{image.url}}" alt="{{image.url}}">
            </div>
          </div>

          <!-- Controls -->
          <span class="left carousel-control" data-target="#carousel-example-generic" role="button" ng-non-bindable data-slide="prev">
            <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
            <span class="sr-only">Previous</span>
          </span>
          <span class="right carousel-control" data-target="#carousel-example-generic" role="button" ng-non-bindable data-slide="next">
            <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
            <span class="sr-only">Next</span>
          </span>
        </div>
        <br>

        <!-- Packing List -->
        <h3 style="text-align: center;">Order Number {{packing_cam_data.order.increment_id}} </h3>
        <div class="packing-list">
          <section>
            <!-- Header with Search Bar -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin: 5px;">
              <h3 style="margin: 0;">Packing List</h3>
              <div class="search-bar" style="margin-right: 5%;">
                <input
                  type="text"
                  class="form-control"
                  style="width: 280px;font-size: 14px;"
                  placeholder="Search by Product Name or SKU"
                  ng-model="searchQuery"
                  ng-change="filterData()">
              </div>
            </div>

            <!-- Table for Filtered Data -->
            <div class="packing-list-log-content table-responsive">
              <table class="table table-borderless" ng-hide="filter_box_data.length > 0">
                <thead>
                  <tr>
                    <th scope="col">Product Name</th>
                    <th scope="col">SKU</th>
                    <th scope="col">Quantity Ordered</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in filteredData">
                    <td>{{item.name}}</td>
                    <td>{{item.sku}}</td>
                    <td>{{item.qty}}</td>
                  </tr>
                </tbody>
              </table>
              <table class="table table-borderless" ng-show="filter_box_data.length > 0">
                <thead>
                  <tr>
                    <th scope="col">Box</th>
                    <th scope="col">Product Name</th>
                    <th scope="col">SKU</th>
                    <th scope="col">Quantity Ordered</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="item in filteredBoxData" ng-style="{ 'border-bottom': $index < filter_box_data.length - 1 && item.box !== filter_box_data[$index + 1].box ? '1px solid #BEBDBD' : '' }">
                    <td>{{item.box}}</td>
                    <td>{{item.name}}</td>
                    <td>{{item.sku}}</td>
                    <td>{{item.qty}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
        </div>
        <br>
        <div class="packing-logs"
          ng-show="packing_cam_data.order_activities && packing_cam_data.order_activities.length > 0">
          <section>
            <h3>Packing Logs</h3><br>
            <div class="packing-list-log-content packing-list-logs">
              <div class="row" ng-repeat="item in packing_cam_data.order_activities">
                <div class="col-md-12">
                  {{item.activitytime | date:'EEEE MM/dd/yyyy hh:mm:ss a'}}<br>
                  {{item.action}} {{item.username ? ' by: ' + item.username : ''}}
                  <br><br>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="box-outer">
      <div class="box">
      </div>
      <div class="col-sm-offset-3 col-sm-6 d-flex">
        <img src="/assets/images/gp-ex-logo.png" class="col-xs-12" width="50px" style="width: 80px;" alt="GroovePacker" />
      </div>
    </div>
    <div class="text-center">
      <h4>Barcode Scan & Pack QC by GroovePacker</h4>
    </div>
  </div>
</div>

<style>
  .container-well {
    background: none;
    background-color: #ececec;
}
/* .main-loader{
  background-color: #ececec;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 999;
  left: 0;
} */
.gradient-well-inner {
    border-radius: 0px;
    padding-top: 0px;
    padding-bottom: 0px;
    box-shadow: none;
    background: none;
    border: none;
}
.gradient-well{
  box-shadow: none;
    background: none;
    border: none;
}
.pricing_page .setup_pricing {
    margin: 0;
}
.bottom-well{
  box-shadow: none;
    background: none;
    border: none;
}
.main-body {
    margin-top: 0px;
}


.pricing_page .box{
  box-shadow: none;
    background: none;
    border: none;
}

/* width */
::-webkit-scrollbar {
  width: 4px !important;
}
/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(215, 215, 215);
  border-radius: 10px;
}
/* Handle */
::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
#carousel-example-generic {
    margin-bottom: 30px;
    text-align: center;
}
.item img {
    margin: 0 auto;
    object-fit: cover;
}
.carousel-control{
  opacity: 0;
}
.carousel-control:hover{
  opacity: 0.9;
}
.packing-list-log-content table thead th:last-child {text-align: center !important;}
.packing-list-log-content table tbody tr td:last-child {text-align: center !important;}
@media(max-width :767px){
  .gradient-well-inner {
    width: 94vw;
  }
  .packing-cam-logo {
    margin-bottom: 20px;
  }
}
</style>

<script>
  window.onload = function () {
    $('#dashboard').hide();
    $(document).ready(function () {
      FreshWidget.destroy();
    });
  }
</script>
