
<div class="modal-body">
  <div class="container-fluid">
    <div>
      <br />
      <br />
      <br />
    </div>
    <div >
      <div class="d-flex justify-content-between">
        
        <div>
          <center>
            <i class="groove-fa fa fa-dropbox fa-5x"></i>

            <div>Shipments</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.max_allowed" class="form-control input-style" />
          </div>
        </div>
        <div >
          <center>
            <i class="groove-fa fa fa-users fa-5x"></i>

            <div>Users</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.max_users" class="form-control input-style" />
          </div>
        </div>
        <!-- <div >
          <center>
            <i class="groove-fa fa fa-users fa-5x"></i>

            <div>Regular Users</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.regular_users" class="form-control input-style" />
          </div>
        </div> -->
        <div >
          <center>
            <i class="groove-fa fa fa-users fa-5x"></i>

            <div>Max Admin only Users</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.max_administrative_users" class="form-control input-style" />
          </div>
        </div>
        <div>
          <center>
            <i class="groove-fa fa fa-cloud-download fa-5x"></i>

            <div>Sources</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.max_import_sources" class="form-control input-style" />
          </div>
        </div>
        <div>
          <center>
            <i class="groove-fa fa fa-usd fa-5x"></i>

            <div>Amount</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="text" ng-model="tenants.single.subscription_info.amount" class="form-control input-style" />
          </div>
        </div>
        <div>
          <center>
            <i class="groove-fa fa fa-calendar fa-5x"></i>
            <div>Days</div>
          </center>
          <div class="col-sm-offset-1 col-sm-10">
            <input type="number" min="0" ng-model="tenants.single.basicinfo.orders_delete_days" class="form-control input-style" ng-blur="update_access_restrictions()" />
          </div>
        </div>
        <div style="clear: both;"><br /></div>
      </div>
      <div class="col-md-12">
        <div class="col-sm-5"></div>
        <div class="col-sm-2">
          <br />
          <br />
          <center>
            <input type="radio" name="interval" value="month" ng-model="tenants.single.subscription_info.interval" /> Monthly
            <shopifytag ng-if="tenants.single.subscription_info.shopify_customer != true"><input type="radio" name="interval" value="year" ng-model="tenants.single.subscription_info.interval" /> Yearly</shopifytag>
          </center>
        </div>
      </div>

      <div class="col-md-12">
        <div class="col-sm-3"></div>
        <div class="col-sm-1">
          <br />
          <center>
            <div class="btn btn-lg" ng-class="{'btn-primary': tenants.single.basicinfo.is_modified == false, 'btn-info': tenants.single.basicinfo.is_modified == true}">
              {{tenants.single.basicinfo.initial_plan_id}}
              <br />
              ( {{tenants.single.basicinfo.is_modified == true ? "Modified" : "Unmodified"}} )
            </div>
          </center>
          <!-- <div class="col-sm-offset-1 col-sm-10">
            <input type="number" ng-model="tenants.single.access_restrictions_info.max_import_sources"
                   class="form-control input-style"/>
          </div> -->
        </div>
        <div class="col-sm-3">
          <br />
          <button ng-click="update_access_restrictions()" class="dropdown-toggle groove-button label label-default label-success pull-right" style="margin-right: 30px;" translate>Update</button>
        </div>
        <div class="col-sm-3" ng-if="tenants.single.subscription_info.shopify_customer != true">
          <br />
          <button ng-click="update_zero_plan()" class="dropdown-toggle groove-button label label-default label-success pull-right" style="margin-right: 30px;" translate>Update Zero Subscription Plan</button>
        </div>
      </div>
      <div class="col-sm-2"></div>
    </div>
    <div style="clear: both;"><br /></div>

    <div class="col-sm-12">
      <div class="col-sm-offset-1 col-sm-6">
        <h4 class="control-label" style="width: 100%; text-align: left;">Plan Notes:</h4>
        <textarea class="form-control input-style" rows="5" ng-model="tenants.single.basicinfo.addon_notes" placeholder="Unfocus will save data..." ng-blur="update_access_restrictions()"></textarea>
        <div>
          <h4 class="control-label" style="width: 100%; text-align: left;">Activity Logs</h4>
          <textarea class="form-control input-style" rows="5" ng-model="tenants.single.basicinfo.activity_log" placeholder="Unfocus will save data..." ng-blur="update_access_restrictions()"></textarea>
        </div>
        <div>
          <h4 class="control-label" style="width: 100%; text-align: left;">Tenant Notes</h4>
          <textarea class="form-control input-style" rows="5" ng-model="tenants.single.basicinfo.note" placeholder="Unfocus will save data..." ng-blur="update_access_restrictions()"></textarea>
        </div>
      </div>

      <div class="col-sm-4" ng-if="tenants.single.subscription_info.shopify_customer != true">
        <div style="clear: both;"></div>
        <div>
          <h4>Stripe Customer <i class="fa fa-circle" aria-hidden="true" ng-style="{ 'color' : tenants.single.subscription_info.verified_stripe_account ? '#5cb85c' : 'red' }"></i></h4>
          <span><a href="https://dashboard.stripe.com/customers/{{tenants.single.subscription_info.customer_id}}" target="_blank">{{tenants.single.subscription_info.email}}</a> </span>
        </div>
        <div style="clear: both;"></div>
        <div>
          <h4 class="control-label">Stripe Plan</h4>
          <span><a href="https://dashboard.stripe.com/plans/{{tenants.single.subscription_info.plan_id}}" target="_blank">{{tenants.single.subscription_info.plan}}</a></span>
        </div>
        <div style="clear: both;"></div>
        <div ng-show="tenants.single.logged_in_user == 'gpadmin'">
          <h4 class="control-label">Customer ID</h4>
          <input type="text" ng-model="tenants.single.subscription_info.customer_id" ng-blur="update_access_restrictions()" class="form-control input-style" />
        </div>
        <div style="clear: both;"></div>
        <div ng-show="tenants.single.logged_in_user == 'gpadmin'">
          <h4 class="control-label">Subscription ID</h4>
          <input type="text" ng-model="tenants.single.subscription_info.customer_subscription_id" ng-blur="update_access_restrictions()" class="form-control input-style" />
        </div>
        <div style="clear: both;"></div>
        <div ng-show="tenants.single.logged_in_user == 'gpadmin'">
          <h4 class="control-label">Plan ID</h4>
          <input type="text" ng-model="tenants.single.subscription_info.plan_id" ng-blur="update_access_restrictions()" class="form-control input-style" />
        </div>
        <div class="tenant_url">
          <a href="https://{{tenants.single.basicinfo.url}}" target="_blank">https://{{tenants.single.basicinfo.url}}/</a>
        </div>
      </div>
      <div class="col-sm-1"></div>
    </div>
    <div style="clear: both;"></div>
    <hr />
    <div>
      <div class="col-sm-1"></div>
      <div class="col-sm-10 delete_feature">
        <div class="col-sm-3" style="width: 20%;">
          <center>
            <div><i class="groove-fa fa fa-file-text-o fa-5x"></i></div>
            <br />
            <button ng-click="delete_orders()" class="dropdown-toggle groove-button label label-default label-danger" translate>Delete Orders</button>
          </center>
        </div>
        <div class="col-sm-3" style="width: 20%;">
          <center>
            <div><i class="groove-fa fa fa-shopping-cart fa-5x"></i></div>
            <br />
            <button ng-click="delete_products()" class="dropdown-toggle groove-button label label-default label-danger" translate>Delete Products</button>
          </center>
        </div>
        <div class="col-sm-3" style="vertical-align: bottom; width: 20%;">
          <center>
            <div style="height: 1em;"></div>
            <div><i class="groove-fa fa fa-file-text-o fa-4x"></i> <i class="glyphicon-plus-style glyphicon glyphicon-plus"></i><i class="groove-fa fa fa-shopping-cart fa-4x"></i></div>
            <br />
            <button ng-click="delete_orders_and_products()" class="dropdown-toggle groove-button label label-default label-danger" translate>Orders & Products</button>
          </center>
        </div>
        <div class="col-sm-3" style="width: 20%;">
          <center>
            <div><i class="groove-fa fa fa-bomb fa-5x"></i></div>
            <br />
            <button ng-click="delete_all()" class="dropdown-toggle groove-button label label-danger" translate>Clear All Data</button>
          </center>
        </div>
        <div class="col-sm-3" style="width: 20%;">
          <center>
            <div><i class="groove-fa fas fa-warehouse fa-5x"></i></div>
            <br />
            <div class="row">
              <i
                class="info icon-large glyphicon glyphicon-info-sign ng-scope"
                style="padding-left: 4px;"
                popover-trigger="mouseenter"
                popover-placement="bottom"
                groov-popover="All open orders should be removed (Awaiting and Action Required) before using this option to zero the allocated inventory. Other inventory levels will not be affected."
              ></i>
              <button ng-click="reset_inventory()" class="dropdown-toggle groove-button label label-danger" translate>Zero Allocated Inv</button>
            </div>
          </center>
        </div>
      </div>
      <div class="col-sm-1"></div>
    </div>
    <div class="col-sm-12">
      <br />
    </div>
    <div style="clear: both;"></div>
    <hr />
    <h4 class="control-label text-center" style="width: 100%;">Other Features</h4>
    <br />
    <div class="row mb-15">
      <div class="col-sm-4"></div>
      <div class="col-sm-2 pd-h-none">
        <label class="control-label d-inline">High SKU</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div
            toggle-switch
            ng-model="tenants.single.basicinfo.price.high_sku_feature.toggle
          "
            ng-click="update_price_field('high_sku_feature', tenants.single.basicinfo.price.high_sku_feature)"
          ></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input class="form-control" ng-model="tenants.single.basicinfo.price.high_sku_feature.amount" ng-blur="update_price_field('high_sku_feature', tenants.single.basicinfo.price.high_sku_feature)" aria-describedby="basic-addon1" />
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-4"></div>
      <div class="col-sm-2 pd-h-none">
        <label class="control-label d-inline">Double High SKU</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div
            toggle-switch
            ng-model="tenants.single.basicinfo.price.double_high_sku.toggle
          "
            ng-click="update_price_field('double_high_sku', tenants.single.basicinfo.price.double_high_sku)"
          ></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input class="form-control" ng-model="tenants.single.basicinfo.price.double_high_sku.amount" ng-blur="update_price_field('double_high_sku', tenants.single.basicinfo.price.double_high_sku)" aria-describedby="basic-addon1" />
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-4"></div>
      <div class="col-sm-2 pd-h-none">
        <label class="control-label d-inline">Cust. Maintenance 1</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div
            toggle-switch
            ng-model="tenants.single.basicinfo.price.cust_maintenance_1.toggle
          "
            ng-click="update_price_field('cust_maintenance_1', tenants.single.basicinfo.price.cust_maintenance_1)"
          ></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.cust_maintenance_1.amount"
              ng-blur="update_price_field('cust_maintenance_1', tenants.single.basicinfo.price.cust_maintenance_1)"
              aria-describedby="basic-addon1"
            />
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-4"></div>
      <div class="col-sm-2 pd-h-none">
        <label class="control-label d-inline">Cust. Maintenance 2</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div
            toggle-switch
            ng-model="tenants.single.basicinfo.price.cust_maintenance_2.toggle
          "
            ng-click="update_price_field('cust_maintenance_2', tenants.single.basicinfo.price.cust_maintenance_2)"
          ></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.cust_maintenance_2.amount"
              ng-blur="update_price_field('cust_maintenance_2', tenants.single.basicinfo.price.cust_maintenance_2)"
              aria-describedby="basic-addon1"
            />
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

 
    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.is_multi_box" ng-click="update_access_restrictions()"></div>
        </div>
        <label class="control-label d-inline">Multi Box</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.multi_box_feature.toggle" ng-click="update_price_field('multi_box_feature', tenants.single.basicinfo.price.multi_box_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.multi_box_feature.amount"
              ng-blur="update_price_field('multi_box_feature', tenants.single.basicinfo.price.multi_box_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.multi_box_feature.is_checked"
                ng-change="update_price_field('multi_box_feature', tenants.single.basicinfo.price.multi_box_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.is_fba" ng-click="update_tenant_settings('is_fba')"></div>
        </div>
        <label class="control-label d-inline">Amazon FBA</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.amazon_fba_feature.toggle" ng-click="update_price_field('amazon_fba_feature', tenants.single.basicinfo.price.amazon_fba_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.amazon_fba_feature.amount"
              ng-blur="update_price_field('amazon_fba_feature', tenants.single.basicinfo.price.amazon_fba_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.amazon_fba_feature.is_checked"
                ng-change="update_price_field('amazon_fba_feature', tenants.single.basicinfo.price.amazon_fba_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.allow_rts" ng-click="update_tenant_settings('allow_rts')"></div>
        </div>
        <label class="control-label d-inline">Real-time Packer Stats</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.allow_Real_time_feature.toggle" ng-click="update_price_field('allow_Real_time_feature',tenants.single.basicinfo.price.allow_Real_time_feature )"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.allow_Real_time_feature.amount"
              ng-blur="update_price_field('allow_Real_time_feature', tenants.single.basicinfo.price.allow_Real_time_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.allow_Real_time_feature.is_checked"
                ng-change="update_price_field('allow_Real_time_feature', tenants.single.basicinfo.price.allow_Real_time_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.scheduled_import_toggle" ng-click="update_scheduled_import_toggle(true)"></div>
        </div>
        <label class="control-label d-inline">Hourly Order Import</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.import_option_feature.toggle" ng-click="update_price_field('import_option_feature', tenants.single.basicinfo.price.import_option_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.import_option_feature.amount"
              ng-blur="update_price_field('import_option_feature',tenants.single.basicinfo.price.import_option_feature )"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.import_option_feature.is_checked"
                ng-change="update_price_field('import_option_feature', tenants.single.basicinfo.price.import_option_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.product_ftp_import" ng-click="update_tenant_settings('product_ftp_import')"></div>
        </div>
        <label class="control-label d-inline">Product FTP Import</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.product_ftp_import.toggle" ng-click="update_price_field('product_ftp_import',tenants.single.basicinfo.price.product_ftp_import )"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.product_ftp_import.amount"
              ng-blur="update_price_field('product_ftp_import', tenants.single.basicinfo.price.product_ftp_import)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.product_ftp_import.is_checked"
                ng-change="update_price_field('product_ftp_import', tenants.single.basicinfo.price.product_ftp_import)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.inventory_report_toggle" ng-click="update_tenant_settings('inventory_report_toggle')"></div>
        </div>
        <label class="control-label d-inline">Inventory Report Option</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div
            toggle-switch
            ng-model="tenants.single.basicinfo.price.inventory_report_option_feature.toggle"
            ng-click="update_price_field('inventory_report_option_feature', tenants.single.basicinfo.price.inventory_report_option_feature)"
          ></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.inventory_report_option_feature.amount"
              ng-blur="update_price_field('inventory_report_option_feature', tenants.single.basicinfo.price.inventory_report_option_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.inventory_report_option_feature.is_checked"
                ng-change="update_price_field('inventory_report_option_feature', tenants.single.basicinfo.price.inventory_report_option_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.api_call" ng-click="update_tenant_settings('api_call')"></div>
        </div>
        <label class="control-label d-inline">Post Scanning API Call </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.post_scanning_feature.toggle" ng-click="update_price_field('post_scanning_feature', tenants.single.basicinfo.price.post_scanning_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.post_scanning_feature.amount"
              ng-blur="update_price_field('post_scanning_feature', tenants.single.basicinfo.price.post_scanning_feature)"
              aria-describedby="basic-addon1"
            />
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.groovelytic_stat" ng-click="update_groovelytic_stat(true)"></div>
        </div>
        <label class="control-label d-inline">Groovelytics Stats </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.groovelytic_stat_feature.toggle" ng-click="update_price_field('groovelytic_stat_feature', tenants.single.basicinfo.price.groovelytic_stat_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.groovelytic_stat_feature.amount"
              ng-blur="update_price_field('groovelytic_stat_feature', tenants.single.basicinfo.price.groovelytic_stat_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.groovelytic_stat_feature.is_checked"
                ng-change="update_price_field('groovelytic_stat_feature', tenants.single.basicinfo.price.groovelytic_stat_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.enable_developer_tools" ng-click="update_tenant_settings('enable_developer_tools')"></div>
        </div>
        <label class="control-label d-inline">Enable Developer Tools </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.enable_developer_tools_feature.toggle" ng-click="update_price_field('enable_developer_tools_feature', tenants.single.basicinfo.price.enable_developer_tools_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.enable_developer_tools_feature.amount"
              ng-blur="update_price_field('enable_developer_tools_feature', tenants.single.basicinfo.price.enable_developer_tools_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.enable_developer_tools_feature.is_checked"
                ng-change="update_price_field('enable_developer_tools_feature', tenants.single.basicinfo.price.enable_developer_tools_feature)" 
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.daily_packed_toggle" ng-click="update_tenant_settings('daily_packed_toggle')"></div>
        </div>
        <label class="control-label d-inline">Daily Packed % Toggle </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.daily_packed_feature.toggle" ng-click="update_price_field('daily_packed_feature', tenants.single.basicinfo.price.daily_packed_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.daily_packed_feature.amount"
              ng-blur="update_price_field('daily_packed_feature', tenants.single.basicinfo.price.daily_packed_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.daily_packed_feature.is_checked"
                ng-change="update_price_field('daily_packed_feature', tenants.single.basicinfo.price.daily_packed_feature)"
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

 
    <div class="row mb-15" ng-show="tenants.single.basicinfo.store_order_respose_log && tenants.single.se_import_data.length">
      <div class="col-sm-4"></div>
      <div class="col-sm-2 pd-h-none">
        <label class="control-label d-inline">SE Order Response Log: </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline" ng-repeat="data in tenants.single.se_import_data">
          <label class="control-label d-inline"><a href="{{data.url}}" target="_blank">{{data.date}}</a></label>&nbsp;&nbsp;
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>


    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.packing_cam" ng-click="update_tenant_settings('packing_cam')"></div>
        </div>
        <label class="control-label d-inline">Packing Cam </label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.packing_cam_feature.toggle" ng-click="update_price_field('packing_cam_feature', tenants.single.basicinfo.price.packing_cam_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.packing_cam_feature.amount"
              ng-blur="update_price_field('packing_cam_feature', tenants.single.basicinfo.price.packing_cam_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input 
                type="checkbox" 
                ng-model="tenants.single.basicinfo.price.packing_cam_feature.is_checked"
                ng-change="update_price_field('packing_cam_feature', tenants.single.basicinfo.price.packing_cam_feature)"
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>
    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.voice_packing" ng-click="update_tenant_settings('voice_packing')"></div>
        </div>
        <label class="control-label d-inline">Voice Packing</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.voice_packing_feature.toggle" ng-click="update_price_field('voice_packing_feature', tenants.single.basicinfo.price.voice_packing_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.voice_packing_feature.amount"
              ng-blur="update_price_field('voice_packing_feature', tenants.single.basicinfo.price.voice_packing_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input
                type="checkbox"
                ng-model="tenants.single.basicinfo.price.voice_packing_feature.is_checked"
                ng-change="update_price_field('voice_packing_feature', tenants.single.basicinfo.price.voice_packing_feature)"
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>
    <div class="row mb-15">
      <div class="col-sm-3"></div>
      <div class="col-sm-3 pd-h-none">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.scan_to_score" ng-click="update_tenant_settings('scan_to_score')"></div>
        </div>
        <label class="control-label d-inline">scan to score</label>
      </div>
      <div class="col-sm-3">
        <div class="controls d-inline">
          <div toggle-switch ng-model="tenants.single.basicinfo.price.scan_to_score_feature.toggle" ng-click="update_price_field('scan_to_score_feature', tenants.single.basicinfo.price.scan_to_score_feature)"></div>
        </div>
        <div class="d-inline">
          <div class="input-group currency_addon">
            <span class="input-group-addon" id="basic-addon1">$</span>
            <input
              class="form-control"
              ng-model="tenants.single.basicinfo.price.scan_to_score_feature.amount"
              ng-blur="update_price_field('scan_to_score_feature', tenants.single.basicinfo.price.scan_to_score_feature)"
              aria-describedby="basic-addon1"
            />
            <label style="margin: 0; display: flex; align-items: center;">
              <input
                type="checkbox"
                ng-model="tenants.single.basicinfo.price.scan_to_score_feature.is_checked"
                ng-change="update_price_field('scan_to_score_feature', tenants.single.basicinfo.price.scan_to_score_feature)"
              />
              /User
            </label>
          </div>
        </div>
      </div>
      <div class="col-sm-3"></div>
    </div>

</div>

  <br />
  <div style="clear: both;"></div>
  <hr />
  <h4 class="control-label text-center" style="width: 100%;">Allow Utility</h4>
  <br />

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.is_delay" ng-click="update_tenant_settings('is_delay')"></div>
      </div>
      <label class="control-label d-inline">Shipwork import with delay </label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.delayed_inventory_update" ng-click="update_tenant_settings('delayed_inventory_update')"></div>
      </div>
      <label class="control-label d-inline">Inventory update with delay </label>
    </div>
    <div class="col-sm-3"></div>
  </div>
  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.show_external_logs_button" ng-click="update_tenant_settings('show_external_logs_button')"></div>
      </div>
      <label class="control-label d-inline">Show External Logs Button </label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.order_cup_direct_shipping" ng-click="update_tenant_settings('order_cup_direct_shipping')"></div>
      </div>
      <label class="control-label d-inline">OrderCup Direct Shipping </label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.store_order_respose_log" ng-click="update_store_order_respose_log(true)"></div>
      </div>
      <label class="control-label d-inline">Get Order Response Log </label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.gdpr_shipstation" ng-click="update_tenant_settings('gdpr_shipstation')"></div>
      </div>
      <label class="control-label d-inline">GDPR ShipStation</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.uniq_shopify_import" ng-click="update_tenant_settings('uniq_shopify_import')"></div>
      </div>
      <label class="control-label d-inline">Unique Shopify Import</label>
    </div>
    <div class="info col-sm-3">
      <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
        groov-popover="Allow workers to discover and terminate duplicate import orders."></i>
    </div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.show_originating_store_id" ng-click="update_tenant_settings('show_originating_store_id')"></div>
      </div>
      <label class="control-label d-inline">Show originating Store ID</label>
    </div>
    <div class="info col-sm-3">
      <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
        groov-popover="When enabled, this would show you the store ID's of different source stores if youre a fulfillment house with different stores. "></i>
    </div>
  </div>

  <br />
  <div style="clear: both;"></div>
  <hr />
  <h4 class="control-label text-center" style="width: 100%;">Allow Logging </h4>
  <br />
  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_sw_imports" ng-click="update_tenant_settings('loggly_sw_imports')"></div>
      </div>
      <label class="control-label d-inline">Loggly Shipwork Import</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_shopify_imports" ng-click="update_tenant_settings('loggly_shopify_imports')"></div>
      </div>
      <label class="control-label d-inline">Loggly Shopify Import</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_se_imports" ng-click="update_tenant_settings('loggly_se_imports')"></div>
      </div>
      <label class="control-label d-inline">Loggly ShippingEasy Import</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_shipstation_imports" ng-click="update_tenant_settings('loggly_shipstation_imports')"></div>
      </div>
      <label class="control-label d-inline">Loggly ShipStation Import</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_veeqo_imports" ng-click="update_tenant_settings('loggly_veeqo_imports')"></div>
      </div>
      <label class="control-label d-inline">Loggly Veeqo Import</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div>
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.loggly_gpx_order_scan" ng-click="update_tenant_settings('loggly_gpx_order_scan')"></div>
      </div>
      <label class="control-label d-inline">Loggly GPX Order Scan</label>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <br />
  <div style="clear: both;"></div>
  <hr />
  <h4 class="control-label text-center" style="width: 100%;">Allow Inventory Pull/Push</h4>
  <br />

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.access_restrictions_info.allow_bc_inv_push" ng-click="allowinventory_pull_push_all(true)"></div>
      </div>
      <label class="control-label d-inline">BigCommerce</label>
    </div>
    <div class="col-sm-3">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.price.bigCommerce_feature.toggle" ng-click="update_price_field('bigCommerce_feature', tenants.single.basicinfo.price.bigCommerce_feature)"></div>
      </div>
      <div class="d-inline">
        <div class="input-group currency_addon">
          <span class="input-group-addon" id="basic-addon1">$</span>
          <input
            ng-model="tenants.single.basicinfo.price.bigCommerce_feature.amount"
            class="form-control"
            ng-blur="update_price_field('bigCommerce_feature', tenants.single.basicinfo.price.bigCommerce_feature)"
            aria-describedby="basic-addon1"
          />
        </div>
      </div>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.access_restrictions_info.allow_shopify_inv_push" ng-click="allowinventory_pull_push_all(true)"></div>
      </div>
      <label class="control-label d-inline">Shopify</label>
    </div>
    <div class="col-sm-3">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.price.shopify_feature.toggle" ng-click="update_price_field('shopify_feature', tenants.single.basicinfo.price.shopify_feature)"></div>
      </div>
      <div class="d-inline">
        <div class="input-group currency_addon">
          <span class="input-group-addon" id="basic-addon1">$</span>
          <input class="form-control" ng-blur="update_price_field('shopify_feature', tenants.single.basicinfo.price.shopify_feature)" ng-model="tenants.single.basicinfo.price.shopify_feature.amount" aria-describedby="basic-addon1" />
        </div>
      </div>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.access_restrictions_info.allow_shopline_inv_push" ng-click="allowinventory_pull_push_all(true)"></div>
      </div>
      <label class="control-label d-inline">Shopline</label>
    </div>
    <div class="col-sm-3">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.price.shopline_feature.toggle" ng-click="update_price_field('shopline_feature', tenants.single.basicinfo.price.shopline_feature)"></div>
      </div>
      <div class="d-inline">
        <div class="input-group currency_addon">
          <span class="input-group-addon" id="basic-addon1">$</span>
          <input class="form-control" ng-blur="update_price_field('shopline_feature', tenants.single.basicinfo.price.shopline_feature)" ng-model="tenants.single.basicinfo.price.shopline_feature.amount" aria-describedby="basic-addon1" />
        </div>
      </div>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.access_restrictions_info.allow_mg_rest_inv_push" ng-click="allowinventory_pull_push_all(true)"></div>
      </div>
      <label class="control-label d-inline">Magento2</label>
    </div>
    <div class="col-sm-3">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.basicinfo.price.magento2_feature.toggle" ng-click="update_price_field('magento2_feature',tenants.single.basicinfo.price.magento2_feature )"></div>
      </div>
      <div class="d-inline">
        <div class="input-group currency_addon">
          <span class="input-group-addon" id="basic-addon1">$</span>
          <input class="form-control" ng-model="tenants.single.basicinfo.price.magento2_feature.amount" ng-blur="update_price_field('magento2_feature', tenants.single.basicinfo.price.magento2_feature)" aria-describedby="basic-addon1" />
        </div>
      </div>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <div class="controls d-inline">
        <div toggle-switch ng-model="tenants.single.access_restrictions_info.allow_teapplix_inv_push" ng-click="allowinventory_pull_push_all(true)"></div>
      </div>
      <label class="control-label d-inline">Teapplix</label>
    </div>
    <div class="col-sm-3">
      <div class="controls d-inline">
        <div
          toggle-switch
          ng-model="tenants.single.basicinfo.price.teapplix_feature.toggle
          "
          ng-click="update_price_field('teapplix_feature', tenants.single.basicinfo.price.teapplix_feature)"
        ></div>
      </div>
      <div class="d-inline">
        <div class="input-group currency_addon">
          <span class="input-group-addon" id="basic-addon1">$</span>
          <input class="form-control" ng-model="tenants.single.basicinfo.price.teapplix_feature.amount" ng-blur="update_price_field('teapplix_feature', tenants.single.basicinfo.price.teapplix_feature)" aria-describedby="basic-addon1" />
        </div>
      </div>
    </div>
    <div class="col-sm-3"></div>
  </div>

  <br />
  <div style="clear: both;"></div>
  <hr />
  <h4 class="control-label text-center" style="width: 100%;">Scan Workflow</h4>
  <br />
  <div class="row mb-15">
    <div class="col-sm-4"></div>
    <div class="col-sm-2 pd-h-none">
      <label class="control-label d-inline">Scan workflow</label>
    </div>
    <div class="controls col-sm-4 form-inline" dropdown>
      <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':tenants.single.basicinfo.scan_pack_workflow != 'default'}">
        <span ng-show="tenants.single.basicinfo.scan_pack_workflow=='default'">Default</span>
        <span ng-show="tenants.single.basicinfo.scan_pack_workflow=='product_first_scan_to_put_wall'">Product First - Scan to Put Wall</span>
        <span ng-show="tenants.single.basicinfo.scan_pack_workflow=='multi_put_wall'">Multi - Put Wall</span>
        <span class="caret"></span>
        <span ng-show="tenants.single.basicinfo.scan_pack_workflow == 'scan_to_cart'">
          Scan to Cart
        </span>
      </button>
      <ul class="dropdown-menu" role="menu">
        <li><a class="dropdown-toggle" ng-click="update_scan_workflow('default')">Default</a></li>
        <li><a class="dropdown-toggle" ng-click="update_scan_workflow('product_first_scan_to_put_wall')">Product First - Scan to Put Wall</a></li>
        <li><a class="dropdown-toggle" ng-click="update_scan_workflow('multi_put_wall')">Multi - Put Wall</a></li>
        <li><a class="dropdown-toggle" ng-click="update_scan_workflow('scan_to_cart')">Scan to Cart</a></li>
      </ul>
    </div>
    <div class="col-sm-3"></div>
  </div>
</div>
