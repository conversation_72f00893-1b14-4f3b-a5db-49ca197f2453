<div class="container-fluid">
  <div class="row">
    <div class="col-sm-12 mt-3">
      <div class="col-lg-7 col-md-7 col-lg-offset-2 col-md-offset-2">
        <div class="col-lg-offset-3 col-md-offset-3 col-lg-9 col-md-9 pull-left">
          <input type="text" class="form-control search-box activity-search" autofocus="autofocus"
            ng-keyup="triggerSearchOnEnter($event)" ng-model="searchQuery" placeholder="Search activity...">
        </div>
      </div>

      <!-- Pagination numbers -->
      <div style="text-align: center;" class="page_nav_wrapper col-lg-12">
        <div class="pagination">
          <button class="page-link" ng-click="prevPage()" ng-disabled="currentPage === 1">Previous</button>
          <button ng-repeat="page in pages track by $index" class="page-link" ng-click="goToPage(page)"
            ng-class="{'active-page': page === currentPage}">
            {{page}}
          </button>
          <button class="page-link" ng-click="nextPage()" ng-disabled="!hasMoreRecords">Next</button>
        </div>
      </div>

      <fieldset class="col-lg-12 activity-table-wrapper" style="overflow: auto;">
        <table class="table table-hover table-bordered table-striped table-condensed activity-table">
          <thead>
            <tr>
              <th width="20%">
                <div>Name</div>
              </th>
              <th width="45%">
                <div>Data</div>
              </th>
              <th width="20%">
                <div>User</div>
              </th>
              <th>
                <div>Time</div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="activity in tenants.single.activity_log_v2">
              <td>{{activity.event}}</td>
              <td>{{activity.saved_changes}}</td>
              <td>{{activity.user}}</td>
              <td>{{activity.timestamp}}</td>
            </tr>
          </tbody>
        </table>
      </fieldset>
      <!-- Pagination numbers -->
      <div style="text-align: center;" class="page_nav_wrapper">
        <div class="pagination">
          <button class="page-link" ng-click="prevPage()" ng-disabled="currentPage === 1">Previous</button>
          <button ng-repeat="page in pages track by $index" class="page-link" ng-click="goToPage(page)"
            ng-class="{'active-page': page === currentPage}">
            {{page}}
          </button>
          <button class="page-link" ng-click="nextPage()" ng-disabled="!hasMoreRecords">Next</button>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .pagination .active-page.page-link {
    background-color: #558641;
    border-color: #609545;
    color: white;
  }

  .pagination .active-page.page-link:hover {
    background-color: #78b24c;
    border: #78b24c;
    color: #fff;
  }

  .pagination .page-link:first-child {
    margin-right: -3px;

  }

  .pagination .page-link:last-child {
    margin-left: -3px;
    border: 0;
  }

  .pagination .page-link {
    font-size: 12px;
    color: #999999;
    border: 0;
    padding: 6px 10px;
    border-right: 1px solid #ddd;
    min-width: 29px;
    color: #333;

  }
  .pagination .page-link:hover {
    background-color: #eeeeee;
    border-color: #ddd;
    color: #333;
  }

  .page_nav_wrapper .pagination {
    border-radius: 50px;
    overflow: hidden;
    background-color: #fff;
  }

  .search-box {
    margin-top: 15px;
  }
  .activity-table {
    box-shadow: 2px 3px 2px rgba(0, 0, 0, .5);
    border-top-left-radius: 0.5em;
    border-top-right-radius: 1e
  }

  /* width */
  .activity-table-wrapper::-webkit-scrollbar {
    width: 20px;
  }

  /* Track */
  .activity-table-wrapper::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey; 
    border-radius: 10px;
  }
  
  /* Handle */
  .activity-table-wrapper::-webkit-scrollbar-thumb {
    background: #558641; 
    border-radius: 10px;
  }

  /* Handle on hover */
  .activity-table-wrapper::-webkit-scrollbar-thumb:hover {
    background: #558641; 
  }
  .activity-table th {
    min-width: 220px;
  }
</style>