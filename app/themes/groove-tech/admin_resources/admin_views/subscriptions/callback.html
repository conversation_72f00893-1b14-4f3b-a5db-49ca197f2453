<div class="row">
  <div class="box-outer">
    <div class="box">
      <div class="pull-right" style="margin-right: 5px;">
        <small ng-click="existing_customer()">Existing customer?</small>
        <br/>

        <!-- <%= link_to "Login", subscriptions_login_path(shop: shop), class: "btn btn-xs btn-success btn-block" %> -->
      </div>

    </div>
    <div class="col-sm-offset-3 col-sm-6">
      <img src="/assets/images/logo.png" class="col-xs-12" alt="GroovePacker"/>
    </div>
  </div>
</div>
<div class="row bottom-well col-sm-offset-2 col-md-offset-2 col-lg-offset-2 col-md-8 col-sm-8 col-lg-8">
  <div class="row">
    <div class="col-md-offset-2 col-md-8 well-header text-center">
      <p>Setup Groovepacker with <br/>{{shop_name}}</p>
    </div>
  </div>
  <div>
    <form action="" method="POST" class="form-vertical">
      <fieldset>
        <div class="form-group">
          <label for="email" class="control-label">Enter your email</label>
          <input type="email" id="tenant_email" ng-model="subscription.email" ng-blur="validate_email()" autofocus class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.email_message}}</div>
          </div>
        </div>

        <button id="continue_button" class="btn btn-primary btn-disabled"
                style="margin-bottom: 15px;" ng-click="continue_button()">
          Continue
        </button>
      </fieldset>
    </form>

    <script type="text/javascript">
      $(document).on('ready page:load', function () {
        var valid = {};

        $('#continue_button').on('click', function (e) {
          e.preventDefault();
          validate_email();
          if (valid.email) {
            var valid_host = '<%=ENV["HOST_NAME"]%>';
            url = 'http://admin.' + valid_host + '/subscriptions/select_plan?email=' +
              encodeURIComponent($('#tenant_email').val()) + '&shop_name=<%=@shop_name%>&shop_type=Shopify';
            window.location.href = url;
          }
        });

        var validate_email = function () {
          var email = $('#tenant_email').val();
          var atpos = email.indexOf("@");
          var dotpos = email.lastIndexOf(".");
          if (email == null || email == "") {
            valid.email = false;
            $('#error-email-block').html("email must be filled out");
          }
          else if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
            valid.email = false;
            $('#error-email-block').html("email is not valid");
          } else {
            $.ajax({
              type: "GET",
              contentType: "application/json; charset=utf-8",
              url: "/subscriptions/valid_email",
              data: {email: email},
              dataType: "json",
              async: false
            }).error(function (response) {

            }).success(function (response) {
              if (!response.valid) {
                $('#error-email-block').html(email + " email already exists");
                valid.email = false;
              } else {
                $('#error-email-block').html("");
                valid.email = true;
              }
            });
          }
        }

        $('#tenant_email').blur(function () {
          validate_email();
        });
      });
    </script>
  </div>
</div>
