<!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  </script>
<noscript>
  <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=****************&ev=PageView&noscript=1"/>
</noscript>
<!-- End Facebook Pixel Code -->

<div class="row">
  <div class="box-outer">
    <div class="box"></div>
    <div class="col-sm-offset-3 col-sm-6">
      <img src="/assets/images/logo.png" class="col-xs-12" alt="GroovePacker"/>
    </div>
  </div>
</div>

<div class="row bottom-well col-sm-offset-3 col-md-offset-3 col-lg-offset-3 col-md-6 col-sm-6 col-lg-6">
  <legend class="text-center">Account Setup</legend>
  <div>
    <form action="" method="POST" class="form-vertical" name="myForm" id="my_form">
      <fieldset>
        <div class="form-group">
          <input type="hidden" id="plan_id" ng-model="subscription.plan_id"/>
          <label class="control-label">Choose your site address*</label>

          <div class="input-group">
            <div class="input-group-addon">https://</div>
            <input type="search" id="tenant_name" class="form-control" autofocus ng-model="subscription.shop_name" ng-blur="validate_tenant_name()"/>

            <div class="input-group-addon">.groovepacker.com</div>
          </div>
          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.message}}</div>
          </div>
        </div>

        <div class="form-group">
          <label for="email" class="control-label">Email*</label>
          <input type="email" id="email" class="form-control"
                 ng-model="subscription.email" ng-blur="validate_email()"/>

          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.email_message}}</div>
          </div>
        </div>

        <hr>

        <div class="form-group">
          <label for="user_name" class="control-label">Username*</label>
          <input type="text" id="user_name" ng-model="subscription.user_name" class="form-control" ng-blur="validate_user_name()"/>

          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.user_name_message}}</div>
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="control-label">Password*</label>
          <input type="password" id="password" ng-model="subscription.password" ng-blur="validate_password()" class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.password_message}}</div>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label">Password Confirmation*</label>
          <input type="password" id="password_conf" ng-model="subscription.password_conf" ng-blur="validate_password_conf()" class="form-control"/>

          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.password_conf_message}}</div>
          </div>
        </div>

        <div class="form-group">
          <div class="checkbox control-label" style="font-weight:bold;"><input type="checkbox" id="tos_checkbox" ng-model="subscription.tos_checkbox" ng-blur="validate_tos_checkbox()"/>
            I agree with the <a href="" id="tos_link">Terms of Service</a> and
            <a href="" id="privacy_link">Privacy Policy</a></div>
          <div class="form-group error" style="color: red;">
            <div class="help-inline">{{subscription.tos_checkbox_message}}</div>
          </div>
        </div>
        <div class="form-group" id="privacy_iframe" style="display:none;">
          <iframe src="/assets/views/privacypolicy.html" width="100%" height="200px"></iframe>
        </div>
        <div class="form-group" id="tos_iframe" style="display:none;">
          <iframe src="/assets/views/termsofservicetos.html" width="100%" height="200px"></iframe>
        </div>
        <hr width="95%">
        <!-- <% if shop_name.blank? %> -->
        <div ng-if="shop_type==''">
          <div>
            <h4>
              <center>Risk Free 30-day Evaluation</center>
            </h4>
          </div>
          <div style="padding-left:12px;font-size: 14px;">
            <label>If you are not completely satisfied, please let us know at any time during the evaluation period and
              all charges will be credited in full.</label>
          </div>
          <br/>

          <div class="form-group">
            <label for="coupon_id" class="control-label">Promotional Code</label>

            <div class="input-group">
              <input type="text" id="coupon_id" ng-model="subscription.coupon_id" class="form-control"/>
              <span class="input-group-btn">
                <button id="apply" ng-click="apply()" class="btn btn-primary">Apply</button>
              </span>
            </div>
            <div class="form-group error">
              <div ng-if="subscription.show_coupon_block == false" class="help-inline" style="color: green;">
                {{subscription.success_coupon}}
              </div>
              <div ng-if="subscription.show_coupon_block ==  true" class="help-inline" style="color: red;">{{subscription.error_coupon}}</div>
            </div>
          </div>

          <script src="https://checkout.stripe.com/checkout.js"></script>
          <script type="text/javascript" src="https://js.stripe.com/v3/"></script>
          <div class="">
            <button ng-click="custom_button('regular')" class="btn btn-primary" style="margin-bottom: 15px;">Purchase</button>

            <div class="alert alert-success" id="processing_alert_success" style="display:none;" ng-show="subscription.not_valid">
              <div><i class="glyphicon glyphicon-refresh spin" style="font-size: 12px;"></i> Your subscription is being
                processed.
              </div>
            </div>
            <div class="alert alert-danger" id="processing_alert_error" ng-model = "subscription.processing_alert_error" style="display:none;">
            </div>
          </div>
        </div>
        <div class="row" ng-if="shop_type!=''">
          <div class="col-md-offset-3 col-md-6 well-header text-center">
            <p>Risk Free Trial Guarantee</p>
          </div>
        </div>
        <div class="form-group" ng-if="shop_type=='BigCommerce'">
          <label for="coupon_id" class="control-label">Promotional Code</label>
          <div class="input-group">
            <input type="text" id="coupon_id" class="form-control" ng-model="subscription.coupon_id"/>
            <span class="input-group-btn">
              <button id="apply" ng-click="apply()" class="btn btn-primary">Apply</button>
            </span>
          </div>
          <div class="form-group error">
            <div ng-if="subscription.show_coupon_block == false" class="help-inline" style="color: green;">
              {{subscription.success_coupon}}
            </div>
            <div ng-if="subscription.show_coupon_block ==  true" class="help-inline" style="color: red;">{{subscription.error_coupon}}</div>
          </div>
         <!--  <div class="form-group error">
            <div id="success-coupon-block" class="help-inline" style="color: green;"></div>
            <div id="error-coupon-block" class="help-inline" style="color: red;"></div>
          </div> -->
        </div>
        <div class="form-group" ng-if="shop_type!='BigCommerce' && shop_type!=''">
          <div class="control-label">
            <i class="fa fa-check-square-o"></i>
            Only the one time startup fee explained on the previous page will be charged today. It is fully refundable should you not decide to continue please email <a href="mailto:<EMAIL>"><EMAIL></a> any time during the free trial.
          </div>
        </div>

        <div class="form-group" ng-if="shop_type=='BigCommerce' || shop_type=='Shopify'">
          <div class="control-label">
            <i class="fa fa-check-square-o"></i>
            Your details are encrypted and secured by Stripe, a certified. Level 1 PCI Service Provider
            <small>(the most stringent level of certification)</small>
          </div>
        </div>
        <div class="form-group" ng-if="shop_type=='Shopify'">
          <div class="control-label">
            <i class="fa fa-check-square-o"></i>
            If you do not see the shopify window please look for an icon in your URL bar that looks like this: IMAGE
            Click this to view the popup and continue.
          </div>
        </div>
        <div class="form-group" ng-if="shop_type!='Shopify' && shop_type!=''">
          <div class="control-label">
            <i class="fa fa-check-square-o"></i>
            Only the one time startup fee explained on the previous page will be charged today. NOTE: When confirming the charge in a moment Shopify's Billing API will say it is Non-Refundable but in fact it can be refunded on request during the trial period. If you should you not decide to continue please email <a href="mailto:<EMAIL>"><EMAIL></a>  any time during the free trial.
          </div>
        </div>
        <div class="text-center">
          <script src="https://checkout.stripe.com/checkout.js"></script>
          <button groov-popover="<img class='icons' src='/assets/images/popup-icon.png'></img> <b> If popups are blocked by your browser please allow the popup to continue.<b>" ng-if="shop_type=='Shopify'" id='auth_button' class="btn btn-success" style="margin-bottom: 5px;" ng-click="auth_button()">Groovy, Start My Trial!</button>
          <button groov-popover="<img class='icons' src='/assets/images/popup-icon.png'></img> <b> If popups are blocked by your browser please allow the popup to continue.<b>" ng-if="shop_type=='BigCommerce'" ng-click="custom_button('BigCommerce')" id='shopButton' class="btn btn-success" style="margin-bottom: 5px;">Groovy, Start My Trial!</button>

          <div ng-if="success==true" class="alert alert-success" id="processing_alert_success">
            <div><i class="glyphicon glyphicon-refresh spin" style="font-size: 12px;"></i> Your subscription is being processed.
            </div>
          </div>
          <div ng-if="success==false" class="alert alert-error" id="processing_alert_error">
            <p>Your subscription could not be processed. Please try again.</p>
          </div>
        </div>

<script type="text/javascript">
// $('#auth_button').on('click', function(e){
//   e.preventDefault();
//   var getUrlParameter = function getUrlParameter(sParam) {
//       var sPageURL = decodeURIComponent(window.location.search.substring(1)),
//           sURLVariables = sPageURL.split('&'),
//           sParameterName,
//           i;

//       for (i = 0; i < sURLVariables.length; i++) {
//           sParameterName = sURLVariables[i].split('=');

//           if (sParameterName[0] === sParam) {
//               return sParameterName[1] === undefined ? true : sParameterName[1];
//           }
//       }
//   };
//   $.ajax({
//     type: "POST",
//     contentType: "application/json; charset=utf-8",
//     url: "/shopify/get_auth",
//     async: false ,
//     data: JSON.stringify({
//       "shop_name": getUrlParameter("shop_name"),
//       "name": getUrlParameter("plan_id"),
//       "email": getUrlParameter("email"),
//     }),
//     dataType: "json"
//   }).success(function (response) {
//     window.open(response.permission_url,"Ratting","width=1250,height=1000,0,status=0");
//   });
// });

  $(document).ready(function () {
    $('#cc_info').popover({
      html: true,
      viewport: '.gradient-well-inner'
    });
  });
</script>

        <input type="radio" id="radio_subscription1" style="display: none;" name="radio_subscription" value="monthly" checked/>
          <input type="radio" id="radio_subscription2" name="radio_subscription" value="annually" style="-webkit-appearance:block; background:green; display: none;"/>
      </fieldset>
    </form>
    <script>
      $(document).ready(function(){
        var subscription_type = "<%= params['radio_subscription'] %>";
        if(subscription_type=="annually") {
          $("[type='radio'][value='annually']").prop('checked',true);
        } else {
          $("[type='radio'][value='monthly']").prop('checked',true);
        }

        $('html').click(function () {
          $('#tos_iframe').hide();
          $('#privacy_iframe').hide();
        });

        $('#tos_link').click(function (event) {
          event.preventDefault();
          event.stopPropagation();
          $('#privacy_iframe').hide();
          $('#tos_iframe').show();
        });

        $('#privacy_link').click(function (event) {
          event.preventDefault();
          event.stopPropagation();
          $('#tos_iframe').hide();
          $('#privacy_iframe').show();
        });
      });

      // var valid = {
      //   tenant_name: false,
      //   email: false,
      //   user_name: false,
      //   password: false,
      //   password_conf: false,
      //   tos: false
      // };

      // var radio_button_value = null;
      // var amount = 0;
      // var plan_id = null;
      // var coupon_id = null;
      // var one_time_payment = $('#one_time_payment').val();
      // var show_coupon_block = false;

      // $('#shopButton').on('click', function (event) {
      //   event.preventDefault();
      //   if ($("input[name='radio_subscription']:checked").length > 0) {
      //     radio_button_value = $('input:radio[name=radio_subscription]:checked').val();
      //     if (radio_button_value == 'annually') {
      //       var str = $('#plan_id').val();
      //       plan_id = 'an-' + str;
      //     }
      //     else {
      //       plan_id = $('#plan_id').val();
      //     }
      //     // open_handler(plan_id, "<%= params[:shop_name] %>");
      //   }
      // })

      // window.CallParent = function() {
      //   plan_id = $('#plan_id').val();
      //   if ($("input[name='radio_subscription']:checked").length > 0) {
      //     radio_button_value = $('input:radio[name=radio_subscription]:checked').val();
      //     var str = $('#plan_id').val();
      //     plan_id = str;
      //     if (radio_button_value == 'annually') {
      //       plan_id = 'an-' + str;
      //     }
      //   }
      //   request_tenant_creation();
      // }

      // var handler = StripeCheckout.configure({
      //   key: $('#stripe_public_key').val(),
      //   image: 'https://admin.groovepacker.com/assets/images/apple-touch-icon.png',
      //   token: function (token) {
      //     request_tenant_creation(token);
      //   }
      // });

      // function request_tenant_creation(token={}) {
      //   $.ajax({
      //       type: "POST",
      //       contentType: "application/json; charset=utf-8",
      //       url: "/subscriptions/confirm_payment",
      //       data: JSON.stringify({
      //         tenant_name: $('#tenant_name').val(),
      //         stripe_user_token: token.id,
      //         email: $('#email').val(),
      //         amount: amount,
      //         plan_id: plan_id,
      //         user_name: $('#user_name').val(),
      //         password: $('#password').val(),
      //         radio_subscription: radio_button_value,
      //         coupon_id: coupon_id,
      //         shop_name: "<%= params[:shop_name] %>",
      //         shop_type: "<%= params[:shop_type] %>"
      //       }),
      //       dataType: "json"

      //     }).error(function (response) {
      //       $('#processing_alert_success').hide();
      //       $('#customButton').show();
      //       $('#shopButton').show();
      //       $('#processing_alert_error').show();
      //       $('#processing_alert_error').append(
      //         "<p>Your subscription could not be processed. Please try again.</p>"
      //       )
      //     }).success(function (response) {
      //       if (!response.valid) {
      //         $('#processing_alert_success').hide();
      //         $('#customButton').show();
      //         $('#shopButton').show();
      //         $('#processing_alert_error').show();
      //         $('#processing_alert_error').append(
      //           "<p>" + response.errors + "</p>"
      //         )
      //       }
      //       else {
      //         var redirect_url = '/subscriptions/show?transaction_id=' + response.transaction_id + '&notice=' + response.notice + '&email=' + response.email + '&next_date=' + response.next_date + '&store=' + response.store
      //         window.location.href = redirect_url
      //       }
      //     });
      //     $('#customButton').hide();
      //     $('#shopButton').hide();
      //     $('#processing_alert_success').show();
      //     $('#processing_alert_error').hide();
      // }

      // $(document).ready(function () {
      //   show_coupon_block = false;
      //   // checkOneTimePayment();
      // });

      // $('#apply').on('click', function (a) {
      //   a.preventDefault();
      //   show_coupon_block = true;
      //   // checkOneTimePayment();
      // });

      // var checkOneTimePayment = function () {
      //   var coupon = $('#coupon_id').val();
      //   if (coupon != '') {
      //     $.ajax({
      //       type: "GET",
      //       contentType: "application/json; charset=utf-8",
      //       url: "/subscriptions/validate_coupon_id",
      //       data: {coupon_id: $('#coupon_id').val()},
      //       dataType: "json"
      //     }).error(function (response) {

      //     }).success(function (response) {
      //       if (response.status == false) {
      //         coupon_id = null;
      //         one_time_payment = $('#one_time_payment').val();
      //         console.log(one_time_payment);
      //         if (show_coupon_block) {
      //           $('#error-coupon-block').show();
      //           $('#success-coupon-block').hide();
      //         }
      //         ;
      //         $('#error-coupon-block').html(response.messages);
      //         $('#monthly_discount_label').hide();
      //         $('#annual_discount_label').hide();
      //         $('#monthly_billing_label').show();
      //         $('#annual_billing_label').show();
      //       }
      //       else {
      //         coupon_id = $('#coupon_id').val();
      //         one_time_payment = $('#one_time_payment').val() - response.discount_amount;
      //         if (show_coupon_block) {
      //           $('#error-coupon-block').hide();
      //           $('#success-coupon-block').show();
      //           $('#success-coupon-block').html(response.messages);
      //         }
      //         ;
      //         $('#monthly_billing_label').hide();
      //         $('#monthly_discount_label').show();
      //         $('#monthly_discount_label').html('The first monthly billing will be charged after 30 days. A one time initialization charge of <strike>$500</strike> $' + one_time_payment / 100 + '($' + response.discount_amount / 100 + ' off) is paid today on deployment.');
      //         $('#annual_billing_label').hide();
      //         $('#annual_discount_label').show();
      //         $('#annual_discount_label').html('The first annual billing will be charged after 30 days at a 10% discount. A one time initialization charge of <strike>$500</strike> $' + one_time_payment / 100 + '($' + response.discount_amount / 100 + ' off) is paid today on deployment.');
      //       }
      //     });
      //   }
      //   ;
      // };

      // $('#tenant_name').blur(function () {
      //   // validate_tenant_name();
      // });

      // $('#email').blur(function () {
      //   Subscription.validate_email();
      // });

      // $('#user_name').blur(function () {
      //   Subscription.validate_username();
      // });

      // $('#password').blur(function () {
      //   Subscription.validate_password();
      // });

      // $('#password_conf').blur(function () {
      //   Subscription.validate_password_confirmation();
      // });

      // $('#tos_checkbox').click(function () {
      //   Subscription.validate_tos();
      // });

      // var open_handler = function (plan_id, channel) {
      //   $.ajax({
      //     type: "GET",
      //     contentType: "application/json; charset=utf-8",
      //     url: "/subscriptions/plan_info",
      //     data: {plan_id: plan_id},
      //     dataType: "json"
      //   }).error(function (response) {

      //   }).success(function (response) {
      //     if (response.status == true) {
      //       amount = response.plan_info.amount;
      //     }
      //     setTimeout(function () {
      //       if (Subscription.validate()) {
      //         // Open Checkout with further options
      //         var description = null;
      //         var amount = 0;
      //         if (channel == 'Shopify' || channel == 'BigCommerce') {
      //           amount = 0;
      //           // amount = Number(one_time_payment / 100).toFixed(2);
      //           description = 'Add a card to your account';
      //         } else {
      //           amount = one_time_payment;
      //           description = 'Initialization fee ($' +
      //             Number(one_time_payment / 100).toFixed(2) + ')';
      //         }

      //         handler.open({
      //           name: 'Groovepacker',
      //           description: description,
      //           amount: amount,
      //           email: $('#email').val(),
      //           allowRememberMe: false
      //         });
      //       }
      //     }, 400);
      //   });
      // }
      // $('#customButton').on('click', function (e) {
      //   e.preventDefault();
      //   if ($("input[name='radio_subscription']:checked").length > 0) {
      //     radio_button_value = $('input:radio[name=radio_subscription]:checked').val();
      //     if (radio_button_value == 'annually') {
      //       var str = $('#plan_id').val();
      //       //var plan_str = str.substring(0,str.lastIndexOf('-'));
      //       plan_id = 'an-' + str;
      //     }
      //     else {
      //       plan_id = $('#plan_id').val();
      //     }
      //     // open_handler(plan_id, "regular");
      //   }
      // });

    </script>

    <script type="text/javascript" src="https://static.leaddyno.com/js"></script>
    <script>
       // LeadDyno.key = "43a989cd4a60d892c484bbfc92d2db94da03236b";
       // LeadDyno.recordVisit();
       // LeadDyno.autoWatch();
    </script>
  </div>
</div>
</div>

<style>
  .container-well {
    background: none;
    background-color: #ececec;
    font-family: 'Poppins';
}
.gradient-well-inner{
  background: none;
  background-color: #69829a;
}
</style>
