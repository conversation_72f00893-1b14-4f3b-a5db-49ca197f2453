<head>
  <!-- <title>Please Complete the following profile while your GroovePacker is initialized.</title> -->
  <!-- <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Roboto:400,700">

  <link href='/static/forms/client/css/1292319201-formview_st_ltr.css' type='text/css' rel='stylesheet'> -->
  <style type="text/css">
    body {
      /*background-color: rgb(230,230,230);*/
    }

    .ss-form-container, .ss-resp-card {
      /*background-color: rgb(255,255,255);*/
    }

    .ss-footer, .ss-response-footer {
      /*background-color: rgb(255,255,255);*/
    }

    .ss-grid-row-odd {
      /*background-color: rgb(242,242,242);*/
    }

    .ss-form-container, .ss-resp-card {
      /*border-color: rgb(212,212,212);*/
    }

    .ss-form-title {
      text-align: left;
    }

    .ss-form-title[dir="rtl"] {
      text-align: right;
    }

    .ss-form-desc {
      text-align: left;
    }

    .ss-form-desc[dir="rtl"] {
      text-align: right;
    }

    .ss-header-image-container {
      height: 0;
    }

    .ss-item {
      /*font-size: 1.080rem;*/
    }

    .ss-choices {
      /*font-size: 1.000rem;*/
    }

    .ss-q-help, .ss-secondary-text {
      margin-left: 20px;
      font-weight: normal;
    }

    .ss-year-dropdown, .ss-day-dropdown {
      margin-left: 0px;
    }

    .ss-secondary-text > ul li {
      list-style-type: disc;
    }

    .jfk-button, .jfk-button-action {
      width: 80px;
      height: 35px;
      position: absolute;
    }

    body {
      /*font-family: "Roboto";
      color: rgb(119,119,119);
      font-weight: 400;
      font-size: 1.5rem;
      font-style: normal;*/
    }

    div input {
      margin-left: 40px;
      border-radius: 5px;
      border-color: rgb(166, 166, 166);
      border-width: 1px;
      /*width: 300px;
      height: 40px;*/
    }

    div select {
      margin-left: 40px;
      border-radius: 5px;
      height: 30px;
    }

    textarea {
      border-radius: 5px;
      width: 400px;
      height: 100px;
    }

    .ss-q-short {
      width: 300px;
      height: 30px;
    }

    .ss-q-other {
      height: 30px;
    }

    .ss-record-username-message {
      /*font-family: "Roboto";
      color: rgb(119,119,119);
      font-weight: 400;
      font-size: 1.080rem;
      font-style: normal;*/
    }

    .ss-form-title {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 2.460rem;
      font-style: normal;*/
    }

    .ss-confirmation {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 2.460rem;
      font-style: normal;*/
    }

    .ss-page-title, .ss-section-title {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 1.845rem;
      font-style: normal;*/
    }

    .ss-form-desc, .ss-page-description, .ss-section-description {
      /*font-family: "Roboto";
      color: rgb(140,140,140);
      font-weight: 400;
      font-size: 1.080rem;
      font-style: normal;*/
    }

    .ss-resp-content {
      /*font-family: "Roboto";
      color: rgb(119,119,119);
      font-weight: 400;
      font-size: 1.080rem;
      font-style: normal;*/
    }

    .ss-q-title {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 700;
      font-size: 1.080rem;
      font-style: normal;*/
    }

    .ss-embeddable-object-container .ss-q-title {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 700;
      font-size: 1.845rem;
      font-style: normal;*/
    }

    .ss-q-help, .ss-q-time-hint {
      /*font-family: "Roboto";
      color: rgb(140,140,140);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;*/
    }

    .ss-choice-label, .video-secondary-text, .ss-gridrow-leftlabel, .ss-gridnumber, .ss-scalenumber, .ss-leftlabel, .ss-rightlabel {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;*/
    }

    .error-message, .required-message, .ss-required-asterisk {
      font-family: "Roboto";
      color: rgb(196, 59, 29);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;
    }

    .ss-send-email-receipt {
      /*font-family: "Roboto";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;*/
    }

    .ss-password-warning {
      /*font-family: "Arial";
      color: rgb(119,119,119);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: italic;*/
    }

    .disclaimer {
      /*font-family: "Arial";
      color: rgb(119,119,119);
      font-weight: 400;
      font-size: 0.850rem;
      font-style: normal;*/
    }

    .ss-footer-content {
      /*font-family: "Arial";
      color: rgb(80,80,80);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;*/
    }

    .progress-label {
      /*font-family: "Roboto";
      color: rgb(140,140,140);
      font-weight: 400;
      font-size: 1.000rem;
      font-style: normal;*/
    }

    a:link {
      color: rgb(0, 0, 238);
    }

    a:visited {
      color: rgb(85, 26, 139);
    }

    a:active {
      color: rgb(252, 0, 0);
    }

    input[type='text'], input:not([type]), textarea {
      /*font-size: 1.000rem;*/
    }

    .error, .required, .errorbox-bad {
      border-color: rgb(196, 59, 29);
    }

    .jfk-progressBar-nonBlocking .progress-bar-thumb {
      /*background-color: rgb(140,140,140);*/
    }

    .ss-logo-image {
      background-image: url('//ssl.gstatic.com/docs/forms/forms_logo_small_dark.png');
      background-size: 108px 21px;
      width: 108px;
      height: 21px;
    }

    @media screen and (-webkit-device-pixel-ratio: 2) {
      .ss-logo-image {
        background-image: url('//ssl.gstatic.com/docs/forms/forms_logo_small_dark_2x.png');
      }
    }

  </style>
  <script type="text/javascript">
    var capterra_vkey = 'f2a34b4c94bd1e4dc870d5fda4725bc4',
      capterra_vid = '2100772',
      capterra_prefix = (('https:' == document.location.protocol) ? 'https://ct.capterra.com' : 'http://ct.capterra.com');
    (function () {
        var ct = document.createElement('script');
        ct.type = 'text/javascript';
        ct.async = true;
        ct.src = capterra_prefix + '/capterra_tracker.js?vid=' + capterra_vid + '&vkey=' + capterra_vkey;
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ct, s);
      })();
  </script>
</head>
<body>
<!-- Google Code for New Subscription Conversion Page -->
<script type="text/javascript">
  /* <![CDATA[ */
  var google_conversion_id = 953394465;
  var google_conversion_language = "en";
  var google_conversion_format = "3";
  var google_conversion_color = "ffffff";
  var google_conversion_label = "sDccCPXf3FoQocrOxgM";
  var google_conversion_value = 500.00;
  var google_conversion_currency = "USD";
  var google_remarketing_only = false;
  /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>

<div class="row">
  <div class="box-outer">
    <div class="box"></div>
    <div style="text-align: center;">
      <img src="/assets/images/logo.png" class="" alt="GroovePacker"/>
    </div>
  </div>
</div>
<div class="row bottom-well col-sm-offset-1 col-sm-10">
  <div class="row" ng-if="shop_type ==''">
  <div class="col-sm-12" style="font-size: 30px;">
    <center><b><i>
      {{subscription.notice}}
    </i></b></center>
    <hr>
  </div>
  </div>
  <div class="row" ng-if="shop_type =='' || shop_type =='BigCommerce'">
    <div class="col-sm-12">
      Payment of your initialization fee is complete. Your transaction id is: {{subscription.transaction_id}}. Your
      subscription will begin billing at the end of your evaluation period on {{subscription.next_date}}. Please finish your account creation by clicking "NEXT" below and anserwing the questions that follow.
    </div>
  </div>
  <div class="row" ng-if="shop_type =='Shopify'">
    <div class="col-sm-12">
      <h3>Nice Work! Your shopify connection is complete! </h3>

      <p>
        When your server deployment is complete, an email will be sent to <i>{{subscription.email}}</i>providing you the login
        details for your account.
      </p>
    </div>
  </div>
  <!-- <div class="row" ng-if="shop_type =='Shopify'">
    <div class="col-sm-12">
      <h3>Nice Work! Your account setup is complete. </h3>

      <p>
        Please complete your account creation by clicking continue below.
      </p>

      <p class="alert alert-danger">
        There was an error authenticating with your Shopify account. Please login with the details in the email to
        resolve the connection issue.
      </p>
    </div>
  </div> -->
  <br/>
  <div class="row">
    <div class="col-sm-12">
      <iframe ng-if="shop_type == 'BigCommerce'" src="https://docs.google.com/forms/d/e/1FAIpQLSe3RSHuSAmJjHTq0MmoLRi7zlnOfMN_UO8tM45wJh2jfLVQ1w/viewform" height="1050px" width="100%"></iframe>
      <iframe ng-if="shop_type != 'BigCommerce'" src="https://docs.google.com/forms/d/1hVrkWJpYphaJukNpGLNQmlBpl5-cZkTyhtEecLQMO_I/formResponse" height="1050px" width="100%"></iframe>
    </div>
  </div>
  <script type="text/javascript" src="https://static.leaddyno.com/js"></script>
  <script>
     // LeadDyno.key = "43a989cd4a60d892c484bbfc92d2db94da03236b";
     // LeadDyno.recordVisit();
     // LeadDyno.autoWatch();
  </script>
</div>
</body>
