<!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '2941492595962136');
  fbq('track', 'PageView');
  </script>
<noscript>
  <img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=2941492595962136&ev=PageView&noscript=1"/>
</noscript>
<!-- End Facebook Pixel Code -->

<div class="pricing_page">
  <div class="row">
    <div class="box-outer">
      <div class="box">
        <!-- <%#= render 'shared/existing_customer', shop: @shop_name %> -->
      </div>
      <div class="col-sm-offset-3 col-sm-6">
        <img src="/assets/images/logo.png" class="col-xs-12" alt="GroovePacker"/>
      </div>
    </div>
  </div>
  <div class="back-groovepacker">
    Back To <a href="https://groovepacker.com">GroovePacker</a>
  </div>
  <div class="row col-md-11 setup_pricing">
    <div class="pricinguser bottom-well">



      <div class="">            
        <div class="row topheading">
          <h1><span>Setup &amp; Pricing</span></h1>
        </div>
        <div class="row topsec">
          <div class="col-md-7 col-sm-7 col-xs-12">
            <h3>1 on 1 VIP setup to ensure success</h3>
            <p>
              A one time $500 initialization will be collected when you
              create your account today. It is <u>fully refundable</u> 
              on cancellation any time during your free trial.
              Monthly billing will not begin until after your trial.
            </p>
          </div>
          <div class="col-md-5 col-sm-5 col-xs-12">
            <h3>Your Initialization will include:</h3>
            <ul>
              <span></span>
              <li class="data-merging">Initial Data Merging and Product Import</li>
              <li class="server">Server Provisioning &amp; Account configuration</li>
              <li class="import-source">Import Source Setup &amp; Configuration</li>
              <li class="support">Unlimited Support &amp; Product Training</li>
            </ul>
          </div>
        </div>

        <div class="row smallrow">
          <div class="col-md-4 col-sm-4 col-xs-12">Simple Pricing - No contracts</div>
          <div class="col-md-4 col-sm-4 col-xs-12 text-center">Risk Free Startup &amp; Trial</div>
          <div class="col-md-3 col-sm-4 col-xs-12 text-right">Satisfaction Guaranteed</div>
        </div>

        <section class="priceingcontent">                
          <div class="row">
            <div class="col-md-7">
              <div class="row">
                <div class="col-md-8">
                  <h3 class="display_pricing_name">
                    <i class="fa fa-dropbox"></i> Unlimited Shipments
                  </h3>
                </div>
                <div class="col-md-3">
                  <h1>+ $0</h1>
                </div>
              </div>
              <div class="row">
                <div class="col-md-8">
                  <h3 class="display_pricing_name">
                    <i class="fa fa-cloud-download"></i> Unlimited Order Sources
                  </h3>
                </div>
                <div class="col-md-3">
                  <h1>+ $0</h1>
                </div>
              </div>
              <div class="row">
                <div class="col-md-8">
                  <h3 style="padding: 18px 5px 0px;">
                    <i class="fa fa-user"></i>
                    <a href="javascript:;" class="no_of_users_dec"><i class="fa fa-chevron-left"></i></a>
                    <span class="no_of_users_label">3</span>
                    <input type="hidden" class="no_of_users" ng-model="subscription.no_of_users" name="no_of_users" value="3"/>
                    <a href="javascript:;" class="no_of_users_inc"><i class="fa fa-chevron-right"></i></a>
                    Total Users
                    <p style="font-size: 18px; padding-top: 30px;">Select 2 or more users at $50/user </p>
                  </h3>
                </div>
                <div class="col-md-4">
                  <h1>+ $<span class="price_label">150<span></h1>
                </div>
              </div>
              <div class="row discount_section fade4">
                <div class="col-md-8">
                  <h3 style="padding: 30px 5px 0px;">
                    <input type="checkbox" class="bill_annually" name="bill_annually"> Bill Annually
                    <p style="font-size: 18px; padding-top: 30px;">Save $<span class="discount_annually">180</span> annually</p>
                  </h3>
                </div>
                <div class="col-md-4">
                  <h1>- $<span class="discount_amount">15</span></h1>
                </div>
              </div>
              <div class="row total">
                <div class="col-md-8">
                  <h3>
                    Price / Month:
                  </h3>
                </div>
                <div class="col-md-4">
                  <h1 style="padding-left: 20px;">$<span class="total_amount">150</span>
                  <img src="/assets/images/guarantee-txt.png" style="position: absolute; margin-left: 30px; margin-top: -10px;" alt="GroovePacker"/>
                  </h1>
                </div>
              </div>
            </div>
            <div class="col-md-5 right pd-rgt-nn">
              <div class="box never_pay_more">
                <span></span>
                <ul>
                  <li><i class="fa fa-angle-right"></i> Never pay more for increased volume.</li>
                  <li><i class="fa fa-angle-right"></i> Import orders from unlimited accounts.</li>
                  <li><i class="fa fa-angle-right"></i> Add or Remove users at any time.</li>
                </ul>
              </div>
              <div class="box never_pay_more">
                <span></span>
                <h2>More than 10K SKUs?</h2>
                <p>
                  Ask about pricing for high sku
                  plans if you have more than
                  10,000 SKUs in your inventory.
                </p>
              </div>
              <p>
                
              </p>
            </div>
          </div>
          <div class="row bttnrow price_font">
            <div class="col-md-5" style="width: 38%">
              <p>
                 <br>
              </p>
            </div>
            <div class="col-md-7 pd-none">
              <button class="btn get_started" ng-click="new_subscription()">Get Started</button>
            </div>
          </div>
          <div class="row terms">
            <div class="col-md-6 pull-right">
              <p>
                * If you are scanning daily orders with GroovePacker and are not saving  more than it costs, simply let us know and we'll cancel your account refund your last month's service!
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>



    <div class="row">
      <div class="col-md-offset-3 col-md-6 well-footer text-center" ng-if="subscription.plan_error">
        <p>{{subscription.plan_error}}</p>
      </div>
    </div>
  </div>
</div>
<style type="text/css">
  .gradient-well{
    max-width: 1400px;
  }
</style>
<!-- <script src="../custom.js"></script> -->
<script type="text/javascript">
  $(".get_started").click(function(){
    // var no_of_users = $('.no_of_users').val();
    // var checked = $(".bill_annually:checked").length;
    // var subscription_type = checked>0 ? "annually" : "monthly"
    // $state.go('login');
    // window.location = "/subscriptions/new?plan_id=GROOV-"+50*no_of_users+"&shop_name=<%=params[:shop_name]%>&shop_type=<%=params[:shop_type]%>&email="+encodeURIComponent("<%=params[:email]%>")+"&radio_subscription="+subscription_type;
  })

  $(document).ready(function(){
    $(".no_of_users").val(3);
    add_discount_if_billing_annually();
    $(".no_of_users_inc").click(function(){
      calculate("+");
    });
    $(".no_of_users_dec").click(function(){
      calculate("-");
    });

    $(".bill_annually").change(function(){
      add_discount_if_billing_annually();
    });

    function calculate(sign){
      var no_of_users = $('.no_of_users').val();
      var total_amount;
      no_of_users = parseFloat(no_of_users);
      if(sign=="+") {
        no_of_users = no_of_users+1;
      } else {
        if(no_of_users>2) {
          no_of_users = no_of_users-1;
        }
      }
      $(".price_label").text(50*no_of_users);
      $(".discount_amount").text(5*no_of_users);
      $(".no_of_users_label").text(no_of_users);
      $(".no_of_users").val(no_of_users);
      $(".discount_annually").text(60*no_of_users);
      add_discount_if_billing_annually();
    }

    function add_discount_if_billing_annually() {
      var no_of_users = $('.no_of_users').val();
      no_of_users = parseFloat(no_of_users);
      var checked = $(".bill_annually:checked").length;
      var total_amount;
      if(checked>0) {
        $('.discount_section').removeClass("fade4");
        total_amount = (50*no_of_users)-(5*no_of_users);
      } else {
        $('.discount_section').addClass("fade4");
        total_amount = 50*no_of_users;
      }
      $(".total_amount").text(total_amount);
    }
    
  });
</script>
