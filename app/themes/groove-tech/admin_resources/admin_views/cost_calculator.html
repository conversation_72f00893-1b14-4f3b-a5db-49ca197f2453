<!-- Facebook Pixel Code -->
<script>
    setTimeout(initFbPixelTracker, 1000);
    function initFbPixelTracker() {
      if (document.documentElement.innerText.indexOf('Tell your team') > -1) {
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '386811195928094');
        fbq('track', 'PageView');
      }
    }
  </script>
  <noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=386811195928094&ev=PageView&noscript=1"
  /></noscript>
  <!-- End Facebook Pixel Code -->

  <div class="errorcalculator">     
  <input type="hidden" id ="montly_expenses" name="montly_expenses" ng-model="cost.monthly_shipping">
    <div class="col-md-12 col-sm-12 col-xs-12">
      <div>
        <h4 style="width:100%; color: #333333">
          <b>
            <div contenteditable="true" class="text-center" ng-model="cost.cost_header" id="cost_header" name="cost_header" style="max-width: 100%; background-color: white;"  rows="1" cols="130">
            {{cost.cost_header || "Modify the estimates below to match your costs and see what your company is currently spending on errors."}}
            </div>
          </b>
        </h4>
      </div>
    </div>
    <div class="col-md-12 col-sm-12 col-xs-12">
      <form id="cost_calc">
        <div class="row out_box">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Error Volume</strong></h4>
            <h1><strong id="error_per_day">
            {{cost.error_per_day}}
            </strong></h1>
            <p>Errors Per day</p>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>Number of packers.</h4>
                  <input id="packer_count" type="text" name="packer_count" ng-model="cost.packer_count">
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>Number of orders an average packer packs per day.</h4>
                  <input id="order_count" type="text" name="order_count" ng-model="cost.order_count" >
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox right-inner-addon">
                  <h4>Error rate for the average packer.</h4>
                  <span style="padding: 7px 90px">%</span>
                  <input id="avg_error" type="text" name="avg_error" ng-model="cost.avg_error">
                </div>
              </div>
            </div>

          </div>
        </div>

        <h2>Tangible Costs</h2>
        <div class="row comunication out_box">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Communication & Customer Service Costs</strong></h4>
            <h1><strong>$<span id="cost_of_comm">{{cost.cost_of_comm}}</span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4 class="right-inner-addon"><span style="padding: 7px 170px">%</span>
                    <input id="regular_percentage" type="text" name="regular_percentage" ng-model="cost.regular_percentage"> &nbsp;are routine communications handled by regular staff.
                  </h4>
                  <h4 class="left-inner-addon">
                    The average cost is: <span style="padding: 7px 5px">$</span>&nbsp;<input id="regular_comm" type="text" name="regular_comm" ng-model="cost.regular_comm" style="width: 60px;">  per communication.
                  </h4>
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4><strong id="escalated_percentage">{{cost.escalated_percentage}}</strong><b>%</b> become escalated communications requiring the owner or management.</h4>
                  <h4 class="left-inner-addon">
                    The average cost is: <span style="padding: 16px 6px;">$</span>&nbsp;<input id="escalated_comm" type="text" name="escalated_comm" ng-model="cost.escalated_comm" style="width: 60px;">  per communication.
                  </h4>
                </div>
              </div>
              <div class="col-md-4 col-sm-4 col-xs-12">
                <div class="box overridebox">
                  <h4>The average number of communications required to resolve an error related issue.</h4>
                  <input id="avg_comm" type="text" name="avg_comm" ng-model="cost.avg_comm">
                </div>
              </div>
            </div>

            <h4>
              For example: One email or call when the issue is reported.<br> One email or call to let the customer know the reshipment is in in progress,<br> a final email or call to verify the customer has successfully received the item.<br> If the customer will be returning an incorrect item an additional email will likely be sent to the customer to let<br> them know it was recieved.
            </h4>

          </div>
        </div>



        <div class="row replacement out_box box_margin">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Replacement Shipment Costs</strong></h4>
            <h1><strong>$<span id="total_replacement_costs">{{cost.total_replacement_costs}}</span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox">

                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_ship_replacement" type="text" name="cost_ship_replacement" ng-model="cost.cost_ship_replacement" ></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                      <h4>
                         Postage for shipping the replacement item (include insurance and other applicable fees)
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="total_expedited" ng-model="cost.total_expedited" disabled></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                      <h4 class="right-inner-addon">
                        1 in <input id="expedited_count" type="text" name="expedited_count" ng-model="cost.expedited_count" > replacement shipments (<input id="expedited_percentage" type="text" name="expedited_percentage" ng-model="cost.expedited_percentage"><span class="override_span input_align">%</span>) require expedited shipping to prevent loosing the customer. The average cost of expedited shipping is 
                        <addon class="left-inner-addon">
                          &nbsp;&nbsp;<span class="override_span input_align">$</span>
                          <input type="text" id="expedited_avg" name="expedited_avg" ng-model="cost.expedited_avg" style="margin: 4px -15px 10px">
                        </addon>
                      </h4>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="total_international" mg-model="cost.total_international" value="&nbsp; &nbsp; &nbsp;" disabled></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4 class="right-inner-addon">
                         1 in <input id="international_count" type="text" name="international_count" ng-model="cost.international_count"> replacement shipments (<input id="international_percentage" type="text" name="international_percentage" ng-model="cost.international_percentage" ><span class="override_span input_align">%</span> ) require international shipping to prevent loosing the customer. The average cost of international shipping is <addon class="left-inner-addon">
                         &nbsp;&nbsp;<span class="override_span input_align">$</span>
                         <input id="avg_order_profit" type="text" name="avg_order_profit" ng-model="cost.avg_order_profit" style="margin: 4px -15px 10px"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="reshipment" type="text" name="reshipment" ng-model="cost.reshipment"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                        Estimated cost of Box, Packing Paper, Label, Tape & printed or promotional materials in Reshipment
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_labor_reshipment" type="text" name="cost_labor_reshipment" ng-model="cost.cost_labor_reshipment"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                         Cost of labor for pick pack on reshipment
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 col-sm-1 col-xs-12 pd-none left-inner-addon">
                    <span>$</span>
                    <input id="cost_apology" type="text" name="cost_apology" ng-model="cost.cost_apology"></div>
                    <div class="col-md-11 col-sm-11 col-xs-12">
                     <h4>
                        Cost of Gift/Apology item
                      </h4>
                    </div>
                  </div>

                </div>
                <h4>
                  These costs are related to getting the correct or missing item(s) to the customer after the error is reported.
                  This cost will be part of every error except when the customer cancels the order as a result of the error.
                  Return shipment costs related to the customer returning an incorrect item are covered in the next section.
                </h4>
              </div>

            </div>

            


          </div>
        </div>


        <div class="row replacement out_box box_margin">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4 class="box_margin"><strong>Return Shipment OR Product Abandonment</strong></h4>
            <h1><strong>$<span id="return_shipment_or_abandonment">{{cost.return_shipment_or_abandonment}}
            </span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none out_box_margin">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox">
                  <h4>
                    Any time an incorrect item is sent there will either be a return shipment cost or a product abandonment cost. The calculation is based on an incorrect item getting sent in <addon class="right-inner-addon"><input type="text" id="total_error_shipment" name="total_error_shipment" ng-model="cost.total_error_shipment"><span class="override_span input_align">%</span></addon> of the error shipments.
                  </h4>
                  <h4 class="right-inner-addon">
                    In <addon class="right-inner-addon"><input type="text" id="product_abandonment_percentage" ng-model="cost.product_abandonment_percentage" ><span class="override_span input_align">%</span></addon> of these instances the incorrect item will be gifted to the customer as the cost is less than the cost of the return. The average cost of the abandoned item is <addon class="left-inner-addon">&nbsp;&nbsp;<span class="override_span input_align">$</span> <input id="avg_product_abandonment" type="text" name="avg_product_abandonment" ng-model="cost.avg_product_abandonment" style="margin: 4px -15px 10px"></addon>
                  </h4>
                  <h4 class="right-inner-addon">
                    In <input id="return_shipping_percentage" type="text" name="return_shipping_percentage" ng-model="cost.return_shipping_percentage"><span class="override_span input_align">%</span> of these instances the incorrect item will need to be returned and the list of costs below will apply:
                  </h4>
                  <ul>
                    <li class="left-inner-addon"><span>$</span>
                      <input type="text" id="cost_return" name="cost_return" ng-model="cost.cost_return" > Optional Cost of return shipping to 3PL and associated 3PL fees
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="return_shipping_cost" type="text" name="return_shipping_cost" ng-model="cost.return_shipping_cost"> 6.00 Cost of return postage from the Customer (or 3PL facility)
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="return_shipping_insurance" type="text" name="return_shipping_insurance" ng-model="cost.return_shipping_insurance"> Cost of insurance and or package tracking on return shipment
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="cost_recieving_process" type="text" name="cost_recieving_process" ng-model="cost.cost_recieving_process"> Cost of receiving and processing the physical return
                    </li>
                    <li class="left-inner-addon"><span>$</span>
                      <input id="cost_confirm" type="text" name="cost_confirm" ng-model="cost.cost_confirm"> Cost of communication to confirm it's receipt with the customer or follow up if it is not received as expected.
                    </li>
                  </ul>
                  
                </div>
              </div>
              
            </div>

            <h4>
              These costs only apply when the wrong item has been sent to the customer and will need to be returned or abandoned because the cost of bringing it back is more than the cost of the product.
            </h4>

          </div>
        </div>


        <h2>Intangible Costs</h2>
        <div class="row replacement out_box" style="padding-bottom: 10px;">
          <div class="col-md-2 col-sm-2 col-xs-12 text-center left pd-none">
            <h4></h4>
            <br><br><br><br>
            <h1><strong>$<span id="intangible_cost">{{cost.intangible_cost}}</span></strong></h1>
          </div>
          <div class="col-md-10 col-sm-10 col-xs-12 right pd-none">
            
            <div class="row">
              <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="box overridebox out_box_margin">
                  <br>

                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="misc_cost" ng-model="cost.misc_cost" ></div>
                    <div class="col-md-11">
                      <h4>
                        Misc. Cost not accounted for elsewhere
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="cancel_order_shipment" ng-model="cost.cancel_order_shipment" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="incorrect_current_order" ng-model="cost.incorrect_current_order"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="incorrect_current_order_per" ng-model="cost.incorrect_current_order_per" > <span class="override_span input_align">%</span></addon>) cause the customer to cancel the current order. The estimated profit for an average order is &nbsp; &nbsp;<addon class="left-inner-addon"><span class="input_align">$</span><input type="text" id="avg_current_order" ng-model="cost.avg_current_order"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span><input type="text" id="lifetime_order_val" ng-model="cost.lifetime_order_val"  disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="incorrect_lifetime_order" ng-model="cost.incorrect_lifetime_order"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="incorrect_lifetime_order_per" ng-model="cost.incorrect_lifetime_order_per"> <span class="override_span input_align">%</span></addon>) cause the customer to place all future orders with competitors. The estimated lifetime value of a customer is &nbsp; &nbsp;<addon class="left-inner-addon"><span class="input_align">$</span><input type="text" id="lifetime_val" ng-model="cost.lifetime_val"></addon>
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon">
                    <span>$</span>
                    <input type="text" id="negative_post_review" ng-model="cost.negative_post_review" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="negative_shipment" ng-model="cost.negative_shipment"> incorrect shipments ( <addon class="right-inner-addon"><input type="text" id="negative_shipment_per" ng-model="cost.negative_shipment_per"><span class="override_span input_align">%</span> </addon>) result in a negative post or review which causes a potential customer to do business with a competitor instead. Based on the lifetime value of $<span id="lifetime_value"></span>.
                      </h4>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-1 pd-none left-inner-addon"><span>$</span><input type="text" id="inventory_shortage" ng-model="cost.inventory_shortage" disabled></div>
                    <div class="col-md-11">
                      <h4>
                        1 In <input type="text" id="inventory_shortage_order" ng-model="cost.inventory_shortage_order"> incorrect shipments (<addon class="right-inner-addon"> <input type="text" id="inventory_shortage_order_per" ng-model="cost.inventory_shortage_order_per"><span class="override_span input_align">%</span> </addon>) result in an inventory shortage or oversell which costs a sale. The estimated profit per sale of $<span id="avg_current_order_val">
                        {{cost.avg_current_order_val}}</span> is used for this calculation.
                      </h4>
                    </div>
                  </div>
                  <br>
                  <br>
                  <br>
                  <br><br>
                                
                </div>
              </div>
              
            </div>


          </div>
        </div>

        <div class="col-md-12">
          <div class="col-md-3"><h2 style="padding-top: 10px;"><strong>Totals</strong></h2></div>
          <div class="col-md-2"></div>
          <div class="col-md-7">
            <button class="save_link_button right btn left" ng-click="send_email(true)" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 25px;margin-top: 25px;">Save & Continue</button>
          </div>
        </div>

        <div class="row total" >
          <div class="col-md-12 col-sm-12 col-xs-12 pd-none out_box" style="padding-top: 15px;">

            <div class="row">
              <div class="col-md-4 col-sm-4 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Total Cost per error</h3>
                  <h1><strong>$<span id="total_cost">{{cost.total_cost}}</span></strong></h1>
                </div>
              </div>
              <div class="col-md-3 col-sm-3 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Error cost per day</h3>
                  <h1><strong>$<span id ="error_cost_per_day">{{cost.error_cost_per_day}}</span></strong></h1>
                </div>
              </div>
              <div class="col-md-5 col-sm-5 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Monthly cost of shipping errors</h3>
                  <h1 class="data_div" style="color: #be1e2d;"><strong>$<span id="monthly_shipping">
                    {{cost.monthly_shipping}}</span></strong></h1>
                  <h2 class="save_and_continue"><b style="color: #8e8d8d">Click Save & Continue</b></h2>
                </div>
              </div>
            </div>

            <div class="row">

              <div class="col-md-5 col-sm-5 col-xs-12 text-center right">
                <div class="box overridebox">
                  <h3>Montly Cost of GroovePacker</h3>
                  <h1><strong>$<span id="gp_cost">{{cost.gp_cost}}</span></strong></h1>
                </div>
              </div>

              <div class="col-md-1 col-sm-1 col-xs-12"></div>

              <div class="col-md-6 col-sm-6 col-xs-12 text-center right pd-lft-nn">
                <div class="box overridebox">
                  <h3>Expected Savings Per month using GroovePacker</h3>
                  <h1 class="data_div" style="color: #006838;"><strong>$<span id="monthly_saving">
                  {{cost.monthly_saving}}
                  </span></strong></h1>
                  <h2 class="save_and_continue"><b style="color: #8e8d8d">Click Save & Continue</b></h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
      <!-- <% if params[:from_app].blank? %> -->
      <div ng-if="cost.from_app != 'true'">
        <h3 style="padding-left: 5px;">Tell your team</h3>
        <div class="out_box out_box_margin" style="padding: 20px;">
          <h4 style="padding-left: 5px;">
            Everyone knows that shipping errors are costly, both financially and to the reputation of your brand, but without putting a number on the actual costs it’s difficult to justify an investment which solves the problem.<br/><br/>

            Our aim with this calculator is to make these costs clear, and in some cases, highlight situations where implementing GroovePacker would more than pay for itself many times over.<br/><br/>

            You can share these results with your team using the form below. <br/><br/>

            If you would like to receive a couple additional emails which explore other benefits like simplified training, packer performance visibility and more, please check the box beside your email address below.
          </h4>
          <div class="row total out_box_margin" style="background-color: rgba(0,0,0,0.5); opacity: 0.8;">
            <div class="col-md-12 col-sm-12 col-xs-12 pd-none">
            <div class="row">
              <div>
                <h3>
               <textarea style="max-width: 100%;" id="email_text" name="email_text" ng-model="cost.email_text" rows="5" cols="87">
              </textarea>
                </h3>
              </div>
              <h3 style="color: white;">Save and email my cost calculator link to me and my team at the following addresses: </h3>
              <div class="col-md-12 out_box email_body" style="padding-top: 20px;">

                <div class="alert alert-success alert-dismissible" role="alert" id="message"> 
                  <button ng-if="remove_message==true"  id="remove_message" type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span></button>
                    <span class="notice"></span>
                </div>

                <!-- <div class="alert alert-success" id="message" style="width: 100% ">
                  <p class="notice" style="width: 90%"></p> &nbsp;&nbsp;<i style="width: 10%" id="remove_message" class="fa fa-times" style="float: right;"></i></div> -->
                <div class="col-md-1"></div>
                <div class="col-md-7" style="line-height: 48px;">
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Your Name</b></h4> <input type="text" id="recipient_name" ng-model="cost.recipient_name" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Your Email</b></h4><input type="text" id="recipient_one"  ng-model="cost.recipient_one" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Team Email 1</b></h4><input type="text" id="recipient_two" ng-model="cost.recipient_two" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div class="display_flex"><h4 class="green_color col-sm-4"><b>Team Email 2</b></h4><input type="text" id="recipient_three" ng-model="cost.recipient_three" style="width: 70%; line-height: 30px; margin-bottom: 5px" class="col-sm-8"></div>
                  <div style="display:flex; margin-left: 186px;">  
                    <input type="checkbox" name="follow_up_email" ng-model="cost.follow_up_email" id="follow_up_email" style="margin: 15px 0 0; width: 123px; height: 21px;">
                    <h5 style="color: white;"><b>Check this box if you would like to recieve  special offers (your email only)</b></h5>
                    <div style="text-align: right;" ><button class="save_link_button right btn" style="padding: 6px 30px;font-size: 20px;" ng-click="send_email(false)">Send</button>
                    </div>
                  </div>
                </div>
               <!--   <div> 
                    <input type="checkbox" name="follow_up_email" id="follow_up_email" style="margin: 15px 0 0">
                    </div>
                  <div> 
                    <h5 style="color: white;"> You may send up to 2 follow up emails about GroovePacker to this address only</h5>
                  </div><br> -->
                <div class="col-md-4"></div>
              </div>
              <!-- <div class="col-md-5 left">
                <div class="col-md-1 right"> 
                  <input type="checkbox" name="follow_up_email" id="follow_up_email" style="margin: 15px 0 0">
                  </div>
                <div class="col-md-11 left"> 
                  <h5 style="color: white;"> You may send up to 2 follow up emails about GroovePacker to this address only</h5>
                </div><br>
              </div> -->
            </div>
            </div>
          </div>
          <div class="box_margin">
            <div style="display: flex;">
              <div class="col-md-2"></div>
              <div class="col-md-6 right footer_out_box">
                <div class="col-sm-3" style="padding-top: 6px;">
                  <i class="fa fa-graduation-cap fa-5x" aria-hidden="true"></i>
                </div>
                <div class="col-sm-9">
                  <h3>Interested in learning more?</h3>&emsp;
                  <p style="margin-top: -30px;">See a demo and get your questions answered</p>
                </div>
              </div>
              <button class="right btn col-md-2" onclick="window.open('https://groovepacker.com/see-a-demo', '_blank');" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 15px;margin-top: 15px; margin-left: 8px;">Learn More</button>
            </div>

            <div style="display: flex;">
              <div class="col-md-2"></div>
              <div class="col-md-6 right footer_out_box">
                <div class="col-sm-3" style="padding-left: 30px; padding-top: 15px;"><i class="fa fa-play-circle fa-5x" aria-hidden="true"></i></div>
                <div class="col-sm-9">
                  <h3>Ready to Groove?</h3>&emsp;
                  <p style="margin-top: -30px;">Select a plan from our pricing page and start your <br/> free trial today.</p>
                </div>
              </div>
              <button class="right btn col-md-2" onclick="window.open('https://groovepacker.com/pricing','_blank');" style="font-size: 20px; background-color: #3fa413; color: white; margin-bottom: 25px;margin-top: 25px; margin-left: 8px;">Get Started</button>
            <!-- </div> -->
          </div>
        </div>
      </div>
      <!-- <%end%> -->

    <div class="clearfix"></div>
  </div>
<div class="clearfix"></div>
<style type="text/css">
.gradient-well-inner{
  max-width: 1200px !important;
}

.main-body{
  margin: 0vh auto !important;
}
</style>

<script type="text/javascript">
  /* Cost calculator start*/
window.onload = function () { 
  $(document).ready(function () {
    cost_calculator_page = $(".errorcalculator").length;
    if(cost_calculator_page<1)return;
    monthly_cost = $("#montly_expenses").val();
    $("#email_text").text($("#email_text").val())
    $("#total_expedited, #total_international, #cancel_order_shipment, #lifetime_order_val, #negative_post_review, #inventory_shortage, #lifetime_value, #avg_current_order_val").css("background", "#dedbdb");
    $(document).on('change paste keyup input', function () {
      var order_count = $("#order_count").val();
      var packer_count = $("#packer_count").val();
      var avg_error = $("#avg_error").val();
      if (order_count != null && packer_count != null && avg_error != null){
        $("#error_per_day").html(parseFloat(order_count*packer_count*(avg_error/100)).toFixed(2));
        $("#error_per_day").val(parseFloat(order_count*packer_count*(avg_error/100)).toFixed(2));
      var error_per_day = $("#error_per_day").val();
      var total_cost_per_error = $("#total_cost_per_error").val();
      $("#daily_cost_error").html(parseFloat(total_cost_per_error*error_per_day));
      $("#daily_cost_error").val(parseFloat(total_cost_per_error*error_per_day));
      };
      $("#cost_of_plan").val(packer_count*50)
      $("#cost_of_plan").html(packer_count*50);
    });

    
    $(document).on('change paste keyup input', function(){ 
      var regular_percentage = parseFloat($("#regular_percentage").val());
      var avg_comm = parseFloat($("#avg_comm").val());
      // var regular_percentage = parseFloat($("#regular_percentage").val());
      var regular_comm = parseFloat($("#regular_comm").val());
      var escalated_comm = parseFloat($("#escalated_comm").val());
      if(regular_percentage != null){
        $("#regular_percentage").on('change paste keyup input', function(){
          $("#escalated_percentage").html(parseFloat(100-regular_percentage));
          $("#escalated_percentage").val(parseFloat(100-regular_percentage));
        });
      };
      if(regular_percentage != null && avg_comm != null && regular_comm != null && regular_percentage != null && escalated_comm != null) {
        $("#cost_of_comm").html(parseFloat(avg_comm*(((regular_comm*regular_percentage)/100)+((escalated_comm*(100-regular_percentage))/100))).toFixed(2));
         $("#cost_of_comm").val(parseFloat(avg_comm*(((regular_comm*regular_percentage)/100)+((escalated_comm*(100-regular_percentage))/100))).toFixed(2))
      };
    });

    // $('#return_shipping_cost, #return_shipping_insurance, #cost_recieving_process, #cost_confirm, #return_shipping_percentage').on('change paste keyup input', function(){
    //   var return_shipping_cost = parseFloat($("#return_shipping_cost").val());
    //   var return_shipping_insurance = parseFloat($("#return_shipping_insurance").val());
    //   var cost_recieving_process = parseFloat($("#cost_recieving_process").val());
    //   var cost_confirm = parseFloat($("#cost_confirm").val());
    //   var return_shipping_percentage = parseFloat($("#return_shipping_percentage").val());
    //   if(return_shipping_cost != null && return_shipping_insurance != null && cost_recieving_process != null && cost_confirm != null && return_shipping_percentage != null ){
    //     $("#return_ship").html(parseFloat((return_shipping_percentage/100)*(return_shipping_cost + return_shipping_insurance + cost_recieving_process + cost_confirm)));
    //     $("#return_ship").val(parseFloat((return_shipping_percentage/100)*(return_shipping_cost + return_shipping_insurance + cost_recieving_process + cost_confirm)))
    //   }
    // });

    $("#return_shipping_percentage").on('change paste keyup input', function(){
      var return_shipping_percentage = $("#return_shipping_percentage").val();
      if(return_shipping_percentage != null){
        $("#product_abandonment_percentage").html(parseFloat(100 - return_shipping_percentage));
        $("#product_abandonment_percentage").val(parseFloat(100 - return_shipping_percentage));
      };
    });

    $("#product_abandonment_percentage").on('change paste keyup input', function(){
      var product_abandonment_percentage = $("#product_abandonment_percentage").val();
      if(product_abandonment_percentage != null){
        $("#return_shipping_percentage").html(parseFloat(100 - product_abandonment_percentage));
        $("#return_shipping_percentage").val(parseFloat(100 - product_abandonment_percentage));
      };
    });

    $(document).on('change paste keyup input', function(){
      var return_shipping_percentage = $("#return_shipping_percentage").val();
      var avg_product_abandonment = $("#avg_product_abandonment").val();
      if(return_shipping_percentage != null && avg_product_abandonment != null){
        $("#product_abandonment").html(parseFloat((100-return_shipping_percentage)*avg_product_abandonment/100).toFixed(2))
        $("#product_abandonment").val(parseFloat((100-return_shipping_percentage)*avg_product_abandonment/100).toFixed(2))
      };
    });

    $(document).on('change paste keyup input', function() {
      var incorrect_item = $("#incorrect_item").val();
      var avg_order_profit = $("#avg_order_profit").val();
      var incorrect_item_per = parseFloat(100/incorrect_item)
      $("#incorrect_percentage").html(incorrect_item_per);
      if(incorrect_item != null && avg_order_profit != null){
        canc = parseFloat(avg_order_profit/incorrect_item);
        $("#calc_cost").html(canc);
        $("#calc_cost").val(canc);
      };
    });

    $(document).on('change paste keyup input', function() {
      var frustration_order = $("#frustration_order").val();
      var avg_customer_value = $("#avg_customer_value").val();
      var incorrect_item_per = parseFloat(100/frustration_order)
      $("#frustration_percentage").html(incorrect_item_per);
      if(frustration_order != null && avg_customer_value != null){
        canc = parseFloat(avg_customer_value/frustration_order).toFixed(2);
        $("#future_calc_cost").html(canc);
        $("#future_calc_cost").val(canc);
      };
    });

    $(document).on('change paste keyup input', function() {
      var social_error = $("#social_error").val();
      var future_customer_value = $("#future_customer_value").val();
      var incorrect_item_per = parseFloat(100/social_error)
      $("#social_error_percentage").html(incorrect_item_per);
      if(social_error != null && future_customer_value != null){
        canc = parseFloat(future_customer_value/social_error).toFixed(2);
        $("#life_long_calc_cost").html(canc);
        $("#life_long_calc_cost").val(canc);
      };
    });

    $(document).on('change paste keyup input', function(){
      var cost_apology = parseFloat($("#cost_apology").val()) || 0;
      var cost_labor_reshipment = parseFloat($("#cost_labor_reshipment").val()) || 0;
      var reshipment = parseFloat($("#reshipment").val()) || 0;
      var cost_ship_replacement = parseFloat($("#cost_ship_replacement").val()) || 0;
      var cost_of_comm = parseFloat($("#cost_of_comm").val()) || 0;
      // var return_ship = parseFloat($("#return_ship").val()) || 0;
      // var product_abandonment = parseFloat($("#product_abandonment").val()) || 0;
      // var calc_cost = parseFloat($("#calc_cost").val()) || 0;
      // var future_calc_cost = parseFloat($("#future_calc_cost").val()) || 0;
      // var life_long_calc_cost = parseFloat($("#life_long_calc_cost").val()) || 0;
      var total_international = parseFloat($("#total_international").val()).toFixed(2) || 0;
      var total_expedited = parseFloat($("#total_expedited").val()).toFixed(2) || 0;

      $("#total_replacement_costs").html(parseFloat(cost_ship_replacement + reshipment + cost_labor_reshipment + cost_apology + total_expedited + total_international).toFixed(2));
      $("#total_replacement_costs").val(parseFloat(cost_ship_replacement + reshipment + cost_labor_reshipment + cost_apology + total_expedited + total_international).toFixed(2));

      var error_per_day = $("#error_per_day").val();
      var total_cost_per_error = $("#total_cost_per_error").val();
      $("#daily_cost_error").html(parseFloat(total_cost_per_error*error_per_day).toFixed(2));
      $("#daily_cost_error").val(parseFloat(total_cost_per_error*error_per_day).toFixed(2));
    });

    $(document).on('change paste keyup input', function(){
      var packing_days_per_month = $('#packing_days_per_month').val();
      var daily_cost_error = $("#daily_cost_error").val();
      $("#monthly_error_cost").html(parseFloat(daily_cost_error*packing_days_per_month).toFixed(2));
      $("#monthly_error_cost").val(parseFloat(daily_cost_error*packing_days_per_month).toFixed(2));

      var monthly_error_cost = $("#monthly_error_cost").val();
      var cost_of_plan = $("#cost_of_plan").val();
      $("#expected_savings").html(parseFloat(monthly_error_cost - cost_of_plan));
      $("#expected_savings").val(parseFloat(monthly_error_cost - cost_of_plan));
    });

    $("#expedited_percentage, #international_percentage").on('change paste keyup input', function(){
      var expedited_percentage = $('#expedited_percentage').val();
      var international_percentage = $('#international_percentage').val();
      $('#international_count').val(parseFloat(100/international_percentage).toFixed(2));
      $('#expedited_count').val(parseFloat(100/expedited_percentage).toFixed(2));
    });

    $("#expedited_count, #international_count").on('change paste keyup input', function(){
      var expedited_count = $('#expedited_count').val();
      var international_count = $('#international_count').val();
      $('#international_percentage').val(parseFloat(100/international_count).toFixed(2));
      $('#expedited_percentage').val(parseFloat(100/expedited_count).toFixed(2));
    });

    $(document).on('change paste keyup input', function(){
      var expedited_percentage = $('#expedited_percentage').val();
      var expedited_avg = $('#expedited_avg').val();
      if (expedited_percentage != null && expedited_avg != null){
        $('#total_expedited').val(parseFloat(expedited_percentage*expedited_avg/100).toFixed(2))
      }
    });

    $(document).on('change paste keyup input', function(){
      var international_percentage = $('#international_percentage').val();
      var avg_order_profit = $('#avg_order_profit').val();
      if (international_percentage != null && avg_order_profit != null){
        $('#total_international').val(parseFloat(international_percentage*avg_order_profit/100).toFixed(2))
      }
    });

    $(document).on('change paste keyup input', 'input', function() {
      var error_per_day = $("#error_per_day").val();
      var total_cost_per_error = $("#total_cost_per_error").val();
      $("#daily_cost_error").html(parseFloat(total_cost_per_error*error_per_day).toFixed(2));
      $("#daily_cost_error").val(parseFloat(total_cost_per_error*error_per_day).toFixed(2));
    });

    $(document).on('change paste keyup input', function(){
      var total_error_shipment = parseFloat($('#total_error_shipment').val()) || 0;
      var product_abandonment_percentage = parseFloat($('#product_abandonment_percentage').val()) || 0;
      var avg_product_abandonment = parseFloat($('#avg_product_abandonment').val()) || 0;
      var return_shipping_percentage = parseFloat($('#return_shipping_percentage').val()) || 0;
      var cost_return = parseFloat($('#cost_return').val()) || 0;
      var return_shipping_cost = parseFloat($('#return_shipping_cost').val()) || 0;
      var return_shipping_insurance = parseFloat($('#return_shipping_insurance').val()) || 0;
      var cost_recieving_process = parseFloat($('#cost_recieving_process').val()) || 0;
      var cost_confirm = parseFloat($('#cost_confirm').val()) || 0;

      $('#return_shipment_or_abandonment').html(
        parseFloat((total_error_shipment/100)*((product_abandonment_percentage*avg_product_abandonment/100) + ((return_shipping_percentage/100)*(cost_return + return_shipping_cost + return_shipping_insurance + cost_recieving_process + cost_confirm)))).toFixed(2));
      $('#return_shipment_or_abandonment').val(
        parseFloat((total_error_shipment/100)*((product_abandonment_percentage*avg_product_abandonment/100) + ((return_shipping_percentage/100)*(cost_return + return_shipping_cost + return_shipping_insurance + cost_recieving_process + cost_confirm)))).toFixed(2));
    });


    $('#incorrect_current_order, #incorrect_lifetime_order, #negative_shipment, #inventory_shortage_order').on('change paste keyup input', function(){
      var incorrect_current_order = parseFloat($('#incorrect_current_order').val()) || 0; 
      var incorrect_lifetime_order = parseFloat($('#incorrect_lifetime_order').val()) || 0;
      var negative_shipment = parseFloat($('#negative_shipment').val()) || 0;
      var inventory_shortage_order = parseFloat($('#inventory_shortage_order').val()) || 0;
      if(incorrect_current_order != null){
        $('#incorrect_current_order_per').html(parseFloat(100/incorrect_current_order).toFixed(2))
        $('#incorrect_current_order_per').val(parseFloat(100/incorrect_current_order).toFixed(2))
      }
      if(incorrect_lifetime_order != null){
        $('#incorrect_lifetime_order_per').html(parseFloat(100/incorrect_lifetime_order).toFixed(2))
        $('#incorrect_lifetime_order_per').val(parseFloat(100/incorrect_lifetime_order).toFixed(2))
      }
      if(negative_shipment != null){
        $('#negative_shipment_per').html(parseFloat(100/negative_shipment).toFixed(2))
        $('#negative_shipment_per').val(parseFloat(100/negative_shipment).toFixed(2))
      }
      if(inventory_shortage_order != null){
        $('#inventory_shortage_order_per').html(parseFloat(100/inventory_shortage_order).toFixed(2))
        $('#inventory_shortage_order_per').val(parseFloat(100/inventory_shortage_order).toFixed(2))
      }
    });

    $('#incorrect_current_order_per, #incorrect_lifetime_order_per, #negative_shipment_per, #inventory_shortage_order_per').on('change paste keyup input', function(){
      var incorrect_current_order_per = parseFloat($('#incorrect_current_order_per').val()) || 0; 
      var incorrect_lifetime_order_per = parseFloat($('#incorrect_lifetime_order_per').val()) || 0;
      var negative_shipment_per = parseFloat($('#negative_shipment_per').val()) || 0;
      var inventory_shortage_order_per = parseFloat($('#inventory_shortage_order_per').val()) || 0;
      if(incorrect_current_order_per != null){
        $('#incorrect_current_order').html(parseFloat(100/incorrect_current_order_per).toFixed(2))
        $('#incorrect_current_order').val(parseFloat(100/incorrect_current_order_per).toFixed(2))
      }
      if(incorrect_lifetime_order_per != null){
        $('#incorrect_lifetime_order').html(parseFloat(100/incorrect_lifetime_order_per).toFixed(2))
        $('#incorrect_lifetime_order').val(parseFloat(100/incorrect_lifetime_order_per).toFixed(2))
      }
      if(negative_shipment_per != null){
        $('#negative_shipment').html(parseFloat(100/negative_shipment_per).toFixed(2))
        $('#negative_shipment').val(parseFloat(100/negative_shipment_per).toFixed(2))
      }
      if(inventory_shortage_order_per != null){
        $('#inventory_shortage_order').html(parseFloat(100/inventory_shortage_order_per).toFixed(2))
        $('#inventory_shortage_order').val(parseFloat(100/inventory_shortage_order_per).toFixed(2))
      }
    });


    $('#avg_current_order, #lifetime_val, #incorrect_current_order_per, #incorrect_lifetime_order_per, #negative_shipment_per, #inventory_shortage_order_per, #incorrect_current_order, #incorrect_lifetime_order, #negative_shipment, #inventory_shortage_order, #misc_cost').on('change paste keyup input', function(){
      var incorrect_current_order_per = parseFloat($('#incorrect_current_order_per').val()) || 0;
      var avg_current_order = parseFloat($('#avg_current_order').val()) || 0;
      var incorrect_lifetime_order_per = parseFloat($('#incorrect_lifetime_order_per').val()) || 0;
      var lifetime_val = parseFloat($('#lifetime_val').val()) || 0;
      var negative_shipment_per = parseFloat($('#negative_shipment_per').val()) || 0;
      var inventory_shortage_order_per = parseFloat($('#inventory_shortage_order_per').val()) || 0;

      if(incorrect_current_order_per != null && avg_current_order != null){
        $('#cancel_order_shipment').val(parseFloat(incorrect_current_order_per*avg_current_order/100).toFixed(2));
        $('#cancel_order_shipment').html(parseFloat(incorrect_current_order_per*avg_current_order/100).toFixed(2));
      }
      if(incorrect_lifetime_order_per != null && lifetime_val != null){
        $('#lifetime_order_val').val(parseFloat(incorrect_lifetime_order_per*lifetime_val/100).toFixed(2));
        $('#lifetime_order_val').html(parseFloat(incorrect_lifetime_order_per*lifetime_val/100).toFixed(2));
      }
      if(negative_shipment_per != null && lifetime_val != null){
        $('#negative_post_review').val(parseFloat(parseFloat(negative_shipment_per).toFixed(2)*parseFloat(lifetime_val).toFixed(2)/100).toFixed(2));
        $('#negative_post_review').html(parseFloat(parseFloat(negative_shipment_per).toFixed(2)*parseFloat(lifetime_val).toFixed(2)/100).toFixed(2));
      }
      if(inventory_shortage_order_per != null && avg_current_order != null){
        $('#inventory_shortage').val(parseFloat(inventory_shortage_order_per*avg_current_order/100).toFixed(2));
        $('#inventory_shortage').html(parseFloat(inventory_shortage_order_per*avg_current_order/100).toFixed(2));
      }

      var misc_cost = parseFloat($('#misc_cost').val()) || 0;
      var cancel_order_shipment = parseFloat($('#cancel_order_shipment').val()) || 0;
      var lifetime_order_val = parseFloat($('#lifetime_order_val').val()) || 0;
      var negative_post_review = parseFloat($('#negative_post_review').val()) || 0;
      var inventory_shortage = parseFloat($('#inventory_shortage').val()) || 0;
      $('#intangible_cost').html(parseFloat(misc_cost + cancel_order_shipment + negative_post_review + lifetime_order_val + inventory_shortage).toFixed(2))
      $('#intangible_cost').val(parseFloat(misc_cost + cancel_order_shipment + negative_post_review + lifetime_order_val + inventory_shortage).toFixed(2))
    });

    $(document).on('change paste keyup input', 'input', function() {  
      calculate_values();
    });

    $(document).ready(function(){
      cost_calculator_page = $(".errorcalculator").length;
      if(cost_calculator_page<1)return;
      $('#misc_cost').change();
      $('#packer_count').change();
      $('#incorrect_current_order').change();
      calculate_values();
    });   

    function hide_total(){
      $(".data_div").hide();
      $(".save_and_continue").show();
    }

    function calculate_values() {
      var intangible_cost = parseFloat($('#intangible_cost').val()) || 0;
      var return_shipment_or_abandonment = parseFloat($('#return_shipment_or_abandonment').val()) || 0;
      var total_replacement_costs = parseFloat($('#total_replacement_costs').val()) || 0;
      var cost_of_comm = parseFloat($('#cost_of_comm').val()) || 0;

      $('#total_cost').html(parseFloat(intangible_cost + return_shipment_or_abandonment + total_replacement_costs + cost_of_comm).toFixed(2))
      $('#total_cost').val(parseFloat(intangible_cost + return_shipment_or_abandonment + total_replacement_costs + cost_of_comm).toFixed(2))

      var total_cost = parseFloat($('#total_cost').val()) || 0;
      var error_per_day = parseFloat($("#error_per_day").val()) || 0;
      $('#error_cost_per_day').html(parseFloat(total_cost*error_per_day).toFixed(2));
      $('#error_cost_per_day').val(parseFloat(total_cost*error_per_day).toFixed(2));

      var error_cost_per_day = parseFloat($('#error_cost_per_day').val()) || 0;      
      $('#monthly_shipping').html(parseFloat(error_cost_per_day*30).toFixed(2));
      $('#monthly_shipping').val(parseFloat(error_cost_per_day*30).toFixed(2))

      var email_text_to_rep = $("#email_text").val();
      if (email_text_to_rep != undefined && event.type == "load"){
        email_text_to_rep = email_text_to_rep.split(monthly_cost).join("")
        email_text_to_rep = email_text_to_rep.replace("about $", "about $" + parseFloat(error_cost_per_day*30).toFixed(2))
        
        // email_text_to_rep = email_text_to_rep.replace(monthly_cost, parseFloat(error_cost_per_day*30).toFixed(2));
        monthly_cost = parseFloat(error_cost_per_day*30).toFixed(2);
        $("#email_text").val(email_text_to_rep);
      }
      var packer_count = parseFloat($('#packer_count').val()) || 0; 
      $('#gp_cost').html(parseFloat(packer_count*50));
      $('#gp_cost').val(parseFloat(packer_count*50));

      var gp_cost = parseFloat($('#gp_cost').val()) || 0;
      var monthly_shipping = parseFloat($('#monthly_shipping').val()) || 0;
      $('#monthly_saving').html(parseFloat(monthly_shipping - gp_cost).toFixed(2));
      $('#monthly_saving').val(parseFloat(monthly_shipping - gp_cost).toFixed(2))
      $('#lifetime_value').val($('#lifetime_val').val());
      $('#avg_current_order_val').val($('#avg_current_order').val());
      $('#lifetime_value').html($('#lifetime_val').val());
      $('#avg_current_order_val').html($('#avg_current_order').val());
    }

    $(document).on('change paste keyup input', 'input', function(e) {  
      if (isNaN(parseFloat(this.value)) == true) {
        $(this.id).val(0);
      } else {
        $(this.id).val(parseFloat(this.value).toFixed(2));
      }
      $(".data_div").hide();
      $(".save_and_continue").show();
      $('#message').hide();

      $("#email_text").val()
    });

    $("#remove_message").click(function(){
      $('#message').hide();
    });

    // $('#send_calculated_email').on('click', function () {
    //   send_email(false);
    // });

    $('.save_link_button').on('click', function () {
      $(".data_div").show();
      $(".save_and_continue").hide();
      // send_email(true);
      var error_cost_per_day = $("#monthly_shipping").val();
      var email_text_to_rep = $("#email_text").val();
      if (email_text_to_rep != undefined && event.type == "click"){
        monthly_cost = " $" + monthly_cost
        email_text_to_rep = email_text_to_rep.split(monthly_cost).join("")
        email_text_to_rep = email_text_to_rep.replace("about", "about $" 
        + parseFloat(error_cost_per_day).toFixed(2))
        monthly_cost = parseFloat(error_cost_per_day).toFixed(2);
        $("#email_text").val(email_text_to_rep);
      }
    });

    // var send_email = function (response) {
    //   var recipient_one = $('#recipient_one').val();
    //   var recipient_two = $('#recipient_two').val();
    //   var recipient_three = $('#recipient_three').val();
    //   $.ajax({
    //     type: "GET",
    //     contentType: "application/json; charset=utf-8",
    //     url: "http://youzar.localpackerapi.com/email_calculations?" + $("#cost_calc").serialize(),
    //     data: {gp_cost: $("#gp_cost").val(), monthly_shipping: $("#monthly_shipping").val(), error_cost_per_day: $("#error_cost_per_day").val(), total_cost: $("#total_cost").val(), recipient_one: recipient_one, recipient_two: recipient_two, monthly_saving: $('#monthly_saving').val(), monthly_shipping: $('#monthly_shipping').val(), recipient_name: $('#recipient_name').val(),
    //     recipient_three: recipient_three,
    //     follow_up_email: $('#follow_up_email').is(':checked'), email_text: $("#email_text").val(), cost_header: $("#cost_header").html(), only_save: response}, 
    //     dataType: "json"
    //   }).success(function (message) {
    //     $('#remove_message').show();
    //     $('.notice').html(message.message);
    //     if (message["only_save"] == "true"){
    //       $('#message').hide();
    //     } else {
    //       if((recipient_one.length + recipient_two +recipient_three) > 0){
    //         $('#message').show();
    //       }
    //     }
    //   });
    // }

  });
}
/* Cost calculator stop*/
</script>