{"common": {"search": "Search", "select_all": "Select All", "select_none": "Select None", "always": "Always", "optional": "Make it optional", "never": "Never", "none": "None", "verify": "Shipping Label Verification", "record": "Record Tracking Number", "packing_slip": "Print Packing Slip", "order_barcode": "Print Order Barcode", "order_number": "Order Number", "tracking_number": "Tracking Number", "packing_slip_scan": "Packing Slip", "shipping_label_scan": "Shipping Label", "packing_slip_or_shipping_label_scan": "Packing Slip or Shipping Label"}, "modal": {"save_close": "Save & Close", "cancel": "Cancel", "update": "Update"}, "products": {"modal": {"tooltips": {"sku": "The first SKU listed is the Primary SKU. It will be displayed during packing. If the same product exists under other SKUs in other stores the alternate SKUs will be added here when the products are Aliased. Additional alias SKUs can be added by comma or enter.", "barcode": "The first barcode in the list is considered the 'Primary' barcode. It will be displayed during packing. It will also be the one that is printed when you click the Print icon. Drag the barcodes to reorder them if you would like to make another barcode the primary. Every barcode in the list can be used to scan this product.", "confirmation": "Text here will be displayed when this product is being scanned. Unless packer confirmations are disabled for the user, confirmations will be required when the Packer Confirmation Required option is selected here.", "placement": "This value from 1-100 controls when the product will be packed in relation to other products in the same box. Lower values are packed first, Higher values later", "time_adjust": "A positive or negative number of seconds can be specified here to increase or decrease the amount of time it should take to scan and pack this item", "skippable": " Skippable products are usually stickers, freebies and other optional items that the order should not be held for", "record_serial": "When enabled, the packer will be prompted to scan a serial number each time this product is packed. The recorded serial can then be viewed in the Order Activity log or downloaded in the Order Serial CSV.", "master_alias": "Aliasing can be used when you have multiple SKU's which refer to the same product. Aliasing essentially copies the SKU & barcode data of the Aliased products to a Primary product. Usually the Primary product will be the one that you imported during setup. It will have the prefered SKU and barcode and will often have other data like bin location, image, weight etc. The Aliases will often be products that were created on the fly during order imports. They often display as \"New\" products because they lack barcodes. When Aliased, the SKU (and barcode if it exists) of the alias products are added to the primary product. Going forward aliases will be recognized and treated just as the primary product when they are found in orders.<br/> <br/>There are two options for Aliasing:<ul class=\"groov-list-bulleted\"><li><b>Make this product an alias of another product:</b><br/> Choose this option if you are currently viewing a newly created product with a SKU that is a duplicate of an existing \"primary\" product. You'll be prompted to choose the primary product from a list. During aliasing the SKU of the current product will be added to the primary product and the current product will no longer be listed separately.</li><li><b>Choose aliases of this product:</b><br/> Select this option if you are viewing the Primary product that you intend to keep and you would like to choose one or more aliases which are all essentially duplicates of this product called by other SKU's. Each product you choose from the list will have it's SKU and barcode added to the primary product and the chosen product(s) will no longer be listed separately.</li></ul>", "product_receiving_instructions": "Product receiving instructions added here will be shown when products are received and added to inventory using the Receive or Recount Inventory feature. This is allows you to specify bagging, labeling, storage and quality control steps to be taken during receiving. In addition to text, any image saved with the product can be selected and shown along with a caption describing the step pictured.", "add_to_any_order": "When this option is enabled for a product you can add it to any order you're packing just by scanning it. This is great for tracking inventory of boxes or bags that are decided on at the time of packing. Catalogs, coupons, freebie items can be scanned and tracked too. Items added will be saved with the order record and included on packing slips printed after packing is complete.", "intangible_item": "Intangible items, also known as transactional items, are non-physical items that do not need to be packed. Examples are Coupon Codes, Insurance, Same day handling charges etc. Many times these items import along with regular order items. To avoid having these items prompted during Scan and Pack you can enable this option to designate them as intangible. <br/>If you have intangible items that are dynamically generated but always contain a specific string you can have GroovePacker automatically set these items to intangible. You'll find this option in the Scan and Pack settings.", "type_in_scan_setting": "When enabled, this setting allows you to scan one of an item and then manually type in a quantity rather than scanning each item one by one. It's ideal when you have dozens of smaller items that are pre-counted, or any time scanning every item of a large multiple is not desirable.<br/> You can set the type-in trigger in the Scan and Pack settings.", "click_scanning_setting": "When Click Scanning is enabled for a product you can emulate a scan of the item and add it to the order in Scan and Pack without scanning or typing the barcode. This is helpful for testing or for when barcodes are damaged and unreadable by the scanner. Because this feature has a potential for being abused it is possible to disable it on a product by product basis or for all items. Although click-scans work exactly like regular scans in all respects they are logged differently and are reported in order export reports.", "multi_pack_barcode": "Multi-pack barcodes allow you to pre-pack a set quantity of the item and assign a barcode and quantity to the pack. The barcode you define would be placed on the pre-counted pack. Scanning the multi-pack barcode will have the same effect as scanning the item X times where X is the quantity assigned to the pack. Consider the following use case: The customer orders 105 of a given item. For efficiency your items are pre-counted in packs of 25 and barcoded with a multi-pack barcode which has been assigned the value 25. The individual items are also barcoded. To scan 105 of the item the packer would begin by scanning 4 of the multi-packs. With each scan 25 of the item would be marked scanned in the order. The remaining 5 individual items would then be scanned to complete scanning for the item.", "custom_product_fields": "These custom product fields can be displayed during the scanning process. They can be entered here manually or imported with a product CSV file."}}}, "orders": {"modal": {"labels": {"se_old_split_shipments": "One of the shipments associated with this order was scanned in GroovePacker and then split in ShippingEasy. Rather than modifying an order that has been scanned we are letting you know so you can take any action that is required", "se_old_combined_shipments": "One of the shipments associated with this order was scanned in GroovePacker and then combined in ShippingEasy. Rather than modifying an order that has been scanned we are letting you know so you can take any action that is required", "se_additional_shipments": "Additional shipments in this order will automatically display after this shipment is scanned.", "remove_shipment": "You can click the shipment number to view the items in it and click the trash icon beside a shipment to remove it.", "remove_acknowledge_shipment": "Once all unwanted shipments are removed you can click here to acknowledge and continue."}, "tooltips": {"confirmation": "The confirmation setting applies to all notes which are set <br/> to display to the packer. If confirmation is required,<br/> the packer will be asked to enter or scan their confirmation code <br/>before packing the order."}}}, "settings": {"users": {"modal": {"tooltips": {"conf_code": "Any value can be chosen for the confirmation code as long as it is unique for each user. The code will be requested when performing various operations, like changing an order from \"Service Issue\" to \"Awaiting\". Their purpose is to confirm intent or allow a manager to grant permission for an action, as well as to provide an audit trail. Each user can optionally have a barcoded ID badge, a scan of their barcode can automatically enter their confirmation id. Additionally, the barcode can now be used to log in directly, bypassing the need to manually enter a username and password. ", "name": "Include the users name here or leave blank to use Username for both Username and Name.", "override_pass": "You can enable this switch if you would like the user to be able to pass all items regardless of the pass settings.", "role": "Choose a role to apply the default permissions for that role or chose <strong>Custom</strong> to adjust individual permissions.", "base_role": "Start your custom role by choosing from any of the default roles. Then adjust any of the permissions as needed on the Products, Orders, User & Systems tabs. When finished you can save your role so it can be applied to users you create in the future.", "section_access": "Users only have access to sections that are checked. This is an easy way to block certain areas. For more precise control the other user tabs offer permissions for each of the sections. Only a SuperAdmin can change permissions for other users. SuperAdmins always have full permissions. ", "custom_fields": "These custom fields can be used to store any user data you like. Common examples might be the warehouse or division where a user works. The fields are visible on the User Stats report. You can modify the name of these custom fields in Settings > System Settings > General Prefrence"}}}, "system": {"general": {"headings": {"inventory": "Inventory Related", "conf_notif": "Confirmations & Notifications", "packing_slips": "Packing Slips", "product_labels": "Product Labels", "generate_numeric_barcode": "Generate Numeric Barcode", "direct_printing_options": "Direct Printing Options", "miscellaneous_settings": "Miscellaneous Settings", "flash_setting": "Flash Setting", "time_zone": "Preferred time zone", "adjust_for_daylight": "Adjust for Daylight Savings Time", "order_item_export": "Order Item Export", "order_tracking_no_order": "Order Tracking Web Page Response: No Order Found", "order_tracking_no_tracking": "Order Tracking Web Page Response: No Tracking Info", "product_weight": "Product Weight Units:", "product_dimension": "Product Dimenions Units:", "pre_day_order": "Pre-day Order Import Schedule", "pre_day_import": "Pre-day Import Time", "import_orders": "Import Orders on", "recurring_ftp": "Recurring FTP Order Import Range", "new_time_zone": "New Time Zone", "api_keys": "A<PERSON> <PERSON>", "create_webhook": "Webhooks"}, "labels": {"inventory_tracking": "Inventory Tracking", "low_inventory_alert_email": "Low Inventory Al<PERSON>", "time_to_send_email": "Time for sending low inventory email", "send_email_on": "Send email on", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "strict_cc": "Strict Confirmation Codes?", "conf_code_product_instruction": "Should confirmation be required on \"Special Instructions to Packer\"?", "default_low_inventory_alert_limit": "Set Default low inventory alert limit", "inventory_auto_allocation": "Inventory Auto-Allocation", "conf_req_on_notes_to_packer": "Should confirmation be required when order notes are shown to the packer?", "send_email_for_packer_notes": "Should an email be sent when \"Notes from <PERSON><PERSON>\" are added?", "email_address": "Email Address", "packing_slip_size": "Packing Slip <PERSON>:", "packing_slip_orientation": "Packing Slip Orientation:", "portrait": "Portrait", "landscape": "Landscape", "system_notifications": "System Notifications", "billing_notifications": "Billing Notifications", "report_out_of_stock_notifications": "Report Out of Stock", "packing_slip_message_to_customer": "Custom Packing-Slip Message-All Stores", "show_primary_bin_loc_in_barcodeslip": "Display location 1 and 2 on the product barcode label", "show_sku_in_barcodeslip": "Display the SKU on the product barcode label", "html_print": "HTML Packing Slips", "inactive_logout_time": "Log out inactive users after", "hex_barcode": "Cue orders with ShipStation's 'Scan to view' barcode.", "print_post_scanning_barcodes": "Post Scanning Order Number Barcode", "print_product_barcode_labels": "Product Barcode Labels", "print_product_receiving_labels": "Product Receiving Labels", "print_packing_slips": "Packing Slips", "print_ss_shipping_labels": "Shipping Labels via ShipStation Integration", "display_sku_total": "Display sku total order by order", "display_one_total": "Display one total for each sku", "standard_order_export": "Standard Order Export", "product_barcode_label_size": "Product Barcode Label Size", "truncated_string": "Truncated String", "truncate_order_number_in_packing_slip": "Truncate Order Number in Packing Slip", "api_key": "Api Key", "generate_api_key": "Generate Api Key", "delete_api_key": "Delete Api Key", "regenerate_api_key": "Regenerate Api Key"}, "tooltips": {"inventory_tracking": "If you wish to track inventory you can enable it here. Please be sure to alias duplicate items and create kits beforehand if applicable. Once enabled, inventory will be automatically allocated for open orders. You will then need to set the Quantity On Hand level for each product using the CSV product importer, the recount feature or by manually editing products. The Quantity On Hand count assumes that all items are \"on shelf\" and included in the count. (ie. Items will be allocated for open orders in the system so please include all items in the count, even if they have been, or will be, picked for open orders in the system.)", "low_inventory_alert_email": "Low Inventory Alerts limits can be set on the product edit page. If you're not using alerts they can be disabled for all products here. To disable alerts for individual products set their alert level to 0 on their page", "default_low_inventory_alert_limit": "The Alert level set here will be assigned to all products that do not have a value set.", "conf_req_on_notes_to_packer": "Notes to Packer can be added for any order from the notes tab of the Order detail window. If a confirmation is required the Packer will be prompted to scan their unique confirmation code to proceed when the note is shown. If you want to ensure that confirmations are always required or never required for this type of notification you can do that here. Leaving it optional allows the user creating the Note to choose if confirmation is required.", "send_email_for_packer_notes": "Notes from Packer can be added by a packer during the scanning of an order. They are often used to make a supervisor aware of a stock situation or exception but the feature can be used in any way that fits your workflow. If you want to ensure that these notes are always emailed to a supervisor you can do that with this setting.", "strict_cc": "When Strict Confirmation Codes are disabled a user can just use the enter key to acknowledge that they've seen a packing or order notification. When enabled a user must scan or enter their own confirmation code to proceed. About Packing notifications: There are two types of packing notifications. The first, \"Notes to Packer\" may be added to an order. It will appear when the order is packed. The second, \"Special Instructions to Packer\" can be added to a product. This notification will appear each time that product is packed. The user's Confirmation Code can be requested for either of these notifications. The strict confirmation code setting controls how the user is required to confirm.", "conf_code_product_instruction": "Special instructions to <PERSON>er can be added for any product from the product detail window. If a confirmation is required the Packer will be prompted to scan their unique confirmation code to proceed when the instructions are shown. If you want to ensure that confirmations are always required or never required for this type of notification you can do that here. Leaving it optional allows this setting to be controlled on a per product basis.", "packing_slip_size": "Choose the paper size you use for printing packing slips 4x6 is usually used for thermal printers. Standard 8.5x11 will print two packing slips per page.", "product_barcode_label_size": "Using It You can Change Size of Product Barcode Label.", "packing_slip_message_to_customer": "This is usually used for a thank you message and possibly instructions for receiving support. A store specific message can be set on the store setup page which will override this general message. Leave Blank to disable this general packing slip message", "order_item_export_message": "The Order Item Export allows you to select orders from the orders list and export them  to a CSV file. When this option is enabled, you'll find the export option in the Orders list Edit drop down. Depending on the option selected here the order items will either be grouped together by item (like a pick list) or the items required for each order will be grouped together by order, as they are on a packing slip.", "show_primary_bin_loc_in_barcodeslip": "Enabling this switch will add the locations to the product barcode label.", "html_print": "Packing slips can be printed as PDFs or using HTML for the layout. PDF works well for smaller volume but if you find that generating the PDFs takes time you can enable the HTML Packing slips to generate them in HTML. They should look the same when printed but if you save and archive them you may find the PDF format easier to work with.", "inactive_logout_time": "Inactive users will be logged out automatically after the number of minutes set here. Leave blank to disable.", "hex_barcode": "The barcode that appears on the ShipStation packing slip by default is called the 'Scan to view' barcode. To use this barcode to cue orders in GroovePacker without making changes to the ShipStation packing slip please leave this switch enabled. If you have an Order Number barcode on your packing slip would like to use that to open orders in GroovePacker, this switch should be disabled.", "custom_fields": "These fields allow you to rename the custom order and user fields to anything you like. The custom order fields are found on the Information tab of the order detail page and can be displayed in the order list as one of the visible columns. The custom user fields are found in the user detail window and are displayed on the user stats email report.", "pre_day_order_import_schedule": "In most cases you'll want to check orders before importing into GroovePacker so orders can be modified prior to importing and so only orders that will actually be shipping on the current day can be imported. If this is not the case and you have a large number of orders to import you may want to begin the import before workers arrive for the day to give the orders time to import. This scheduler can be used to run an import before the day begins so orders will be imported and ready.", "recurring_ftp_order_import_range": "The FTP will be checked for new orders every 10 minutes during the range specified here. Limiting the range to only the hours needed saves resources and increases performance", "display_kit_parts": "When enabled the part skus in a kit will be shown on the packing slip. If disabled only the kit sku will be displayed.", "remove_order_items": "In some cases line items in an order will be imported with a quantity of 0 from the shopping system. Since a 0 qty can not be scanned this is normally seen as an error and the order is flagged 'Action Required' so it can be reviewed. If you are aware that this occurs in your system and you would prefer that GroovePacker automatically remove the 0 qty line items please enable this option.", "show_imported_skus": "Display Imported SKUs in Order Items", "starting_value": "Please double click to enter starting value", "direct_printing_tooltip": "Enable the direct print options below to have these print jobs sent direct to your printer using the QZ print app. You can download and learn more about the app from <a href=\"https://groovepacker.freshdesk.com/en/support/solutions/articles/6000245428-direct-printing-sending-print-jobs-directly-to-the-printer-on-windows-mac-and-linux\" target=\"_blank\">here</a>.", "truncate_order_number_in_packing_slip": "Enable this Switch to Truncate the Order Number on Packing Slips. If enabled, specified string will be used to Truncate the Order Number..", "api_key": "Api Key", "generate_api_key": "Generate Api Key", "delete_api_key": "Delete Api Key", "regenerate_api_key": "Regenerate Api Key"}}, "scan_pack": {"headings": {"options": "Scan & Pack Options", "feedback": "<PERSON><PERSON><PERSON> Feedback", "scan_actions": "Scan Actions", "multi_box_option": "Multi-box Options", "scan_actions_sub_head": "Scanning these special action barcodes during order processing will perform the listed actions.", "product_first_scan_to_put_wall": "Product First - Scan to put wall settings", "js_printmanager": "JSPrintManager Configuration", "camera_options": "Camera Options", "packing_options": "Packing Cam & Recipient Page", "per_box_packing_slip": "Per-box Packing Slips", "per_box_shipping_label_creation": "Shipping Label Creation"}, "labels": {"tracking_number_validation": "Tracking Number Validation", "enable_click_sku": "<PERSON><PERSON>", "requires_assigned_orders": "Quick-Scan workflow requires assigned orders", "ask_tracking_number": "Post-Scanning Functions 1", "show_success_image": "Display Success Image", "reset_order_barcode_label_printer": "Reset Order Barcode Label Printer", "show_fail_image": "Display Fail Image", "show_order_complete_image": "Display Image on Order Completion", "for": "for", "scan": "scan", "seconds": "seconds", "play_success_sound": "Play Success Sound", "play_fail_sound": "Play Fail Sound", "play_order_complete_sound": "Play Sound on Order Complete", "skip_code": "Skips the current item if the item is Skippable.", "remove_skipped": "Remove Skipped Items", "note_from_packer_code": "Create a 'note from packer' for the current order", "service_issue_code": "Give current order \"Service Issue\" status", "restart_code": "Restart current Order", "click_scan_code": "Simulate a correct scan for the current item marking it scanned.", "scanned_code": "Mark all items on the current order Scanned", "add_next_code": "Add items to the order using the scanner", "partial_code": "Remove all unscanned items from the order and mark the order Scanned.", "remove_code": "Remove the currently suggested item from the order.", "type_scan_code": "Type-In Counts", "escape_string": "Barcode suffix removal", "lot_number": "Record Lot Number", "show_customer_notes": "Show Customer Notes", "show_internal_notes": "Show Internal Notes", "send_external_logs": "Send External Logs", "show_tags": "Show Tags", "cue_orders_for_scanpack": "Cue Orders For Scan & Pack By Tracking Number", "intangible_setting": "Auto-Recognise Intangible Items", "intangible_string": "Intangible Identifier", "cue_order_for_scan": "Cue orders for Scan and Pack using", "ask_tracking_number_second": "Post-Scanning Functions 2", "simple_product": "Simple Product Scanning", "scan_all": "Scan-<PERSON>", "scan_to_cart": "<PERSON><PERSON>", "camera_option": "Camera Option", "capture_event": "Capture Event", "choice_of_resolution": "Choice Of Resolution", "packing_cam_enabled": "Enable", "email_customer_option": "Email Customer", "capture_image_option": "Capture Image", "email_subject": "Email Subject", "email_reply": "Reply To Email", "subject": "Subject", "email_logo": "<PERSON><PERSON>", "scanning_log": "Display Scanning Log", "email_insert_dropdown": "Insert", "customer_email_insert_dropdown": "Insert", "customer_page_logo": "Packing Cam Logo", "multi_box_shipment": "Multi-box Shipments", "print_when_order_complete": "Print when the order is complete", "print_when_new_box": "Print when new boxes are started", "manually_print": "Are printed manually", "per_box_shipping_label_creation_none": "None", "per_box_shipping_label_creation_after_box": "After each box is completed", "per_box_shipping_label_creation_after_order": "After order is completed", "order_num_esc_str_removal": "Removal String", "order_num_esc_str_enabled": "Order number prefix removal"}, "tooltips": {"tracking_number_validation": "When this option is disabled any input provided by the user will be recorded as the tracking number. This option attempts to check that the input is actually a valid tracking number by comparing the characters that the scan begins with to the strings saved here. When this option is enabled the input must begin with one of the saved validation strings or the scan will not be saved.", "enable_click_sku": "When Click Scanning is enabled for a product you can emulate a scan of the item and add it to the order in Scan and Pack without scanning or typing the barcode.<br/>This is helpful for testing or for when barcodes are damaged and unreadable by the scanner. Because this feature has a potential for being abused it is possible to disable it on a product by product basis or for all items. Although click-scans work exactly like regular scans in all respects they are logged differently and are reported in order export reports.", "requires_assigned_orders": "The Quick-Scan workflow is an order-by-order, paperless workflow. Users click the Quick-Scan button and an order that needs to be scanned is selected for them automatically. Enabling this switch will ensure that the Quick-Scan button only suggests orders assigned to the user. This option is required when multiple team members are using the Quick-Scan workflow at the same time.", "ask_tracking_number": "When this option is enabled the packer will be prompted to scan a tracking number after the last item of an order has been packed.", "feedback": "Scan Success and scan fail messages are visual feedback to let the packer know if they scanned the correct product.", "skip_code": "This will skip the current item if it is skippable", "remove_skipped": "The SKIP action barcode can be used to skip specific items. The skip option must be enabled on the product's options page. When an item is skipped, allocated inventory is returned to available. If the \"Remove Skipped Items\" option is enabled the skipped items will be removed from the order completely and no longer show in the order item list or on a packing slip, otherwise they will still be listed among the order items. The skip option can be used for non-essential order items like promo items that can not be included.", "note_from_packer_code": "This will open the text area so the packer can begin typing.", "service_issue_code": "If a packer realizes during the packing of an order that packing can not be completed due to damaged items or an inventory issue, they can scan this code to save the current order as a service issue and leave a note from packer explaining the issue.", "restart_code": "Rather than clicking the Restart Order button on the Scan & Pack interface you can scan this barcode. All items already scanned for the current order will be removed and GroovePacker will be ready for you to rescan the same order or another order.", "type_scan_code": "Scanning the barcode defined here lets <PERSON><PERSON><PERSON><PERSON> know you're about to enter a Type-In Scan Count. By using a short barcode like the default * sign, you can quickly type it in and press enter, rather than scanning if desired. Click the printer icon to print your Type-In barcode.", "type_in_counts": "When enabled this setting allows you to scan one of an item and then manually type in a quantity rather than scanning each item one by one. It's ideal when you have dozens of smaller items that are pre-counted, or any time scanning every item of a large multiple is not desirable.<br/><br/>To trigger the type-in count feature you'll use the trigger defined here. By default it's an Asterisk but it can be modified if needed.<br/><br/>To enter a Type-In Count for an item:<br/>Scan at least 1 of the item <br/>Scan (or type and enter) your Type-In counts trigger. <br/>Type in the count when prompted and press Enter<br/><br/>If the amount entered is not correct you will be prompted to try again, otherwise the amount entered will be added to the order as if it had been scanned.", "show_customer_notes": "Normally only <strong>Notes to Packer</strong> and special product instructions are displayed during packing. Enabling this switch will also display the <strong>Customer Comments</strong> to the packer when available.", "show_internal_notes": "Normally only <strong>Notes to Packer</strong> and special product instructions are displayed during packing. Enabling this switch will also display the <strong>Internal notes</strong> to the packer when available.", "send_external_logs": " <strong>Only for Debugging*</strong> External logs would be sent to loggly for debugging the logs .", "show_tags": "Normally only <strong>Notes to Packer</strong> and special product instructions are displayed during packing. Enabling this switch will also display the <strong>Tags</strong> to the packer when available.", "escape_string": "When recording is enabled the suffix after the string will be saved and can later be exported. This can be used to record lot or serial numbers that are part of the barcode. You can export including the order information and the lot numbers scanned from Settings > Backup and Export > Order Export. Use the 'include order items with Lot/Serial numbers' option", "record_suffix": "When enabled, the barcode suffix removal feature can automatically recognize and separate a barcode into two parts. The first part is the UPC used to identify the correct products. The second part is a lot number or serial number preceeded by a seperator string. The string that should be used is saved here. There are two fields so it is possible to have two seperate strings if needed.", "ask_post_scanning_functions": "The post scanning function you choose will occur immediately after the last item is scanned in each order.<br><br>Shipping Label Verification: If you import orders into GroovePacker after creating shipping labels and the tracking number is imported into GroovePacker along with the order info, you can choose the Shipping Label Verification option to prompt the packer to scan the shipping label before adhering it to the box. This ensures the correct label is always applied.<br><br>Record Tracking Number: If you import orders into GroovePacker before shipping labels are created, no tracking information will be available, but you can use the Record Tracking Number function to prompt packers to scan the shipping label after it's printed so the tracking information is saved with the order in GroovePacker. This makes it available for customer service refrence and serves as a double check that the shipping label was actually created.<br><br>Print Packing Slip: If new items are added to your order during scanning this option will automatically generate a packing slip immediately after completing an order so it can be included in the package.<br><br>Print Order Barcode: If you print shipping labels in a separate process or location, after packing all shipments, you'll likely want to seal each box after packing to ensure nothing is added or removed. This option automatically prints a 3\" x 1\" label with the recipients name and the order number as a barcode after each order is packed. This label can be applied to the outside of the box and used to cue the order in your shipping software when you're ready to create a shipping label. It may also be used for RMA purposes when items are returned or shipments are refused.", "cue_orders_optons": "In most cases a packing slip with an order number barcode is scanned to cue orders for packing. If you are printing shipping labels which include the order packing list before importing orders you can choose to cue orders by scanning the the tracking number from the shipping label using this option.", "intangible_setting": "Items that do not get physically packed can be hidden from Scan & Pack and packing slips using the intangible opiton on the products page.<br><br>This setting allows GroovePacker to automatically identify intangible items based on a string that is always included in their product name or SKU. This allows new items that have never been imported to be properly set to intangible.<br>For exmaple if all coupons start with: DISC- You would enter that as the identifier. If you have multiple coupons and require multiple identifiers you can list them separated by commas, and with no spaces: DISC-,OFFR-,SP-", "intangible_setting_gen_barcode_from_sku": "Coupon codes and other intangible items are often imported without barcodes and set to 'New'.  When enabled, this option will generate a barcode from the sku for any intangible items found during an order import. Enable this option of you find orders are being set to 'Action Required' due to intangible items.", "post_scan_pause": "This option should only be enabled if GroovePacker indicates the barcodes being scanned are incorrect and the incorrect value submitted would be right except that it's missing the last digit or two.", "display_location_on_scanning_page": "When enabled the respective bin location will be displayed when items are suggested for scanning. Location 1 is displayed on the mobile app. All 3 locations can be displayed on the web app.", "string_removal": "When enabled the string entered here will be ignored during scan and pack. This is helpful when your product barcodes contain an extra character or string that is not part of the barcode value you have imported into GroovePacker. Any character entered here as well as spaces are counted as part of the string.", "order_verification": "This feature allows GroovePacker to be used to track the processing of orders instead of verifying order items. When enabled the barcode used to display the order in the scan and pack section can be scanned again to mark the order as Scanne<PERSON> and move it to the scanned list. The order's activity log will show that Single Scan Verification was used.", "cue_order_for_scan": "Select \"packing slip\" if you will be scanning an order number barcode or ShipStation's \"scan to view\" barcode to cue orders for scanning. If you will be scanning the tracking number barcode on the shipping label, please choose the Shipping Label option.", "return_to_orders": "If you cue orders for Scan and <PERSON> from the Orders list rather than by scanning on the Scan and Pack page you can enable this switch to have GroovePacker display the orders list after scanning is complete.", "scanning_sequence_tool": "By default, any item in an order can be scanned in any sequence. These options can be used in situations when scanning in the suggested sequence is critical. The sequence in which items are suggested is determined first by their bin location. If no bin location is present it is determined by their SKU. Items missing a bin location are shown before those that have a bin location so they can be identified and action can be taken.", "click_scan_tooltip": "Click-scanning allows the packer to add items to an order that have a missing or damaged barcode. Normally this is done by clicking or tapping the SKU of the item on the Scan and Pack interface. However, this requires the use of the mouse, so it could potentially slow the scanning process. The CLICKSCAN action barcode can be scanned instead, allowing the currently suggested item to be packed using only the scanner.", "pass_scan_tooltip": "The Pass function allows the packer to visually verify items that have a missing or damaged barcode. One can also click the Pass button on the Scan and Pack interface to pass items but scanning the PASS barcode allows items to be passed with only the scanner", "scanned_barcode_tooltip": "Scanning the SCANNED action barcode will mark all un-scanned items as scanned and move the order to the scanned status.", "partial_barcode_tooltip": "Scanning the REMOVE-ALL action barcode will remove all un-scanned items from the order. The items will continue to show in the order item list but any remaining quantity of the item will be removed from the total quantity of the item. Because no un-scanned items will remain in the order after this action, the order will be moved to the scanned status.", "remove_barcode_tooltip": "Scanning the REMOVE action barcode will remove the currently suggested item from the order. The item will continue to show in the order item list but any remaining quantity of the item will be removed from the total quantity of the item.", "add_next_barcode_tooltip": "Normally items must be present in an order for them to be scanned and accepted. Scanning the ADDNEXT barcode will prompt the user to scan an item that will be added to the order. This can be used along with the REMOVE barcode to make substitutions", "ask_post_scanning_functions_second": "The second post scanning function allows two separate actions to be triggered after the order scanning is complete.", "require_serial": "To prevent product or order barcodes from being accidentally scanned as serial or lot numbers, you can assign a prefix to all of your lot/serial numbers and enter it here. When enabled, only serial/lot numbers with this prefix will be accepted.", "replace_gp_code_tootip": "This option can automatically replace coupon codes with a generic coupon item. It is useful when you have many different discount codes used daily and they are increasing your product count.", "simple_product_scanning": "This option disables non-critical scanning features (ie making barcodes case-insensitive). When enabled it reduces scanning time by a small amount. If you have orders with a high number of different skus you may notice more of a speed increase.", "scan_all": "This option is only present in the GPX App. When enabled, the user will be given an option button to confirm all remaining pieces of an item. The scan-all button will only be available after the user has scanned the first piece correctly and there are 2 or more pieces remaining. The option is not shown when the first piece is passed using the pass button.", "scan_to_cart": "When enabled the scan to cart workflow will be available to all users on the account.", "packing_cam_enabled": "When enabled, an image of the items scanned can be taken when the scanning of the order is complete. The image will be saved with the order for future reference.", "email_customer_option": "When enabled, the customer will be emailed a link where they can view the image of their order items. The text of the email and the packing detail page can be customized using the fields below.", "capture_image_option": "", "scanning_log": "Display Scanning Log", "multi_box_shipment": "Multi-box Shipments", "order_num_esc_str_enabled": "This option applies only to the GPX app. If the order number barcode or tracking number used to cue orders for scanning contains a prefix, this setting can be used to ignore the portion of the barcode that does not match the order number or tracking number provided to GroovePacker."}}}, "backup_restore": {"tooltips": {"unique_order_items": "This setting is only enabled when orders are comprised of unique or custom items which each require their own SKU and barcode.", "generate_barcode_from_sku": "In some cases you may want to use your SKU as both a SKU and in internal barcode. To do so, enable this setting.", "permit_duplicate_barcodes": "By default, each barcode is checked to ensure it is unique before it is saved. When this switch is enabled the check is skipped so the same barcode can be saved with multiple product records. For most cases, you'll want to leave this option disabled.", "use_sku_as_product_name": "In some cases you may want to use your SKU as both a SKU and product name. To do so, enable this setting.", "order_date_time_format": "Choose the Date/Time format closest to your file. Please note that both double digit ( 05/05 ) and single digit ( 5/5 ) date formats are accepted.", "day_month_sequence": "Select the appropriate radio button to specify the sequence of month and day in the mapped column of csv Order Date/Time.", "create_new_product": "Creates a new product for each record if the product doesn't already exist. Otherwise, skips the record.", "add_update_existing_product": "Updates the existing products only. Skips the record if the product doesn't exist", "create_update_product": "Creates a new product if the product doesn't exist. Otherwise, updates the product", "skip_order": "All existing orders will be skipped - left as they are", "update_order": "All un-scanned orders that differ will be updated to match the most recent file."}}, "csv_modal": {"tooltips": {"ftp_address": "Include the path to the directory the csv files are found. GroovePacker will automatically detect the most recently updated csv file that does not include \"-imported\" in its file name. After a successfull import \"-imported\" will be appended to the file name.", "import_from_ftp": "Please choose a saved import map to use for the FTP import.<br/> If you do not have a saved map you can import an order file with the same format from your computer and save a map that can be used for FTP imports.", "pull_combined_qoh": "When enabled, the combined quantities from all the shopify locations will be saved to Groovepacker.", "shopify_inventory_sync": "Each product in GroovePacker has a setting to control whether it’s QOH will be synced during Push and Pull operations. You can use these buttons to overwrite the setting for all items in bulk. For example, if you want all but a few products to be synced you can use the “Enable Sync for every item” option, and then modify just those items that you do not wish to sync."}}, "accounts": {"headings": {"credit_cards": "Card Details", "invoices": "Invoices"}, "labels": {"all_cards": "Your Cards"}}}}