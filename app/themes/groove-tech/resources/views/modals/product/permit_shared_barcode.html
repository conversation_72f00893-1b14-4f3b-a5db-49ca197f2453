<!-- show order modal -->
<div style="padding-top: 100px;">

  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Shared Barcode</div>
  </div>
  <div class="modal-body" style="font-size: 16px;">
    <div class="container-fluid form-horizontal">
      <div class="row">
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
          <h2 class="text-center">Permit a Shared Barcode to be used across multiple items?</h2>
          <div class="text-center">
            <br/>
            <img src="/assets/images/shared_barcode.png" alt="shared_barcode" style="width: 20%;height: 20%;"/>
            <br/><br/>
            <b>Barcode: {{matching_barcode}}</b>
            <br/><br/>
          </div>
          Since the items are different it is recommended to assign unique barcodes so that <PERSON><PERSON><PERSON> will be able to distinguish between them.
          </br></br>
          There are some special cases when you might decide to have the same barcode used for two or more unique items. This is not a recommended practice since GroovepPacker and other apps that use the barcode will not be able to tell them apart.
        </div>
        <div class="col-lg-2"></div>
      </div>
      </br>
      <div class="row text-center alias_popup_buttons">
        <button ng-click="allow_shared_barcode()" class="modal-cancel-button" style="background: #d7a549;font-size: 16px;">Yes, I want to permit these separate items to have the same barcode</button>
        <button ng-click="have_unique_barcode()" class="modal-cancel-button" style="background: green;font-size: 16px;">No I will assign unique barcode to this item</button>
      </div>
     <br/>
      <div class="row">
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
          <div>
            <b>Name: </b>{{current_product_data.name}}
            <br/>
            <b>SKU: </b> {{current_product_data.sku}}
            <br/><br/>
          </div>
          <div ng-repeat="shared_product in shared_bacode_products">
            <b>Name: </b>{{shared_product.name}}
            <br/>
            <b>SKU: </b> {{shared_product.sku}}
            <br/><br/>
          </div>
        </div>
        <div class="col-lg-2"></div>
      </div>
      <br/><br/>
      <div class="row alias_popup_notiy" style="background: white;width: auto;">
        <i class="fa fa-lightbulb-o fa-2x" aria-hidden="true"></i>&nbsp; An example of when this might be used would be where a manufacturer has used the same barcode on 3 variants of an item. Perhaps the item is the same but the packaging differs slighly. It is not practical to re-barcode items since their would be no cost incurred if the wrong variant was shipped.
        <br/><br/>
        Another scenario would be a regular item and a "tester" version. Both share the same barcode and are the same item, but they have differing SKUs for inventory tracking purposes. Here it would be possible to alias the items but by keeping them separate it is possible to show the packer instructions that are specific to one of the SKUs.
      </div>
      <br/>
    </div>
    <br/>
  </div>

  <div class="modal-footer">
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>