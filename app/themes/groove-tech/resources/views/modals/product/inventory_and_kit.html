<div class="container-fluid form-horizontal" style="margin-top: 15px; margin-bottom: 15px;">
<!--  <h4 class="text-right">Print Inventory Receiving Label&nbsp;<a href="" ng-click="print_receive_label($event)"
                                                                     style="padding:10px;"><i
        class="icon-large glyphicon glyphicon-print"></i></a></h4> -->

      <div class="form-group" ng-hide="products.single.basicinfo.is_kit==1">
        <div class="controls col-xs-12">
          <div groov-data-grid="warehouseGridOptions" groov-list="products.single.inventory_warehouses"></div>
        </div>
      </div>
      <!-- <div class="form-group">
        <div class="controls col-xs-12" dropdown>
          <button class="modal-save-button dropdown-toggle" ng-show="warehouses.list.length">Add warehouse <span
            class="caret"></span></button>
          <button ng-click="remove_warehouses()" class="modal-remove-button">Remove selected</button>
          <ul class="dropdown-menu" role="menu">
            <li ng-repeat="warehouse in warehouses.list"><a
              ng-click="add_warehouse(warehouse)">{{warehouse.info.name}}</a></li>
          </ul>
        </div>
      </div> -->

      <fieldset>
        <div>
        <span ng-show="products.single.basicinfo.is_kit==1">
        <legend>Kit Options</legend>
        <div class="row">
          <div class="col-xs-12">
            <div groov-data-grid="kitEditableOptions" groov-list="products.single.productkitskus"></div>
          </div>
        </div>
        <!-- <table class="table table-hover table-bordered table-striped table-condensed">
          <thead>
          <tr>
            <th>
              <div>Item Name</div>
            </th>
            <th>
              <div>Item SKU</div>
            </th>
            <th>
              <div>Quantity in Kit</div>
            </th>
            <th>
              <div>Available Inv</div>
            </th>
            <th>
              <div>QTY On Hand</div>
            </th>
            <th>
              <div>Product Status</div>
            </th>
            <th>
              <div>Packing Order</div>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr ng-repeat="kit_sku in products.single.productkitskus" ng-class="{selected:kit_sku.checked}"
              ng-click="kit_sku.checked = !kit_sku.checked">
            <td><a href="" ng-click="load_kit(kit_sku,$event)">{{kit_sku.name}}</a></td>
            <td>{{kit_sku.sku}}</td>
            <td groov-editable="kitEditableOptions" ng-model="kit_sku" prop="qty"
                identifier="kit_sku-qty-{{$index}}"></td>
            <td>{{kit_sku.available_inv}}</td>
            <td>{{kit_sku.qty_on_hand}}</td>
            <td><span class="label label-default" ng-class="{'label-success': kit_sku.product_status=='active', 'label-info': kit_sku.product_status=='new'}" style="text-transform: capitalize;">{{kit_sku.product_status}}</span></td>
            <td groov-editable="kitEditableOptions" ng-model="kit_sku" prop="packing_order"
                identifier="kit_sku-packing_order-{{$index}}"></td>
          </tr>
          </tbody>
        </table> -->

        <div class="form-group">
          <div class="controls col-sm-8">
            <button class="modal-save-button"
                    ng-click="product_alias('kit',kit_products.list,products.single.basicinfo.id)">Add product to kit
            </button>
            <button ng-click="remove_skus_from_kit()" class="modal-remove-button">Remove selected</button>
          </div>
        </div>
        <span ng-show="products.single.unacknowledged_kit_activities.length > 0">
          <h4>
            Activity Log
            <span style="cursor: pointer; font-size: 18px; margin-left: 1px;">
              <span ng-click="sortActivities('asc')" title="Sort Oldest First" style="margin: 0; padding: 0;">↑</span>
              <span ng-click="sortActivities('desc')" title="Sort Newest First" style="margin: -5px; padding: 0;">↓</span>
            </span>
          </h4>
        
          <div class="form-group">
            <div class="controls col-sm-8"
                 ng-repeat="activity in products.single.unacknowledged_kit_activities | orderBy:sortOrder">
              <h5>{{ activity.created_at | date:'EEEE MM/dd/yyyy hh:mm:ss a' }}</h5>
              {{ activity.activity_message }} - by: {{ activity.username }}
            </div>
          </div>
        </span>
        

        <h4>This Kit Should</h4>
        </span>
        <div class="form-group  col-sm-6">
          <div class="controls" ng-show="products.single.basicinfo.is_kit==1" style="margin-bottom: 25px;">
            <div class="radio">
              <label class="control-label">
                <input type="radio" ng-change="update_single_product()" ng-model="products.single.basicinfo.kit_parsing"
                       value="single"/> Always be scanned as single SKU
              </label>
            </div>
            <div class="radio">
              <label class="control-label">
                <input type="radio" ng-change="update_single_product()" ng-model="products.single.basicinfo.kit_parsing"
                       value="individual"/> Always be scanned as individual part SKUs
              </label>
            </div>
            <div class="radio">
              <label class="control-label">
                <input type="radio" ng-change="update_single_product()" ng-model="products.single.basicinfo.kit_parsing"
                       value="depends"/> Automatically switch as needed when part SKUs are scanned
              </label>
            </div>
          </div>
          <div>
            <div ng-if="products.single.access_restrictions.allow_bc_inv_push">
              <div class="form-group" style="margin-bottom: 0px;">
                <label class="control-label col-sm-3">BigCommerce Inventory Sync</label>

                <div class="controls col-sm-2">
                  <div toggle-switch ng-model="products.single.sync_option.sync_with_bc"
                       groov-click="update_product_sync_options()"></div>
                </div>
                <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_bc">
                  <input type="text" ng-model="products.single.sync_option.bc_product_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="BigCommerce Product ID"/>
                </div>
                <div class="controls col-sm-4" ng-show="products.single.sync_option.sync_with_bc">
                  <input type="text" ng-model="products.single.sync_option.bc_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="BigCommerce Product Sku"/>
                </div>
              </div>
              <div ng-show="products.single.sync_option.sync_with_bc">
                <div class="form-group" ng-hide="products.single.sync_option.bc_product_id && products.single.sync_option.bc_product_sku">
                  <div class="control-label col-sm-3"></div>
                  <div class="col-sm-7">
                    <small>*Adding product ID and SKU is necessary for non-BigCommerce products ineventory sync.</small>
                  </div>
                </div>
              </div>
            </div>

            <div ng-if="products.single.access_restrictions.allow_mg_rest_inv_push">
              <div class="form-group" style="margin-bottom: 0px;">
                <label class="control-label col-sm-3">Magento Inventory Sync</label>

                <div class="controls col-sm-2">
                  <div toggle-switch ng-model="products.single.sync_option.sync_with_mg_rest"
                       groov-click="update_product_sync_options()"></div>
                </div>
                <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_mg_rest">
                  <input type="text" ng-model="products.single.sync_option.mg_rest_product_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Magento 2 Product ID"/>
                </div>
                <div class="controls col-sm-4" ng-show="products.single.sync_option.sync_with_mg_rest">
                  <input type="text" ng-model="products.single.sync_option.mg_rest_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Magento Product Sku"/>
                </div>
              </div>

              <div ng-show="products.single.sync_option.sync_with_mg_rest">
                <div class="form-group" ng-hide="products.single.sync_option.mg_rest_product_id && products.single.sync_option.mg_rest_product_sku">
                  <div class="control-label col-sm-3"></div>
                  <div class="col-sm-7">
                    <small>*Adding product ID and SKU is necessary for for magento products inventory sync.</small>
                  </div>
                </div>
              </div>
            </div>

            <div ng-if="products.single.store.store_type=='Shopify' && products.single.access_restrictions.allow_shopify_inv_push">
              <div class="form-group" style="margin-bottom: 0px;">
                <label class="control-label col-sm-3">Shopify Inventory Sync</label>

                <div class="controls col-sm-2">
                  <div toggle-switch ng-model="products.single.sync_option.sync_with_shopify"
                       groov-click="update_product_sync_options()"></div>
                </div>
                <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_shopify">
                  <input type="text" ng-model="products.single.sync_option.shopify_product_variant_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Shopify Product Variant ID"/>
                </div>
              </div>
              <div ng-show="products.single.sync_option.sync_with_shopify">
                <div class="form-group" ng-hide="products.single.sync_option.shopify_product_variant_id">
                  <div class="control-label col-sm-3"></div>
                  <div class="col-sm-7">
                    <small>*Adding product variant ID is necessary for non-Shopify products ineventory sync.</small>
                  </div>
                </div>
              </div>
            </div>
            <div ng-if="products.single.store.store_type=='Shopline' && products.single.access_restrictions.allow_shopline_inv_push">
              <div class="form-group" style="margin-bottom: 0px;">
                <label class="control-label col-sm-3">Shopline Inventory Sync</label>

                <div class="controls col-sm-2">
                  <div toggle-switch ng-model="products.single.sync_option.sync_with_shopline"
                       groov-click="update_product_sync_options()"></div>
                </div>
                <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_shopline">
                  <input type="text" ng-model="products.single.sync_option.shopline_product_variant_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Shopline Product Variant ID"/>
                </div>
              </div>
              <div ng-show="products.single.sync_option.sync_with_shopline">
                <div class="form-group" ng-hide="products.single.sync_option.shopline_product_variant_id">
                  <div class="control-label col-sm-3"></div>
                  <div class="col-sm-7">
                    <small>*Adding product variant ID is necessary for non-Shopline products ineventory sync.</small>
                  </div>
                </div>
              </div>
            </div>

            <div ng-if="products.single.access_restrictions.allow_teapplix_inv_push">
              <div class="form-group" style="margin-bottom: 0px;">
                <label class="control-label col-sm-3">Teapplix Inventory Sync</label>

                <div class="controls col-sm-2">
                  <div toggle-switch ng-model="products.single.sync_option.sync_with_teapplix"
                       groov-click="update_product_sync_options()"></div>
                </div>
                <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_teapplix">
                  <input type="text" ng-model="products.single.sync_option.teapplix_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Teapplix Product Sku"/>
                </div>
              </div>
              <div ng-show="products.single.sync_option.sync_with_teapplix">
                <div class="form-group" ng-hide="products.single.sync_option.teapplix_product_sku">
                  <div class="control-label col-sm-3"></div>
                  <div class="col-sm-7">
                    <small>*Adding product SKU is necessary for Teapplix products ineventory sync.</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
          <div class="col-sm-6 remove_padding_left subtract_margin_left15" style="float: right;">
          <label class="control-label col-sm-4 remove_padding_left"><br/><br/>Product Receiving Instructions</label>

          <div class="controls col-sm-8 remove_padding">
            <i class="info icon-large glyphicon glyphicon-info-sign pull-right" popover-trigger="mouseenter"
               groov-popover="{{translations.tooltips.product_receiving_instructions}}"></i>

            <div text-angular
                 ta-toolbar="[['html','h1','h2','h3','h4','h5','h6','p','ol','ul'],['bold','italics','underline','strikeThrough'],['justifyLeft','justifyCenter','justifyRight','indent','outdent'],['wordcount','charcount']]"
                 ng-model="products.single.basicinfo.product_receiving_instructions"
                 ng-blur="update_single_product()"></div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-4"></label>

            <div class="controls col-sm-8">
              <div class="img-thumbnail"
                   ng-repeat="image in products.single.images | filter:{added_to_receiving_instructions:true} track by image.id">
                <img ng-src="{{ image.image }}" class="img-polaroid img-thumb img-responsive"
                     style="border: 1px solid grey;"/>
                <i class="glyphicon glyphicon-remove" ng-click="remove_instruction_image(image)"></i>
              </div>
              <button class="modal-save-button" ng-click="add_image_for_receiving_instructions()">Add Image</button>
            </div>
          </div>
           <h4 class="text-right">Print Inventory Receiving Label&nbsp;<a href="" ng-click="print_receive_label($event)" style="padding:10px;"><i
        class="icon-large glyphicon glyphicon-print"></i></a></h4>
        </div>
      </fieldset>
     <!--  <h4 class="text-right">Print Inventory Receiving Label&nbsp;<a href="" ng-click="print_receive_label($event)" style="padding:10px;"><i
        class="icon-large glyphicon glyphicon-print"></i></a></h4> -->
</div>