<!-- show order modal -->
<div style="padding-top: 100px;">

  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Potential Alias</div>
  </div>
  <div class="modal-body" style="font-size: 16px;">
    <div class="container-fluid form-horizontal">
      <div class="row text-center">
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
          <h3 class="text-center">Duplicate barcode identified. An alias may be required.</h3>
          <br/>
          <p style="text-align: left;">
            You have tried to add a barcode that already exists on another item.
            Aliasing is often used in this situation to combine multiple SKUs in the same item record.
            </br></br>
            If both of these SKUs, refer to the same physical product, we can add the new sku to the existing item.
          </p>
        </div>
        <div class="col-lg-2"></div>
      </div>
      </br>
      <div class="row popup_alias_row">
        <div class="col-lg-1 square_div_col"></div>
        <div class="col-lg-3 square_div" style="background: #DBEAF7;">
          <b>This new item</b>
          <br/><br/>
          <b>Name: </b>{{current_product_data.name}}
          <br/>
          <b>SKU: </b> {{current_product_data.sku}}
          <br/>
          <b>Barcode: </b> {{current_product_data.barcode}}
          <br/>
        </div>
        <div class="col-lg-1 square_div_col">
          <img src="/assets/images/plus_sign.png" alt="plus"/>
        </div>
        <div class="col-lg-3 square_div" style="background: #C0E7C3;">
          <b>Will be added to this existing item</b>
          <br/><br/>
          <b>Name: </b>{{alias_product_data.name}}
          <br/>
          <b>SKU: </b> {{alias_product_data.sku}}
          <br/>
          <b>Barcode: </b> {{alias_product_data.barcode}}
          <br/>
        </div>
        <div class="col-lg-1 square_div_col">
          <img src="/assets/images/equal_sign.png" alt="equal"/>
        </div>
        <div class="col-lg-3 square_div" style="background: #009E0F;color: white;">
          <b>Resulting in this item</b>
          <br/><br/>
          <b>Name: </b>{{after_alias_product_data.name}}
          <br/>
          <b>SKU: </b> {{after_alias_product_data.sku}}
          <br/>
          <b>Barcode: </b> {{after_alias_product_data.barcode}}
          <br/>
        </div>
      </div>
      <br/><br/>
      <div class="row text-center">
        Not to sound ominous but....<br/>
        This can not be undone.
      </div>
      <br/><br/>
      <div class="row text-center alias_popup_buttons">
        <button ng-click="consider_different_items()" class="modal-cancel-button" style="background: #d7a549;font-size: 16px;">No Thanks, These items are different</button>
        <button ng-click="proceed_with_aliasing()" class="modal-cancel-button" style="background: green;font-size: 16px;">Proceed with Aliasing</button>
      </div>
      <br/>
      <div class="row text-center alias_popup_notiy">
        <i class="fa fa-lightbulb-o fa-2x" aria-hidden="true" style=" vertical-align: middle;"></i>&nbsp;
        If you are seeing this often you may want to use a CSV to add multiple SKUs to the same item.
      </div>
    </div>
    <br/>
  </div>

  <div class="modal-footer">
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>

<script language="Javascript" type="text/javascript">
  setTimeout(function(){ set_cols_width(); }, 1000);
  function set_cols_width(){
    var max_height = 0;
    if ($('.popup_alias_row').children().length) {
      angular.forEach($('.popup_alias_row').children(), function(value, key){
        if (max_height < value.offsetHeight ) {
          max_height = value.offsetHeight;
        }
      });
      angular.forEach($('.popup_alias_row').children(), function(value, key){
        value.style.height = max_height + "px";
      });
    }
  }
</script>
