<div class="container-fluid form-horizontal">

     <!--  <div ng-if="products.single.access_restrictions.allow_bc_inv_push">
        <div class="form-group" style="margin-bottom: 0px;">
          <label class="control-label col-sm-2">BigCommerce Inventory Sync</label>

          <div class="controls col-sm-1 col-lg-1">
            <div toggle-switch ng-model="products.single.sync_option.sync_with_bc"
                 groov-click="update_product_sync_options()"></div>
          </div>
          <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_bc">
            <input type="text" ng-model="products.single.sync_option.bc_product_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="BigCommerce Product ID"/>
          </div>
          <div class="controls col-sm-4" ng-show="products.single.sync_option.sync_with_bc">
            <input type="text" ng-model="products.single.sync_option.bc_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="BigCommerce Product Sku"/>
          </div>
        </div>
        <div ng-show="products.single.sync_option.sync_with_bc">
          <div class="form-group" ng-hide="products.single.sync_option.bc_product_id && products.single.sync_option.bc_product_sku">
            <div class="control-label col-sm-3"></div>
            <div class="col-sm-7">
              <small>*Adding product ID and SKU is necessary for non-BigCommerce products ineventory sync.</small>
            </div>
          </div>
        </div>
      </div>

      <div ng-if="products.single.access_restrictions.allow_mg_rest_inv_push">
        <div class="form-group" style="margin-bottom: 0px;">
          <label class="control-label col-sm-2">Magento Inventory Sync</label>

          <div class="controls col-sm-1 col-lg-1">
            <div toggle-switch ng-model="products.single.sync_option.sync_with_mg_rest"
                 groov-click="update_product_sync_options()"></div>
          </div>
          <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_mg_rest">
            <input type="text" ng-model="products.single.sync_option.mg_rest_product_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Magento 2 Product ID"/>
          </div>
          <div class="controls col-sm-4" ng-show="products.single.sync_option.sync_with_mg_rest">
            <input type="text" ng-model="products.single.sync_option.mg_rest_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Magento Product Sku"/>
          </div>
        </div>

        <div ng-show="products.single.sync_option.sync_with_mg_rest">
          <div class="form-group" ng-hide="products.single.sync_option.mg_rest_product_id && products.single.sync_option.mg_rest_product_sku">
            <div class="control-label col-sm-3"></div>
            <div class="col-sm-7">
              <small>*Adding product ID and SKU is necessary for for magento products inventory sync.</small>
            </div>
          </div>
        </div>
      </div>

      <div ng-if="products.single.access_restrictions.allow_shopify_inv_push">
        <div class="form-group" style="margin-bottom: 0px;">
          <label class="control-label col-sm-2">Shopify Inventory Sync</label>

          <div class="controls col-sm-1 col-lg-1">
            <div toggle-switch ng-model="products.single.sync_option.sync_with_shopify"
                 groov-click="update_product_sync_options()"></div>
          </div>
          <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_shopify">
            <input type="text" ng-model="products.single.sync_option.shopify_product_variant_id" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Shopify Product Variant ID"/>
          </div>
        </div>
        <div ng-show="products.single.sync_option.sync_with_shopify">
          <div class="form-group" ng-hide="products.single.sync_option.shopify_product_variant_id">
            <div class="control-label col-sm-3"></div>
            <div class="col-sm-7">
              <small>*Adding product variant ID is necessary for non-Shopify products ineventory sync.</small>
            </div>
          </div>
        </div>
      </div>

      <div ng-if="products.single.access_restrictions.allow_teapplix_inv_push">
        <div class="form-group" style="margin-bottom: 0px;">
          <label class="control-label col-sm-2">Teapplix Inventory Sync</label>

          <div class="controls col-sm-1 col-lg-1">
            <div toggle-switch ng-model="products.single.sync_option.sync_with_teapplix"
                 groov-click="update_product_sync_options()"></div>
          </div>
          <div class="controls col-sm-3" ng-show="products.single.sync_option.sync_with_teapplix">
            <input type="text" ng-model="products.single.sync_option.teapplix_product_sku" ng-focus="update_product_sync_options()" ng-blur="update_product_sync_options()" class="form-control input-style" placeholder="Teapplix Product Sku"/>
          </div>
        </div>
        <div ng-show="products.single.sync_option.sync_with_teapplix">
          <div class="form-group" ng-hide="products.single.sync_option.teapplix_product_sku">
            <div class="control-label col-sm-3"></div>
            <div class="col-sm-7">
              <small>*Adding product SKU is necessary for Teapplix products ineventory sync.</small>
            </div>
          </div>
        </div>
      </div>
 -->
      <div class="form-group"
           ng-show="products.single.store.store_type=='Amazon' && products.single.amazon_product.show_shipping_weight_only==true">
        <label class="control-label col-sm-2">Shipping Weight</label>

        <div class="col-sm-8 controls">
          <div class="input-group col-sm-12 controls">
            <input class="form-control input-style" aria-describedby="basic-addon2"
                   ng-model="products.single.shipping_weight" ng-blur="update_single_product()" onclick="select()">

            <div class="input-group-btn dropdown">
              <button class="btn btn-default dropdown-toggle input-style" type="button" data-toggle="dropdown"
                      aria-expanded="false"><span>{{products.single.basicinfo.weight_format}}</span> <span
                class="caret"></span></button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_setting('weight_format','lb')">lb</a></li>
                <li><a class="dropdown-toggle" ng-click="change_setting('weight_format','oz')">oz</a></li>
                <li><a class="dropdown-toggle" ng-click="change_setting('weight_format','kg')">kg</a></li>
                <li><a class="dropdown-toggle" ng-click="change_setting('weight_format','g')">g</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <h4 style="margin-bottom: 25px;">Packing options</h4>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label class="control-label col-sm-4">Packing placement</label>

            <div class="controls col-sm-3">
              <input type="number" min="1" max="100" ng-blur="update_single_product()" class="form-control input-style"
                     ng-model="products.single.basicinfo.packing_placement"/>
            </div>
            <div class="info col-sm-4"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                          groov-popover="{{translations.tooltips.placement}}"></i></div>
          </div>
        </div>

        <div class="col-md-6 remove_padding">
          <div class="form-group">
            <label class="control-label col-sm-4">Packing Instructions Confirmation</label>

            <div class="controls col-sm-4 col-lg-2"
                 ng-show="general_settings.single.conf_code_product_instruction =='optional'">
              <div toggle-switch ng-model="products.single.basicinfo.packing_instructions_conf"
                   groov-click="update_single_product()"></div>
            </div>
            <div class="control-label col-sm-4 col-lg-2"
                 ng-show="general_settings.single.conf_code_product_instruction !='optional'">
              <div class="label col-xs-12 label-default"
                   ng-class="{'label-success':general_settings.single.conf_code_product_instruction=='always'}">
                <span ng-show="general_settings.single.conf_code_product_instruction=='always'"
                      translate>common.always</span>
                <span ng-show="general_settings.single.conf_code_product_instruction=='never'"
                      translate>common.never</span>
              </div>
            </div>
            <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                          groov-popover="{{translations.tooltips.confirmation}} <br/> {{confirmation_setting_text}}"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 remove_padding">
          <!-- <div class="form-group">
            <label class="control-label col-sm-4">Packing placement</label>

            <div class="controls col-sm-3">
              <input type="number" min="1" max="100" ng-blur="update_single_product()" class="form-control input-style"
                     ng-model="products.single.basicinfo.packing_placement"/>
            </div>
            <div class="info col-sm-4"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                          groov-popover="{{translations.tooltips.placement}}"></i></div>
          </div> -->
          <!--
          <div class="form-group">
              <label class="control-label col-sm-2">Packing time adjustment</label>

              <div class="controls col-sm-1">
                  <input type="number" class="form-control input-style" ng-blur="update_single_product()" ng-model="products.single.basicinfo.pack_time_adj"/>
              </div>
              <div class="info col-sm-2" ><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.time_adjust}}"></i></div>
          </div>
          -->

          <div class="form-group">
            <label class="control-label col-sm-4">Special instructions for Packer</label>

              <div text-angular
                class="controls col-sm-8"
                ta-toolbar="[['html','h1','h2','h3','h4','h5','h6','p','ol','ul'],['bold','italics','underline','strikeThrough'],['justifyLeft','justifyCenter','justifyRight','indent','outdent'],['wordcount','charcount'], ['insertImage', 'insertLink']]"
                ng-model="products.single.basicinfo.packing_instructions"
                ng-blur="update_single_product()"></div>
          </div>

       <!--    <div class="form-group">
            <label class="control-label col-sm-4">Packer Confirmation</label>

            <div class="controls col-sm-4 col-lg-2"
                 ng-show="general_settings.single.conf_code_product_instruction =='optional'">
              <div toggle-switch ng-model="products.single.basicinfo.packing_instructions_conf"
                   groov-click="update_single_product()"></div>
            </div>
            <div class="control-label col-sm-4 col-lg-2"
                 ng-show="general_settings.single.conf_code_product_instruction !='optional'">
              <div class="label col-xs-12 label-default"
                   ng-class="{'label-success':general_settings.single.conf_code_product_instruction=='always'}">
                <span ng-show="general_settings.single.conf_code_product_instruction=='always'"
                      translate>common.always</span>
                <span ng-show="general_settings.single.conf_code_product_instruction=='never'"
                      translate>common.never</span>
              </div>
            </div>
            <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                          groov-popover="{{translations.tooltips.confirmation}} <br/> {{confirmation_setting_text}}"></i>
            </div>
          </div> -->
        </div>
      </div>
      <!-- <div class="col-md-6 remove_padding"> -->
        <!-- <div class="form-group">
          <label class="control-label col-sm-4">Packing placement</label>

          <div class="controls col-sm-3">
            <input type="number" min="1" max="100" ng-blur="update_single_product()" class="form-control input-style"
                   ng-model="products.single.basicinfo.packing_placement"/>
          </div>
          <div class="info col-sm-4"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                        groov-popover="{{translations.tooltips.placement}}"></i></div>
        </div> -->
        <!--
        <div class="form-group">
            <label class="control-label col-sm-2">Packing time adjustment</label>

            <div class="controls col-sm-1">
                <input type="number" class="form-control input-style" ng-blur="update_single_product()" ng-model="products.single.basicinfo.pack_time_adj"/>
            </div>
            <div class="info col-sm-2" ><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.time_adjust}}"></i></div>
        </div>
        -->

        <!-- <div class="form-group">
          <label class="control-label col-sm-4">Special instructions for Packer</label>

            <div text-angular
              class="controls col-sm-8"
              ta-toolbar="[['html','h1','h2','h3','h4','h5','h6','p','ol','ul'],['bold','italics','underline','strikeThrough'],['justifyLeft','justifyCenter','justifyRight','indent','outdent'],['wordcount','charcount'], ['insertImage', 'insertLink']]"
              ng-model="products.single.basicinfo.packing_instructions"
              ng-blur="update_single_product()"></div>
        </div> -->

     <!--    <div class="form-group">
          <label class="control-label col-sm-4">Packer Confirmation</label>

          <div class="controls col-sm-4 col-lg-2"
               ng-show="general_settings.single.conf_code_product_instruction =='optional'">
            <div toggle-switch ng-model="products.single.basicinfo.packing_instructions_conf"
                 groov-click="update_single_product()"></div>
          </div>
          <div class="control-label col-sm-4 col-lg-2"
               ng-show="general_settings.single.conf_code_product_instruction !='optional'">
            <div class="label col-xs-12 label-default"
                 ng-class="{'label-success':general_settings.single.conf_code_product_instruction=='always'}">
              <span ng-show="general_settings.single.conf_code_product_instruction=='always'"
                    translate>common.always</span>
              <span ng-show="general_settings.single.conf_code_product_instruction=='never'"
                    translate>common.never</span>
            </div>
          </div>
          <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                        groov-popover="{{translations.tooltips.confirmation}} <br/> {{confirmation_setting_text}}"></i>
          </div>
        </div> -->
      <!-- </div> -->
      <div class="col-md-12 remove_padding">
      <!-- <div class="form-group">
          <label class="control-label col-sm-4">Packing Instructions Confirmation</label>

          <div class="controls col-sm-4 col-lg-2"
               ng-show="general_settings.single.conf_code_product_instruction =='optional'">
            <div toggle-switch ng-model="products.single.basicinfo.packing_instructions_conf"
                 groov-click="update_single_product()"></div>
          </div>
          <div class="control-label col-sm-4 col-lg-2"
               ng-show="general_settings.single.conf_code_product_instruction !='optional'">
            <div class="label col-xs-12 label-default"
                 ng-class="{'label-success':general_settings.single.conf_code_product_instruction=='always'}">
              <span ng-show="general_settings.single.conf_code_product_instruction=='always'"
                    translate>common.always</span>
              <span ng-show="general_settings.single.conf_code_product_instruction=='never'"
                    translate>common.never</span>
            </div>
          </div>
          <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                        groov-popover="{{translations.tooltips.confirmation}} <br/> {{confirmation_setting_text}}"></i>
          </div>
        </div> -->
       <!--  <label class="control-label col-sm-4 remove_padding_left"><br/><br/>Product Receiving Instructions</label>

        <div class="controls col-sm-8 remove_padding">
          <i class="info icon-large glyphicon glyphicon-info-sign pull-right" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.product_receiving_instructions}}"></i>

          <div text-angular
               ta-toolbar="[['html','h1','h2','h3','h4','h5','h6','p','ol','ul'],['bold','italics','underline','strikeThrough'],['justifyLeft','justifyCenter','justifyRight','indent','outdent'],['wordcount','charcount']]"
               ng-model="products.single.basicinfo.product_receiving_instructions"
               ng-blur="update_single_product()"></div>
        </div>
        <div class="form-group">
          <label class="control-label col-sm-4"></label>

          <div class="controls col-sm-8">
            <div class="img-thumbnail"
                 ng-repeat="image in products.single.images | filter:{added_to_receiving_instructions:true} track by image.id">
              <img ng-src="{{ image.image }}" class="img-polaroid img-thumb img-responsive"
                   style="border: 1px solid grey;"/>
              <i class="glyphicon glyphicon-remove" ng-click="remove_instruction_image(image)"></i>
            </div>
            <button class="modal-save-button" ng-click="add_image_for_receiving_instructions()">Add Image</button>
          </div>
        </div> -->
      <!-- </div><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/> -->

      <!-- <h4>Scanning Options</h4> -->

      <div class="form-group">
        <label class="control-label col-sm-2">Product can be skipped</label>

        <div class="controls col-sm-2 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.is_skippable"
               groov-click="update_single_product()"></div>
        </div>
        <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                      groov-popover="{{translations.tooltips.skippable}}"></i></div>

        <div ng-hide="products.single.basicinfo.is_kit==1">
          <label class="control-label col-sm-2 subtract_margin_left60">Add to any order</label>

          <div class="controls col-sm-2 col-lg-1">
            <div toggle-switch ng-model="products.single.basicinfo.add_to_any_order"
                 groov-click="update_single_product()"></div>
          </div>
          <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                        groov-popover="{{translations.tooltips.add_to_any_order}}"></i></div>
        </div>
        <label class="control-label col-sm-2 subtract_margin_left20">Type-In Count Setting</label>

        <div class="col-sm-2 col-lg-2" dropdown ng-show="scan_pack_settings.settings.type_scan_code_enabled">
          <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':products.single.basicinfo.type_scan_enabled=='on',
                            'label-warning':products.single.basicinfo.type_scan_enabled=='on_with_confirmation'}">
            <span ng-show="products.single.basicinfo.type_scan_enabled=='on'">On</span>
            <span ng-show="products.single.basicinfo.type_scan_enabled=='off'">Off</span>
            <span
              ng-show="products.single.basicinfo.type_scan_enabled=='on_with_confirmation'"> On with Confirmation</span>
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a class="dropdown-toggle" ng-click="change_setting('type_scan_enabled','on')">On</a></li>
            <li><a class="dropdown-toggle" ng-click="change_setting('type_scan_enabled','off')">Off</a></li>
            <li><a class="dropdown-toggle" ng-click="change_setting('type_scan_enabled','on_with_confirmation')">On with
              confirmation</a></li>
          </ul>
          &nbsp;
          <i class="info icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.type_in_scan_setting}}"></i>
        </div>
        <div class="col-sm-2" ng-hide="scan_pack_settings.settings.type_scan_code_enabled">
          <button class="groove-button label label-default">Disabled</button>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-sm-2">Record Serial Number 1</label>

        <div class="controls col-sm-1 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.record_serial"
               groov-click="update_single_product()"></div>
        </div>
        <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                      groov-popover="{{translations.tooltips.record_serial}}"></i></div>

        <div ng-hide="products.single.basicinfo.is_kit==1">
          <label class="control-label col-sm-2 subtract_margin_left60">Intangible Item</label>

          <div class="controls col-sm-1 col-lg-1">
            <div
              ng-show="products.single.basicinfo.contains_intangible_string && products.single.basicinfo.is_intangible">
              <div toggle-switch ng-model="products.single.basicinfo.is_intangible" disabled="disabled"
                   style="opacity: 0.5;"></div>
            </div>
            <div
              ng-hide="products.single.basicinfo.contains_intangible_string && products.single.basicinfo.is_intangible">
              <div toggle-switch ng-model="products.single.basicinfo.is_intangible"
                   groov-click="update_single_product()"></div>
            </div>
          </div>

          <div class="info col-sm-1"><i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                                        groov-popover="{{translations.tooltips.intangible_item}}"></i></div>
        </div>

        <label class="control-label col-sm-2 subtract_margin_left20">Click Scanning Setting</label>

        <div class="col-sm-2 col-lg-2" dropdown ng-show="scan_pack_settings.settings.enable_click_sku">
          <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':products.single.basicinfo.click_scan_enabled=='on',
                            'label-warning':products.single.basicinfo.click_scan_enabled=='on_with_confirmation'}">
            <span ng-show="products.single.basicinfo.click_scan_enabled=='on'">On</span>
            <span ng-show="products.single.basicinfo.click_scan_enabled=='off'">Off</span>
            <span
              ng-show="products.single.basicinfo.click_scan_enabled=='on_with_confirmation'">On with Confirmation</span>
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a class="dropdown-toggle" ng-click="change_setting('click_scan_enabled','on')">On</a></li>
            <li><a class="dropdown-toggle" ng-click="change_setting('click_scan_enabled','off')">Off</a></li>
            <li><a class="dropdown-toggle" ng-click="change_setting('click_scan_enabled','on_with_confirmation')">On
              with confirmation</a></li>
          </ul>
          &nbsp;
          <i class="info icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.click_scanning_setting}}"></i>
        </div>
        <div class="col-sm-2" ng-hide="scan_pack_settings.settings.enable_click_sku">
          <button class="groove-button label label-default">Disabled</button>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-sm-2">Record Serial Number 2</label>

        <div class="controls col-sm-1 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.second_record_serial"
               groov-click="update_single_product()"></div>
        </div>
        <div class="info col-sm-1">
        <!-- <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" -->
                                      <!-- groov-popover="{{translations.tooltips.record_serial}}"></i> -->
        </div>
      </div>
    <div style="display: flex;"><h4>Multi-pack Barcodes </h4><i style="top: 12px; margin-left: 10px;" class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" popover-placement="right"  groov-popover="{{translations.tooltips.multi_pack_barcode}}"></i></div>
    <div ng-repeat="(index, barcode) in products.single.basicinfo.multibarcode" class="form-group" style="margin-bottom: 5px;">
      <label class="control-label col-sm-2">Multi-pack Barcode {{index}}</label>
        <div class="controls col-sm-5" style="display: flex;">
          <input type="text" class="form-control input-style" ng-blur="update_single_product()" ng-model="barcode.barcode"/>
          <a href="" ng-click="generate_product_barcode_slip_pdf(products.single.basicinfo.id, barcode.barcode)">
            <i style="margin-left: 8px; top: 8px;" class="icon-large glyphicon glyphicon-print"></i>
          </a>
        </div>
      <label class="control-label pull-left">Pack Count</label>
        <div class="controls col-sm-3">
           <input type="number" min=1 ng-blur="update_single_product()" class="form-control input-style" ng-model="barcode.packcount"/>
        </div>
    </div>
    <div class="form-group" style="margin-bottom: 5px;" >
      <div id="multi_pack_barcode_container"></div>
    </div>


    <i class="fa fa-plus-circle" ng-click="append_multipack_barcode()" aria-hidden="true" style="margin-left: 10px;cursor:pointer; margin-bottom: 15px;"><b style="font-family: 'Play', sans-serif;" > Add another</b></i>
    <div ng-show="check.custom_product_fields==true">
      <div style="display: flex;"><h4>Custom Product Fields</h4><i style="top: 12px; margin-left: 10px;" class="icon-large glyphicon glyphicon-info-sign" groov-popover="{{translations.tooltips.custom_product_fields}}" popover-trigger="mouseenter" popover-placement="right"></i></div>
      <div class="form-group" style="margin-bottom: 5px;">
        <label class="control-label col-sm-2">Custom Product Data 1</label>
          <div class="controls col-sm-5" style="display: flex;">
          <input type="text" class="form-control input-style" ng-blur="update_single_product()" ng-model="products.single.basicinfo.custom_product_1"/>
          </div>
          <label class="control-label pull-left">Display During Packing</label>
         <div class="controls col-sm-1 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.custom_product_display_1"
               groov-click="update_single_product()"></div>
        </div>
      </div>
      <div class="form-group" style="margin-bottom: 5px;">
          <label class="control-label col-sm-2">Custom Product Data 2</label>
          <div class="controls col-sm-5" style="display: flex;">
          <input type="text" class="form-control input-style" ng-blur="update_single_product()" ng-model="products.single.basicinfo.custom_product_2"/>
          </div>
          <label class="control-label pull-left">Display During Packing</label>
         <div class="controls col-sm-1 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.custom_product_display_2"
               groov-click="update_single_product()"></div>
        </div>
      </div>
      <div  class="form-group" style="margin-bottom: 5px;">
        <label class="control-label col-sm-2">Custom Product Data 3</label>
          <div class="controls col-sm-5" style="display: flex;">
          <input type="text" class="form-control input-style" ng-blur="update_single_product()" ng-model="products.single.basicinfo.custom_product_3"/>
          </div>
        <label class="control-label pull-left">Display During Packing</label>
        <div class="controls col-sm-1 col-lg-1">
          <div toggle-switch ng-model="products.single.basicinfo.custom_product_display_3"
               groov-click="update_single_product()"></div>
        </div>
      </div>
    </div>
</div>
