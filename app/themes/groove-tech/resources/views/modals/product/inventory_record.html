<!-- show order modal -->
<div style="margin-top: 90px;">

  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Select Product to add</div>
  </div>


  <div class="modal-body">
    <div class="container-fluid bottom-well">
      <div class="row">
        <div class="col-sm-offset-3 col-sm-6">
          <div>
            <input type="text" id="alias-search-query-{{custom_identifier}}" autofocus="autofocus"
                  ng-model="products.setup.search" class="form-control search-box" placeholder="Search term"/>

            <span>
              <label ng-click="products.setup.advanced_search = !products.setup.advanced_search" style="cursor: pointer;font-weight: lighter;" groov-popover='When enabled, search by extra fields like categories to find products which contain it.' popover-trigger="mouseenter" popover-placement="bottom">
                <input type="checkbox"
                        ng-model="products.setup.advanced_search"
                        ng-click="products.toggle_advanced_search(!products.setup.advanced_search);products.setup.advanced_search = !products.setup.advanced_search;">
                Search all fields
              </label>
            </span>
          </div>
        </div>
      </div>
      <div class="row top-buffer">
        <div ng-click="clear()" style="cursor: pointer; padding-bottom: 40px;" class="col-md-6">
          <i class="fa fa-eraser" aria-hidden="true"></i>
          <label>Clear Selection</label>
        </div>
        <div class="col-md-6 text-right">
          <div ng-hide="selected_report != null">
            <label >Save Report As: &nbsp;&nbsp;</label>
            <div style="display: flex;">
              <input class="col-md-4 form-control search-box ng-pristine ng-valid" type="text" autofocus="autofocus" ng-model="products.setup.report_name" class="form-control search-box"/>&nbsp;&nbsp;
              <button ng-click="ok()" class="modal-save-button" translate>modal.save_close</button>&nbsp;&nbsp;
              <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
            </div>
          </div>
          <div ng-show="selected_report != null">
            <button ng-click="add_to_report()" class="modal-save-button">Add Products</button>
            <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
          </div>
        </div>
        <div class="col-md-12">
          <div class="row" ng-if="inventory_report_page != true">
            <div class="col-xs-12">
              <div groov-data-grid="gridOptions" groov-list="products.list"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer" style="margin-bottom: 35px;">
    <div ng-show="selected_report != null">
      <button ng-click="add_to_report()" class="modal-save-button">Add Products</button>
      <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
    </div>
    <div ng-hide="selected_report != null">
      <button ng-click="ok()" class="modal-save-button" translate>modal.save_close</button>
      <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
    </div>
  </div>
</div>
<!-- Kit product modal ends -->
