<!-- show order modal -->
<div style="margin-top: 90px;">

  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Select Product to <span ng-show="is_kit">add to Kit</span> <span ng-show="is_order">add to Order</span><span
      ng-hide="is_kit || is_order">Alias</span></div>
  </div>


  <div class="modal-body">
    <div class="container-fluid bottom-well">
      <div class="row">
        <div class="col-sm-offset-3 col-sm-6">
          <div>
            <input type="text" id="alias-search-query-{{custom_identifier}}" autofocus="autofocus"
                  ng-model="products.setup.search" class="form-control search-box" placeholder="Search term"/>

            <span>
              <label ng-click="products.setup.advanced_search = !products.setup.advanced_search" style="cursor: pointer;font-weight: lighter;" groov-popover='When enabled, search by extra fields like categories to find products which contain it.' popover-trigger="mouseenter" popover-placement="bottom">
                <input type="checkbox"
                        ng-model="products.setup.advanced_search"
                        ng-click="products.toggle_advanced_search(!products.setup.advanced_search);products.setup.advanced_search = !products.setup.advanced_search;">
                Search all fields
              </label>
            </span>
          </div>
        </div>
      </div>
      <div class="row top-buffer">
        <div class="col-md-12 text-center" ng-show="paginate.show">
          <pagination total-items="paginate.total_items" ng-model="paginate.current_page" max-size="paginate.max_size"
                      items-per-page="paginate.items_per_page" class="pagination-sm" rotate="false"></pagination>
        </div>
      </div>
      <div class="row top-buffer">
        <div class="col-lg-offset-10 col-lg-2 col-md-offset-9 col-md-3">
          <button ng-click="ok()" class="modal-save-button" translate>modal.save_close</button>
          <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">

          <table class="well table-well table table-striped table-alt aliasproductstbl">
            <thead>
            <tr>
              <th ng-class="{active: products.setup.sort=='name'}">
                <div ng-click="handlesort('name')" ng-class="products.setup.order">Item Name</div>
              </th>
              <th ng-class="{active: products.setup.sort=='sku'}">
                <div ng-click="handlesort('sku')" ng-class="products.setup.order">Primary Sku</div>
              </th>
              <th ng-class="{active: products.setup.sort=='status'}">
                <div ng-click="handlesort('status')" ng-class="products.setup.order">Status</div>
              </th>
              <th ng-class="{active: products.setup.sort=='barcode'}">
                <div ng-click="handlesort('barcode')" ng-class="products.setup.order">Barcode</div>
              </th>
            </tr>
            </thead>
            <tbody>
            <tr ng-click="add_alias_product(product)" ng-class="{selected: product.checked}"
                ng-repeat="product in products.list">
              <td>{{product.name}}</td>
              <td>{{product.sku}}</td>
              <td>{{product.status}}</td>
              <td>{{product.barcode}}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>

  <div class="modal-footer">
    <button ng-click="ok()" class="modal-save-button" translate>modal.save_close</button>
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>
<!-- Kit product modal ends -->
