<!-- show order modal -->
<div>

  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Receive or Recount Inventory</div>
  </div>


  <div class="modal-body">
    <div class="container-fluid form-horizontal">
      <div class="col-sm-5">
        <div class="form-group">
          <label class="control-label col-sm-5">Inventory Warehouse</label>

          <div class="controls col-sm-7" dropdown>
            <button class="dropdown-toggle groove-button">
              <span ng-repeat="inv_wh in warehouses.list" ng-show="inventory_manager.single.inv_wh_id==inv_wh.info.id">{{inv_wh.info.name}}</span>
              <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
              <li ng-repeat="inv_wh in warehouses.list"><a class="dropdown-toggle"
                                                           ng-click="handle_change_event(inv_wh.info.id)">{{inv_wh.info.name}}</a>
              </li>
            </ul>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-sm-5">Receive or Recount Inventory</label>

          <div class="controls col-sm-7">
            <label class="radio inline">
              <input type="radio"
                     ng-model="inventory_manager.single.method" value="receive" ng-change='handle_change_event()'/>
              Receive Inventory
            </label>
            <label class="radio inline">
              <input type="radio"
                     ng-model="inventory_manager.single.method" value="recount" ng-change='handle_change_event()'/>
              Recount Inventory
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-sm-5">Scan Product Barcode</label>

          <div class="controls col-sm-5">
            <input id="inventorymanagerbarcode" type="text" ng-model="inventory_manager.single.product_barcode"
                   ng-keypress="_handle_inv_manager_key_event($event)" class="form-control input-style"/>
          </div>
        </div>
      </div>
      <div class="col-sm-7">
        <div class="col-sm-12"
             ng-hide="products_inv_manager.single.basicinfo.product_receiving_instructions==null || products_inv_manager.single.basicinfo.product_receiving_instructions=='' || inventory_manager.single.method=='recount'">
          <div class="row bottom-well" style="background-color: #fff;">
            <div class="controls col-sm-12">
              <div ng-bind-html="products_inv_manager.single.basicinfo.product_receiving_instructions"></div>
            </div>
          </div>
          <div class="col-sm-12 form-group">
            <div class="controls col-sm-12">
              <div class="single-image-clickable" ng-model="products_inv_manager.single.images"
                   ng-repeat="image in products_inv_manager.single.images | filter:{added_to_receiving_instructions: true} track by image.id">
                <a ng-click="openLightboxModal($index)">
                  <img ng-src="{{image.image}}" class="img-polaroid img-thumb img-responsive" alt="">

                  <div class="img-note mini-popover" popover-trigger="mouseenter"
                       groov-popover="{{image.image_note}}"></div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-12" ng-show="products_inv_manager.single.basicinfo.id > 0">
        <div class="col-sm-8">
          <br/>

          <h3>Product Information</h3>
        </div>
        <div class="col-sm-4">
          <div class="print_barcode_label">
              <label class="control-label">Print Product Barcode QTY</label>
            <div class="info">
               <input id="inventory_count" type="number" min="1" ng-model="inventory_manager.single.barcodes_qty"
                class="form-control input-style" ng-keypress="_handle_inv_count_key_event($event)" style="width: 59px;"/>
            </div> 
            <div class="info">
              <a ng-click="generate_barcode_slip_pdf(products_inv_manager.single.basicinfo.id, inventory_manager.single.barcodes_qty)"
                target="_blank" class="generate_pdf">
                <i class="glyphicon glyphicon-print icon-large"></i>
              </a>
            </div>
          </div>
          <div class="print_barcode_label">
            <label class="control-label">Print Receiving Label</label>

            <div class="info">
              <a ng-click="print_receive_label($event)">
                <i class="glyphicon glyphicon-print icon-large"></i>
              </a>
            </div>
          </div>
        </div>
        <table class="well table-well table table-striped table-hover table-alt">
          <thead>
          <tr>
            <th>
              <div>Product Name</div>
            </th>
            <th>
              <div>SKU</div>
            </th>
            <th>
              <div>Quantity on Hand</div>
            </th>
            <th>
              <div>Inventory Warehouse</div>
            </th>
            <th>
              <div>Location Primary</div>
            </th>
            <th>
              <div>Location Secondary</div>
            </th>
            <th>
              <div>Location Tertiary</div>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td><a ui-sref="products.type.filter.page.single({product_id: products_inv_manager.single.basicinfo.id})">{{products_inv_manager.single.basicinfo.name}}</a>
            </td>
            <td>{{products_inv_manager.single.skus[0].sku}}</td>
            <td><span ng-repeat="warehouse in products_inv_manager.single.inventory_warehouses"
                      ng-show="warehouse.warehouse_info.id == inventory_manager.single.inv_wh_id">
                    {{warehouse.info.quantity_on_hand}}</span></td>
            <td><span ng-repeat="warehouse in products_inv_manager.single.inventory_warehouses"
                      ng-show="warehouse.warehouse_info.id == inventory_manager.single.inv_wh_id">
                    {{warehouse.warehouse_info.name}}</span></td>
            <td><span ng-repeat="warehouse in products_inv_manager.single.inventory_warehouses"
                      ng-show="warehouse.warehouse_info.id == inventory_manager.single.inv_wh_id">
                    {{warehouse.info.location_primary}}</span></td>
            <td><span ng-repeat="warehouse in products_inv_manager.single.inventory_warehouses"
                      ng-show="warehouse.warehouse_info.id == inventory_manager.single.inv_wh_id">
                    {{warehouse.info.location_secondary}}</span></td>
            <td><span ng-repeat="warehouse in products_inv_manager.single.inventory_warehouses"
                      ng-show="warehouse.warehouse_info.id == inventory_manager.single.inv_wh_id">
                    {{warehouse.info.location_tertiary}}</span></td>
          </tr>
          </tbody>
        </table>

        <div ng-show="inventory_manager.single.inv_wh_id>0 &&
        (products_inv_manager.single.inventory_warehouses.length == 0 || inv_wh_found == false)">
          <p class="alert alert-danger">
            This item is not currently found in the selected warehouse. Enter an inventory value to add it
            automatically.
          </p>
        </div>

        <div class="form-group" ng-show="inventory_manager.single.inv_wh_id > 0 ">
          <label class="control-label col-sm-2">Enter Count</label>

          <div class="controls col-sm-2">
            <input id="inventory_count" type="text" ng-model="inventory_manager.single.inventory_count"
                   class="form-control input-style" ng-keypress="_handle_inv_count_key_event($event)"/>
          </div>
          <label class="control-label col-sm-2 col-sm-offset-2">Primary Location</label>

          <div class="controls col-sm-2">
            <input id="primary_location" type="text" ng-model="inventory_manager.single.location_primary"
                   class="form-control input-style" ng-keypress="_handle_inv_count_key_event($event)"/>
          </div>
        </div>
        <div class="form-group" ng-show="inventory_manager.single.inv_wh_id > 0 ">
          <label class="control-label col-sm-2">Secondary Location</label>

          <div class="controls col-sm-2">
            <input id="secondary_location" type="text" ng-model="inventory_manager.single.location_secondary"
                   class="form-control input-style" ng-keypress="_handle_inv_count_key_event($event)"/>
          </div>
          <label class="control-label col-sm-2 col-sm-offset-2">Tertiary Location</label>

          <div class="controls col-sm-2">
            <input id="tertiary_location" type="text" ng-model="inventory_manager.single.location_tertiary"
                   class="form-control input-style" ng-keypress="_handle_inv_count_key_event($event)"/>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>



