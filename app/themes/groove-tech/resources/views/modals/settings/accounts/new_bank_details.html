<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="ok()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">
      <span>Add Bank Details</span>
    </div>
  </div>
  <form name="payments_form" role="form" class="form-validation" novalidate>
    <div class="modal-body">
      <div class="container-fluid form-horizontal">
        <div><br></div>
        <div class="form-group">
          <label class="control-label col-md-3">Account Number </label>

          <div class="controls col-md-6"
               ng-class="{'has-error' : payments_form.number.$invalid && (!payments_form.number.$pristine || submitted) }">
            <input class="form-control input-style" ng-model="payments.single.last4" name="number" type="text"
                   required/>
            <span class="has-error help-block"
                  ng-show="payments_form.number.$invalid && (!payments_form.number.$pristine || submitted)">Account number is required.
            </span>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Routing Number </label>

          <div class="controls col-md-6"
               ng-class="{'has-error' : payments_form.routing_number.$invalid && (!payments_form.routing_number.$pristine || submitted) }">
            <input class="form-control input-style" ng-model="payments.single.routing_number" name="routing_number" type="text" required/>
            <span class="has-error help-block"
                  ng-show="payments_form.routing_number.$invalid && (!payments_form.routing_number.$pristine || submitted)">Routing Number is required.
            </span>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Account Holder Name </label>

          <div class="controls col-md-6"
               ng-class="{'has-error' : payments_form.account_holder_name.$invalid && (!payments_form.account_holder_name.$pristine || submitted) }">
            <input class="form-control input-style" ng-model="payments.single.account_holder_name" name="account_holder_name" type="text" required/>
            <span class="has-error help-block"
                  ng-show="payments_form.account_holder_name.$invalid && (!payments_form.account_holder_name.$pristine || submitted)">Account Holder Name is required.
            </span>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Account Holder Type </label>

          <div class="controls col-md-6"
               ng-class="{'has-error' : payments_form.account_holder_type.$invalid && (!payments_form.account_holder_type.$pristine || submitted) }">
            <input class="form-control input-style" ng-model="payments.single.account_holder_type" name="account_holder_type" type="text" required/>
            <span class="has-error help-block"
                  ng-show="payments_form.account_holder_type.$invalid && (!payments_form.account_holder_type.$pristine || submitted)">Account Holder Type is required.
            </span>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Country </label>

          <div class="controls col-md-6"
               ng-class="{'has-error' : payments_form.country.$invalid && (!payments_form.country.$pristine || submitted) }">
            <input class="form-control input-style" ng-model="payments.single.country" name="country" type="text" required/>
            <span class="has-error help-block"
                  ng-show="payments_form.country.$invalid && (!payments_form.country.$pristine || submitted)">Country is required.
            </span>
          </div>
        </div>
      </div>

    </div>
    <div class="modal-footer">
      <button ng-click="addBankDetails(payments_form.$valid)" class="modal-save-button" translate>Add Bank Details</button>
      <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
    </div>
  </form>
</div>
