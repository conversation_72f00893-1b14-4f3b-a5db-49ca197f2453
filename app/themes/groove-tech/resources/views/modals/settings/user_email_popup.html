<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Update Email </div>
  </div>

  <div class="modal-body" style="padding-top: 40px;">
    <div class="tabbable">
      <label class="radio inline">
      &emsp; Hi There! Please take a second to add an email address to your user account. This will allow you to reset your password in the future if you should need to.
        <input style="width: 50%; margin-top:20px; margin-left: 15px;" type="text"
               ng-model="user_email_address" class="form-control input-style"/>
      </label><br/>
      <label class="radio inline">
      Note: If other (non super admin) users on your account request a password reset the link will be sent to the address you provide. You also have the option of setting separate addresses for each user so they can reset their own password without assistance.
      </label>
    </div>
  </div>
</div>
  <div class="modal-footer" style="padding-top: 15px;">
    <button ng-click="update_user_email_address(user_email_address)" class="modal-save-button" translate>Continue</button>
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>
