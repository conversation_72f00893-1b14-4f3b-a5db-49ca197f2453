<div class="container-fluid form-horizontal">
  <div class="row">
    <div class="col-md-6">
      <legend>User permissions</legend>
      <div class="form-group">
        <label class="control-label col-md-3">Can make super admins</label>

        <div class="controls col-md-2">
          <input name="make_super_admin" ng-model="users.single.role.make_super_admin"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox"/>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Add and edit users</label>

        <div class="controls col-md-2">
          <input name="add_edit_users" ng-model="users.single.role.add_edit_users"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
      </div>


      <div class="form-group">
        <label class="control-label col-md-3">View Dashboard Drop Down</label>

        <div class="controls col-md-2">
          <input name="dashboard_switch" ng-model="users.single.dashboard_switch"
                 ng-disabled="check_input_is_disable()"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
      </div>


    </div>
  </div>
</div>
