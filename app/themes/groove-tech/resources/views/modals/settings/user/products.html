<div class="container-fluid form-horizontal">
  <div class="row">
    <div class="col-md-12">
      <legend>Product Settings</legend>
      <div class="form-group">
        <label class="control-label col-md-3">
          Add and Edit Products and Edit Product Status
        </label>

        <div class="controls col-md-2">
          <input name="add_edit_products" ng-change="update_single_user()"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-model="users.single.role.add_edit_products" type="checkbox"/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-3">Edit Product Locations</label>

        <div class="controls col-md-2">
          <input name="edit_product_location" ng-model="users.single.role.edit_product_location"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox"/>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Edit Product Quantity</label>

        <div class="controls col-md-2">
          <input name="edit_product_quantity" ng-model="users.single.role.edit_product_quantity"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox"/>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Delete products</label>

        <div class="controls col-md-2">
          <input name="delete_products" ng-model="users.single.role.delete_products"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox"/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-3">Import products</label>

        <div class="controls col-md-2">
          <input name="import_products" ng-model="users.single.role.import_products"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox"/>
        </div>
      </div>
    </div>
  </div>
</div>
