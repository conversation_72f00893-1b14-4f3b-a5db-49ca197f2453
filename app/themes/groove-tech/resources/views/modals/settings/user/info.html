<div class="container-fluid form-horizontal">
  <div class="row">
    <div class="col-md-6">
      <legend>Basic User Info</legend>
      <div class="form-group">
        <label class="control-label col-md-4">Username *</label>

        <div class="controls col-md-7">
          <input type="search" class="form-control input-style" name="username" ng-blur="update_single_user()"
                 ng-model="users.single.username" type="text" value="" required/>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-4">Password Recovery Email *</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="email" ng-blur="update_single_user()"
                 ng-model="users.single.email" type="text" value="" required/>
        </div>
      </div>
      <form autocomplete="off"> 
        <div class="form-group">
          <label class="control-label col-md-4">Password *</label>
      
          <div class="controls col-md-7" ng-show="show_password">
            <input class="form-control input-style" ng-minlength="6" name="password" ng-blur="update_single_user()"
                   ng-model="users.single.password" type="password" required/>
          </div>
          <div class="controls col-md-7" ng-hide="show_password">
            <a href="" ng-click='change_password()'> Change Password</a>
          </div>
        </div>
      
        <div class="form-group" ng-show="show_password">
          <label class="control-label col-md-4">Confirm Password *</label>
      
          <div class="controls col-md-7">
            <input class="form-control input-style" ng-minlength="6" name="conf_password" ng-blur="update_single_user()"
                   ng-model="users.single.conf_password" type="password" required/>
          </div>
        </div>
      
        <div ng-show="show_password" class="col-md-9 col-md-offset-2 password-label">
          Please choose a password with at least 6 characters.
        </div>
      </form>
      <div class="form-group">
        <label class="control-label col-md-4">Confirmation Code *</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="confirmation_code" ng-blur="update_single_user()"
                 ng-model="users.single.confirmation_code" type="text"/>
        </div>
        <div class="info col-sm-1">
          <a ng-show="users.single.id != null" ng-click="print_confirmation_code_pdf(users.single.id)"
             target="_blank" class="generate_pdf"><i class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.conf_code}}"></i>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-4">First Name</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="name" ng-blur="update_single_user()"
                 ng-model="users.single.name" type="text"/>
        </div>
        <div class="info col-sm-1">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.name}}"></i>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-4">Last Name</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="last_name" ng-blur="update_single_user()"
                 ng-model="users.single.last_name" type="text"/>
        </div>
      </div>

      <!-- custom fields -->
      <div class="form-group" ng-if="general_settings.single.custom_user_field_one">
        <label class="control-label col-md-4">{{general_settings.single.custom_user_field_one}}</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="custom_field_one" ng-blur="update_custom_fields_onchange()"
                 ng-model="users.single.custom_field_one" ng-keyup="auto_complete($event, 'custom_user_field_one')" type="text"/>
        </div>
        <div class="info col-sm-1">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.custom_fields}}"></i>
        </div>
      </div>

      <div class="form-group" ng-if="ready_for_auto_complete && custom_user_field_one_auto_complete_data" style="margin-bottom: 0px">
        <ul class="col-sm-offset-4 col-sm-7 auto_complete_menu">
          <li ng-repeat="item in custom_user_field_one_auto_complete_data" ng-click="users.single.custom_field_one = item; clear_auto_complete('custom_user_field_one')">{{item}}</li>
        </ul>
      </div>

      <div class="form-group" ng-if="general_settings.single.custom_user_field_two">
        <label class="control-label col-md-4">{{general_settings.single.custom_user_field_two}}</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="custom_field_two" ng-blur="update_custom_fields_onchange()"
                 ng-model="users.single.custom_field_two" ng-keyup="auto_complete($event, 'custom_user_field_two')" type="text"/>
        </div>
      </div>

      <div class="form-group" ng-if="ready_for_auto_complete && custom_user_field_two_auto_complete_data" style="margin-bottom: 0px">
        <ul class="col-sm-offset-4 col-sm-7 auto_complete_menu">
          <li ng-repeat="item in custom_user_field_two_auto_complete_data" ng-click="users.single.custom_field_two = item; clear_auto_complete('custom_user_field_two')">{{item}}</li>
        </ul>
      </div>


      <div class="form-group">
        <label class="control-label col-md-4">Other</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="other1" ng-blur="update_single_user()"
                 ng-model="users.single.other" type="text"/>
        </div>
      </div>

      <div class="form-group" ng-show="general_settings.single.ss_api_create_label == true">
        <label class="control-label col-md-4">Shipping Location Zip Code</label>

        <div class="controls col-md-7">
          <input class="form-control input-style" name="warehouse_postcode" ng-blur="update_single_user()"
                 ng-model="users.single.warehouse_postcode" type="text"/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-4">Active</label>

        <div class="controls col-md-2">
          <div toggle-switch ng-model="users.single.active" groov-click="update_single_user()"></div>
        </div>
      </div>

      <div ng-show="general_settings.single.groovelytic_stat == true" >
        <div class="form-group" ng-show="users.single.current_user.name == 'gpadmin' || $root.current_user_data.role.name == 'Super Admin' || $root.current_user_data.dashboard_switch">
        <label class="control-label col-md-4">View Dashboard</label>
        <div class="controls col-md-7">
          <select class="form-control input-style" ng-model="users.single.view_dashboard" ng-change="update_single_user()">
            <option ng-repeat="x in dashboard_options" value="{{x.value}}" ng-selected="x.value == users.single.view_dashboard">{{x.name}}</option>
          </select>
        </div>
        </div>
      </div>  

      <div class="form-group">
        <label class="control-label col-md-4">Pass button overide</label>

        <div class="controls col-md-2">
          <div toggle-switch ng-model="users.single.override_pass_scanning" groov-click="update_single_user()"></div>
        </div>
        <div class="info col-sm-1">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.override_pass}}"></i>
        </div>
      </div>

    </div>
    <div class="col-md-6">
      <legend>Apply and Manage Roles</legend>
      <div class="form-group">
        <label class="control-label col-md-1">Role</label>    
        <div class="controls col-md-7">
          <select class="form-control input-style"
                  ng-model="roles_data.selectedRole"
                  ng-change="set_selected_role($event)"
                  ng-options="role as (role.name === 'Administrative' ? 'Admin Only' : role.name) for role in getAvailableRoles() track by (role.id + '_' + edit_status + '_' + (roles_data.selectedRole ? roles_data.selectedRole.id : 'null'))"
                  ng-disabled="getAvailableRoles().length === 0 || (edit_status && users.single.role && users.single.role.name === 'Administrative')">
            <option value="" ng-if="(edit_status && users.single.role && users.single.role.name !== 'Administrative') || (!edit_status && users.user_counts && users.user_counts.regular_slots_remaining > 0)">Custom</option>
          </select>

          <!-- Role availability messages - only show when creating new user -->
          <div class="help-block" ng-if="!edit_status">
            <span ng-if="users.user_counts.regular_slots_remaining > 0" class="text-info">
              Regular user slots available. All roles except Admin Only are shown.
            </span>
            <span ng-if="users.user_counts.regular_slots_remaining <= 0 && users.user_counts.admin_slots_remaining > 0" class="text-warning">
              Regular user slots full. Only Admin Only role is available.
            </span>
            <span ng-if="users.user_counts.regular_slots_remaining <= 0 && users.user_counts.admin_slots_remaining <= 0" class="text-danger">
              No user slots available. Cannot create new users.
            </span>
          </div>
        </div>
        
        <div class="info col-sm-1">
          <i class="icon-large mini-popover glyphicon glyphicon-info-sign" 
             popover-trigger="mouseenter"
             groov-popover="{{ roles_data.selectedRole && roles_data.selectedRole.name === 'Admin Only' 
                             ? 'Admin Only role is restricted when regular user slots are available' 
                             : translations.tooltips.role }}">
          </i>
        </div>        
      </div>   
      <div ng-show="roles_data.selectedRole == null" class="form-group">
        <label class="control-label col-md-2">Select Base Role to customize</label>

        <div class="controls col-md-7 col-md-4">
          <table class="well table-well table table-striped table-hover table-roles">
            <tr ng-repeat="role in getAvailableRoles()" ng-click="set_base_role(role)">
              <td>{{role.name}}</td>
            </tr>
          </table>
        </div>
        <div class="info col-sm-1">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="{{translations.tooltips.base_role}}"></i>
        </div>
      </div>
      <div ng-show="!roles_data.showSelectBaseRole && roles_data.selectedRole == null" class="form-group">
        <label class="control-label col-md-4">Save new Role</label>

        <div class="controls col-md-5">
          <input class="form-control input-style" type="text" ng-model="users.single.role.new_name"
                 placeholder="Role name"/>
        </div>
        <div class="col-md-2">
          <button class="modal-save-button" ng-click="make_new_role()">Save</button>
        </div>
      </div>

      <div
        ng-show="!roles_data.showSelectBaseRole && roles_data.selectedRole != null && users.single.role.custom && users.single.role.display"
        class="form-group">
        <label class="control-label col-md-4">Delete current Role</label>

        <div class="controls col-md-7">
          <button class="modal-remove-button" ng-click="delete_role()">Delete</button>
        </div>
      </div>
      <div class="col-md-12">
        <legend>Section access</legend>

        <div class="form-group">
          <label class="control-label col-md-3">Settings</label>

          <div class="controls col-md-2">
            <input name="access_settings" ng-model="users.single.role.access_settings"
                   ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                   ng-change="update_single_user()" type="checkbox" checked/>
          </div>
          <div class="info col-sm-1">
            <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
               groov-popover="{{translations.tooltips.section_access}}"></i>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Scan &amp; Pack</label>

          <div class="controls col-md-2">
            <input name="access_scanpack" ng-model="users.single.role.access_scanpack" ng-change="update_single_user()"
                   type="checkbox" disabled/>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Orders</label>

          <div class="controls col-md-2">
            <input name="access_orders" ng-model="users.single.role.access_orders"
                   ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                   ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-3">Products</label>

          <div class="controls col-md-2">
            <input name="access_products" ng-model="users.single.role.access_products"
                   ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                   ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
      </div>
    </div>

  </div>

</div>
