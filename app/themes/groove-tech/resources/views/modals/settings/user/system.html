<div class="container-fluid form-horizontal" style="margin-bottom: 110px;">
  <div class="row">
    <div class="col-md-12">
      <legend>System settings permissions</legend>
      <div class="form-group">
        <label class="control-label col-md-3">Edit General Preferences</label>

        <div class="controls col-md-2">
          <input name="edit_general_prefs" ng-model="users.single.role.edit_general_prefs"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
        <div class="controls col-md-6">
          <label class="control-label col-md-6">Edit Shipping Settings</label>

          <div class="controls col-md-3">
            <input name="edit_shipping_settings" ng-model="users.single.role.edit_shipping_settings"
                  ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Edit Scan and pack Preferences</label>

        <div class="controls col-md-2">
          <input name="edit_scanning_prefs" ng-model="users.single.role.edit_scanning_prefs"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
        <div class="controls col-md-6">
          <label class="control-label col-md-6">Edit Visible Services</label>

          <div class="controls col-md-3">
            <input name="edit_visible_services" ng-model="users.single.role.edit_visible_services"
                  ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Create, Edit and Delete Stores</label>

        <div class="controls col-md-2">
          <input name="add_edit_stores" ng-model="users.single.role.add_edit_stores"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
        <div class="controls col-md-6">
          <label class="control-label col-md-6">Add Edit Shortcuts</label>

          <div class="controls col-md-3">
            <input name="add_edit_shortcuts" ng-model="users.single.role.add_edit_shortcuts"
                  ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Create Backups</label>

        <div class="controls col-md-2">
          <input name="create_backups" ng-model="users.single.role.create_backups"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
        <div class="controls col-md-6">
          <label class="control-label col-md-6">Add Edit Dimensions Labels</label>

          <div class="controls col-md-3">
            <input name="add_edit_dimension_presets" ng-model="users.single.role.add_edit_dimension_presets"
                  ng-change="update_single_user()" type="checkbox" checked/>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-3">Restore Backups</label>

        <div class="controls col-md-2">
          <input name="restore_backups" ng-model="users.single.role.restore_backups"
                 ng-disabled="roles_data.selectedRole!=null || roles_data.showSelectBaseRole==true"
                 ng-change="update_single_user()" type="checkbox" checked/>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <legend>User Specific Settings</legend>

      <div class="form-group col-sm-12">
        <label class="control-label col-md-4">Packing Slip Size</label>

        <div class="controls col-sm-3" dropdown>
          <button class="dropdown-toggle groove-button">
            {{users.single.packing_slip_size}}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 6')">Standard 4" x 6"</a></li>
            <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','8.5 x 11')">8.5" x 11"</a></li>
            <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','Custom 4 x 6')">Custom 4" x 6"</a></li>
            <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 4')">Custom 4" x 4"</a></li>
            <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 2')">Custom 4" x 2"</a></li>
          </ul>
        </div>
        <div class="info col-sm-1">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
            groov-popover="You can Select the Packing Slip Size Manually From here."></i>
        </div>
      </div>
    </div>
  </div>

</div>
