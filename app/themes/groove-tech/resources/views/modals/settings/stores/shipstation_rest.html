<div class="row">
  <div class="col-md-6">
    <legend>Shipstation Credentials</legend>
    <div class="form-group">
      <label class="control-label col-md-4">API Key</label>
      <div class="controls col-md-8">
        <input name="api_key" xs-input-sync ng-model="stores.single.api_key" type="text" ng-blur="update_single_store(true)"
               class="form-control input-style" value="" placeholder="paste API Key here" required/>
        <i class="fas fa-check right-icon" ng-if= "stores.single.api_key.length == 32 && stores.single.api_key!= null"></i>
        <i class="fas fa-exclamation-triangle icon-remove" popover-trigger="mouseenter"
             groov-popover="The details you have entered may not be valid. Please ensure the entire string is included and no extra characters or spaces are entered before or after. In other words, we need the whole key and pass and nothing but the key and pass." ng-if= "stores.single.api_key.length != 32 && stores.single.api_key!= null"></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">API Secret</label>

      <div class="controls col-md-8">
        <input name="api_secret" xs-input-sync ng-model="stores.single.api_secret" type="text"
               ng-blur="update_single_store(true)" class="form-control input-style" value="" placeholder="paste API Secret here" required/>
        <i class="fas fa-check right-icon " ng-if= "stores.single.api_secret.length == 32 && stores.single.api_secret!= null"></i>
        <i class="fas fa-exclamation-triangle icon-remove" popover-trigger="mouseenter"
             groov-popover="The details you have entered may not be valid. Please ensure the entire string is included and no extra characters or spaces are entered before or after. In other words, we need the whole key and pass and nothing but the key and pass." ng-if= "stores.single.api_secret.length != 32 && stores.single.api_secret!= null"></i>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Webhook</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Webhook Endpoint</label>
      <div class="controls col-md-8">
        <p style="display: inline; font-family: Play, sans-serif; font-size: 18px; ">{{stores.single.webhook_endpoint}}</p>
        <button class="btn btn-success label" style="margin-left: 10px;" ng-click="copied(stores.single.webhook_endpoint)">
          Copy Url
        </button>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-sm-4">Screct Key</label>
      <div class="col-lg-6 col-md-8">
        <div ng-if="stores.single.webhook_secret">
          <div class="input-group">
            <input class="form-control input-style" style="border-radius: 7px;" type="text" ng-model="stores.single.webhook_secret" readonly ng-trim="false" />
          </div>
          <div>
            <button class="btn btn-danger label" groov-click="update_screct_key_ss(false)">Delete Screct Key</button>
            <button class="btn btn-success label" groov-click="update_screct_key_ss(true)">Regenerate Screct Key</button>
          </div>
        </div>

        <div ng-if="stores.single.webhook_secret === ''">
          <button class="btn btn-success" groov-click="update_screct_key_ss(true)">Generate Screct Key</button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Orders with following statuses</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Awaiting Shipment</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_awaiting_shipment"
             groov-click="fix_import_dates();update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="Orders with the statuses enabled here will be imported during each import. Existing orders will be recognized by order number and skipped. The date range included in the import can be extended by using the Deep Import option in the Import Orders popover.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
            If all statuses are disabled here only orders with the GP Ready tag will be imported, otherwise the tagged orders will be imported in addition to orders of the enabled status."></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Pending Fulfillment</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_pending_fulfillment" groov-click="fix_import_dates();update_single_store(true)"></div>

      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Shipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_shipped" groov-click="fix_import_dates();update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group" ng-show="false">
      <label class="control-label col-md-4">Awaiting Payment GP Ready</label>
      <div class="controls col-md-8">
        <button class="btn btn-success" groov-click="shipstation_verify_awaiting(stores.single.id)">Test tags
        </button>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Use Shopify as Product Source</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.use_shopify_as_product_source_switch" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div ng-if="stores.single.use_shopify_as_product_source_switch">
      <table class="table table-alt table-striped table-well">
        <thead>
          <tr>
            <th scope="col" height="30px">Shopify Store</th>
            <th scope="col">Status</th>
            <th scope="col">Product Import From</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="store in stores.single.shopify_active_stores">
            <td>{{ store.name }}</td>
            <td><span class="label" ng-class="{'label-success':store.status,'label-danger':!store.status}" >{{store.status ? 'ACTIVE' : 'INACTIVE'}}</span></td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.product_source_shopify_store_id"
                ng-value="store.id"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import specific orders using a tag</legend>
    <div class="form-group">
      <label class="control-label col-md-4">GP Ready Tag Import</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.tag_import_option" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">{{stores.single.gp_ready_tag_name}}</label>

      <div class="controls col-md-8">
        <button class="btn btn-success" clip-copy="stores.single.gp_ready_tag_name" ng-click="clipboard(stores.single.gp_ready_tag_name)">Copy to clipboard</button>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" groov-popover="The GP Ready Tag can be used to import or re-import (update) specific orders from ShipStation. Any order with the GP Ready tag will be imported to GroovePacker on the next import regardless of the order status or order date. Once imported, the GP Ready tag will be removed.
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
          For additional confirmation you can also create a GP Imported tag. After the order is successfully imported the GP Ready tag will be replaced with a GP Imported tag. Both tags need to be created once in ShipStation before they can be used by GroovePacker.
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
          &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
          The tags can be copied to the clipboard and pasted when you create the tag in ShipStation in ensure an exact match. Once you've created the tags you can use the Test Tag button to verify they are found."></i>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">{{stores.single.gp_imported_tag_name}}</label>

      <div class="controls col-md-8">
        <button class="btn btn-success" clip-copy="stores.single.gp_imported_tag_name" clip-click="alert('clicked')" ng-click="clipboard(stores.single.gp_imported_tag_name)">
          Copy to clipboard
        </button>

        <button class="btn btn-success pull-right" ng-click="shipstation_verify_tags(stores.single.id)">Test tags
        </button>
        <div class="alert"
             ng-class="{'alert-success':verification_tags.verification_result, 'alert-danger': verification_tags.verification_result == false}"
             style="margin-top: 10px; margin-bottom: 0px;">
          <p>{{verification_tags.message}}</p>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Options</legend>
    <!-- <div class="form-group">
      <label class="control-label col-md-4">Regular Import Range</label>

      <div class="controls col-md-8" dropdown>
        <button class="dropdown-toggle groove-button" style="text-transform: none;">
          <span>{{stores.single.regular_import_range}} day(s)</span>
          <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
          <li ng-repeat="day in stores.single.import_days">
            <a class="dropdown-toggle" ng-click="stores.single.regular_import_range=day; update_single_store(true)">{{day}}
              day(s)</a>
          </li>
        </ul>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="This setting allows you to choose how many days prior to the previous import GroovePacker should check when looking for orders during a regular import. A lower setting will reduce import times. This is particularly helpful when you are importing Shipped orders and here are hundreds or thousands of orders that match the status and date range.<br/> If you routinely place orders on hold for 3 days, then a setting of 4 days would be advisable as you would rarely have out of range orders. Orders that have been placed on hold for longer, and then changed to awaiting, will be outside of the Date range that is checked. These can be imported using the Deep import which looks back 7 days prior to the previous import or by using the GP Ready tag to import select orders."></i>
      </div>
    </div> -->

    <div class="form-group">
      <label class="control-label col-md-4">Import Tracking Info</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_tracking_info" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="When enabled an additional request will be made for the tracking number so it can be saved with the order. If you do not require the tracking number in GroovePacker you can disable this option an increase import speed." />
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Remove Cancelled Orders from GP</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.remove_cancelled_orders"
             groov-click="fix_import_dates();update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="Cancelled orders present in GP will be removed if the switch is enabled"></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Require Tracking Number</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_shipped_having_tracking" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Add GP SCANNED Tag</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.add_gpscanned_tag" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="Enabling this will allow Groovepacker to add GP SCANNED tag to the order in Shipstation."></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Generate Barcode from SKU</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.gen_barcode_from_sku" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="If your Barcodes match your SKU, you can enable this option to have GroovePacker automatically generate Barcodes when new products are found during order imports."/>
      </div>
    </div>

    <div class="form-group" ng-if="stores.single.gen_barcode_from_sku == true">
      <div class="form-group">
      <label class="control-label col-md-4">Import UPC if available</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_upc" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="If your products use a mix of internal barcodes which match the SKU and regular UPC barcodes which are associated with your products in your order manager, this option can be used to import the UPC when it exists. When no UPC is found it will be generated from the SKU. If you do not have any UPC codes associated with your items in your order manager please leave this disabled to increase import speed."/>
      </div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">On Demand v2</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.on_demand_import_v2" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="When enabled, scanned orders that have not been previously imported will be queued for import in the background while the packer continues to scan other orders."/>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Import User Assignments from ShipStation</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_user_assignments" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="When enabled, orders will be assigned to users in GroovePacker based on assignments made in ShipStation. Note: This requires GroovePacker usernames to match the usernames in ShipStation."/>
      </div>
    </div>

    <!-- <div class="form-group">
      <label class="control-label col-md-4">Version 2 Import</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.regular_import_v2" groov-click="update_single_store(true);v2_toggle();"></div>
      </div>
    </div> -->

    <div class="form-group">
      <label class="control-label col-md-4">Run QuickFix with On-Demand Import</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.quick_fix" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="This option will automatically run a QuickFix import to try and find any orders that may have been missed just prior to or after the order that was scanned."/>
      </div>
    </div>
<!--
    <div class="form-group">
      <label class="control-label col-md-4">Import Troubleshooter</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.troubleshooter_option" groov-click="update_single_store(true)"></div>
      </div>
    </div> -->

    <!-- <div class="form-group">
      <label class="control-label col-md-4">On Demand Import</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.on_demand_import" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="When enabled, scanned orders that have not been previously imported will be queued for import in the background while the packer continues to scan other orders."/>
      </div>
    </div> -->
    <div class="form-group">
      <label class="control-label col-md-4">Allow duplicate order id</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.allow_duplicate_order" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <!-- discount switch -->
    <div class="form-group" ng-show="stores.single.set_coupons_to_intangible">
      <label class="control-label col-md-4">Remove coupon codes from orders</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_discounts_option" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="When this setting is enabled coupons found in incoming orders will not be saved with the order. When disabled, the coupons will be saved as intangible items that will not require scanning."/>
      </div>
    </div>
    <!-- <div class="form-group">
      <label class="control-label col-md-4"></label>
      <div class="controls col-md-8">

        <button class="btn btn-success" confirm-click="Are you sure? This action will reset Last Import and Last Quick Import date to 24 hours ago." ng-click="fix_import_dates()">Fix it</button>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="This action will reset 'Last import' and 'Last Quick Import' to 24 hours ago."/>

      </div>
    </div> -->
    <div class="form-group">
      <label class="control-label col-md-4"></label>
      <div class="controls col-md-8">

        <button class="btn btn-success" ng-model="stores.single.download_ss_image" ng-click="update_product_image()">Add Image to existing products</button>
        <!-- <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="This action will reset 'Last import' and 'Last Quick Import' to 24 hours ago."/> -->

      </div>
    </div>

  </div>
</div>

<div class="row" ng-show="stores.single.ss_api_create_label == true">
  <div class="col-md-6">
    <legend>Create Label API</legend>
      <div class="form-group">
        <label class="control-label col-md-4">Use API to Create Shipstation Labels</label>
        <div class="controls col-md-8">
          <div toggle-switch ng-model="stores.single.use_api_create_label"
              groov-click="use_api_create_label()"></div>
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
            groov-popover="Enabling this will allow users to use API to create labels instead of using chrome extension."></i>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-4">Store Post Code</label>

        <div class="controls col-md-3">
          <input name="postcode" id="postcode" ng-model="stores.single.postcode" ng-blur="update_single_store(true)" class="form-control input-style" value=""/>
        </div>
        <div class="controls col-md-5">
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
            groov-popover="This is required to get rates when creating Shipping Labels"/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-4">Skip confirmation for orders with pre-selected rates</label>
        <div class="controls col-md-8">
          <div toggle-switch ng-model="stores.single.skip_ss_label_confirmation" groov-click="update_single_store(true)"></div>
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover=" When imported ordes always have the correct method and weight pre-assigned you can enable this switch and have the label print without confirmation. Orders with missing or conflicting options will still be shown."></i>
        </div>
      </div>
  </div>
</div>

<!-- <div class="row" ng-show="stores.single.ss_api_create_label == true">
  <div class="col-md-6">
    <legend>Store Address</legend>
    <div class="form-group">
      <label class="control-label col-sm-3">Name</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.full_name"
               placeholder="Name" ng-blur="update_single_store(true)"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-3">Address Line 1</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.street1"
               placeholder="Address Line 1" ng-blur="update_single_store(true)"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-3">Address Line 2</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.street2"
               placeholder="Address Line 2" ng-blur="update_single_store(true)"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-3">City</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.city"
               placeholder="City" ng-blur="update_single_store(true)"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-3">State</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.state"
               placeholder="State" ng-blur="update_single_store(true)"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Store Post Code</label>

      <div class="controls col-md-3">
        <input name="postcode" id="postcode" ng-model="stores.single.postcode" ng-blur="update_single_store(true)" class="form-control input-style" value=""/>
      </div>
      <div class="controls col-md-5">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This is required to get rates when creating Shipping Labels"/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-3">Country</label>

      <div class="controls col-sm-7">
        <input type="text" class="form-control input-style"
               ng-model="stores.single.country"
               placeholder="Country" ng-blur="update_single_store(true)"/>
      </div>
    </div>
  </div>
</div> -->

<div class="row">
  <div class="col-md-6">
    <legend>Import Notes</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Import Internal Notes</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_internal_notes"
             groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="Notes enabled here will be imported along with the orders.<br/><br/>You should also turn on the <strong>Show Internal Notes</strong> and <strong>Show Customer Notes</strong><strong>Show Tags</strong> in the Scan Pack settings to view the notes while scanning and packing."></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Import Customer Notes</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_customer_notes"
             groov-click="update_single_store(true)"></div>
      </div>
    </div>


    <div class="form-group" ng-if="stores.single.use_chrome_extention == true">
      <label class="control-label col-md-4">Auto Return to Groovepacker</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.switch_back_button"
             groov-click="switch_back_button()"></div>
      </div>
    </div>

    <div class="form-group" ng-if="stores.single.switch_back_button == true && stores.single.use_chrome_extention == true">
      <label class="control-label col-md-4">Return to Orders list</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.return_to_order"
             groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group" ng-if="stores.single.use_chrome_extention == true">
      <label class="control-label col-md-4">Auto-click Create label</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.auto_click_create_label"
             groov-click="auto_click_create_label()"></div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="row">
  <div class="col-md-6">
    <legend>Export Products</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Export Active Products</label>

      <div class="controls col-md-8">
        <button class="btn btn-success" ng-click="export_active_products()">Export</button>
      </div>
    </div>
  </div>
</div> -->

<div ng-if="stores.single.show_originating_store_id">
  <div class="row">
    <div class="col-md-12">
      <legend class="mb-4">Origin Stores</legend>
      <!-- switch to show order's origin source store's name  -->
      <div class="form-group">
        <label class="control-label col-md-4">Display original order origin store name</label>
  
        <div class="controls col-md-2">
          <div toggle-switch ng-model="stores.single.display_origin_store_name"
               groov-click="update_single_store(true)"></div>
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="By default the name of the store connection is displayed in GroovePacker. Enabling this option will allow you to display unique names for each order sources that import into this store. You’ll need to look up the recent orders to determine the source so it can be saved and displayed."></i>
        </div>
      </div>
      <!-- switch end -->
      <div ng-if="stores.single.origin_stores.length > 0">
        <table class="well table-well table table-striped table-alt">
          <thead>
            <tr>
              <th style="padding: 10px;">Origin Store ID</th>
              <th style="padding: 10px; width:50%">Recent Order Details</th>
              <th style="padding: 10px;">Store Name</th> 
              <th style="padding: 10px;">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="originStore in stores.single.origin_stores">
              <td>{{ originStore.origin_store_id }}</td>
              <td>{{ originStore.recent_order_details }}</td>
              <td>
                <input style="border-radius: 4px;" type="text" maxlength="25" ng-model="originStore.store_name" ng-blur="updateOriginStore(originStore.origin_store_id, originStore.store_name)">
                <div style="color: red; font-size: small;"ng-if= "originStore.store_name.length > 24 ">Max length is 25 characters</div>
              </td>
              <td>
                <button class="btn btn-success btn-sm" ng-click="updateOriginStore(originStore.origin_store_id, originStore.store_name)">
                  <i class="fa fa-save"></i> 
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Export Products</legend>
    <div class="form-group">
      <label class="control-label col-md-4">ShipStation Warehouse</label>

      <div class="controls col-md-8">
        <button class="btn btn-success" ng-click="bin_location_api_push(stores.single.id)">Bin Location API Push</button>
        <button class="btn btn-success" style="float: right;" ng-click="bin_location_api_pull(stores.single.id)">Bin Location API Pull</button>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend groov-click="expand_beta_option()">Beta Options</legend>
    <div ng-if= "beta_option== true">

      <div class="form-group">
        <label class="control-label col-md-4">Only import orders created in the last days.</label>
        <div class="controls col-md-3">
          <input name="order_import_range_days" id="order_import_range_days" ng-model="stores.single.order_import_range_days" type="number" min="0" ng-blur="change_order_import_range_days()" class="form-control input-style" value=""/>
        </div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
        groov-popover="Occasionally older orders are unexpectedly imported due to automated changes in the order manager. This can result in large imports of unwanted orders. To prevent this we filter orders and exclude those with an order date outside this range.  You can decrease the range if you always fulfill orders shortly after receiving them or extend the range if your items have a longer turn around time. Entering 0 will disable this filter and is not advised.  The filter is not applied when the Range import is used with the “Created” option selected."/>
      </div>

    </div>
  </div>
</div>
<textarea id="clipboard" type="hidden" style="opacity: 0;"></textarea>
