<div class="row">
  <div class="col-md-6">
    <legend>Shippo Credentials</legend>
    <div class="form-group">
      <label class="control-label col-md-4">API Key</label>
      <div class="controls col-md-8">
        <input name="name" xs-input-sync ng-model="stores.single.api_key" type="text" placeholder="Enter API Key"
               ng-blur="update_single_store(true)" class="form-control input-style" value="" required/>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">API Version</label>
      <div class="controls col-md-8">
        <input name="name" xs-input-sync ng-model="stores.single.api_version" type="text" placeholder="Enter API Version"
               ng-blur="update_single_store(true)" class="form-control input-style" value="" required/>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Options</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Require Tracking Number</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_shipped_having_tracking"
             groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
             groov-popover="If the tracking number will be used to cue or verify orders you can enable this switch to prevent orders from importing before they have a tracking number assigned."></i>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Orders with following statuses</legend>

    <div class="form-group" ng-hide="stores.single.import_any == true">
      <label class="control-label col-md-4 form-inline">Paid</label>

      <div class="controls col-md-8 form-inline">
        <div toggle-switch ng-model="stores.single.import_paid" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group" ng-hide="stores.single.import_any == true">
      <label class="control-label col-md-4 form-inline">Awaiting Payment</label>

      <div class="controls col-md-8 form-inline">
        <div toggle-switch ng-model="stores.single.import_awaitpay" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group" ng-hide="stores.single.import_any == true">
      <label class="control-label col-md-4 form-inline">Partially fulfilled</label>

      <div class="controls col-md-8 form-inline">
        <div toggle-switch ng-model="stores.single.import_partially_fulfilled" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group" ng-hide="stores.single.import_any == true">
      <label class="control-label col-md-4">Shipped</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_shipped" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Any</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_any" groov-click="update_single_store(true)"></div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <legend>For SKUs that do not have barcodes assigned</legend>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generate_barcode_option" value="do_not_generate" ng-model="stores.single.generate_barcode_option" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Do not generate barcodes</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generate_barcode_option" value="generate_from_sku" ng-model="stores.single.generate_barcode_option" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Generate barcodes from the SKU</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generate_barcode_option" value="generate_numeric_barcode" ng-model="stores.single.generate_barcode_option" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Generate numeric barcodes</label>
      </div>
    </div>
  </div>
</div>