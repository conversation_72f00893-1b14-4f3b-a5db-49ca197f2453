<div class="row">
  <div class="col-md-6">
    <legend>Veeqo Credentials</legend>
    <div class="form-group">
      <label class="control-label col-md-4">API Key</label>
      <div class="controls col-md-8">
        <input name="api_key" xs-input-sync ng-model="stores.single.api_key" type="text" ng-blur="update_single_store(true)"
               class="form-control input-style" value="" placeholder="Paste API Key Here" required/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6">
    <legend>Import Orders with following Statuses</legend>

    <div class="form-group">
      <label class="control-label col-md-4">Awaiting Fulfillment</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.awaiting_fulfillment_status"
             groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Awaiting Amazon Fulfillment</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.awaiting_amazon_fulfillment_status"
             groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Shipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Use Shopify as Product Source</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.use_shopify_as_product_source_switch" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div ng-if="stores.single.use_shopify_as_product_source_switch">
      <table class="table table-alt table-striped table-well">
        <thead>
          <tr>
            <th scope="col" height="30px">Shopify Store</th>
            <th scope="col">Status</th>
            <th scope="col">Product Import From</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="store in stores.single.shopify_active_stores">
            <td>{{ store.name }}</td>
            <td><span class="label" ng-class="{'label-success':store.status,'label-danger':!store.status}" >{{store.status ? 'ACTIVE' : 'INACTIVE'}}</span></td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.product_source_shopify_store_id"
                ng-value="store.id"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>

  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Options</legend>

    <div class="form-group">
      <label class="control-label col-md-4">Use Veeqo Order ID</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.use_veeqo_order_id" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Require Tracking Number</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.import_shipped_having_tracking" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Generate Barcode from SKU</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.gen_barcode_from_sku" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="If your Barcodes match your SKU, you can enable this option to have GroovePacker automatically generate Barcodes when new products are found during order imports."/>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Allow Duplicate Order Id</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.allow_duplicate_order" groov-click="update_single_store(true)"></div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Import Notes</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Import Internal Notes</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_internal_notes"
             groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="Notes enabled here will be imported along with the orders.<br/><br/>You should also turn on the <strong>Show Internal Notes</strong> and <strong>Show Customer Notes</strong><strong>Show Tags</strong> in the Scan Pack settings to view the notes while scanning and packing."></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Import Customer Notes</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shall_import_customer_notes"
             groov-click="update_single_store(true)"></div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend groov-click="expand_beta_option()">Beta Options</legend>
    <div ng-if= "beta_option== true">

      <div class="form-group">
        <label class="control-label col-md-4">Only import orders created in the last days.</label>
        <div class="controls col-md-3">
          <input name="order_import_range_days" id="order_import_range_days" ng-model="stores.single.order_import_range_days" type="number" min="0" ng-blur="change_order_import_range_days()" class="form-control input-style" value=""/>
        </div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
        groov-popover="Occasionally older orders are unexpectedly imported due to automated changes in the order manager. This can result in large imports of unwanted orders. To prevent this we filter orders and exclude those with an order date outside this range.  You can decrease the range if you always fulfill orders shortly after receiving them or extend the range if your items have a longer turn around time. Entering 0 will disable this filter and is not advised.  The filter is not applied when the Range import is used with the “Created” option selected."/>
      </div>

    </div>
  </div>
</div>
<textarea id="clipboard" type="hidden" style="opacity: 0;"></textarea>
