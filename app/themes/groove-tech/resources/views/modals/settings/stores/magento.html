<div class="row">
  <div class="col-md-8">
    <legend>Import Orders with following statuses</legend>
    <div class="form-group">
      <label class="control-label col-md-3">Processing</label>
      <div class="controls col-md-6">
        <div toggle-switch ng-model="stores.single.shall_import_processing" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-3">Pending</label>
      <div class="controls col-md-6">
        <div toggle-switch ng-model="stores.single.shall_import_pending" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-3">Closed</label>
      <div class="controls col-md-6">
        <div toggle-switch ng-model="stores.single.shall_import_closed" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-3">Complete</label>
      <div class="controls col-md-6">
        <div toggle-switch ng-model="stores.single.shall_import_complete" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-3">Suspected Fraud</label>
      <div class="controls col-md-6">
        <div toggle-switch ng-model="stores.single.shall_import_fraud" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    
    <legend>Magento Credentials</legend>
    <div class="form-group">
      <label class="control-label col-md-3">URL</label>

      <div class="controls col-md-6">
        <input name="host" ng-blur="update_single_store(true)" ng-model="stores.single.host"
               class="form-control input-style" placeholder="http://www.domain.com/store" type="text" value=""
               required/><span>Please use https if the store is hosted at a secure URL.</span>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-3">Username</label>

      <div class="controls col-md-6">
        <input name="username" ng-blur="update_single_store(true)" ng-model="stores.single.username"
               class="form-control input-style" type="text" value="" required/>
      </div>
    </div>
    <!--
    <div class="form-group">
      <label class="control-label col-md-4">Password</label>
      <div class="controls col-md-8">
        <input name="password" ng-blur="update_single_store(true)"  ng-model="stores.single.password" class="form-control input-style" type="text" value="" required/>
       </div>
    </div>
    -->

    <div class="form-group">
      <label class="control-label col-md-3">API Key</label>

      <div class="controls col-md-6">
        <input name="apikey" ng-blur="update_single_store(true)" ng-model="stores.single.api_key"
               class="form-control input-style" type="text" value="" required/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8">
    <legend>Store Settings</legend>

    <label class="checkbox">
      <input type="checkbox" ng-change="update_single_store(true)"
             ng-model="stores.single.updated_patch"
             name="new_magento_patch"/> New Magento Patch <br/>
    </label>
    <label class="checkbox">
      <input type="checkbox" ng-change="update_single_store(true)"
             ng-model="stores.single.import_images"
             name="import_images"/> Import Images <br/>
    </label>
    <label class="checkbox">
      <input type="checkbox" ng-change="update_single_store(true)"
             ng-model="stores.single.import_products"
             name="import_products"/> Import Product Categories<br/>
    </label>

    <div class="form-group">
      <label class="control-label col-md-6">Enable/Disable Orders Status Update on Magento</label>
      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.enable_status_update" groov-click="update_single_store(true)"></div>
      </div>
      <div class="controls col-md-4">
        <div class="controls" ng-show="stores.single.enable_status_update" dropdown>
          <button class="dropdown-toggle groove-button">
            <span>{{stores.single.status_to_update}}</span>
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li>
              <a class="dropdown-toggle" ng-click="change_value_of_status_to_update('complete')">Complete</a>
            </li>
            <li>
              <a class="dropdown-toggle" ng-click="change_value_of_status_to_update('closed')">Closed</a>
            </li>
          </ul>
        </div>
      </div>
    </div>



    <div class="form-group" ng-show="stores.single.enable_status_update && stores.single.allow_magento_soap_tracking_no_push">
      <label class="control-label col-md-6">Push Tracking Number</label>
      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.push_tracking_number" groov-click="update_single_store(true)"></div>
      </div>
      <div class="controls col-md-1">
        <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;" popover-trigger="mouseenter" popover-placement="top" groov-popover="When enabled the tracking number associated with the order in GroovePacker will be pushed back to Magento. Note: Any existing tracking number in Magento for the order will be overwritten."></i>
      </div>
    </div>


  </div>
</div>
<div class="row" ng-hide="true">
  <div class="col-md-6">
    <legend>Import Orders</legend>
    <div class="form-group">
      <div class="controls col-md-8">
        <button ng-click="import_orders()" class="modal-save-button">Import Orders</button>
      </div>
    </div>
    <div class="form-group">
      <div class="col-xs-12">
        <label class="alert alert-info" ng-show="stores.import.order.status_show">{{stores.import.order.status}}</label>
      </div>
    </div>
  </div>
</div>

<div class="row" ng-hide="true">
  <div class="col-md-6">
    <legend>Import Products</legend>
    <div class="form-group">
      <div class="controls col-md-8">
        <button ng-click="import_products()" class="modal-save-button">Import Products</button>
      </div>
    </div>

    <div class="form-group">
      <div class="col-xs-12">
        <label class="alert alert-info"
               ng-show="stores.import.product.status_show">{{stores.import.product.status}}</label>
      </div>
    </div>
  </div>
</div>