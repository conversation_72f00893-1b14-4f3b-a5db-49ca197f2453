<div class="row">
  <div class="col-md-6">
    <legend>Store Settings</legend>
    <label class="checkbox">
      <input type="checkbox" ng-change="update_single_store(true)"
             ng-model="stores.single.import_images"
             name="import_images"/> Import Images <br/>
    </label>
    <label class="checkbox">
      <input type="checkbox" ng-change="update_single_store(true)"
             ng-model="stores.single.import_products"
             name="import_products"/> Import Product Categories<br/>
    </label>
  </div>
</div>
<br/>
<div class="row">
  <div class="col-md-6">
    <legend>Import Orders with following statuses</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Shipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Unshipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.unshipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-4">
    <legend>Access Token</legend>
    <!-- for api import with a valid state -->
    <div ng-show="stores.import.order.type=='apiimport'">

      <div class="form-group" ng-show="stores.ebay.show_url==true && stores.ebay.signin_url_status==true">
        <label class="control-label col-md-4"></label>

        <div class="controls col-md-8">
          <a href="" ng-click="launch_ebay_popup()">Click here to access ebay</a>
        </div>
      </div>
      <div ng-hide="stores.ebay.show_url">
        <p class="alert alert-success">Auth Token is successfully retrieved and hidden for security reasons. You can
          import orders by clicking on Import Orders button on the top navigation menu.</p>

        <div>
          <a href="" ng-click="disconnect_ebay_seller()">Disconnect from seller account</a>
        </div>
      </div>

      <div ng-show="stores.import.order.type=='csvimport'">
        <div class="form-group">
          <div class="col-xs-12">
            <input name="csvfile" ng-model="ordercsvfile" type="file" value="" required/>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
<div class="row" ng-hide="true">
  <div class="col-md-6">
    <legend>Import Products</legend>

    <div class="form-group">
      <div class="controls col-md-8">
        <button ng-click="import_products()" class="modal-save-button">Import Products</button>
      </div>
    </div>
    <div class="form-group">
      <div class="col-xs-12">
        <label class="alert alert-info"
               ng-show="stores.import.product.status_show">{{stores.import.product.status}}</label>
      </div>
    </div>
  </div>
</div>
