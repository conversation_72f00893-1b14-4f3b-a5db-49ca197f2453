<div class="row">
  <div class="col-md-8">
    <legend>Shopify Information</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Shop Name</label>

      <div class="controls col-md-8">
        <div class="input-group">
          <span class="input-group-addon"><strong>https://</strong></span>
          <input  type="search" name="username" xs-input-sync ng-model="stores.single.shop_name" type="text"
                 ng-blur="update_single_store(true)" class="form-control input-style" placeholder="yourshopname" value=""
                 required/>
          <span class="input-group-addon"><strong>.myshopify.com</strong></span>
        </div>
      </div>
    </div>
    <div class="form-group" ng-if="stores.single.shopify_access_scopes != null">
      <label class="control-label col-md-4">Access Scopes</label>
      <div class="controls col-md-8">
        <span ng-repeat="access_scope in stores.single.shopify_access_scopes" class="badge badge-important">{{access_scope}}</span>
      </div>
    </div>
    <div class="form-group" ng-if="stores.single.shopify_access_scopes === null">
      <label class="control-label col-md-4">Access Scopes</label>
      <div class="col-md-8">
        <div class="alert alert-danger" role="alert" style="margin-bottom: 0px;">
          Please check the Shopify connection & use valid credentials.
        </div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Access Token</label>
      <div class="controls col-md-8">
        <input name="access_token" xs-input-sync ng-model="stores.single.access_token" type="text"
               ng-blur="update_single_store(true)" class="form-control input-style" value="" placeholder="paste Access Token here" required/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4"></label>

      <div class="controls col-md-8">
        <button class="btn btn-success" ng-click="update_single_store(true);launch_shopify_popup($event)">
          Authorize access
        </button>
      </div>
    </div>
    <div ng-hide="stores.single.access_token == null || stores.single.access_token == 'null'">
      <div class="form-group">
        <label class="control-label col-md-4">Access token</label>

        <div class="controls col-md-8">
          <p class="form-control-static">Available</p>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-4"></label>

        <div class="controls col-md-8">
          <button class="btn btn-success" ng-click="disconnect_shopify()">
            Disconnect access
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" ng-show="stores.single.import_updated_sku">
  <div class="col-md-8">
    <legend>Updated SKU handling</legend>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="updated_sku_handling" value="remove_all" ng-model="stores.single.updated_sku_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Remove all GroovePacker SKU data for the product and save the updated Shopify SKU.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This is the best option if the data in Shopify is correct and there is only one valid sku per item."/>
      </div>
    </div>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="updated_sku_handling" value="add_to_existing" ng-model="stores.single.updated_sku_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Add the updated Shopify SKU to the other skus saved in GroovePacker.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This option should be chosen when GroovePacker contains additional skus that are not saved in Shopify but are required. Keep in mind that additional skus may increase product scanning response time."/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8">
    <legend>Modified barcode handling</legend>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="modified_barcode_handling" value="remove_all" ng-model="stores.single.modified_barcode_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Remove all GroovePacker barcode data for the product and save the updated Shopify UPC.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This is the best option if the data in Shopify is correct and there is only one valid barcode per item."/>
      </div>
    </div>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="modified_barcode_handling" value="add_to_existing" ng-model="stores.single.modified_barcode_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Add the Shopify UPC to the other barcodes saved in GroovePacker.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This option should be chosen when GroovePacker contains additional barcodes that are not saved in Shopify but are required. Keep in mind that additional barcodes may increase product scanning response time."/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8">
    <legend>Generating Barcodes</legend>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes" value="generate_from_sku" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">The barcode should match the SKU for all products. UPC’s saved in Shopify will be ignored and a barcode will be generated from the SKU for every item.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes" value="generate_numeric_barcode" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Generate Numeric Barcode for the products that do not have a UPC saved in Shopify. Any UPC saved in Shopify should be imported.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes" value="import_from_shopify" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">The barcode matches the SKU for products that do not have a UPC saved in Shopify. Any UPC saved in Shopify should be imported. Other items should have a barcode generated from the SKU.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes" value="do_not_generate" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Do not generate barcodes from the SKU.</label>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Status</legend>
    <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
       groov-popover="These switches control which orders will be imported from Shopify. You will need to have at least one switch from each group enabled. Orders must match at least one switch from both groups to be imported. For example, if the “Open” status switch and the switch for “Unfulfilled” were the only ones enabled, the fulfilled and partially fulfilled orders would not be imported, even if they have a status of Open." />
    
    <div class="form-group">
      <label class="control-label col-md-4">Open</label>
      <div class="controls col-md-8">
        <input type="checkbox"
          ng-checked="stores.single.shopify_status === 'open' || stores.single.shopify_status === 'any'"
          ng-click="toggleStatus('open')">
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Archived</label>
      <div class="controls col-md-8">
        <input type="checkbox"
          ng-checked="stores.single.shopify_status === 'closed' || stores.single.shopify_status === 'any'"
          ng-click="toggleStatus('closed')">
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Fulfillment</legend>
    <div class="form-group">
      <label class="control-label col-md-4">On Hold</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.on_hold_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Unfulfilled</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.unshipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Partially Fulfilled</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.partial_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Fulfilled</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div ng-if="stores.single.shipped_status == true" class="form-group">
      <div>
        <label class="control-label col-md-4">Require tracking number</label>
        <div class="controls col-md-8">
          <div toggle-switch ng-model="stores.single.import_fulfilled_having_tracking" groov-click="update_single_store(true)"></div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6">
    <legend>Options</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Add GP SCANNED Tag</label>
      <div class="controls col-md-3">
        <div toggle-switch ng-model="stores.single.add_gp_scanned_tag" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="Enabling this will allow Groovepacker to add GP SCANNED tag to the order in Shopify."></i>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Open Shopify to create shipping label after scanning</label>
      <div class="controls col-md-3">
        <div toggle-switch ng-model="stores.single.open_shopify_create_shipping_label" groov-click="update_single_store(true)"></div>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-4">Set Scanned order items to "Fulfilled" in Shopify</label>
      <div class="controls col-md-3">
        <div toggle-switch ng-model="stores.single.mark_shopify_order_fulfilled" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="Enabling this switch will modify the status of your Shopify order items after they are Scanned in GroovePacker. All orders with no remaining Unscanned orders in GroovePacker will be updated in Shopify. If some order items will be Fulfilled later they should be marked “Not Fullfilled” in GroovePacker. Before enabling this opiton please verify that this status change will not cause any issues or conflicts with other apps in your workflow that may rely on or modify the order status in Shopify."/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8" ng-if="stores.single.allow_shopify_inv_push">
    <legend>Inventory Management</legend>
    <div class="form-group">
      <label class="control-label col-md-3">Inventory Push/Pull</label>

      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.update_inv" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="The Pull Inventory button can be used to overwrite the QOH for all products in GroovePacker that are associated with (created by) this Shopify store. The Push Inventory button can be used to overwrite the QOH saved in Shopify for the same products."/>
      </div>
    </div>

    <div class="form-group" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
      <label class="control-label col-md-1"></label>

      <div class="col-sm-4">
        <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="pull_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Pull Inventory
        </button>
      </div>
      <div class="col-sm-5">
        <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="push_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Push Inventory
        </button>
      </div>
    </div>
    <div class="form-group col-sm-12" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
      <div>
        <label class="col-sm-5"><b>From Shopify To GroovePacker</b></label>
        <label class="col-sm-5"><b>From GroovePacker To Shopify</b></label>
      </div><br><br>
      <div class="form-group col-sm-5" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
        <button ng-click="toggle_shopify_sync('enable')" class="dropdown-toggle groove-button label label-default label-warning" translate>Enable Sync for every item
        </button>
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
            groov-popover="{{translations.tooltips.shopify_inventory_sync}}"/><br><br>
        <button ng-click="toggle_shopify_sync('disable')" class="dropdown-toggle groove-button label label-default label-warning" translate>Disable Sync for every item
        </button>
      </div>
    </div>

    <div class="form-group">
      <label class="control-label col-md-3">Pull Combined QOH</label>

      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.pull_combined_qoh" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="{{translations.tooltips.pull_combined_qoh}}"/>
      </div>
    </div>

    <div>
      <table class="table table-alt table-striped table-well">
        <thead>
          <tr>
            <th scope="col">Name</th>
            <th scope="col">Location Status</th>
            <th scope="col">Push To</th>
            <th scope="col">Pull From</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="location in stores.single.shopify_locations">
            <td>{{ location.name }}</td>
            <td><span class="label" ng-class="{'label-success':location.active,'label-danger':!location.active}" >{{location.active ? 'ACTIVE' : 'INACTIVE'}}</span></td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.push_inv_location_id"
                ng-value="location.id"
              />
            </td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.pull_inv_location_id"
                ng-value="location.id"
                ng-disabled="stores.single.pull_combined_qoh"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <br/>
</div>

<div class="row">
  <div class="col-md-12">
    <legend>Shopify Integration Setup</legend>
    <iframe width="100%" height="358" src="https://www.loom.com/embed/790fb49575a44555b3441c0a73d2db61?sid=5ac3ac67-6dd5-401c-a483-4b0eb08ce093" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
    <ol class="list-group">
      <li class="list-group-item">
        <p><a href="https://help.shopify.com/en/manual/apps/custom-apps" target="_blank">https://help.shopify.com/en/manual/apps/custom-apps</a></p>
      </li>
      <li class="list-group-item">
        <p>1. Enable custom app development from the Shopify admin
          <a href="https://help.shopify.com/en/manual/apps/custom-apps#enable-custom-app-development-from-the-shopify-admin" target="_blank">Click Here</a></p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">2. Create and install a custom app
          <a href="https://help.shopify.com/en/manual/apps/custom-apps#create-and-install-a-custom-app">Click Here</a></p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">3 . Get the API credentials for a custom app
          <a href="https://help.shopify.com/en/manual/apps/custom-apps#get-the-api-credentials-for-a-custom-app">Click Here</a></p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">4 . Update Admin API scopes for a custom app
          <a href="https://help.shopify.com/en/manual/apps/custom-apps#get-the-api-credentials-for-a-custom-app">Click Here</a><br>
          Select read_orders, read_inventory, write_inventory, write_orders, write_products and read_products</p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">5 . Install the application if not installed
        </p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">6 . Get the access token
        </p>
      </li>
    </ol>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend groov-click="expand_beta_option()">Beta Options</legend>
    <div ng-if= "beta_option== true">

      <div class="form-group">
        <label class="control-label col-md-4">Only import orders created in the last days.</label>
        <div class="controls col-md-3">
          <input name="order_import_range_days" id="order_import_range_days" ng-model="stores.single.order_import_range_days" type="number" min="0" ng-blur="change_order_import_range_days()" class="form-control input-style" value=""/>
        </div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
        groov-popover="Occasionally older orders are unexpectedly imported due to automated changes in the order manager. This can result in large imports of unwanted orders. To prevent this we filter orders and exclude those with an order date outside this range.  You can decrease the range if you always fulfill orders shortly after receiving them or extend the range if your items have a longer turn around time. Entering 0 will disable this filter and is not advised.  The filter is not applied when the Range import is used with the “Created” option selected."/>
      </div>

    </div>
  </div>
</div>
