<div class="row">
  <div class="col-sm-4 container-fluid">
    <legend>Order Import</legend>
    <br/>

    <div class="form-group">
      <div class="controls col-xs-12">
        <input name="orderfile" file-upload type="file" value="" required/>
        <br/>
      </div>
      <div class="controls col-sm-8 text-center" ng-show="stores.csv.mapping.order_csv_map_id">
        <span class="glyphicon glyphicon-globe glyphicon-5x"></span><br/>
        <span class="glyphicon-2x">Map Ready</span>
      </div>
      <div class="controls col-sm-8 text-center" ng-hide="stores.csv.mapping.order_csv_map_id">
        <span><img src="/assets/images/caution_icon.png"/></span><br/>
        <span class="glyphicon-2x">No Map Found</span>
      </div>


    </div>
    <div class="form-group">
      <div class="controls col-sm-12" dropdown>
        <button class="groove-button dropdown-toggle"><span
          ng-hide="stores.csv.mapping.order_csv_map_id">Select Map</span> <span
          ng-repeat="map in stores.csv.maps['order']"
          ng-if="map.id == stores.csv.mapping.order_csv_map_id">{{map.name}}</span> <span class="caret"></span></button>
        <ul class="dropdown-menu">
          <li ng-repeat="map in stores.csv.maps['order']" style="position: relative;"><a class="dropdown-toggle text-center" ng-click="select_map(map)">{{map.name}}</a>
            <i class="fa fa-times" aria-hidden="true" ng-click="delete_map(map)" style="position: absolute; top: 10px; right: 4px;"></i>
          </li>
        </ul>
      </div>
    </div>
    <div class="form-group text-center">
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="clear_map('order')">Clear Map</button>
      </div>
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="edit_map()">Edit Map</button>
      </div>
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="check_map()">Check Map</button>
      </div>
      <div class="controls col-sm-4">
        <button class="modal-save-button" ng-click="import_map()">Import Orders</button>
      </div>
    </div>
  </div>

  <div class="col-sm-6">
    <div class="form-group" ng-if="stores.single.is_fba == true">
      <label class="control-label col-sm-7 remove_padding_left">FBA Import</label>
      <div class="controls col-sm-5">
        <div toggle-switch ng-model="stores.single.fba_import" ng-click="update_fba()"></div>
      </div>
    </div>
    <br/><br/></br>
    <div class="form-group">
      <label class="control-label col-sm-7 remove_padding_left">FTP/SFTP Order Import</label>
      <div class="controls col-sm-5">
        <div toggle-switch ng-model="stores.single.use_ftp_import" ng-click="update_ftp_credentials()"></div>
      </div>
    </div>
    <div class="form-group" ng-show="stores.single.use_ftp_import">
      <label class="control-label col-sm-3 remove_padding_left">FTP/SFTP Address</label>
      <div class="controls col-sm-5 remove_padding_right remove_padding_left">
        <input type="search" class="form-control input-style" placeholder="ftp.yourserver.com/files/"  ng-disabled="check_ftp_input_is_disable()" ng-model="stores.single.host" autocomplete="off" ng-blur="update_ftp_credentials()"/>
      </div>
      <div class="info col-sm-1">
        &nbsp;&nbsp;&nbsp;<i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="{{translations.tooltips.ftp_address}}"></i>
      </div>
      <!-- <label class="control-label col-sm-1">Port</label>
      <div class="controls col-sm-2">
        <input type="text" class="form-control input-style" ng-model="stores.single.port" placeholder=21 />
      </div> -->
      <label class="control-label col-sm-1 remove_padding">FTP<input  type="radio" class="pull-left" ng-model="stores.single.connection_method" value="ftp" checked="checked" ng-change="update_ftp_credentials()"/></label>
      <label class="control-label col-sm-2">SFTP<input type="radio" class="pull-left" ng-model="stores.single.connection_method" value="sftp" ng-change="update_ftp_credentials()">&nbsp;&nbsp;</label>
    </div>
    <div class="form-group" ng-show="stores.single.use_ftp_import">
      <label class="control-label col-sm-3">Username</label>
      <div class="controls col-sm-3 remove_padding_left">
        <input type="search" class="form-control input-style col-sm-6" ng-model="stores.single.username" autocomplete="off" ng-disabled="check_ftp_input_is_disable()" ng-blur="update_ftp_credentials()"/>
      </div>
      <label class="control-label col-sm-3">Password</label>
      <div class="controls col-sm-3 remove_padding_left">
        <input type="password" class="form-control input-style" ng-model="stores.single.password" ng-disabled="check_ftp_input_is_disable()" autocomplete="off" ng-blur="update_ftp_credentials()"/>
      </div>
    </div>
    <div class="col-sm-offset-3 text-center" ng-show="stores.single.use_ftp_import">
      <br/><br/><br/>

      <button class="modal-save-button mini-popover" ng-click="import_ftp()">Import From FTP</button>
      &nbsp;&nbsp;&nbsp;<i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.import_from_ftp}}"></i>
      <!-- <button class="modal-save-button mini-popover" style="opacity: .50;" popover-trigger="mouseenter"  ng-show="!stores.single.connection_established || !stores.csv.mapping.order_csv_map_id" groov-popover="{{translations.tooltips.import_from_ftp}}">Import From FTP</button> -->
    </div>
    <br/><br/><br/><br/>
   <!--  <div class="form-group">
      <label class="control-label col-sm-7 remove_padding_left">CSV beta</label>
      <div class="controls col-sm-5">
        <div toggle-switch ng-model="stores.single.use_csv_beta" ng-click="update_ftp_credentials()"></div>
      </div>
    </div> -->
  </div>
  <div class="col-sm-2">
    <br/><br/><br/><br/><br/><br/><br/><br/>
    <div class="" ng-show="stores.single.use_ftp_import">
      <button id="test_connection" class="modal-save-button" ng-click="establish_connection()">Test Connection</button>
    </div>
    <div class="" ng-show="stores.single.use_ftp_import">
      <button id="test_connection" class="modal-save-button" ng-click="check_imported_folder()">Create Folder</button>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-4">
    <div class="form-group">

    </div>
  </div>
</div>
<div class="row">
  <div class=" col-sm-4">
    <legend>Product Import</legend>
    <br/>

    <div class="form-group">
      <div class="controls col-md-12">
        <input name="productfile" file-upload type="file" value="" required/>
        <br/>
      </div>
      <div class="controls col-sm-8 text-center" ng-show="stores.csv.mapping.product_csv_map_id">
        <span class="glyphicon glyphicon-globe glyphicon-5x"></span><br/>
        <span class="glyphicon-2x">Map Ready</span>
      </div>
      <div class="controls col-sm-8 text-center" ng-hide="stores.csv.mapping.product_csv_map_id">
        <span><img src="/assets/images/caution_icon.png"/></span><br/>
        <span class="glyphicon-2x">No Map Found</span>
      </div>

    </div>
    <div class="form-group">
      <div class="controls col-sm-12" dropdown>
        <button class="groove-button dropdown-toggle"><span
          ng-hide="stores.csv.mapping.product_csv_map_id">Select Map</span> <span
          ng-repeat="map in stores.csv.maps['product']" ng-if="map.id == stores.csv.mapping.product_csv_map_id">{{map.name}}</span>
          <span class="caret"></span></button>
        <ul class="dropdown-menu">
          <li ng-repeat="map in stores.csv.maps['product']" style="position: relative;"><a class="dropdown-toggle text-center" ng-click="select_map(map)">{{map.name}}</a>
          <i class="fa fa-times" aria-hidden="true" ng-click="delete_map(map)" style="position: absolute; top: 10px; right: 4px;"></i></li>
        </ul>
      </div>
    </div>
    <div class="form-group text-center">
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="clear_map('product')">Clear Map</button>
      </div>
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="edit_map()">Edit Map</button>
      </div>
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="check_map()">Check Map</button>
      </div>
      <div class="controls col-sm-4">
        <button class="modal-save-button" ng-click="import_map()">Import Products</button>
      </div>
    </div>
  </div>
  <div class="col-sm-6" ng-if="stores.single.product_ftp_import == true">
    <br/><br/></br>
    <div class="form-group">
      <label class="control-label col-sm-7 remove_padding_left">FTP/SFTP Product Import</label>
      <div class="controls col-sm-5">
        <div toggle-switch ng-model="stores.single.use_product_ftp_import" ng-click="update_ftp_credentials()"></div>
      </div>
    </div>
    <div class="form-group" ng-show="stores.single.use_product_ftp_import">
      <label class="control-label col-sm-3 remove_padding_left">FTP/SFTP Address</label>
      <div class="controls col-sm-5 remove_padding_right remove_padding_left">
        <input type="search" class="form-control input-style" placeholder="ftp.yourserver.com/files/"  ng-disabled="check_ftp_input_is_disable()" ng-model="stores.single.product_ftp_host" autocomplete="off" ng-blur="update_ftp_credentials()"/>
      </div>
      <div class="info col-sm-1">
        &nbsp;&nbsp;&nbsp;<i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="{{translations.tooltips.ftp_address}}"></i>
      </div>
      <!-- <label class="control-label col-sm-1">Port</label>
      <div class="controls col-sm-2">
        <input type="text" class="form-control input-style" ng-model="stores.single.port" placeholder=21 />
      </div> -->
      <label class="control-label col-sm-1 remove_padding">FTP<input  type="radio" class="pull-left" ng-model="stores.single.product_ftp_connection_method" value="ftp" checked="checked" ng-change="update_ftp_credentials()"/></label>
      <label class="control-label col-sm-2">SFTP<input type="radio" class="pull-left" ng-model="stores.single.product_ftp_connection_method" value="sftp" ng-change="update_ftp_credentials()">&nbsp;&nbsp;</label>
    </div>
    <div class="form-group" ng-show="stores.single.use_product_ftp_import">
      <label class="control-label col-sm-3">Username</label>
      <div class="controls col-sm-3 remove_padding_left">
        <input type="search" class="form-control input-style col-sm-6" ng-model="stores.single.product_ftp_username" autocomplete="off" ng-disabled="check_ftp_input_is_disable()" ng-blur="update_ftp_credentials()"/>
      </div>
      <label class="control-label col-sm-3">Password</label>
      <div class="controls col-sm-3 remove_padding_left">
        <input type="password" class="form-control input-style" ng-model="stores.single.product_ftp_password" ng-disabled="check_ftp_input_is_disable()" autocomplete="off" ng-blur="update_ftp_credentials()"/>
      </div>
    </div>
    <div class="col-sm-offset-3 text-center" ng-show="stores.single.use_product_ftp_import">
      <br/><br/><br/>

      <button class="modal-save-button mini-popover" ng-click="import_product_ftp()">Import From FTP</button>
      &nbsp;&nbsp;&nbsp;<i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.import_from_ftp}}"></i>
      <!-- <button class="modal-save-button mini-popover" style="opacity: .50;" popover-trigger="mouseenter"  ng-show="!stores.single.product_ftp_connection_established || !stores.csv.mapping.product_csv_map_id" groov-popover="{{translations.tooltips.import_from_ftp}}">Import From FTP</button> -->
    </div>
    <br/><br/><br/><br/>
   <!--  <div class="form-group">
      <label class="control-label col-sm-7 remove_padding_left">CSV beta</label>
      <div class="controls col-sm-5">
        <div toggle-switch ng-model="stores.single.use_csv_beta" ng-click="update_ftp_credentials()"></div>
      </div>
    </div> -->
  </div>
  <div class="col-sm-2">
    <br/><br/><br/><br/><br/><br/><br/><br/>
    <div class="" ng-show="stores.single.use_product_ftp_import">
      <button id="test_connection" class="modal-save-button" ng-click="establish_product_ftp_connection()">Test Connection</button>
    </div>
    <div class="" ng-show="stores.single.use_product_ftp_import">
      <button id="test_connection" class="modal-save-button" ng-click="check_product_ftp_imported_folder()">Create Folder</button>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-4">
    <div class="form-group">

    </div>
  </div>
</div>
<div class="row">
  <div class=" col-sm-4">
    <legend>Kits Import</legend>
    <br/>

    <div class="form-group">
      <div class="controls col-md-12">
        <input name="kitfile" file-upload type="file" value="" required/>
        <br/>
      </div>
      <div class="controls col-sm-8 text-center" ng-show="stores.csv.mapping.kit_csv_map_id">
        <span class="glyphicon glyphicon-globe glyphicon-5x"></span><br/>
        <span class="glyphicon-2x">Map Ready</span>
      </div>
      <div class="controls col-sm-8 text-center" ng-hide="stores.csv.mapping.kit_csv_map_id">
        <span><img src="/assets/images/caution_icon.png"/></span><br/>
        <span class="glyphicon-2x">No Map Found</span>
      </div>

    </div>
    <div class="form-group">
      <div class="controls col-sm-12" dropdown>
        <button class="groove-button dropdown-toggle"><span
          ng-hide="stores.csv.mapping.kit_csv_map_id">Select Map</span> <span ng-repeat="map in stores.csv.maps['kit']"
          ng-if="map.id == stores.csv.mapping.kit_csv_map_id">{{map.name}}</span>
          <span class="caret"></span></button>
        <ul class="dropdown-menu">
          <li ng-repeat="map in stores.csv.maps['kit']" style="position: relative;"><a class="dropdown-toggle text-center" ng-click="select_map(map)">{{map.name}}</a>
          <i class="fa fa-times" aria-hidden="true" ng-click="delete_map(map)" style="position: absolute; top: 10px; right: 4px;"></i>
          </li>
        </ul>
      </div>
    </div>
    <div class="form-group text-center">
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="clear_map('kit')">Clear Map</button>
      </div>
      <div class="controls col-sm-6 col-md-4 col-lg-3">
        <button class="groove-button" ng-click="edit_map()">Edit Map</button>
      </div>
      <div class="controls col-sm-4">
        <button class="modal-save-button" ng-click="import_map()">Import Kits</button>
      </div>
    </div>
  </div>
</div>
