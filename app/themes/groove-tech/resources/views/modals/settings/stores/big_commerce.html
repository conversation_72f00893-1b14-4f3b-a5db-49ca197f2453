<div class="row">
  <div class="col-md-6">
    <legend>Import Options</legend>
    <div class="form-group">
      <label class="control-label col-md-4">On Demand Import</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.on_demand_import" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="When enabled, scanned orders that have not been previously imported will be queued for import in the background while the packer continues to scan other orders."/>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-8">
    <legend>BigCommerce Information</legend>
    <div ng-show="stores.single.store_hash == null || stores.single.store_hash == 'null' || stores.single.access_token == null || stores.single.access_token == 'null'">
      <div class="form-group">
        <label class="control-label col-md-4"></label>

        <div class="controls col-md-8">
          <div class="input-group">
            1)-Please click the "Install" button below to launch the BigCommerce app store in a new tab.
          </div>
          <br/>
          <button class="btn btn-success" ng-click="update_single_store(true);launch_big_commerce_popup()" ng-disabled="stores.single.name == null || stores.single.name == 'null'">
            Install
          </button>
        </div>
      </div>
      
      <div class="form-group">
        <label class="control-label col-md-4"></label>
        <div class="controls col-md-8">
          <div class="input-group">
            2)-Click "Get it Now" on the GroovePacker App page and follow the prompts to complete the install.
          </div>
          <div class="input-group">
            3)-When complete please return and click "Check Connection" below.
          </div>
        </div>
      </div>
    </div>

    <div class="form-group">
      <div ng-hide="stores.single.store_hash == null || stores.single.store_hash == 'null' || stores.single.access_token == null || stores.single.access_token == 'null'">
        <div class="form-group">
          <label class="control-label col-md-4">Access token</label>
          <div class="controls col-md-8">
            <p class="form-control-static">Available</p>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-md-4"></label>
          <div class="controls col-md-8">
            <button class="btn btn-success" ng-click="check_bigcommerce_connection()">
              Check Connection
            </button>

            <button confirm-click="Are you sure?" class="btn btn-success" ng-click="disconnect_bigcommerce_connection()">
              Disconnect
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label class="control-label col-md-4"></label>
          <div class="controls col-md-8">
            <!--Connection message will display here on clicking 'Check Connection'-->
            {{stores.single.message}}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
