<div class="row">
  <div class="col-md-8">
    <legend>Shopline Information</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Shop Name</label>

      <div class="controls col-md-8">
        <div class="input-group">
          <span class="input-group-addon"><strong>https://</strong></span>
          <input  type="search" name="username" xs-input-sync ng-model="stores.single.shop_name" type="text"
                 ng-blur="update_single_store(true)" class="form-control input-style" placeholder="yourshopname" value=""
                 required/>
          <span class="input-group-addon"><strong>.myshopline.com</strong></span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Access Token</label>
      <div class="controls col-md-8">
        <input name="access_token" xs-input-sync ng-model="stores.single.access_token" type="text"
               ng-blur="update_single_store(true)" class="form-control input-style" value="" placeholder="paste Access Token here" required/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4"></label>

      <div class="controls col-md-8">
        <button class="btn btn-success" ng-click="update_single_store(true);launch_shopline_popup($event)">
          Authorize access
        </button>
      </div>
    </div>
    <div ng-hide="stores.single.access_token == null || stores.single.access_token == 'null' || stores.single.access_token == 'undefined'">
      <div class="form-group">
        <label class="control-label col-md-4">Access token</label>

        <div class="controls col-md-8">
          <p class="form-control-static">Available</p>
        </div>
      </div>
      <div class="form-group">
        <label class="control-label col-md-4"></label>

        <div class="controls col-md-8">
          <button class="btn btn-success" ng-click="disconnect_shopify()">
            Disconnect access
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" ng-show="stores.single.import_updated_sku">
  <div class="col-md-8">
    <legend>Updated SKU handling</legend>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="updated_sku_handling" value="remove_all" ng-model="stores.single.updated_sku_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Remove all GroovePacker SKU data for the product and save the updated Shopline SKU.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This is the best option if the data in Shopline is correct and there is only one valid sku per item."/>
      </div>
    </div>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="updated_sku_handling" value="add_to_existing" ng-model="stores.single.updated_sku_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Add the updated Shopline SKU to the other skus saved in GroovePacker.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This option should be chosen when GroovePacker contains additional skus that are not saved in Shopline but are required. Keep in mind that additional skus may increase product scanning response time."/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8">
    <legend>Modified barcode handling</legend>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="modified_barcode_handling_value" value="remove_all" ng-model="stores.single.modified_barcode_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Remove all GroovePacker barcode data for the product and save the updated Shopline UPC.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This is the best option if the data in Shopline is correct and there is only one valid barcode per item."/>
      </div>
    </div>
    <div class="form-row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="modified_barcode_handling_value" value="add_to_existing" ng-model="stores.single.modified_barcode_handling" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-8">
        <label style="margin-bottom: 15px;">Add the Shopline UPC to the other barcodes saved in GroovePacker.</label>
      </div>
      <div class="col-md-1">
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="This option should be chosen when GroovePacker contains additional barcodes that are not saved in Shopline but are required. Keep in mind that additional barcodes may increase product scanning response time."/>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-8">
    <legend>Generating Barcodes</legend>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes_value" value="generate_from_sku" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">The barcode should match the SKU for all products. UPC’s saved in Shopline will be ignored and a barcode will be generated from the SKU for every item.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes_value" value="generate_numeric_barcode" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Generate Numeric Barcode for the products that do not have a UPC saved in Shopline. Any UPC saved in Shopline should be imported.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes_value" value="import_from_shopline" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">The barcode matches the SKU for products that do not have a UPC saved in Shopline. Any UPC saved in Shopline should be imported. Other items should have a barcode generated from the SKU.</label>
      </div>
    </div>
    <div class="row">
      <div class="text-right form-group col-md-3">
        <input type="radio" name="generating_barcodes_value" value="do_not_generate" ng-model="stores.single.generating_barcodes" ng-change="update_single_store(true)">
      </div>
      <div class="col-md-9">
        <label style="margin-bottom: 15px;">Do not generate barcodes from the SKU.</label>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Status</legend>
     <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="These switches control which orders will be imported from Shopline. You will need to have at least one switch from each group enabled. Orders must match at least one switch from both groups to be imported. For example, if the “Open” status switch and the switch for “Unfulfilled” were the only ones enabled, the fulfilled and partially fulfilled orders would not be imported, even if they have a status of Open."/>
    <div class="form-group">
      <label class="control-label col-md-4">Open</label>

      <div class="controls col-md-8">
        <input type="radio" name="interval" value="open" ng-model="stores.single.shopline_status"  ng-change="update_single_store(true)">
      </div>
    </div>
      <div class="form-group">
      <label class="control-label col-md-4">Archived</label>

      <div class="controls col-md-8">
        <input type="radio" name="interval" value="archived" ng-model="stores.single.shopline_status" ng-change="update_single_store(true)">
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6">
    <legend>Fulfillment</legend>
    <!-- <div class="form-group">
      <label class="control-label col-md-4">On Hold</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.on_hold_status" groov-click="update_single_store(true)"></div>
      </div>
    </div> -->
    <div class="form-group">
      <label class="control-label col-md-4">Unfulfilled</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.unshipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Partially Fulfilled</label>

      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.partial_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Fulfilled</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <!-- <div ng-if="stores.single.shipped_status == true" class="form-group">
      <div>
        <label class="control-label col-md-4">Require tracking number</label>
        <div class="controls col-md-8">
          <div toggle-switch ng-model="stores.single.import_fulfilled_having_tracking" groov-click="update_single_store(true)"></div>
        </div>
      </div>
    </div> -->
  </div>
</div>
<div class="row">
  <div class="col-md-8" ng-if="stores.single.allow_shopline_inv_push">
    <legend>Inventory Management</legend>
    <div class="form-group">
      <label class="control-label col-md-3">Inventory Push/Pull</label>

      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.update_inv" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="The Pull Inventory button can be used to overwrite the QOH for all products in GroovePacker that are associated with (created by) this Shopline store. The Push Inventory button can be used to overwrite the QOH saved in Shopline for the same products."/>
      </div>
    </div>

    <div class="form-group" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
      <label class="control-label col-md-1"></label>

      <div class="col-sm-4">
        <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="pull_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Pull Inventory
        </button>
      </div>
      <div class="col-sm-5">
        <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="push_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Push Inventory
        </button>
      </div>
    </div>
    <div class="form-group col-sm-12" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
      <div>
        <label class="col-sm-5"><b>From Shopline To GroovePacker</b></label>
        <label class="col-sm-5"><b>From GroovePacker To Shopline</b></label>
      </div><br><br>
      <!-- <div class="form-group col-sm-5" ng-show="stores.single.update_inv && stores.single.id && stores.single.access_token">
        <button ng-click="toggle_shopify_sync('enable')" class="dropdown-toggle groove-button label label-default label-warning" translate>Enable Sync for every item
        </button>
          <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
            groov-popover="{{translations.tooltips.shopify_inventory_sync}}"/><br><br>
        <button ng-click="toggle_shopify_sync('disable')" class="dropdown-toggle groove-button label label-default label-warning" translate>Disable Sync for every item
        </button>
      </div> -->
    </div>

    <!-- <div class="form-group">
      <label class="control-label col-md-3">Pull Combined QOH</label>

      <div class="controls col-md-2">
        <div toggle-switch ng-model="stores.single.pull_combined_qoh" groov-click="update_single_store(true)"></div>
        <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
           groov-popover="{{translations.tooltips.pull_combined_qoh}}"/>
      </div>
    </div>

    <div>
      <table class="table table-alt table-striped table-well">
        <thead>
          <tr>
            <th scope="col">Name</th>
            <th scope="col">Location Status</th>
            <th scope="col">Push To</th>
            <th scope="col">Pull From</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="location in stores.single.shopify_locations">
            <td>{{ location.name }}</td>
            <td><span class="label" ng-class="{'label-success':location.active,'label-danger':!location.active}" >{{location.active ? 'ACTIVE' : 'INACTIVE'}}</span></td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.push_inv_location_id"
                ng-value="location.id"
              />
            </td>
            <td>
              <input
                type="radio"
                ng-change="update_single_store(true)"
                ng-model="stores.single.pull_inv_location_id"
                ng-value="location.id"
                ng-disabled="stores.single.pull_combined_qoh"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div> -->
  </div>
  <br/>
</div>

<div class="row">
  <div class="col-md-12">
    <legend>Shopline Integration Setup</legend>
    <ol class="list-group">
      <li class="list-group-item">
        <p><a href="https://help.shopline.com/hc/en-001/articles/13091611056153-Develop-Apps-Configuration-Instructions" target="_blank">https://help.shopline.com/hc/en-001/articles/13091611056153-Develop-Apps-Configuration-Instructions</a></p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">1. Create a custom app
          <a href="https://help.shopline.com/hc/en-001/articles/13091611056153-Develop-Apps-Configuration-Instructions#h_01HJ8NSQSWWCDVMY87CC9TVTM8" target="_blank">Click Here</a>
        </p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">2 . Admin API Permissions
          <a href="https://help.shopline.com/hc/en-001/articles/13091611056153-Develop-Apps-Configuration-Instructions#h_01HJ8NVRTG16YVDJMC3GTTCY7S" target="_blank">Click Here</a><br>
          Select write_orders, read_orders, write_products, read_products, write_inventory, read_inventory, manage location, and read location.
        </p>
      </li>
      <li class="list-group-item">
        <p class="list-group-item-text">3 . App Access Token
          <a href="https://help.shopline.com/hc/en-001/articles/13091611056153-Develop-Apps-Configuration-Instructions#h_01HJ8NW5AQ5QJ96SV0EMFTT6TP" target="_blank">Click Here</a>
        </p>
      </li>
    </ol>
  </div>
</div>
