<div class="store-modal">
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="ok()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">
      <span ng-show="edit_status">Currently Updating {{stores.single.name}}</span>
      <span ng-hide="edit_status">Create Store</span>
    </div>
  </div>
  <div class="modal-body scrollable-main-body">
    <div class="container-fluid form-horizontal">
      <div class="row">
        <div class="col-md-8">
          <legend>Store Information</legend>
          <div class="form-group">
            <label class="control-label col-md-3">Store Name</label>

            <div class="controls col-md-9">
              <input name="name" ng-model="stores.single.name" ng-blur="update_single_store(true)" type="text"
                    class="form-control input-style" value="" autocomplete="none" data-lpignore="true" required/>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-md-3">Order Import</label>

            <div class="controls col-md-2">
              <div toggle-switch ng-model="stores.single.status" groov-click="update_single_store(true)"></div>
            </div>
            <div class="col-sm-6" ng-if="['Shopify', 'BigCommerce', 'Magento API 2', 'Shopline'].includes(stores.single.store_type)">
              <button ng-click="import_product_catalog()" class="groove-button label label-default label-danger" translate>Import Product Catalog
              </button>
              <button ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)" ng-click="fix_broken_images({{stores.single.id}})" class="groove-button label label-default label-danger" translate>Fix Broken Images
              </button>
            </div>

            <div class="col-sm-2" ng-if="stores.single.store_type=='Teapplix'">
              <button ng-click="import_product_catalog()" class="groove-button label label-default label-danger" translate>Import/Update Product Catalog
              </button>
            </div>

            <div class="clearfix"></div>
            <div class="col-md-3 col-sm-3"></div>
            <div class="col-sm-9 col-md-9" ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)" style="padding-top: 10px;">
              <button ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)" ng-click="cancel_shopify_product_imports()" class="groove-button label label-default label-danger" translate>Cancel All Product Imports</button>
            </div>

            <div class="clearfix"></div>
            <div class="col-md-3 col-sm-3"></div>
            <div class="col-sm-9 col-md-9" ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)">
              <div class="catalog-radio-main">
                <input type="radio" ng-click="update_product_import_type('refresh_catalog')" name="product_import_type" value="refresh_catalog">
                <label class="control-label col-md-5">Refresh the entire catalog</label>

                <input type="radio" ng-click="update_product_import_type('new_updated')" name="product_import_type" value="new_updated" checked>
                <label class="control-label col-md-5">New and Updated Items</label>
              </div>
            </div>

            <div class="clearfix"></div>
            <div class="col-md-3 col-sm-3"></div>
            <div class="col-sm-9 col-md-9" ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type) && show_product_import_range_days">
              <div class="">
                <label class="control-label col-md-10" style="text-align: left;">Import Products created or modified in the last days.</label>
                <div class="col-md-2">
                  <input name="product_import_range_days" id="product_import_range_days" class="form-control input-style" value="730" />
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <div class="col-md-3 col-sm-3"></div>
            <div class="col-sm-9 col-md-9" ng-if="stores.single.store_type=='Shopify'">
              <button ng-click="re_associate_all_products({{stores.single.id}})" class="dropdown-toggle groove-button label label-default label-danger" translate>Re-associate all products with Shopify
              </button>
              <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="This option allows GroovePacker to search Shopfiy for each SKU and retrieve the Shopify product ID so that catalog updates and fix image operations will work."/>
              <div class="catalog-radio-main">
                <input type="radio" name="re_associate_shopify_products" ng-model="stores.single.re_associate_shopify_products" ng-change="update_single_store(true)" value="associate_items">
                <label class="control-label col-md-5">Associate unconnected items with Shopify</label>
          
                <input type="radio" name="re_associate_shopify_products" ng-model="stores.single.re_associate_shopify_products" ng-change="update_single_store(true)" value="re_associate_items" checked>
                <label class="control-label col-md-5">Re-associate all items with Shopify</label>
              </div>
            </div>
          </div>

          <div ng-if="stores.single.packing_cam">
            <div class="form-group">
              <label class="control-label col-md-3">Disable Packing Cam</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.disable_packing_cam" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <div ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)">
            <div class="form-group">
              <label class="control-label col-md-3">Import inventory QOH from {{stores.single.store_type}}</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.import_inventory_qoh" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <div ng-if="stores.single.store_type=='Shopify'">
            <div class="form-group">
              <label class="control-label col-md-3">Permit Shared Barcodes</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.permit_shared_barcodes" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <!-- <div ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)"> -->
          <div ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)">
            <div class="form-group">
              <label class="control-label col-md-3">Import updated SKU from {{stores.single.store_type}}</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.import_updated_sku" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <div ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type)">
            <div class="form-group">
              <label class="control-label col-md-3">Fix all Product Images</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.fix_all_product_images" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <div ng-if="stores.single.store_type=='Shopify'">
            <div class="form-group">
              <label class="control-label col-md-3">On Demand Import</label>

              <div class="controls col-md-8">
                <div toggle-switch ng-model="stores.single.on_demand_import" groov-click="update_single_store(true)"></div>
                <i class="pull-right icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                   groov-popover="When enabled, scanned orders that have not been previously imported will be queued for import in the background while the packer continues to scan other orders."/>
              </div>
            </div>
          </div>

          <div ng-if="['Shopify', 'Shopline'].includes(stores.single.store_type) && stores.single.custom_product_fields">
            <div class="form-group">
              <label class="control-label col-md-3">Import Variant Names</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.import_variant_names" groov-click="update_single_store(true)"></div>
                <i class="pull-right icon-large glyphicon glyphicon-info-sign"  style="margin-top: 10px;" popover-trigger="mouseenter"
                   groov-popover="This switch should be enabled if you have product variants in your Shopify store and you would like the variant names to import and display during packing."/>
              </div>
            </div>
          </div>
        <div ng-if="stores.single.store_type=='Shopify'">
          <div class="form-group">
            <label class="control-label col-md-3">Webhooks Based Order Imports</label>
            <div class="controls col-md-2">
              <div toggle-switch ng-model="stores.single.webhook_order_import" groov-click="update_single_store(true)"></div>
              <i class="pull-right icon-large glyphicon glyphicon-info-sign"  style="margin-top: 10px;" popover-trigger="mouseenter"
                 groov-popover="This switch should be enabled if you want to automatically import orders when they are created or updated via shopify webhooks"/>
            </div>
          </div>
        </div>

          <div ng-if="stores.single.store_type=='Amazon'">
            <div class="form-group">
              <label class="control-label col-md-3"></label>
              <div  class="col-sm-4">
                <input name="productfile" file-upload type="file" value="" required style="width: 220px;" />
              </div>
              <div class="col-sm-2">
                  <button class="modal-save-button" ng-click="amazon_products_import()">Import Poducts</button>
              </div>
            </div>
            <div class="form-group">
              <label class="control-label col-md-3"></label>
              <div class="col-md-9">
                <small>( Please upload only unmodified txt file. )</small>
              </div>
            </div>

          </div>

          <div class="form-group" ng-if="stores.general_settings.email_address_for_packer_notes==null || stores.general_settings.email_address_for_packer_notes=='null' || stores.general_settings.email_address_for_packer_notes.length<1">
            <label class="control-label col-md-3">Note*</label>
            <div class="col-md-9">
              To receive an email notification when the import completes, please add an address for 'system notifications' in general settings.</a>
            </div>
          </div>

          <div ng-if="stores.single.store_type=='BigCommerce' && stores.single.allow_bc_inv_push">
            <div class="form-group">
              <label class="control-label col-md-3">Inventory Push/Pull</label>

              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.update_inv" groov-click="update_single_store(true)"></div>
              </div>
            </div>

            <div class="form-group" ng-show="stores.single.update_inv && stores.single.id && stores.single.store_hash && stores.single.access_token">
              <label class="control-label col-md-3"></label>

              <div class="col-sm-2">
                <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="pull_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Pull Inventory
                </button>
              </div>
              <div class="col-sm-2">
                <button confirm-click="Are you sure? This will overwrite inventory data!" ng-click="push_store_inventory()" class="dropdown-toggle groove-button label label-default label-danger" translate>Push Inventory
                </button>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-md-3"></label>

            <div class="col-sm-9" ng-show="stores.single.update_inv && !stores.single.id">
              'Push Inventory' and 'Pull Inventory' buttons will be provided once the store is created.
            </div>
            <div class="col-sm-9">
              <span style="color:RED;">{{stores.single.connection_message}}</span>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-md-3">Warehouse Location </label>

            <div class="controls col-md-9" dropdown>
              <button class="dropdown-toggle groove-button">
                <span ng-repeat="inv_wh in warehouses.list"
                      ng-show="stores.single.inventory_warehouse_id==inv_wh.info.id">{{inv_wh.info.name}}</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li ng-repeat="inv_wh in warehouses.list"><a class="dropdown-toggle"
                                                             ng-click="change_opt('inventory_warehouse_id',inv_wh.info.id)">{{inv_wh.info.name}}</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-md-3">Custom Packing-Slip Message For Customer</label>

            <div class="controls col-md-9">
              <div ng-controller="storeSingleCtrl">
                <div text-angular ng-model="stores.single.thank_you_message_to_customer" ng-blur="update_single_store(true)"></div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-md-3">Store Type</label>

            <div class="controls col-md-9" dropdown>
              <button class="dropdown-toggle groove-button">
                <span ng-repeat="(id ,store) in stores.types"
                      ng-show="stores.single.store_type==id">{{store.name}}</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li ng-repeat="(id ,store) in stores.types">
                  <!-- <a class="dropdown-toggle" ng-if="hide_if_mg_rest(store.name)" ng-click="change_opt('store_type',id)">{{store.name}}</a> -->
                  <a class="dropdown-toggle" ng-click="change_opt('store_type',id)">{{store.name}}</a>

                </li>
              </ul>
            </div>
          </div>

          <div ng-if="stores.single.store_type=='CSV' && stores.single.order_cup_direct_shipping_enabled">
            <div class="form-group">
              <label class="control-label col-md-3">OrderCup Direct Shipping</label>
              <div class="controls col-md-2">
                <div toggle-switch ng-model="stores.single.order_cup_direct_shipping" groov-click="update_single_store(true)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ng-repeat="(id, store_type) in stores.types" groov-include="{{store_type.file}}"
           ng-show="stores.single.store_type == id"></div>

    </div>
  </div>

  <div class="modal-footer">
    <button ng-click="ok()" class="modal-save-button" translate>modal.save_close</button>
    <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
  </div>
</div>
