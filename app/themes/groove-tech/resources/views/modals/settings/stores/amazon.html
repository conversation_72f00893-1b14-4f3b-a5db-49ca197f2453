<div class="row">
  <div class="col-md-6">
    <legend>Amazon Credentials</legend>
    <div ng-show="stores.import.order.type=='apiimport'">

      <div class="form-group">
        <div class="col-xs-12">
          <p><a href="https://developer.amazonservices.com/" target="_blank">Click here</a> to grant GroovePacker access
            to your Amazon Seller account.</p>

          <p>Sign Up for MWS and Choose Option 2: Use an application to access my Amazon Seller Account. Provide the
            following details of Groovepacker</p>
        </div>
      </div>

      <div class="form-group">
        <label class="col-md-5 text-right">Application Name</label>

        <div class="controls col-md-7">
          <p>GroovePacker</p>
        </div>
      </div>

      <div class="form-group">
        <label class="col-md-5 text-right">Developer Account Number</label>

        <div class="controls col-md-7">
          <p>1512-6494-8396</p>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-3">Seller ID</label>

        <div class="controls col-md-8">
          <input ng-blur="update_single_store(true)" name="merchant_id" ng-model="stores.single.merchant_id"
                 class="form-control input-style" type="text" value="" required/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-3">Marketplace ID</label>

        <div class="controls col-md-8">
          <input ng-blur="update_single_store(true)" name="marketplace_id" ng-model="stores.single.marketplace_id"
                 class="form-control input-style" type="text" value="" required/>
        </div>
      </div>

      <div class="form-group">
        <label class="control-label col-md-3">MWS Auth Token</label>

        <div class="controls col-md-8">
          <input ng-blur="update_single_store(true)" name="mws_auth_token" ng-model="stores.single.mws_auth_token" class="form-control input-style" type="text" value="" required/>
        </div>
        <div class="controls col-md-1" style="padding: 7px;">
          <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter" popover-placement="top" groov-popover="MWS Auth Token is optional unless there are multiple marketplaces (ie Amazon.ca, Amazon.mx etc.)"></i>
        </div>
      </div>

    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6">
    <legend>Store Settings</legend>

    <div class="form-group">
      <div class="col-xs-12">
        <input ng-change="update_single_store(true)" type="radio" ng-model="stores.single.show_shipping_weight_only"
               value="0" selected="selected"/> Display the Amazon Product Weight for products imported from this store
        <br/>
        <input ng-change="update_single_store(true)" type="radio" ng-model="stores.single.show_shipping_weight_only"
               value="1"> Display the Amazon Shipping Weight for products imported from this store
      </div>
    </div>

    <label class="checkbox">
      <input ng-change="update_single_store(true)" type="checkbox"
             ng-model="stores.single.import_images"
             name="import_images"/> Import Images <br/>
    </label>
    <label class="checkbox">
      <input ng-change="update_single_store(true)" type="checkbox"
             ng-model="stores.single.import_products"
             name="import_products"/> Import Product Categories<br/>
    </label>
  </div>
</div>
<br/>
<div class="row">
  <div class="col-md-6">
    <legend>Import Statuses</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Shipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.shipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Unshipped / PartiallyShipped</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.unshipped_status" groov-click="update_single_store(true)"></div>
      </div>
    </div>
  </div>
</div>
<br/>
<div class="row">
  <div class="col-md-6">
    <legend>Fulfillment Channel</legend>
    <div class="form-group">
      <label class="control-label col-md-4">Import orders Fulfilled by Amazon</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.afn_fulfillment_channel" groov-click="update_single_store(true)"></div>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-md-4">Import orders Fulfilled by the Seller</label>
      <div class="controls col-md-8">
        <div toggle-switch ng-model="stores.single.mfn_fulfillment_channel" groov-click="update_single_store(true)"></div>
      </div>
    </div>
</div>

<div class="row" ng-hide="true">
  <div class="col-md-6">
    <legend>Import Orders</legend>
    <div class="form-group">
      <div class="controls col-md-9">
        <button ng-click="import_orders()" class="modal-save-button">Import Orders</button>
      </div>
    </div>
    <div class="form-group">
      <div class="col-xs-12">
        <label class="alert alert-info" ng-show="stores.import.order.status_show">{{stores.import.order.status}}</label>
      </div>
    </div>
  </div>
</div>

<div class="row" ng-hide="true">
  <div class="col-md-6">
    <legend>Import Products</legend>
    <div ng-show="stores.import.product.type=='apiimport'">

      <div class="form-group">
        <div class="controls col-md-9">
          <button ng-click="request_import_products()" class="modal-save-button" ng-show="edit_status">Request Product
            Import
          </button>
        </div>
      </div>
      <div class="form-group" ng-show="stores.single.productreport_id!='' && !stores.single.productgenerated_report_id">
        <div class="controls col-md-9">
          <button ng-click="check_request_import_products()" class="modal-save-button" ng-show="edit_status">Check
            Report Status
          </button>
        </div>
      </div>
      <div class="form-group">
        <div class="controls col-md-9">
          <button ng-click="import_products(stores.single.productgenerated_report_id)" class="modal-save-button">Import
            Products
          </button>
        </div>
      </div>
      <div class="form-group">
        <div class="col-xs-12">
          <label class="alert alert-info"
                 ng-show="stores.import.product.status_show">{{stores.import.product.status}}</label>
        </div>
      </div>


    </div>
  </div>
</div>
