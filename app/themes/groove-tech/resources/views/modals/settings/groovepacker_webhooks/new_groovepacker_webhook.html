<div class="general-settings">
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="close()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">
      <span>{{formTitle}} Webhook</span>
    </div>
  </div>
  <form name="webhooks_form" role="form" class="form-validation" novalidate>
    <div class="modal-body">
      <div class="container-fluid form-horizontal">
        <div><br></div>
        <div class="form-group">
          <label class="control-label col-sm-3">Event</label>
          <div class="controls col-md-6" dropdown>
            <button class="dropdown-toggle groove-button" ng-model="webhook.event" ng-required="false">
              {{ webhook.event || 'Select Event' }}
              <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" role="menu">
              <li><a class="dropdown-toggle" ng-click="change_opt('Order Scanned')">Order Scanned</a></li>
            </ul>
          </div>
        </div>

        <div class="form-group" ng-show="false">
          <div class="col-md-3"></div>
          <div class="controls col-md-6" ng-class="{'has-error' : webhooks_form.$error.required && webhooks_form.$submitted }">
            <span class="has-error help-block">Webhook Event is required.
            </span>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">URL</label>

          <div class="controls col-md-6" ng-class="{'has-error' : webhooks_form.url.$invalid && (!webhooks_form.url.$pristine || webhooks_form.$submitted) }">
            <input type="text" class="form-control input-style" name="url"
                   ng-model="webhook.url"
                   placeholder="Webhook Url" required/>
            <span class="has-error help-block"
                   ng-show="webhooks_form.url.$invalid && (!webhooks_form.url.$pristine || webhooks_form.$submitted)">Webhook url is required.
            </span>
          </div>
          <div class="info col-sm-1">
            <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="Url should be like this: https://www.targetwebsiteurl.com/webhookendpoint."></i>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-md-3">Secret Key </label>

          <div class="controls col-md-6"
               ng-class="">
            <input class="form-control input-style" ng-model="webhook.secret_key" name="secretKey" type="text"
                   placeholder="Secret Key"/>
          </div>
        </div>

      </div>
    </div>
    <div class="modal-footer">
      <button ng-show="formTitle == 'Edit'" ng-click="updateWebhook(webhooks_form, webhook.id)" class="modal-save-button" translate>{{submitButtonText}} Webhook</button>
      <button ng-show="formTitle == 'Add'" ng-click="createWebhook(webhooks_form)" class="modal-save-button" translate>{{submitButtonText}} Webhook</button>
      <button ng-click="cancel()" class="modal-cancel-button" translate>modal.cancel</button>
    </div>
  </form>
</div>