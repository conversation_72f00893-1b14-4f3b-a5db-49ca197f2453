<div class="container-fluid">
  <div class="row" ng-show="orders.single.se_old_split_shipments">
    <div class="col-sm-12" style="padding: 25px;">
      <b>{{translations.labels.se_old_split_shipments}}</b>
      <div style="padding: 10px;">
        <div ng-repeat="shipment in orders.single.se_old_split_shipments" style="font-weight: bold;">
          <div class=row>
            <div class="col-md-2">
              <i class="fa fa-trash" aria-hidden="true" ng-click="$event.preventDefault();delete_shipment_order('{{shipment.id}}')"></i>&ensp;
              <a href ng-click="$event.preventDefault();get_order_details('{{shipment.increment_id}}')" style="color: #4c864c;">{{shipment.increment_id}}</a>&ensp;
            </div>
            <div class="col-md-10">
              {{shipment.status}}
            </div>
          </div>
        </div>
        </br>
        <div class="text-center">
          <b>{{translations.labels.remove_shipment}}</b>
          <div class="shipment_handling_bar_1" ng-click="orders.single.se_old_split_shipments = false">
            <b>{{translations.labels.remove_acknowledge_shipment}}</b>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row" ng-show="orders.single.se_old_combined_shipments">
    <div class="col-sm-12" style="padding: 25px;">
      <b>{{translations.labels.se_old_combined_shipments}}</b>
      <div style="padding: 10px;">
        <div ng-repeat="shipment in orders.single.se_old_combined_shipments" style="font-weight: bold;">
          <div class=row>
            <div class="col-md-2">
              <i class="fa fa-trash" aria-hidden="true" ng-click="$event.preventDefault();delete_shipment_order('{{shipment.id}}')"></i>&ensp;
              <a href ng-click="$event.preventDefault();get_order_details('{{shipment.increment_id}}')" style="color: #4c864c;">{{shipment.increment_id}}</a>&ensp;
            </div>
            <div class="col-md-10">
              {{shipment.status}}
            </div>
          </div>
        </div>
        </br>
        <div class="text-center">
          <b>{{translations.labels.remove_shipment}}</b>
          <div class="shipment_handling_bar_1" ng-click="orders.single.se_old_combined_shipments = false">
            <b>{{translations.labels.remove_acknowledge_shipment}}</b>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row" ng-show="orders.single.unacknowledged_activities.length > 0">
    <div class="col-sm-12">
      <fieldset>
        <h4>There have been changes to the order. Kindly acknowledge the activity if the change is expected or contact
          your administrator</h4>
          <span ng-repeat="activity in orders.single.unacknowledged_activities">
              <div class="form-group alert alert-acknowledge" ng-show="activity.activity_type=='deleted_item'">
                <div class="pull-right">
                  <button class="btn btn-info btn-xs"
                          ng-click="acknowledge_activity(activity.id)">Acknowledge
                  </button>
                </div>
                <h5>{{ activity.activitytime.replace('Z', '').slice(0, -6) | date:'EEEE MM/dd/yyyy hh:mm:ss a'}}</h5>
                {{ activity.action}} - by: {{ activity.username }}
              </div>
          </span>
      </fieldset>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-12">
      <div class="btn-group pull-right">
        <button class="modal-save-button"
                ng-show="orders.single.add_items_permitted"
                ng-click="item_order('order',item_products.list)"
                ng-hide="orders.single.basicinfo.status == 'scanned'">
          Add Item
        </button>
        <button class="modal-remove-button"
                ng-show="orders.single.remove_items_permitted"
                ng-click="item_remove_selected()"
                ng-hide="orders.single.basicinfo.status == 'scanned'">
          Remove selected Items
        </button>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-12">
      <div groov-data-grid="gridOptions" groov-list="orders.single.items"></div>
    </div>
  </div>
  <hr/>
</div>
