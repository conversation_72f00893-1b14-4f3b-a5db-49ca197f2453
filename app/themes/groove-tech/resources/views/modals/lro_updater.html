<!-- show order modal -->
<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title">Please choose the start of the import range.</div>
  </div>
  <div class="modal-body">
    <div class="tabbable px-5">
      <div class="nav nav-tabs modal-nav">
        <div class="row" style="padding-top: 100px;">
          <div class="col date-selector">
            <div class="text-center">
              <div class="group-input">
                <div class="input-group">
                  <input type="text" class="form-control" datepicker-popup="MM-dd-yyyy" ng-model="store_lro.date"
                    is-open="store_lro.open" ng-required="true" close-text="Close" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default" ng-click="open_picker($event, store_lro)"><i
                        class="glyphicon glyphicon-calendar"></i></button>
                  </span>
                </div>
              </div>
              <div class="controls">
                <timepicker ng-model="store_lro.date" show-meridian="true"></timepicker>
              </div>
              <button type="button" class="btn modal-save-button" ng-click="update_lro(store_lro.date)"
                style="margin-right: 20px;">Save</button>
              <button type="button" ng-click="cancel()" class="btn modal-save-button">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
