<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="ok()"><i class="glyphicon glyphicon-remove"></i></button>
  </div>
  <div class="modal-body">
    <div class="container-fluid form-horizontal" style="font-size: x-large;">
      <div class="row" style="padding: 0px 20px;">
        <div class="col-md-4">
          {{data.barcode}}
        </div>
        <div class="col-md-8 text-right">
          {{data.product.name}}
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12 text-center">
          <h1><b>SCAN & collect {{data.tote_identifier}} {{data.tote.name}} to Finalize the Order</b></h1>
          <img src="/assets/images/groovepacker-barcode.png" alt="GroovePacker" style="width: 50%;"/>
          <br>
          <img src="/assets/images/tote-full-arrow.png" alt="Tote" style="width: 20%;"/>
          <div class="row">
            <div class="col-md-8 col-md-offset-2" style="margin-top: 10px;">
              <input type="text" ng-model="data.tote_input" ng-keydown="scan_to_tote($event, 'scan_tote_to_complete', data.tote, data.order_item.id, data.tote_input, data.barcode_input)" autofocus="autofocus" tabindex="1"
              class="form-control search-box text-center" placeholder="Scan or Enter {{data.tote_identifier}} Barcode"/>
            </div>
          </div>
          <h3>Order #{{data.order.increment_id}} is assigned to {{data.tote.name}}</h3>
          <h3>{{data.order_qty.scanned_qty}} of {{data.order_qty.scanned_qty + data.order_qty.qty_remaining}} items have been scanned for this order.</h3>
          <div ng-repeat="order_item in data.order_items_scanned">
            <div style="color: green;"><i class="fa fa-check-circle"></i>&nbsp;&nbsp;{{order_item.scanned_qty}} of {{order_item.scanned_qty + order_item.qty_remaining}} {{order_item.sku}}</div>
          </div>
      </div>
    </div>
  </div>
</div>
<div ui-view></div>
<div class="scan_pack_success_fail row">
  <div class="middle col-xs-12" ng-show="scan_pack.scan_states.fail.image.enabled && scan_pack_state=='fail'">
  <img class="col-xs-8 col-xs-offset-2" ng-src="{{scan_pack.scan_states.fail.image.src}}" alt=""/>
  </div>
</div>

