<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <h3 style="font-weight:bold;">Initial Import Range</h3>
  </div>
  <div class="modal-body">
    <div class="container-fluid form-horizontal" tabindex="-1">
      <div>
        <div ng-hide="type == 'remaining_orders' || type == 'processed_orders'">
          <p style="font-size:18px;" ng-bind-html="message.message"></p>
        </div>

        <div ng-show="type == 'remaining_orders'">
          <h5>Range of the import in progress:</h5>
          <div>
            Start Date/Time: {{import_start}}
          </div>
          <div>
            End Date/Time: {{import_end}}
          </div>
          <div>
            Run by: {{run_by}}
          </div>
        </div>
        
        <div ng-show="type == 'processed_orders'">
          <h5>3 Most Recently Processed Orders:</h5>
          <div ng-repeat="order in message.processed_orders">
            <p>
              {{ order.id }}| - Order number {{ order.increment_id }} - OSLMT: {{ order.updated_at }} - Order Status: {{ order.status }}
            </p>
          </div>
        </div>        
        <br>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="container-fluid">
      <!-- <button ng-click="ok()" class="modal-save-button" ng-hide="type == 'remaining_orders' || type == 'processed_orders'" translate>Continue</button> -->
      <!-- <button ng-click="cancel()" class="modal-cancel-button" translate>Cancel</button> -->
      
      <div class="row">
        <div class="col-xs-6 text-left">
          <button ng-click="one_day(message.store_id)" class="modal-save-button" ng-hide="type == 'remaining_orders' || type == 'processed_orders'" translate>
            Continue
          </button>
        </div>
        <div class="col-xs-6 text-right">
          <button ng-click="ok()" class="modal-save-button" ng-hide="type == 'remaining_orders' || type == 'processed_orders'" translate>
            Adjust
          </button>
        </div>
      </div>
      
    </div>
  </div>
</div>
<div ui-view></div>
