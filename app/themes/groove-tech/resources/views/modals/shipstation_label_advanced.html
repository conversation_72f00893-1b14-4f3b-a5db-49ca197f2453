<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()">
      <i class="glyphicon glyphicon-remove"></i>
    </button>
    <div class="modal-title">Shipstation Shipping Label Options</div>
  </div>
  <div class="modal-body text-center" style="padding: 25px;">
    <div>
      <p>
        When imported ordes always have the correct method and weight pre-assigned you can enable this switch and have the label print without confirmation. Orders with missing or conflicting options will still be shown.
      </p>
    </div>
    <div class="form-group">
      <label class="control-label col-sm-5 col-sm-offset-3">Skip confirmation for orders with pre-selected rates</label>
      <div class="controls pull-left">
        <div toggle-switch ng-model="ss_label_advanced_options.skip_ss_label_confirmation"></div>
      </div>
    </div>
  </div>
</div>
<div ui-view></div>