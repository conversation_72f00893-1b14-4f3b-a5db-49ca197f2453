<!-- show order modal -->
<div>
  <div class="modal-header">
    <button type="button" class="close-btn" ng-click="cancel()"><i class="glyphicon glyphicon-remove"></i></button>
    <div class="modal-title"></div>
  </div>
  <div class="modal-body">
    <div class="tabbable px-5">
      <div class="nav nav-tabs modal-nav">
        <div class="tabbable">
          <div class="row missing-troubleshooter">
            <h1 class="text-center title"><img src="/assets/images/missing_order_troubleshooter.png"></h1>
            <div class="col-md-6 col-md-offset-3 text-center" ng-show="show_search== true" id="searchBar">
              <label>
                Find missing orders by entering an order number below.
              </label>
              <input type="text"
                ng-model="stores.order_no" ng-keydown="ok(stores.order_no, store_id, $event)" class="form-control input-style text-left" autofocus="autofocus" placeholder="Scan or Enter an Order Number here"/>
              <p>
                The details of the order you provide will be displayed and can be used to find other orders that have been missed by an import
              </p>
            </div>
          </div>
          <div id="order_details" ng-hide="show_order==true">
            <div class="row" id="top-order-details">
              <div class="col-md-6">
                <b class="text-center top-head" ng-if="order_show.startsWith('#')">Details for order {{order_show}}</b>
                <b class="text-center top-head" ng-if="!order_show.startsWith('#')">Details for order #{{order_show}}</b>
                  <div class="row pb-5">
                  <div class="col-md-7 text-right">
                    Order Last Modified Date:
                  </div>
                  <div class="col-md-5 text-left">
                    {{modifyed_at | date:' MM-dd-yyyy hh:mm:ss a '}} ({{ (stores.single.store_type == 'ShippingEasy' || stores.single.store_type == 'Shopify' || stores.single.store_type == 'Shopline') ? '(GMT-00:00) UTC' : '(GMT-08:00) PST'}})
                  </div>
                </div>
                <div class="row pb-5">
                  <div class="col-md-7 text-right">
                    Order Date:
                  </div>
                  <div class="col-md-5 text-left">
                    {{created_at  | date:' MM-dd-yyyy hh:mm:ss a '}} ({{gp_tz_name}})
                  </div>
                </div>
                <div class="row pb-5">
                  <div class="col-md-7 text-right">
                    Order Status in {{ getStoreNameForStatus(stores.single.store_type) }}:
                  </div>
                  <div class="col-md-5 text-left">
                    {{order_status}}
                  </div>
                </div>
                <div class="row pb-5" ng-show="stores.single.store_type == 'Shipstation API 2'">
                  <div class="col-md-7 text-right">
                    Tagged GP Ready:
                  </div>
                  <div class="col-md-5 text-left">
                    {{gp_ready_status}}
                  </div>
                </div>
                <div class="row pb-5">
                  <div class="col-md-7 text-right">
                    <button type="button" class="btn modal-save-button" ng-click="clear_details()">Clear Details</button>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <p class="heading-list"> Your Groovepacker Settings <a class="btn set-paddings" href="#/settings/stores/{{store_id}}" ><i class="fa fa-external-link" aria-hidden="true"></i></a>
                <p>

                <div ng-show="stores.single.store_type == 'ShippingEasy'">
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Orders List</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.import_ready_for_shipment" groov-click="send_data(true)"></div>
                    </div>
                  </div>
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Ready To Ship</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.ready_to_ship"
                        groov-click="send_data(true)"></div>
                    </div>
                  </div>
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Shipped</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.import_shipped" groov-click="send_data(true)"></div>
                    </div>
                  </div>
                </div>

                <div ng-show="stores.single.store_type == 'ShipStation API 2'">
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Shipped</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.shall_import_shipped" groov-click="send_data(true)"></div>
                    </div>
                  </div>
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Awaiting</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.shall_import_awaiting_shipment"
                        groov-click="send_data(true)"></div>
                    </div>
                  </div>
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">Pending</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.shall_import_pending_fulfillment" groov-click="send_data(true)"></div>
                    </div>
                  </div>
                  <div class="form-group padding-btm">
                    <p class="control-label col-md-4">GP Ready Tag Import</p>
                    <div class="controls col-md-8">
                      <div toggle-switch ng-model="stores.single.tag_import_option" groov-click="send_data(true)"></div>
                    </div>
                  </div>
                </div>

              </div>
            </div>
            <hr/>
            <div class="row">
              <div class="col-md-8">
                <p ng-show= "gp_order=='awaiting'">
                  This order is in GroovePacker and has the status "Awaiting". It is ready to scan. <span ng-if="['Shipstation API 2', 'ShippingEasy', 'Veeqo'].includes(stores.single.store_type) && ss_similar_order_found"><br><a href="#" ng-click="import_same_order_no(ss_similar_order_found, order_show, stores.single.id); cancel()">Import additional orders with the same Order Number</a></span>
                </p>
                <p ng-show= "gp_order=='onhold'">
                  This order is in GroovePacker but it's status is "Action Required". This is likely due to 1 or more items in the order having no barcode assigned. Please See <a href="https://groovepacker.freshdesk.com/support/solutions/articles/6000058066"  target="_blank">This Article</a>  for more info on how Order and Product statuses are used in GroovePacker. You can also <a href="/#/orders/onhold/1/{{order_id}}">click here</a> to open this order directly.<span ng-if="['Shipstation API 2', 'ShippingEasy', 'Veeqo'].includes(stores.single.store_type) && ss_similar_order_found"><br><a href="#" ng-click="import_same_order_no(ss_similar_order_found, order_show, stores.single.id); cancel()">Import additional orders with the same Order Number</a></span>
                </p>
                 <p ng-show= "gp_order=='scanned'">
                  This order is in GroovePacker and it's status is "Scanned" because it has already been completely scanned. You can manually change it's status back to "Awaiting" if needed.<span ng-if="['Shipstation API 2', 'ShippingEasy', 'Veeqo'].includes(stores.single.store_type) && ss_similar_order_found"><br><a href="#" ng-click="import_same_order_no(ss_similar_order_found, order_show, stores.single.id); cancel()">Import additional orders with the same Order Number</a></span>
                </p>
                <p ng-show= "gp_order=='cancelled'">
                 This order is in GroovePacker and it's status is "Cancelled". You can manually change it's status back to "Awaiting" if needed.<span ng-if="['Shipstation API 2', 'ShippingEasy', 'Veeqo'].includes(stores.single.store_type) && ss_similar_order_found"><br><a href="#" ng-click="import_same_order_no(ss_similar_order_found, order_show, stores.single.id); cancel()">Import additional orders with the same Order Number</a></span>
                </p>
                <p ng-show= "gp_order == undefined">{{ stores.single.import_shipped_having_tracking == false || stores.single.import_fulfilled_having_tracking == false ? 'Order ' + order_show + ' has been imported and is now available.' : 'This order may not be imported if the order does not have tracking number as your current settings prevent importing orders that do not have tracking numbers. To import this order you can process the label before importing it or you can temporarily disable the option to require tracking numbers in the settings for this import source.' }} {{ !['Shopify', 'Shopline', 'Shippo', 'Veeqo'].includes(stores.single.store_type)  ? 'If an issue occurred during a previous import there may be other missing orders. A QuickFix import can be run to locate and import other orders modified
                  around the same time.' : '' }}</p>
              </div>
              <div class="col-md-4 text-center" ng-hide="['Shopify', 'Shopline', 'Shippo', 'Veeqo'].includes(stores.single.store_type)">
                <img src="/assets/images/quick_fix.png" class="quickfix_img" />
                <div class="tooltip-box">
                  <button type="button" ng-click="run_quick_fix_import(store_id, 'quickfix')"  class="btn modal-save-button">Run a quickfix import</button>
                  <i class="icon-large glyphicon glyphicon-info-sign ng-scope" popover-trigger="mouseenter" groov-popover=" The timestamp on the order you entered will be used to request a range of orders likely to include any others that are missing."></i>
                </div>
              </div>
            </div>
          </div>
          <div class="bottom-section" ng-hide="['Shopify', 'Shopline', 'Veeqo'].includes(stores.single.store_type)">
            <hr/>
            <h3 class="text-center">Range Import</h3>
            <div class="row"  ng-hide= "show_search==true">
              <div class="col-md-6 col-md-offset-3">
                <p> Since order details have been provided the Range Import below gives the option to extendthe QuickFix import range. To import a range that is relative to the current date/time click the Clear Details button. </p>
              </div>
            </div>
            <div class="row"  ng-show= "show_search==true">
              <div class="col-md-6 col-md-offset-3">
                <p> The Range Import below will allow you to Import orders based on specific dates and times. Ranges with a large number of orders take longer to complete.  </p>
              </div>
            </div>
            <div class="row">
              <div class="col-md-5 date-selector">
                <div class="group-input">
                  <div class="input-group">
                    <input type="text" class="form-control" ng-blur="time_changed();" datepicker-popup="MM-dd-yyyy" ng-model="start_date.start.time" is-open="start_date.start.open" ng-required="true" close-text="Close"/>
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="open_picker($event,start_date.start)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                  </div>
                  <h6 class="text-center date-heading">Start</h6>
                </div>
                <div class="controls">
                  <timepicker ng-model="start_date.start.time" ng-change="time_changed();" show-meridian="true"></timepicker>
                </div>
              </div>
              <div class="col-md-5 date-selector">
                <div class="group-input">
                  <div class="input-group">
                    <input type="text" class="form-control" ng-blur="time_changed();" datepicker-popup="MM-dd-yyyy" ng-model="end_date.end.etime" is-open="end_date.end.open"
               ng-required="true" close-text="Close"/>
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="open_picker($event,end_date.end)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                  </div>
                  <h6 class="text-center date-heading">End</h6>
                </div>
                <div class="controls">
                  <timepicker ng-model="end_date.end.etime" ng-change="time_changed();" show-meridian="true"></timepicker>
                </div>
              </div>
              <div class="col-md-2 radio-selector">
                <div class="controls ">
                  <label class="radio inline">
                    <input type="radio"  ng-model="import_type" ng-change="change_import_type('modified')" value="modified">
                    Modified <br/>
                    ({{ stores.single.store_type == 'ShippingEasy' ? '(GMT-00:00) UTC' : '(GMT-08:00) PST'}})
                  </label>
                  <label class="radio inline">
                    <input type="radio" ng-model="import_type" ng-change="change_import_type('created')" value="created" class="ng-pristine ng-valid" >
                    Created
                    <i class="icon-large glyphicon glyphicon-info-sign ng-scope" popover-trigger="mouseenter" groov-popover="The range of orders can be based on the date they were originally created/placed  by the customer, or the date they were last modified in the order manager."></i><br/>
                    ({{gp_tz_name}})
                  </label>
                </div>
              </div>
            </div>
            <div class="row">
              <label  class="pull-left">Preset Ranges:</label>

                <div class="controls form-inline col-sm-4 col-md-3"   ng-hide="show_search== true" dropdown>
                  <button class="dropdown-toggle groove-button label label-default">
                    <span ng-show="import_range=='quickfix'" translate>QuickFix</span>
                    <span ng-show="import_range=='quickfix+2hr'" translate>QuickFix + 2hrs</span>
                    <span ng-show="import_range=='quickfix+6hr'" translate>QuickFix + 6hrs</span>
                    <span ng-show="import_range=='quickfix+12hr'" translate>QuickFix + 12hrs</span>
                    <span ng-show="import_range=='quickfix+24hr'" translate>QuickFix + 24hrs</span>
                  </button>
                  <ul class="dropdown-menu" role="menu">
                  <li><a class="dropdown-toggle" ng-click="set_import_range('quickfix')" translate>QuickFix</a>
                  </li>
                  <li><a class="dropdown-toggle" ng-click="set_import_range('quickfix+2hr')" translate>QuickFix + 2hrs</a>
                  </li>
                  <li><a class="dropdown-toggle" ng-click="set_import_range('quickfix+6hr')" translate>QuickFix + 6hrs</a>
                  </li>
                  <li><a class="dropdown-toggle" ng-click="set_import_range('quickfix+12hr')" translate>QuickFix + 12hrs</a>
                  </li>
                  <li><a class="dropdown-toggle" ng-click="set_import_range('quickfix+24hr')" translate>QuickFix + 24hrs</a>
                  </li>
                  </ul>
                </div>
                <div class="controls form-inline col-sm-4 col-md-3"   ng-show="show_search== true" dropdown>
                  <button class="dropdown-toggle groove-button label label-default">
                      <span ng-show="import_range=='last_2hr'" translate>Last 2hrs</span>
                      <span ng-show="import_range =='last_6hr'" translate>Last 6hrs</span>
                      <span ng-show="import_range=='last_12hr'" translate>Last 12hrs</span>
                      <span ng-show="import_range=='last_24hr'" translate>Last 24hrs</span>
                      <span ng-show="import_range=='last_2days'" translate>Last 2 Days</span>
                      <span ng-show="import_range=='last_3days'" translate>Last 3 Days</span>
                      <span ng-show="import_range=='last_4days'" translate>Last 4 Days</span>
                  </button>
                  <ul class="dropdown-menu" role="menu">
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_2hr')" translate>Last 2hrs</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_6hr')" translate>Last 6hrs</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_12hr')" translate>Last 12hrs</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_24hr')" translate>Last 24hrs</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_2days')" translate>Last 2 Days</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_3days')" translate>Last 3 Days</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="set_import_range('last_4days')" translate>Last 4 Days</a>
                      </li>
                  </ul>
                </div>

              <button type="button" class="btn modal-save-button"  ng-click="run_quick_fix_import(store_id, 'range_import')"style="margin-right: 20px;">Import this range</button>
              <button type="button" ng-click="clear_details()" class="btn modal-save-button">Clear Details</button>
            </div>
            </div>
            </div>
            <div class="row" style="padding-top: 200px;" ng-hide="stores.single.store_type == 'Shopify' || stores.single.store_type == 'Shopline' || stores.single.store_type == 'Shippo'">
              <div class="col-md-5 date-selector set_lro" ng-hide="set_lro==false">
                <div class="group-input">
                  <h6 class="text-center date-heading">Update LRO</h6>
                  <div class="input-group">
                    <input type="text" class="form-control" datepicker-popup="MM-dd-yyyy" ng-model="store_lro.date" is-open="store_lro.open" ng-required="true" close-text="Close"/>
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="open_picker($event, store_lro)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                  </div>
                </div>
                <div class="controls">
                  <timepicker ng-model="store_lro.date" show-meridian="true"></timepicker>
                </div>
                <button type="button" class="btn modal-save-button" ng-click="update_lro(store_lro.date)"style="margin-right: 20px;">Save</button>
                <button type="button" ng-click="update_lro(false)" class="btn modal-save-button">Cancel</button>
              </div>
              <div class="col-md-12">
                Current Import Date Range for the next regular import:
                <span ng-dblclick="update_lro(true)">{{last_imported_at | date:' MM-dd-yyyy hh:mm a '}} to {{current_time | date:' MM-dd-yyyy hh:mm a '}}</span>
                <span>
                  (Current Time
                  <span ng-if="stores.single.store_type == 'ShippingEasy'">{{ general_settings.single.abbreviated_time_zone }}</span>
                  <span ng-if="stores.single.store_type != 'ShippingEasy'">PST</span>
                  )
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
