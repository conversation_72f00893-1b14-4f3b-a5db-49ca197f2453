<script src="https://js.stripe.com/v3/"></script>
<div>
  <div class="modal-body text-center" ng-show="loading">
    <img src="/assets/images/loading.gif" alt="loading..." width="230px">
  </div>
  <div class="modal-body payment-popup" ng-hide="loading">
    <div class="plan-detail">
      <p>
        {{stripe_checkout_data.shop_name }}
      </p>
      <p class="plan-amount">
        ${{stripe_checkout_data.amount/100}} Startup
      </p>      
    </div>
    <div class="text-center" style="padding: 15px 20px">
      <form id="payment-form">
        <div class="stripe-card-element">
          <div id="card-element">
            <!--Stripe.js injects the Card Element-->
          </div>
          <button id="submit" class="pay-button">
            <div class="spinner hidden" id="spinner"></div>
            <span id="button-text">Pay now</span>
          </button>
        </div>
        <p id="card-error" role="alert"></p>
        <p class="result-message hidden">Details Submitted</p>
      </form>
    </div>
    <img class="bottom-img" src="/assets/images/Stripe.png" width= "80px"">
  </div>
</div>

<style type="text/css">
  input {
    border-radius: 6px;
    margin-bottom: 6px;
    padding: 12px;
    border: 1px solid rgba(50, 50, 93, 0.1);
    height: 44px;
    font-size: 16px;
    width: 100%;
    background: white;
  }
  .result-message {
    line-height: 22px;
    font-size: 16px;
  }
  .result-message a {
    color: rgb(89, 111, 214);
    font-weight: 600;
    text-decoration: none;
  }
  .hidden {
    display: none;
  }
  #card-error {
    color: rgb(105, 115, 134);

    text-align: left;

    font-size: 13px;

    line-height: 17px;

    margin-top: 12px;
  }

  #card-element {
    border-radius: 4px 4px 0 0;

    padding: 12px;

    border: 1px solid rgba(50, 50, 93, 0.1);

    height: 44px;

    width: 100%;

    background: white;
  }

  #payment-request-button {
    margin-bottom: 32px;
  }

  /* Buttons and links */

  button {
    background: #5469d4;

    color: #ffffff;

    font-family: Arial, sans-serif;

    border-radius: 0 0 4px 4px;

    border: 0;

    padding: 12px 16px;

    font-size: 16px;

    font-weight: 600;

    cursor: pointer;

    display: block;

    transition: all 0.2s ease;

    box-shadow: 0px 4px 5.5px 0px rgba(0, 0, 0, 0.07);

    width: 100%;
  }

  button:hover {
    filter: contrast(115%);
  }

  button:disabled {
    opacity: 0.5;

    cursor: default;
  }

  /* spinner/processing state, errors */

  .spinner,
  .spinner:before,
  .spinner:after {
    border-radius: 50%;
  }

  .spinner {
    color: #ffffff;

    font-size: 22px;

    text-indent: -99999px;

    margin: 0px auto;

    position: relative;

    width: 20px;

    height: 20px;

    box-shadow: inset 0 0 0 2px;

    -webkit-transform: translateZ(0);

    -ms-transform: translateZ(0);

    transform: translateZ(0);
  }

  .spinner:before,
  .spinner:after {
    position: absolute;

    content: "";
  }

  .spinner:before {
    width: 10.4px;

    height: 20.4px;

    background: #5469d4;

    border-radius: 20.4px 0 0 20.4px;

    top: -0.2px;

    left: -0.2px;

    -webkit-transform-origin: 10.4px 10.2px;

    transform-origin: 10.4px 10.2px;

    -webkit-animation: loading 2s infinite ease 1.5s;

    animation: loading 2s infinite ease 1.5s;
  }

  .spinner:after {
    width: 10.4px;

    height: 10.2px;

    background: #5469d4;

    border-radius: 0 10.2px 10.2px 0;

    top: -0.1px;

    left: 10.2px;

    -webkit-transform-origin: 0px 10.2px;

    transform-origin: 0px 10.2px;

    -webkit-animation: loading 2s infinite ease;

    animation: loading 2s infinite ease;
  }

</style>
