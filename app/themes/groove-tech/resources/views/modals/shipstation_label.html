<div>
  <div class="modal-header" id="ss_label_rates_model">
    <button type="button" class="close-btn" ng-click="cancel()">
      <i class="glyphicon glyphicon-remove"></i>
    </button>
    <div class="modal-title">Shipstation Shipping Label Options</div>
  </div>
  <div class="modal-body" tabindex="-1">
    <div class="container-fluid form-horizontal text-center" ng-show="rate_loading">
      <img src="/assets/images/loading.gif" alt="loading..." width="230px">
    </div>
    <div class="container-fluid form-horizontal" ng-show="!rate_loading">
      <div>
        <div class="text-center">
          <h2>Order Number {{ '#' + order_number + ' (' + order_id +')' }}</h2>
        </div>
        <br />

        <div class="row">
          <div class="col-md-3" style="padding: 0px 35px">
            <div class="row">
              <div class="form-group text-center">
                <label class="row">Ship Date*</label>
                <div groov-click="">
                  <p class="input-group" groov-click="">
                    <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy"
                      ng-model="ship_date.current.time" is-open="ship_date.current.open"
                      ng-required="true" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default" ng-click="open_picker($event,ship_date.current)">
                        <i class="glyphicon glyphicon-calendar"></i>
                      </button>
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="form-group text-center">
                <label class="row">Weight<button class="label_shortcut_btn" ng-dblclick="set_label_shortcut('weight', $event)" ng-class="(get_rate_shortcut('weight').length) ? '' : 'text-muted'">({{get_rate_shortcut('weight') || 'Double-Click to set'}})</button></label>

                <div class="row row_sep_option_ss_label">
                  <div class="col-md-6">
                    <input class="form-control input-style" placeholder="Weight" ng-model="weight_dimensions.weight.value" id="ss_label_weight"
                      ng-blur="change_value('weight', weight)" ng-keypress="($event.charCode==13) ? change_weight(weight) : return" type="number" min="0" />
                  </div>
                  <div class="controls col-md-6" dropdown>
                    <button class="dropdown-toggle groove-button ss_label_selection_button">
                      {{weight_dimensions.weight.units || 'Select Unit'}}
                      <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('weight_unit', 'pounds')">Pounds</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('weight_unit', 'ounces')">Ounces</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('weight_unit', 'grams')">Grams</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="form-group text-center">
                <label class="row">Dimension<button class="label_shortcut_btn" ng-dblclick="set_label_shortcut('dimension', $event)" ng-class="(get_rate_shortcut('dimension').length) ? '' : 'text-muted'">({{get_rate_shortcut('dimension') || 'Double-Click to set'}})</button></label>

                <div class="row row_sep_option_ss_label">
                  <div class="col-md-6">
                    <input class="form-control input-style" placeholder="Length" ng-model="weight_dimensions.dimensions.length" id="ss_label_length"
                      ng-blur="change_value('length', length)"  ng-keyup="$event.keyCode === 13 && [change_value('length', length), updateRealtimeRates()]" type="number" min="0" type="number" min="0" />
                  </div>
                  <div class="col-md-6">
                    <input class="form-control input-style" placeholder="Width" ng-model="weight_dimensions.dimensions.width"
                      ng-blur="change_value('width', width)"  ng-keyup="$event.keyCode === 13 && [change_value('width', width), updateRealtimeRates()]" type="number" min="0" />
                  </div>
                </div>
                <div class="row row_sep_option_ss_label">
                  <div class="col-md-6">
                    <input class="form-control input-style" placeholder="Height" ng-model="weight_dimensions.dimensions.height"
                      ng-blur="change_value('height', height)" ng-keyup="$event.keyCode === 13 && [change_value('height', height), updateRealtimeRates()]"  type="number" min="0" />
                  </div>
                  <div class="controls col-md-6" dropdown>
                    <button class="dropdown-toggle groove-button ss_label_selection_button">
                      {{weight_dimensions.dimensions.units || 'Select Unit'}}
                      <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('length_unit', 'inches')">Inches</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle"
                          ng-click="change_label_opt('length_unit', 'centimeters')">Centimeters</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="form-group text-center">
                <label class="row">Confirmation*</label>

                <div class="row row_sep_option_ss_label">
                  <div class="controls col-md-12" dropdown>
                    <button class="dropdown-toggle groove-button ss_label_selection_button">
                      {{confirmation || 'Select Confirmation Type'}}
                      <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                      <li>
                        <a class="dropdown-toggle"
                          ng-click="change_label_opt('confirmation', '_select_confirmation')">Select Confirmation</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('confirmation', 'none')">None</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('confirmation', 'delivery')">Delivery</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle"
                          ng-click="change_label_opt('confirmation', 'signature')">Signature</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle" ng-click="change_label_opt('confirmation', 'adult_signature')">Adult
                          Signature</a>
                      </li>
                      <li>
                        <a class="dropdown-toggle"
                          ng-click="change_label_opt('confirmation', 'direct_signature')">Direct Signature</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="form-group text-center" id="shipping_address_div">
                <label class="row" style="width: 100%;">Shipping Address</label>
                <div class="row row_sep_option_ss_label">
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="Name" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.name"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="Address Line 1" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.address1"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="Address Line 2" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.address2"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="City" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.city"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="State" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.state"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="Zip" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.postal_code"/>
                  </div>
                  <div>
                    <input class="form-control input-style" ng-blur="save_ss_label_data('')" placeholder="Country" ng-keyup="$event.keyCode === 13 && [save_ss_label_data('get_rate')]" ng-model="shipping_address.country"/>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-9" style="padding: 0px 35px">
            <div class="rate-button-top">
              <div class="text-center" style="font-weight: bold;" ng-show="update_rate==false">Rates</div>
              <div class="text-center"><button class="rate_label_btn"  ng-show="update_rate" ng-click="updateRealtimeRates()">Update Rates</button></div>
              <div class="text-center advance-button" style="font-weight: bold;" ><button class="label_shortcut_btn" ng-click="show_ss_label_advanced_popup()">Advanced</button></div>
            </div>
            <div ng-if="available_carriers.length" >
              <div class="panel panel-default rates_carrier_div" ng-repeat="_carrier in available_carriers track by $index">
                <div class="panel-heading rates_carrier_name" ng-dblclick="toggle_carrier_visibility(_carrier)">
                  {{_carrier.name}}
                  <i ng-show="_carrier.visible" class="fa fa-caret-up pull-right" aria-hidden="true" ng-click="toggle_carrier_visibility(_carrier)"></i>
                  <i ng-hide="_carrier.visible" class="fa fa-caret-down pull-right" aria-hidden="true" ng-click="toggle_carrier_visibility(_carrier)"></i>
                </div>
                <div class="panel-body" ng-if="_carrier.visible" style="max-width: 100%;">
                  <div class="alert alert-danger" ng-if="_carrier.errors" role="alert" ng-bind-html="_carrier.errors"></div>
                  <ul class="list-group" ng-if="_carrier.rates.length">
                    <div><button class="label_shortcut_btn" ng-if="!_carrier.edit_rates" ng-click="toggle_rate_visibility(_carrier, 'carrier')">Edit Visible Methods</button>
                    <div><button class="label_shortcut_btn" ng-if="_carrier.edit_rates" ng-click="save_rate_visibility(_carrier, 'carrier')">Save Changes</button>
                    </i></div>
                    <div ng-repeat="_rate in _carrier.rates track by $index " ng-if="_rate.visible || _carrier.edit_rates">

                      <div class="rate_button">
                        <div ng-if="_carrier.edit_rates" class="eye-icon">
                          <i ng-show="_rate.visible" class="fa fa-eye fa-lg pull-right" aria-hidden="true" ng-click="_rate.visible=false" item-left></i>
                          <i ng-hide="_rate.visible" class="fa fa-eye-slash fa-lg pull-right" aria-hidden="true" ng-click="_rate.visible=true"></i>
                        </div>
                        <button class="rate_btn" ng-class="(rate && rate==_rate) ? 'active_rate_btn' : ''"
                                ng-click="change_label_opt('rate', _rate)">
                          {{_rate.serviceName}}&nbsp;<span class="badge pull-right">$ {{_rate.cost}}</span>
                        </button>
                        <button class="label_shortcut_btn" ng-dblclick="set_label_shortcut(_rate, $event)" ng-class="(get_rate_shortcut(_rate.serviceName).length) ? '' : 'text-muted'">{{get_rate_shortcut(_rate.serviceName) || 'Double-Click to set'}}</button>
                          <button  class="rate_label_btn" ng-if="rate && rate==_rate" data-toggle="tooltip" title="Click this button to create a label with the currently selected options" ng-class="(get_rate_shortcut('Print Label').length) ? '' : 'text-muted'" style="float: right;" ng-dblclick="set_label_shortcut('Print Label', $event)" ng-click="ok()">
                            <i class="glyphicon glyphicon-print"></i> Print Label
                          </button><br>
                          <button class="label_shortcut_btn" ng-if="rate && rate==_rate" ng-dblclick="set_label_shortcut('Print Label', $event)" ng-class="(get_rate_shortcut('Print Label').length) != 1 ? 'print-label-shortcut text-muted' : 'print-label-shortcut1'">{{get_rate_shortcut('Print Label') || 'Double-Click to set'}}</button>
                      </div>
                    </div>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-md-3" style="padding: 0px 35px">
            <div class="form-group text-center">
              <label class="">Carrier*</label>
              <ul class="list-group" ng-if="available_carriers.length">
                <button class="btn btn-block" ng-repeat="_carrier in available_carriers track by $index"
                  ng-click="change_label_opt('carrier', _carrier)"
                  ng-class="(_carrier==carrier) ? 'btn-primary' : 'btn-default'">
                  {{_carrier.name}}
                </button>
              </ul>
            </div>
          </div>

          <div class="col-md-6" style="padding: 0px 35px">
            <div class="form-group text-center">
              <div class="col-md-6">
                <label class="">Service*</label>
              </div>
              <div class="col-md-6">
                <label class="">Package*</label>
              </div>
              <ul class="list-group" ng-if="available_services.length">
                <div ng-repeat="_service in available_services track by $index">
                  <div class="row">
                    <div class="col-md-6">
                      <button class="btn btn-block"
                        ng-click="change_label_opt('service', _service)"
                        ng-class="(_service==service) ? 'btn-primary' : 'btn-default'">
                        {{_service.name}}
                      </button>
                    </div>
                    <div class="col-md-6">
                      <ul class="list-group" ng-if="_service.packages.length">
                        <button class="btn btn-block" ng-repeat="_package in _service.packages track by $index"
                          ng-click="change_label_opt('package', _package)"
                          ng-class="(service != null && _package==package && _package.serviceName == service.name) ? 'btn-primary' : 'btn-default'">
                          {{_package.name}}&nbsp;<span class="badge pull-right">$ {{_package.rate}}</span>
                        </button>
                      </ul>
                    </div>
                  </div>
                </div>
              </ul>
            </div>
          </div>
 -->
          <!-- <div class="col-md-3" style="padding: 0px 35px">
            <div class="form-group text-center">
              <label class="">Package*</label>
              <ul class="list-group" ng-if="service && service.packages.length">
                <button class="btn btn-block" ng-repeat="_package in service.packages track by $index"
                  ng-click="change_label_opt('package', _package)"
                  ng-class="(_package==package) ? 'btn-primary' : 'btn-default'">
                  {{_package.name}}&nbsp;<span class="badge pull-right">$ {{_package.rate}}</span>
                </button>
              </ul>
            </div>
          </div>
        </div> -->

        <!-- <button type="button" class="btn modal-save-button pull-right" ng-click="ok()" style="margin-right: 20px">
          Print Label
        </button> -->
      </div>
    </div>
  </div>
</div>
<div ui-view></div>
