<div id="notification" groov-persist-notification style="margin-left: -2.8%;"></div>
<div class="container-fluid min-header-width">
  <div class="row">
    <div class="box-outer top-nav-bar">
      <div class="box"></div>
      <div class="col-lg-10 col-md-11 col-sm-10">
        <ul class="nav remove_popup_margin">
          <li class="navbar-brand nav-li col-lg-5 col-md-4">
            <a href="#/home"><img src="/assets/images/logo.png" alt="GroovePacker"/></a>
          </li>
          <li ng-show="current_user.can('access_orders')" class="nav-li">
            <a ng-class="{active:is_active_tab('orders')}" ui-sref="orders">
              Orders<br/>
              <img class="icons" src="/assets/images/document.png">
            </a>
          </li>
          <li ng-show="current_user.can('access_products')" class="nav-li">
            <a ng-class="{active:is_active_tab('products')}" ui-sref="products.type.filter.page({type: 'product', filter: 'active', page: '1'})" ng-click="products.setup.search=''" ui-sref-opts="{reload: true}">
              Products<br/>
              <img class="icons" src="/assets/images/db.png">
            </a>
          </li>
          <li ng-show="current_user.can('access_settings')" class="nav-li">
            <a ng-class="{active:is_active_tab('settings')}" href="#/settings">
              Settings<br/>
              <img class="icons" src="/assets/images/settings.png">
            </a>
          </li>
          <li ng-show="current_user.can('access_scanpack')" class="nav-li">
            <a ng-class="{active:is_active_tab('scanpack')}" ui-sref="scanpack.rfo">
              Scan and Pack<br/>
              <img class="icons" src="/assets/images/box.png">
            </a>
          </li>
          <!--<li class="nav-li" groov-popover="{{import_groov_popover.content}}" popover-title="{{import_groov_popover.title}}" popover-placement="bottom">
            <a href="" ng-click='import_all_orders()'><i ng-show="import_summary.import_info.status =='in_progress'" class="glyphicon glyphicon-refresh groov-refresh-spin"></i>
              Import Orders<br/>
              <img class="icons" src="/assets/images/cloud.png">
            </a>
          </li>-->

          <!-- <li class="nav-li" ng-mouseenter="update_popup_display_setting(true);" ng-mouseleave="update_popup_display_setting(false);" groov-popover="{{import_groov_popover.content}}" popover-title="{{import_groov_popover.title}}" popover-placement="bottom"> -->
          <li class="nav-li" id="import_tab" ng-if="current_user.get()['role']['import_orders']">
            <a href="" ng-click='import_all_orders($event)' ng-show="import_summary.import_info.status !='in_progress'">
              Import Orders<br/>
              <img class="icons" src="/assets/images/cloud.png" groov-popover="{{import_groov_popover.content}}" popover-title="{{import_groov_popover.title}}" popover-placement="bottom" popover-popup-delay="0" popover-popup-hide="10" popover-popup-trigger="#import_tab">
            </a>
            <a href="" ng-click='cancel_all_imports($event)' ng-show="import_summary.import_info.status =='in_progress'">
              <i class="glyphicon glyphicon-refresh groov-refresh-spin" groov-popover="{{import_groov_popover.content}}" popover-title="{{import_groov_popover.title}}" popover-placement="bottom" popover-popup-delay="0" popover-popup-hide="10" popover-popup-trigger="#import_tab"></i>
              Check Status<br/>
              <img class="icons" src="/assets/images/cloud.png">
            </a>
          </li>
        </ul>
      </div>
      <div class="col-lg-2 col-md-1 col-sm-2" dropdown>
        <button type="button" class="nav-button dropdown-toggle pull-right"><u>{{current_user.get().username}}</u>
          <span><img class="arrow-icon" src="/assets/images/down.png"></span>
        </button>
        <ul class="dropdown-menu drop-width" role="menu">
          <li><a ng-click='sign_out()'>Sign Out</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div groov-notification></div>
