<!-- Modal for warehouse details -->
<div ng-keydown="handle_keydown($event)" id="showWarehouse{{custom_identifier}}" class="modal hide fade" tabindex="-1"
     role="dialog" aria-labelledby="showWarehouse{{custom_identifier}}"
     aria-hidden="true">

  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icon-remove"></i></button>
    <h3>Warehouse Id: {{warehouses.single.id}}</h3>
  </div>
  <div class="form-horizontal form-horizontal-create">
    <fieldset>
      <div class="modal-body">

        <h5>Warehouse Details</h5>

        <div class="control-group">
          <label class="control-label">Name</label>

          <div class="controls">
            <input type="text" ng-model="warehouses.single.name" ng-blur="update_single_warehouse()" class="span8"/>
          </div>
        </div>

        <div class="control-group">
          <label class="control-label">Location</label>

          <div class="controls">
            <input type="text" ng-model="warehouses.single.location" ng-blur="update_single_warehouse()" class="span8"/>
          </div>
        </div>

        <div class="control-group">
          <label class="control-label">Status</label>

          <div class="controls">
            <h5>Status </h5>
            <label class="radio"><input type="radio" ng-change="update_single_warehouse()"
                                        ng-model="warehouses.single.status" value="active"> Active </label>
            <label class="radio"><input type="radio" ng-change="update_single_warehouse()"
                                        ng-model="warehouses.single.status" value="inactive"> Inactive </label>
          </div>
        </div>


      </div>
    </fieldset>
    <div class="modal-footer">
      <div class="row-fluid">
        <div class="span12">
          <div class="pull-right">
            <button data-dismiss="modal" aria-hidden="true" class="btn btn-primary">Save & close</button>
            <button class="btn" data-dismiss="modal" ng-click="rollback()" aria-hidden="true">Cancel</button>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
