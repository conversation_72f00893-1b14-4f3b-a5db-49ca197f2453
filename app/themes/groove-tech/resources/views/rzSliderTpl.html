<div class="rzslider">
  <!-- // 0 (left) Highlight outside two handles -->
  <span class="rz-bar-wrapper rz-left-out-selection">
    <span class=rz-bar></span>
  </span>
  <!-- // 1 (right) Highlight outside two handles -->
  <span class="rz-bar-wrapper rz-right-out-selection">
    <span class=rz-bar></span>
  </span>
  <!-- // 2 The slider bar -->
  <span class="rz-bar-wrapper">
    <span class="rz-bar"></span>
  </span>
  <!-- // 3 Highlight between two handles -->
  <span class="rz-bar-wrapper">
    <span class="rz-bar rz-selection" ng-style="barStyle"></span>
  </span>
  <!-- // 4 Left slider handle -->
  <span class="rz-pointer rz-pointer-min" ng-style=minPointerStyle></span>
  <!-- // 5 Right slider handle -->
  <span class="rz-pointer rz-pointer-max" ng-style=maxPointerStyle></span>
  <!-- // 6 Floor label -->
  <span class="rz-bubble rz-limit rz-floor"></span>
  <!-- // 7 Ceiling label -->
  <span class="rz-bubble rz-limit rz-ceil"></span>
  <!-- // 8 Label above left slider handle -->
  <span class="rz-bubble rz-model-value"></span>
  <!-- // 9 Label above right slider handle -->
  <span class="rz-bubble rz-model-high"></span>
  <!-- // 10 Range label when the slider handles are close ex. 15 - 17 -->
  <span class="rz-bubble"></span>
  <!-- // 11 The ticks -->
  <ul ng-show="showTicks" class="rz-ticks">
    <li ng-repeat="t in ticks track by $index" class="rz-tick" ng-class="{'rz-selected': t.selected}" ng-style="t.style" ng-attr-uib-tooltip="{{ t.tooltip }}"
      ng-attr-tooltip-placement="{{t.tooltipPlacement}}" ng-attr-tooltip-append-to-body="{{ t.tooltip ? true : undefined}}">
      <span ng-if="t.value != null" class="rz-tick-value" ng-attr-uib-tooltip="{{ t.valueTooltip }}" ng-attr-tooltip-placement="{{t.valueTooltipPlacement}}">{{ t.value }}</span>
      <span ng-if="t.legend != null" class="rz-tick-legend">{{ t.legend }}</span>
    </li>
  </ul>
</div>