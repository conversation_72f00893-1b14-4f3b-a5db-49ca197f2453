<div class="container-fluid">
  <div class="row bottom-well">
    <div class="col-lg-2 col-md-2 accordion-parent" style="width: 147px !important; margin: 0 auto;">
      <div class="row">
        <accordion class="panel-body-new">
          <accordion-group is-open="firstOpen">
            <accordion-heading>
              <i></i>
              <a ui-sref="orders.filter.page({filter:'awaiting',page:1})" ng-click="orders.setup.search=''">Orders</a>
            </accordion-heading>
            <li ng-click="create_order()">Create</li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='all'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'all',page:1})"
                ui-sref-opts="{reload: true}">Show All
            </li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='awaiting'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'awaiting',page:1})"
                ui-sref-opts="{reload: true}">Awaiting
            </li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='partially_scanned'}"
              ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'partially_scanned',page:1})"
              ui-sref-opts="{reload: true}">Partially Scanned
            </li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='onhold'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'onhold',page:1})"
                ui-sref-opts="{reload: true}">Action Required
              &nbsp;&nbsp;<i class="icon-warning glyphicon glyphicon-warning-sign"
                             ng-show="orders.orders_count.onhold > 0" tooltip="{{orders.orders_count.onhold}}"></i></li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='serviceissue'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'serviceissue',page:1})"
                ui-sref-opts="{reload: true}">Service
              Issue
            </li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='cancelled'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'cancelled',page:1})"
                ui-sref-opts="{reload: true}">Cancelled
            </li>
            <li ng-class="{active:orders.setup.search=='' && orders.setup.filter=='scanned'}"
                ng-click="orders.setup.search=''" ui-sref="orders.filter.page({filter:'scanned',page:1})"
                ui-sref-opts="{reload: true}">Scanned
            </li>
          </accordion-group>
        </accordion>
      </div>
    </div>
    <div class="col-lg-10 col-md-10">
      <div class="row">
        <div class="col-lg-6 col-md-6">
          <div class="col-lg-offset-3 col-md-offset-3 col-lg-7 col-md-7 pull-left">
            <input type="text" class="form-control search-box" autofocus="autofocus" id="order-search-query"
                   ng-model="orders.setup.search" placeholder="{{ general_settings.single.search_by_product ? 'Order item search' : 'Search'}}"
                   ng-if="orders.orders_count.all < 10000">

            <input type="text" class="form-control search-box" autofocus="autofocus" id="order-search-query"
                   ng-model="orders.setup.search_value" placeholder="{{ general_settings.single.search_by_product ? 'Order item search' : 'Search'}}"
                   ng-if="orders.orders_count.all > 10000"
                   ng-keypress="orders.setup.search = $event.keyCode == 13 ? orders.setup.search_value : orders.setup.search">
          </div>
          <div class="col-lg-1 col-md-1 no-padding" ng-if="orders.orders_count.all > 10000">
            <i class="fa fa-lg fa-arrow-right" ng-click="orders.setup.search = orders.setup.search_value" style="cursor:pointer;" aria-hidden="true" ng-style="{ 'color' : orders.setup.search_value  ? '#335223' : '#BDBDBD' }"></i>
          </div>

          <div class="col-lg-1 col-md-1 no-padding" groov-click="search_by_product()" groov-popover='When enabled, search by a product to find open orders which contain it.' popover-trigger="mouseenter" popover-placement="bottom" style="position: static;">
            <i class="fa fa-lg fa-search search-responsive" style="cursor:pointer;" aria-hidden="true" ng-style="{ 'color' : general_settings.single.search_by_product ? '#335223' : '#BDBDBD' }" ></i>
          </div>
        </div>
        <div class="col-lg-2 col-md-2 set_dropdwoan_width" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Printing <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a class="dropdown-toggle" ng-if="general_settings.single.ss_api_create_label" ng-click="print_shipping_label()">Print Shipping
              Label <span class="badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="generate_orders_pick_list_and_packing_slip()">Print Pick List and
              Packing Slips <span class="badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="generate_orders_pick_list()">Print Only Pick list <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="generate_orders_packing_slip()">Print Only Packing Slips <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="generate_product_barcode_slip()">Print Product Barcode Label <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
          </ul>
        </div>
        <div class="col-lg-1 col-md-1 set_dropdwoan_width" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Edit <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li ng-if="general_settings.single.export_items != 'disabled'"><a class="dropdown-toggle"
                                                                              ng-click="generate_orders_items_list()">Export
              Items <span class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="order_delete()">Delete <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="order_duplicate()">Duplicate <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a class="dropdown-toggle" ng-click="clear_assigned_tote()" ng-if="general_settings.single.scan_pack_workflow == 'product_first_scan_to_put_wall' || general_settings.single.scan_pack_workflow == 'multi_put_wall'">Clear Assigned Tote<span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
          </ul>
        </div>
        <div class="col-lg-2 col-md-2 set_dropdwoan_width" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Change Status <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a ng-click="order_change_status('awaiting')">Awaiting <span class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a ng-click="order_change_status('serviceissue')">Service Issue <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li ng-if="allow_status_changes.cancelled"><a ng-click="order_change_status('cancelled')">Cancelled <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li ng-if="allow_status_changes.scanned"><a ng-click="order_change_status('scanned')">Scanned <span
              class="pull-right badge badge-important">{{orders.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
          </ul>
        </div>

      </div>
      <div class="row">
        <div class="col-xs-12" ng-if="showgrid">
          <div groov-data-grid="gridOptions" groov-list="orders.list"></div>
        </div>
      </div>
      <div ui-view></div>
    </div>
  </div>
</div>
<style type="text/css">
  .popover {
    max-width: 260px;
  }
 /* .accordion-parent {
    width: 16.66666667% !important;
  }*/
</style>
