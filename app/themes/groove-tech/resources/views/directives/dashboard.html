<div class="persist-notifications-background persist-notifications-background-bottom"
     ng-click="toggle_dashboard_detail()"></div>
<div class="persist-notifications persist-notifications-bottom">
  <div class="details text-center">
    <h3>Dashboard</h3>

    <div class="tabbable col-md-offset-1 col-md-10 col-lg-offset-1 col-lg-10">
      <tabset class="nav nav-tabs dashboard-nav">
        <tab ng-repeat="tab in dash_tabs" heading="{{tab.heading}}" active="tab.active" ng-click="switch_tab(tab)">
          <div groov-include="{{tab.templateUrl}}"></div>
        </tab>
      </tabset>
    </div>
  </div>


  <div class="under-bar" ng-click="($root.current_user_data.view_dashboard == 'none' || $root.current_user_data.view_dashboard == 'packer_dashboard' ) ? '' : toggle_dashboard_detail()" ng-class="{glow:bar_glow}"><i class="glyphicon"  ng-if="$root.current_user_data.view_dashboard == 'admin_dashboard' || $root.current_user_data.username == 'gpadmin' || $root.current_user_data.view_dashboard == 'admin_dashboard_with_packer_stats' || $root.current_user_data.view_dashboard == 'packer_dashboard' "  ng-class="{'glyphicon-chevron-up':!dashbord_detail_open,'glyphicon-top glyphicon-chevron-down':dashbord_detail_open}"></i>
    <div class="row pull-bar-up">
      <div class="col-md-12">
        <div style="margin: auto;text-align: center;">
        <div ng-if="$root.current_user_data.view_dashboard == 'admin_dashboard' || $root.current_user_data.username == 'gpadmin' || $root.current_user_data.view_dashboard == 'admin_dashboard_with_packer_stats' " style="display:inline-block;">
          
            <div class="stat-pill">Accuracy: {{dashboard.main_summary.packing_accuracy_summary.current_period}}%</div>
            <div class="stat-pill">Speed: {{dashboard.packing_speed_summary.current_period}}%</div>
            <div class="stat-pill">Packed: {{dashboard.manager_data.packed}}</div> 
          </div> 
          <div ng-if="$root.current_user_data.view_dashboard == 'packer_dashboard' || $root.current_user_data.username == 'gpadmin'|| $root.current_user_data.view_dashboard == 'admin_dashboard_with_packer_stats'" style="display:inline-block;">
          
            <div class="stat-pill">You: {{dashboard.manager_data.cu_order}} Orders / {{dashboard.manager_data.cu_order_item}} Items</div>
         
            <div class="stat-pill">Current Leader: {{dashboard.manager_data.leader_order}} Orders / {{dashboard.manager_data.leader_order_item}} Items</div>
          </div>
        </div>  
      </div>
    </div>  
    </div>
  </div>
</div>
