<div class="notifications">
  <alert ng-show="notif.show" class="slide" ng-repeat="notif in notifs" type="{{notif.alert}}" close="notif.show=false">
  	<div style="display: flex;"> 
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    	<span ng-bind-html="notif.msg" style="width: 850px;"></span> &nbsp;
    	<button ng-if="notif.msg.includes('re-import this file please')" ng-click="reimport_from_scratch()" type="button" style="margin-top: 23px; border-radius: 6px; float: right;"> click-here</button>
    </div>
    <button ng-if="notif.msg.length > 400" type="button" ng-click="close(notif)" style="border-radius: 6px; float: right;">OK</button>
  </alert>
</div>
