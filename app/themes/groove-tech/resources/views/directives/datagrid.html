<div bindonce="options" class="binder row">
  <div bo-if="options.select_all !== false" class="select_all_box col-md-2 col-lg-2" dropdown
       ng-mouseenter="show_dropdown()" ng-mouseleave="start_dropdown_timer()" is-open="dropdown.show" style="padding-left: 30px;">
    <button ng-click="options.select_all(!options.setup.select_all)" type="button" class="btn groove-button"
            ng-class="{active:options.setup.select_all, inverted: options.setup.inverted}">
      <span ng-hide="options.setup.select_all" translate>common.select_all</span>
      <span ng-show="options.setup.select_all" translate>common.select_none</span>
      <br/>
      <span ng-show="options.paginate.show">{{options.paginate.total_items}}</span>
      <span ng-hide="options.paginate.show">{{rows.length}}</span>
    </button>
    <ul class="dropdown-menu" role="menu" ng-mouseleave="dropdown.show = false" ng-mouseenter="show_dropdown()">
      <li><a ng-click="options.select_all(false)" class="dropdown-toggle">Deselect all <span
        class="pull-right badge badge-important">{{options.selections.selected_count}}</span></a></li>
      <li><a ng-click="options.selections.show()" class="dropdown-toggle">Show Selected <span
        class="badge badge-important">{{options.selections.selected_count}}</span></a></li>
    </ul>
  </div>
  <div class="col-md-12 text-center" ng-show="options.paginate.show && options.paginate.total_items > options.paginate.items_per_page">
    <pagination total-items="options.paginate.total_items" ng-model="options.paginate.current_page"
                max-size="options.paginate.max_size" items-per-page="options.paginate.items_per_page"
                class="pagination-sm" rotate="false"></pagination>
  </div>
  <div class="container-fluid clear-both">

    <i ng-if='options.features' class="pull-right icon-large glyphicon glyphicon-info-sign ng-scope" popover-trigger="mouseenter" popover-placement='left' groov-popover="{{options.features}}" style="font-size"></i>
    <div class="table-parent col-xs-12 col-md-12 col-lg-12 col-sm-12">
      <table groov-dragtable="dragOptions" groov-enabled="options.draggable" groov-model="theads"
             id="{{custom_identifier}}" class="well table-well table table-striped table-hover table-alt">
        <thead groov-rt-click="context_menu_event($event)" hm-press="context_menu_event($event)">
        <tr>
          <th ng-repeat="field in theads" class="{{options.all_fields[field].class}}"
              groov-draggable="{{options.all_fields[field].draggable}}" ng-class="{active: options.setup.sort == field}">
            <div bo-if="options.all_fields[field].sortable"
                 ng-click="options.sort_func(field); $event.preventDefault(); $event.stopPropagation();"
                 bo-text="options.all_fields[field].name" ng-class="options.setup.order"></div>
            <div bo-if="!options.all_fields[field].sortable" bo-text="options.all_fields[field].name"></div>
            <span class="holder" ng-show="!$last"></span>
            <!-- <i ng-if="options.setup.sort == field" tooltip="Click for default Sort Order: Last modified Date/Time" tooltip-placement='right' class='fa fa-refresh middle' ng-click='options.sort_func(" ")'>
            </i> -->
          </th>
        </tr>
        </thead>
        <tbody>
        <tr ng-click="check_uncheck(row,$index,$event)" ng-repeat="row in rows"
            ng-class="{selected: row.checked, inverted:options.setup.inverted}">
          <td ng-repeat="field in theads"
            ng-mouseover="row.show_action_icon ? (row.show_action_icon[field]=true) : (row.show_action_icon={})"
            ng-mouseleave="row.show_action_icon ? (row.show_action_icon[field]=false) : (show_action_icon={})"
            style="padding: 7px;">
            <div style="display:inline-block;"
              bo-if="options.editable != false && options.all_fields[field].editable == true"
              hm-press="compile($parent.$parent.$index,field)" groov-rt-click="compile($parent.$parent.$index,field)">
              <div ng-class='{"grid-editable-field": !editable[field][$parent.$parent.$index], "grid-editing-mode": editable[field][$parent.$parent.$index]}'
                ng-hide="editable[field][$parent.$parent.$index]"
                ng-style="{'min-width' : (row[field].toString().length > (options.all_fields[field].col_length || options.col_length) ? ((options.all_fields[field].col_length || options.col_length) + 1) : (row[field].toString().length || 5) + 1) + 'rem'}">
                <span class='fa fa-pencil datagrid-pencil pull-right'
                    ng-show="row.show_action_icon[field] && !editable[field][$parent.$parent.$index]" groov-click="compile($parent.$parent.$index,field)"></span>
                <span bo-if="options.all_fields[field].transclude == '' && row[field] !== null && row[field].toString().length > 25"
                      bo-text="row[field] | cut:false:(25*options.no_of_lines)"
                      tooltip='{{row[field]}}'
                      tooltip-placement='{{set_position(row, rows)}}'
                      class="grid-text">
                </span>


                <span bo-if="options.all_fields[field].transclude == '' && row[field] !== null && row[field].toString().length <= 25"
                      bo-text="row[field].toString() | cut:false:(25*options.no_of_lines)" class="grid-text">
                </span>


                <span bo-if="options.all_fields[field].transclude == '' && (row[field] == null || !row[field].toString().length)"
                    tooltip="{{'Edit ' + options.all_fields[field].name}}" tooltip-placement='{{set_position(row, rows)}}' class="grid-text">
                  &nbsp;{{row[field]}}</span>
                <span bo-if="options.all_fields[field].transclude != ''"
                      ng-bind-html="options.all_fields[field].transclude" class="grid-text" compile-template>&nbsp;</span>
              </div>
              <div ng-bind-html="editable[field][$parent.$parent.$index]" compile-template></div>
            </div>
            <div style="display:inline-block;"
              bo-if="options.editable == false || options.all_fields[field].editable == false"
              hm-press="compile($parent.$parent.$index,field)" groov-rt-click="compile($parent.$parent.$index,field)"
              >
              <div ng-class='{"grid-copyable-field": options.all_fields[field].copyable, "grid-simple-text": !options.all_fields[field].copyable}'
                ng-style="{'min-width' : (row[field].toString().length > (options.all_fields[field].col_length || options.col_length) ? ((options.all_fields[field].col_length || options.col_length)) : (row[field].toString().length || 5)) + 'rem'}">
                <span class='fa fa-copy datagrid-copy pull-right' id='copy-text'
                      ng-show="row.show_action_icon[field] && options.all_fields[field].copyable"
                      ng-click="copied(row[field])"></span>
                <span bo-if="options.all_fields[field].transclude != '' "
                      ng-bind-html="options.all_fields[field].transclude" compile-template></span>
                <span bo-if="options.all_fields[field].transclude == '' && row[field] !== null && row[field].toString().length > 25"
                      bo-text="row[field].toString() | cut:false:(25)" tooltip="{{row[field]}}" tooltip-placement='{{set_position(row, rows)}}' class="grid-text">
                </span>
                <span bo-if="options.all_fields[field].transclude == '' && row[field] !== null && row[field].toString().length <= 25"
                      bo-text="row[field].toString() | cut:false:(25)" class="grid-text">
                </span>
                <span bo-if="options.all_fields[field].transclude == '' && (row[field] == null || !row[field].toString().length)"
                  class="grid-text">
                  &nbsp;{{row[field] | cut:false:(25)}}</span>
              </div>
            </div>
          </td>

        </tr>
        </tbody>
      </table>
    </div>
    <textarea id="copy" style="opacity: 0;"></textarea>
    <div class="col-md-12 text-center" ng-show="options.paginate.show && options.paginate.total_items > options.paginate.items_per_page">
      <pagination total-items="options.paginate.total_items" ng-model="options.paginate.current_page"
                  max-size="options.paginate.max_size" items-per-page="options.paginate.items_per_page"
                  class="pagination-sm" rotate="false"></pagination>
    </div>
    <div bo-if="options.show_hide">
      <div class="clearfix" ng-click="context_menu_event()" groov-rt-click="context_menu_event()" dropdown
           is-open="context_menu.shown">
        <ul class="dropdown-menu" ng-style="context_menu.style" role="menu">
          <li ng-repeat="field in options.all_fields" ng-show="field.hideable">

            <a href="" ng-click="show_hide(field); $event.stopPropagation();"
               groov-rt-click="show_hide(field); $event.stopPropagation();"> <i class="glyphicon pull-left"
                                                                                ng-class="{'glyphicon-ok':!field.hidden, 'glyphicon-remove':field.hidden}"></i>
              <span bo-text="field.name"></span></a>
          </li>
        </ul>
      </div>
    </div>
  </div>

</div>
