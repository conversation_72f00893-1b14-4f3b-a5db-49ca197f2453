<div class="col-md-offset-1 col-md-10 col-lg-offset-1 col-lg-10">
  <div class="row">
    <progressbar ng-show="dashboard.percentage!=null" type="{{dashboard.type}}"  style="z-index:1000;  width:65%; top:35px;" value="dashboard.percentage"> {{dashboard.stat_status ? (dashboard.percentage | number:2) + "% processing" : "some error occurred. Please try again."}} {{dashboard.stat_message}}</progressbar>
    <a ng-click="generate_stat()" class="pull-right" style="margin-top: 10px; margin-bottom:10px; margin-left: 10px; color: black; cursor:pointer;" popover-trigger="mouseenter" groov-popover='Click here to export CSV file containing the stats for the current period. It will be emailed to {{dashboard.email}} when the export completes. The address can be changed  <a href="#/settings/export/stats_export"> here.</a>' popover-placement="bottom"><i class="fa fa-envelope-o fa-2x" aria-hidden="true"/></a>
    <button ng-click="get_stat($event)" ng-disabled='dashboard.percentage != null' class="pull-right groove-button label label-default label-danger" style="margin-top: 10px; margin-bottom:10px; margin-right: 0px; margin-left: 10px; background-color: green;">Update Stats</button>
    <div class="pull-right" style="margin-top: 10px; margin-bottom:10px; margin-right: 0px;" dropdown>
      <button class="dropdown-toggle groove-button">
        <span ng-repeat="filter in charts.days_filters"
              ng-show="charts.current_filter_idx==$index">{{filter.name}}</span>
        <span class="caret"></span>
      </button>
      <ul class="dropdown-menu" role="menu">
        <li ng-repeat="filter in charts.days_filters"><a class="dropdown-toggle"
                                                         ng-click="charts.change_days_filter($index)">
          <i class="fa fa-check" style="margin-left:-18px;"
             ng-show="charts.days_filters[charts.current_filter_idx].name==filter.name"/>
          {{filter.name}}</a></li>
      </ul>
    </div>
  </div>
  <div class="row">
    <div class="col-md-4 col-lg-4 tab-col" ng-click="charts.set_type('packing_stats')">        
      <div class="tab-pill tab-red tab-pill-active" ng-class="{'tab-pill-active': charts.type=='packing_error' || charts.type=='packing_stats'}">
        <div class="tab-col-row">
          <div class="col-md-6 col-lg-6">
            <span class="fa fa-bullseye fa-dash"></span>

            <h3 ng-hide="charts.type=='packing_error'">PACKING ACCURACY</h3>
            <h3 ng-show="charts.type=='packing_error'">PACKING ERROR</h3>
          </div>
          <div class="col-md-4 col-lg-4 pill-col">
            <div class="row">
              <!-- <div class="col-md-6 col-lg-6"></div> -->
              <div class="button-pill col-md-offset-6 col-lg-offset-6 col-md-1 col-lg-1 button-pill-active" ng-class="{'button-pill-active': charts.type!='packing_error'}" ng-click="charts.alter_type('packing_stats')"></div>
              <div class="col-md-1 col-lg-1 button-pill" ng-class="{'button-pill-active': charts.type=='packing_error'}" ng-click="charts.alter_type('packing_error')"></div>
            </div>
            <div class="well">
              <h4 ng-hide="charts.type=='packing_error'">{{dashboard.main_summary.packing_accuracy_summary.current_period}}%</h4>
              <h4 ng-show="charts.type=='packing_error'">{{dashboard.main_summary.packing_error_summary.current_period}}%</h4>
              <span class="fa fa-calendar fa-pill-col"></span>
              <span>Current Period</span>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.main_summary.packing_accuracy_summary.delta == 0,
          'pill-col-last-tall':dashboard.main_summary.packing_accuracy_summary.delta > 0,
          'pill-col-last-short':dashboard.main_summary.packing_accuracy_summary.delta < 0}" ng-hide="charts.type=='packing_error'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_accuracy_summary.delta > 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_accuracy_summary.delta < 0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_accuracy_summary.delta == '-' || dashboard.main_summary.packing_accuracy_summary.delta == 0"></span>

              <p>{{dashboard.main_summary.packing_accuracy_summary.delta}}% From Prev
                {{charts.days_filters[charts.current_filter_idx].name}}</p>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.main_summary.packing_error_summary.delta == 0,
          'pill-col-last-tall':dashboard.main_summary.packing_error_summary.delta > 0,
          'pill-col-last-short':dashboard.main_summary.packing_error_summary.delta < 0}" ng-show="charts.type=='packing_error'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_error_summary.delta > 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_error_summary.delta < 0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-red"
                    ng-show="dashboard.main_summary.packing_error_summary.delta == '-' || dashboard.main_summary.packing_error_summary.delta == 0"></span>

              <p>{{dashboard.main_summary.packing_error_summary.delta}}% From Prev
                {{charts.days_filters[charts.current_filter_idx].name}}</p>
            </div>
          </div>
        </div>
        <div class="tab-col-row">
          <div class="col-md-12 col-lg-12 tab-white tab-pill-active" ng-class="{'tab-pill-active': charts.type=='packing_stats' || charts.type=='packing_error'}">
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 col-lg-4 tab-col" ng-click="charts.set_type('packing_speed_stats')">
      <div class=" tab-pill tab-green" ng-class="{'tab-pill-active': charts.type=='packing_speed_stats' || charts.type=='packing_time_stats'}">
        <div class="tab-col-row">
          <div class="col-md-6 col-lg-6">
            <span class="fa fa-tachometer fa-dash"></span>

            <h3 ng-hide="charts.type=='packing_time_stats'">SPEED SCORE</h3>
            <h3 ng-show="charts.type=='packing_time_stats'">PACKING TIME</h3>
            <span class="no-padding font-12"><span class="col-md-3 col-lg-3 no-padding remove-margin-left">Max</span><span class="col-md-4 col-lg-4 no-padding top-margin-2p"><input type="number" class="col-md-12 col-lg-12 no-padding dashboard-input" ng-model="dashboard.max_time_per_item" ng-blur="update_max()"></span><span class="col-md-5 col-lg-5 no-padding">Sec/Item</span></span>
          </div>
          <div class="col-md-4 col-lg-4 pill-col">
            <div class="row">
              <!-- <div class="col-md-6 col-lg-6"></div> -->
              <div class="button-pill col-md-offset-6 col-lg-offset-6 col-md-1 col-lg-1" ng-class="{'button-pill-active': charts.type!='packing_time_stats'}" ng-click="charts.alter_type('packing_speed_stats')"></div>
              <div class="col-md-1 col-lg-1 button-pill" ng-class="{'button-pill-active': charts.type=='packing_time_stats'}" ng-click="charts.alter_type('packing_time_stats')"></div>
            </div>
            <div class="well">
              <h4 ng-hide="charts.type=='packing_time_stats'">{{dashboard.packing_speed_summary.current_period}}%</h4>
              <h4 ng-show="charts.type=='packing_time_stats'">{{dashboard.packing_time_summary.current_period}}sec</h4>
              <span class="fa fa-calendar fa-pill-col"></span>
              <span>Current Period</span>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.packing_speed_summary.delta == 0,
          'pill-col-last-tall':dashboard.packing_speed_summary.delta > 0,
          'pill-col-last-short':dashboard.packing_speed_summary.delta < 0}" ng-hide="charts.type=='packing_time_stats'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-green"
                    ng-show="dashboard.packing_speed_summary.delta > 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-green"
                    ng-show="dashboard.packing_speed_summary.delta < 0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-green"
                    ng-show="dashboard.packing_speed_summary.delta == '-' || dashboard.packing_speed_summary.delta == 0"></span>

              <p>{{dashboard.packing_speed_summary.delta}}% From Prev
                {{charts.days_filters[charts.current_filter_idx].name}}</p>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.packing_time_summary.delta == 0,
          'pill-col-last-tall':dashboard.packing_time_summary.delta > 0,
          'pill-col-last-short':dashboard.packing_time_summary.delta < 0}" ng-show="charts.type=='packing_time_stats'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-green"
                    ng-show="dashboard.packing_time_summary.delta > 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-green"
                    ng-show="dashboard.packing_time_summary.delta < 0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-green"
                    ng-show="dashboard.packing_time_summary.delta == '-' || dashboard.packing_time_summary.delta == 0"></span>

              <p>{{dashboard.packing_time_summary.delta}}sec From Prev
                {{charts.days_filters[charts.current_filter_idx].name}}</p>
            </div>
          </div>
        </div>
        <div class="tab-col-row">
          <div class="col-md-12 col-lg-12 tab-white" ng-class="{'tab-pill-active': charts.type=='packing_speed_stats' || charts.type=='packing_time_stats'}">
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4 col-lg-4 tab-col tab-col-last"
         ng-click="charts.set_type('packed_item_stats')">
      <div class="tab-pill tab-violet" ng-class="{'tab-pill-active': charts.type=='packed_order_stats' || charts.type=='packed_item_stats'}">
        <div class="tab-col-row">
          <div class="col-md-6 col-lg-6">
            <span class="fa fa-flag-checkered fa-dash"></span>

            <h3 ng-hide="charts.type=='packed_order_stats'">ITEMS PACKED</h3>
            <h3 ng-show="charts.type=='packed_order_stats'">ORDERS PACKED</h3>
          </div>
          <div class="col-md-4 col-lg-4 pill-col">
            <div class="row">
              <!-- <div class="col-md-6 col-lg-6"></div> -->
              <div class="button-pill col-md-offset-6 col-lg-offset-6 col-md-1 col-lg-1" ng-class="{'button-pill-active': charts.type!='packed_order_stats'}" ng-click="charts.alter_type('packed_item_stats')"></div>
              <div class="col-md-1 col-lg-1 button-pill" ng-class="{'button-pill-active': charts.type=='packed_order_stats'}" ng-click="charts.alter_type('packed_order_stats')"></div>
            </div>
            <div class="well">
              <h4 ng-hide="charts.type=='packed_order_stats'">{{dashboard.main_summary.packed_items_summary.current_period}}</h4>
              <h4 ng-show="charts.type=='packed_order_stats'">{{dashboard.main_summary.packed_orders_summary.current_period}}</h4>
              <span class="fa fa-calendar fa-pill-col"></span>
              <span>Current Period</span>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.main_summary.packed_items_summary.delta == 0,
          'pill-col-last-tall':dashboard.main_summary.packed_items_summary.delta > 0,
          'pill-col-last-short':dashboard.main_summary.packed_items_summary.delta < 0}" ng-hide="charts.type=='packed_order_stats'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_items_summary.delta>0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_items_summary.delta == '-' || dashboard.main_summary.packed_items_summary.delta == 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_items_summary.delta<0"></span>

              <p style="line-height: 19px;">
                {{dashboard.main_summary.packed_items_summary.delta}}
                From Prev {{charts.days_filters[charts.current_filter_idx].name}}
              </p>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 pill-col-last" ng-class="
          {'pill-col-last':dashboard.main_summary.packed_orders_summary.delta == 0,
          'pill-col-last-tall':dashboard.main_summary.packed_orders_summary.delta > 0,
          'pill-col-last-short':dashboard.main_summary.packed_orders_summary.delta < 0}" ng-show="charts.type=='packed_order_stats'">
            <div class="well">
              <span class="fa fa-arrow-circle-up fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_orders_summary.delta>0"></span>
              <span class="fa fa-minus-circle fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_orders_summary.delta == '-' || dashboard.main_summary.packed_orders_summary.delta == 0"></span>
              <span class="fa fa-arrow-circle-down fa-pill-col fa-violet"
                    ng-show="dashboard.main_summary.packed_orders_summary.delta<0"></span>

              <p style="line-height: 19px;">
                {{dashboard.main_summary.packed_orders_summary.delta}}
                From Prev {{charts.days_filters[charts.current_filter_idx].name}}
              </p>
            </div>
          </div>
        </div>
        <div class="tab-col-row">
          <div class="col-md-12 col-lg-12 tab-white" ng-class="{'tab-pill-active': charts.type=='packed_item_stats' || charts.type=='packed_order_stats'}">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-4 col-lg-4 tab-col-next">
      <div class="col-md-12 col-lg-12 curve-left tab-pill-active" ng-class="{'tab-pill-active': charts.type=='packing_stats' || charts.type=='packing_error'}" ng-show="charts.type == 'packing_stats' || charts.type=='packing_error'">
      </div>
      <div class="col-md-12 col-lg-12 curve-right" ng-class="{'tab-pill-active': charts.type=='packing_speed_stats' || charts.type=='packing_time_stats'}" ng-show="charts.type == 'packing_speed_stats' || charts.type=='packing_time_stats'">
      </div>
    </div>
    <div class="col-md-4 col-lg-4 tab-col-next">
      <div class="col-md-12 col-lg-12 curve-left" ng-class="{'tab-pill-active': charts.type=='packing_speed_stats' || charts.type=='packing_time_stats'}" ng-show="charts.type == 'packing_speed_stats' || charts.type=='packing_time_stats'">
      </div>
      <div class="col-md-12 col-lg-12 curve-right" ng-class="{'tab-pill-active': charts.type=='packed_item_stats' || charts.type=='packed_order_stats'}" ng-show="charts.type == 'packed_item_stats' || charts.type=='packed_order_stats'">
      </div>
    </div>
    <div class="col-md-4 col-lg-4 tab-col-next">
      <div class="col-md-12 col-lg-12 no-curve" ng-class="{'tab-pill-active': charts.type=='packed_item_stats' || charts.type=='packed_order_stats'}" ng-show="charts.type == 'packed_item_stats' || charts.type=='packed_order_stats'">
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 col-lg-12" ng-class="{'dash-home-container-left': charts.type=='packing_stats' || charts.type=='packing_error', 'dash-home-container': charts.type=='packing_speed_stats' || charts.type=='packing_time_stats', 'dash-home-container-right': charts.type=='packed_item_stats' || charts.type=='packed_order_stats'}">
      <nvd3-line-chart
        data="dashboard.packing_stats"
        height="310"
        xAxisTickValues="xAxisTickValuesFunction()"
        xAxisTickFormat="xAxisTickFormatFunction()"
        yAxisTickValues="yAxisTickValuesFunction(true)"
        yAxisTickFormat="yAxisTickFormatFunction()"
        showXAxis="true"
        showYAxis="true"
        interactive="true"
        forceY
        tooltips="true"
        tooltipContent="toolTipContentFunction()"
        showLegend="true"
        color="legendColorFunction()"
        legendRightAlign="false"
        legendColor="legendColorFunction()" ng-if="charts.type=='packing_stats' || charts.type=='packing_error'">
        <svg></svg>
      </nvd3-line-chart>
      <nvd3-line-chart
        data="dashboard.packing_speed_stats"
        height="310"
        xAxisTickValues="xAxisTickValuesFunction()"
        xAxisTickFormat="xAxisTickFormatFunction()"
        yAxisTickValues="yAxisTickValuesFunction(true)"
        yAxisTickFormat="yAxisTickFormatFunction()"
        showXAxis="true"
        showYAxis="true"
        interactive="true"
        forceY
        tooltips="true"
        tooltipContent="toolTipContentFunction()"
        showLegend="true"
        color="legendColorFunction()"
        legendRightAlign="false"
        legendColor="legendColorFunction()" ng-if="charts.type=='packing_speed_stats' || charts.type=='packing_time_stats'">
        <svg></svg>
      </nvd3-line-chart>
      <nvd3-line-chart
        data="dashboard.packed_item_stats"
        height="310"
        xAxisTickValues="xAxisTickValuesFunction()"
        xAxisTickFormat="xAxisTickFormatFunction()"
        yAxisTickValues="yAxisTickValuesFunction(false)"
        yAxisTickFormat="yAxisTickFormatFunction()"
        showXAxis="true"
        showYAxis="true"
        interactive="true"
        forceY
        tooltips="true"
        tooltipContent="toolTipContentFunction()"
        showLegend="true"
        color="legendColorFunction()"
        legendRightAlign="false"
        legendColor="legendColorFunction()" ng-if="charts.type=='packed_item_stats' || charts.type=='packed_order_stats'">
        <svg></svg>
      </nvd3-line-chart>
    </div>
  </div>
</div>

