<div class="col-lg-10 col-md-10">
  <div class="row">
    <div class="col-lg-7 col-md-7">
      <div class="col-lg-offset-3 col-md-offset-3 col-lg-9 col-md-9 pull-left">
        <input type="text" ng-model="warehouses.setup.search" autofocus="autofocus" class="form-control search-box"
               placeholder="Search">
      </div>
    </div>
    <div class="col-lg-2 col-md-2">
      <button class="groove-button" ng-click="create_warehouse()">Add Warehouse</button>
    </div>
    <div class="col-lg-1 col-md-1">
      <button class="groove-button" ng-click="warehouse_delete()">Delete</button>
    </div>
    <div class="col-lg-2 col-md-2" dropdown>
      <button class="dropdown-toggle groove-button">
        Change Status
        <span class="caret"></span>
      </button>
      <ul class="dropdown-menu" role="menu">
        <li><a class="dropdown-toggle" ng-click="warehouse_change_status('active')">Active</a></li>
        <li><a class="dropdown-toggle" ng-click="warehouse_change_status('inactive')">Inactive</a></li>
      </ul>
    </div>
  </div>
  <div class="row">
    <div class="col-xs-12">
      <div groov-data-grid="gridOptions" groov-list="warehouses.list"></div>
    </div>
  </div>
</div>
