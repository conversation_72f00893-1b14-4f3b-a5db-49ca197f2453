<div class="col-lg-10 col-md-10">
  <h3 class="col-xs-12">Order Exceptions</h3>

  <div class="col-xs-12">
    <div class="form-group col-sm-6">
      <label class="control-label">Start Date</label>

      <div class="container-fluid form-horizontal">
        <div class="row">
          <div class="control-label timepicker-label col-md-8">
            <p class=" input-group">
              <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy" ng-model="exception.start.time"
                     is-open="exception.start.open" ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="open_picker($event,exception.start)"><i
                                      class="glyphicon glyphicon-calendar"></i></button>
                                </span>
            </p>
          </div>

          <div class="controls col-md-4">
            <timepicker ng-model="exception.start.time" show-meridian="false"></timepicker>
          </div>
        </div>

      </div>
    </div>
    <div class="form-group col-sm-6">
      <label class="control-label">End Date</label>

      <div class="container-fluid form-horizontal">
        <div class="row">
          <div class="control-label timepicker-label col-md-8">
            <p class=" input-group">
              <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy" ng-model="exception.end.time"
                     is-open="exception.end.open" ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="open_picker($event,exception.end)"><i
                                      class="glyphicon glyphicon-calendar"></i></button>
                                </span>
            </p>
          </div>

          <div class="controls col-md-4">
            <timepicker ng-model="exception.end.time" show-meridian="false"></timepicker>
          </div>
        </div>

      </div>
    </div>
    <div class="col-sm-2 text-center col-sm-offset-4">
      <button ng_click="download_csv('exception')" class="modal-save-button">Download</button>
    </div>
  </div>
  <h3 class="col-xs-12">Order Serials</h3>

  <div class="col-xs-12">
    <div class="form-group col-sm-6">
      <label class="control-label">Start Date</label>

      <div class="container-fluid form-horizontal">
        <div class="row">
          <div class="control-label timepicker-label col-md-8">
            <p class=" input-group">
              <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy" ng-model="serial.start.time"
                     is-open="serial.start.open" ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="open_picker($event,serial.start)"><i
                                      class="glyphicon glyphicon-calendar"></i></button>
                                </span>
            </p>
          </div>

          <div class="controls col-md-4">
            <timepicker ng-model="serial.start.time" show-meridian="false"></timepicker>
          </div>
        </div>

      </div>
    </div>
    <div class="form-group col-sm-6">
      <label class="control-label">End Date</label>

      <div class="container-fluid form-horizontal">
        <div class="row">
          <div class="control-label timepicker-label col-md-8">
            <p class=" input-group">
              <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy" ng-model="serial.end.time"
                     is-open="serial.end.open" ng-required="true" close-text="Close"/>
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default"
                                            ng-click="open_picker($event,serial.end)"><i
                                      class="glyphicon glyphicon-calendar"></i></button>
                                </span>
            </p>
          </div>

          <div class="controls col-md-4">
            <timepicker ng-model="serial.end.time" show-meridian="false"></timepicker>
          </div>
        </div>

      </div>
    </div>
    <div class="col-sm-2 text-center col-sm-offset-4">
      <button ng_click="download_csv('serial')" class="modal-save-button">Download</button>
    </div>
  </div>
</div>
