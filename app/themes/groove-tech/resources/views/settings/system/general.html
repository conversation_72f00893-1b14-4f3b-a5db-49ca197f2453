<div class="col-lg-10 col-sm-10 general-settings">
  <div class="row">
    <!-- INVENTORY RELATED SETTINGS -->
    <div class="col-xs-12">
      <h3>{{translations.headings.inventory}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.inventory_tracking}}</label>

            <div class="controls col-sm-2">
              <div toggle-switch ng-model="confirmation_hash.inventory_tracking"
                   groov-click="confirm_and_update('inventory_tracking')"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.inventory_tracking}}"></i>
            </div>
          </div>
          <div ng-show="general_settings.single.inventory_tracking">
            <div class="form-group">
              <label class="control-label col-sm-3">{{translations.labels.low_inventory_alert_email}}</label>

              <div class="controls col-sm-2">
                <div toggle-switch ng-model="general_settings.single.low_inventory_alert_email"
                     groov-click="update_settings()"></div>
              </div>
              <div class="info col-sm-1">
                <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.low_inventory_alert_email}}"></i>
              </div>
            </div>
            <div class="form-group">
              <div class="controls col-sm-offset-3 col-sm-5"
                   ng-show="general_settings.single.low_inventory_alert_email== '1'">
                <input type="text" class="form-control input-style"
                       ng-model="general_settings.single.low_inventory_email_address" placeholder="Email address"
                       ng-blur="update_settings()"/>
              </div>
            </div>
            <div class="form-group" ng-show="general_settings.single.low_inventory_alert_email== '1'">
              <label class="control-label col-sm-3">{{translations.labels.time_to_send_email}}</label>

              <div class="controls col-sm-7">
                <timepicker ng-model="general_settings.single.time_to_send_email" ng-change="show_button=true" show-meridian="true" style="display: inline-block;vertical-align: middle;"></timepicker>
                <span style="display: inline-block;vertical-align: middle;padding-left: 13px;"> Current Time: {{general_settings.single.current_time}}
                  <i class="groove-fa fa fa-clock-o" popover-trigger="mouseenter" groov-popover="{{'To adjust the time zone please <a ng-click=scrollup() href=#/settings/system/general>click here.</a>'}}"></i>
              </span>
              </div>
            </div>
            <div class="row" ng-show="show_button">
              <div class="col-sm-4 col-sm-offset-3">
                <button class="modal-save-button" ng-click="update_settings()">Update</button>
              </div>
            </div>
            <div class="form-group" ng-show="general_settings.single.low_inventory_alert_email== '1'">
              <label class="control-label col-sm-3">{{translations.labels.send_email_on}}</label>

              <div class="controls col-sm-9 form-inline">
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_mon"
                         ng-change="update_settings()"> {{translations.labels.mon}}
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_tue"
                         ng-change="update_settings()"> {{translations.labels.tue}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_wed"
                         ng-change="update_settings()"> {{translations.labels.wed}}
                </label>
              </div>
              <div class="controls col-sm-9 col-sm-offset-3 form-inline">
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_thurs"
                         ng-change="update_settings()"> {{translations.labels.thu}}
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_fri"
                         ng-change="update_settings()"> {{translations.labels.fri}}
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_sat"
                         ng-change="update_settings()"> {{translations.labels.sat}}
                </label>
                <label class="checkbox">
                  <input type="checkbox" ng-model="general_settings.single.send_email_on_sun"
                         ng-change="update_settings()"> {{translations.labels.sun}}
                </label>
              </div>
            </div>
            <div class="form-group" ng-show="general_settings.single.low_inventory_alert_email== '1'">
              <label class="control-label col-sm-3">{{translations.labels.default_low_inventory_alert_limit}}</label>

              <div class="controls col-sm-2">
                <input class="form-control input-style" type="number" min="1"
                       ng-model="general_settings.single.default_low_inventory_alert_limit"
                       placeholder="Default low inventory alert limit" ng-blur="update_settings()"/>
              </div>
              <div class="info col-sm-1">
                <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.default_low_inventory_alert_limit}}"></i>
              </div>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>

  <!-- Confirmation and Notifications -->
  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.conf_notif}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.strict_cc}}</label>

            <div class="controls col-sm-2">
              <div toggle-switch ng-model="general_settings.single.strict_cc" groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.strict_cc}}"></i>
            </div>
          </div>

          <!-- <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.hex_barcode}}</label>

            <div class="controls col-sm-1 form-inline">
              <div toggle-switch ng-model="general_settings.single.hex_barcode" groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.hex_barcode}}"></i>
            </div>
          </div> -->
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.conf_req_on_notes_to_packer}}</label>

            <div class="controls col-sm-3 well-main " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':general_settings.single.conf_req_on_notes_to_packer=='always',
                            'label-warning':general_settings.single.conf_req_on_notes_to_packer=='optional'}">
                <span ng-show="general_settings.single.conf_req_on_notes_to_packer=='always'"
                      translate>common.always</span>
                <span ng-show="general_settings.single.conf_req_on_notes_to_packer=='optional'" translate>common.optional</span>
                <span ng-show="general_settings.single.conf_req_on_notes_to_packer=='never'"
                      translate>common.never</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_req_on_notes_to_packer','always')" translate>common.always</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_req_on_notes_to_packer','optional')"
                       translate>common.optional</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_req_on_notes_to_packer','never')" translate>common.never</a>
                </li>
              </ul>
            </div>
            <div class="info well-main col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.conf_req_on_notes_to_packer}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.conf_code_product_instruction}}</label>

            <div class="controls col-sm-3 well-main " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':general_settings.single.conf_code_product_instruction=='always',
                            'label-warning':general_settings.single.conf_code_product_instruction=='optional'}">
                <span ng-show="general_settings.single.conf_code_product_instruction=='always'"
                      translate>common.always</span>
                <span ng-show="general_settings.single.conf_code_product_instruction=='optional'" translate>common.optional</span>
                <span ng-show="general_settings.single.conf_code_product_instruction=='never'"
                      translate>common.never</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_code_product_instruction','always')"
                       translate>common.always</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_code_product_instruction','optional')"
                       translate>common.optional</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('conf_code_product_instruction','never')" translate>common.never</a>
                </li>
              </ul>
            </div>
            <div class="info well-main col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.conf_code_product_instruction}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.send_email_for_packer_notes}}</label>

            <div class="controls col-sm-3 well-main " dropdown>
              <button class="dropdown-toggle groove-button label label-default"
                      ng-class="{'label-success':general_settings.single.send_email_for_packer_notes=='always','label-warning':general_settings.single.send_email_for_packer_notes=='optional'}">
                <span ng-show="general_settings.single.send_email_for_packer_notes=='always'"
                      translate>common.always</span>
                <span ng-show="general_settings.single.send_email_for_packer_notes=='optional'" translate>common.optional</span>
                <span ng-show="general_settings.single.send_email_for_packer_notes=='never'"
                      translate>common.never</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('send_email_for_packer_notes','always')" translate>common.always</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_opt('send_email_for_packer_notes','optional')"
                       translate>common.optional</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('send_email_for_packer_notes','never')" translate>common.never</a>
                </li>
              </ul>
            </div>
            <div class="info well-main col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.send_email_for_packer_notes}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.system_notifications}}</label>
            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.email_address_for_packer_notes"
                     placeholder="{{translations.labels.email_address}}" ng-blur="update_settings()"/>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.billing_notifications}}</label>

            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.email_address_for_billing_notification"
                     placeholder="{{translations.labels.email_address}}" ng-blur="update_settings()"/>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.report_out_of_stock_notifications}}</label>
            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.email_address_for_report_out_of_stock"
                     placeholder="{{translations.labels.email_address}}" ng-blur="update_settings()"/>
            </div>
          </div>

        </fieldset>
      </div>
    </div>
  </div>

  <!-- Numeric Barcode Generation -->
  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.generate_numeric_barcode}}</h3>
      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.barcode_length}}</label>
            <div class="controls col-sm-2">
                     <input id='barcode_length' type="number" class="form-control input-style"
                     ng-model="general_settings.single.barcode_length" min = "1" max = "40"
                     placeholder="{{translations.labels.barcode_length}}" ng-blur="update_settings()"/>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.starting_value}}</label>
            <div class="controls col-md-2">
              <input  type="text" class="form-control input-style"
                     ng-model="general_settings.single.starting_value"  ng-readonly="starting_value_disabled" ng-dblclick="starting_value_disabled=false"
                     placeholder="{{translations.labels.starting_value}}" ng-blur="update_settings()"/>
            </div>

            <div class="controls col-sm-2 form-inline">
              <div  ng-model="translations.tooltips.starting_value" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.starting_value}}"></i>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>


  <!-- Miscellaneous Settings -->
  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.miscellaneous_settings}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <!-- <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.flash_setting}}</label>
            <div class="controls col-sm-8" style="top: 7px;">
              <div id="flash">Currently Flash toggle is <b>{{general_settings.single.flash}}</b> in your browsers content setting
                <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="This site uses flash to enable copying and pasting to the clipboard, To use this you need to go to browser's content setting and allow flash"></i>
              </div>
            </div>
          </div>  -->

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.time_zone}}</label>
            <div class="controls col-sm-8" dropdown>
              <select class="dropdown-toggle groove-button controls col-sm-8" ng-change="fetch_time_zone()" ng-model="general_settings.single.new_time_zone" class="dropdown-toggle groove-button">
                <option class="dropdown-toggle" ng-repeat="(key, value) in general_settings.single.new_time_zones" value="{{value}}" ng-selected="value == general_settings.single.new_time_zone">{{key}}</option>
              </select>
              <div class="controls col-sm-4" style="padding-top: 4px; font-weight: 600;">
                Current Time: {{general_settings.single.current_time}}
              </div>
            </div>
          </div>
          <!-- <div class="form-group">
            <label class="control-label col-sm-3"></label>
            <div class="controls col-sm-8">
              <button class="modal-save-button" ng-click="update_auto_time_zone()">Auto Time Zone</button>
            </div>
          </div> -->

          <!-- <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.time_zone}}</label>
             <div class="controls col-sm-8" dropdown>
              <select class="dropdown-toggle groove-button controls col-sm-8" ng-blur="fetch_time_zone()" ng-model="general_settings.single.time_zone" class="dropdown-toggle groove-button">
                <option class="dropdown-toggle" ng-selected="general_settings.single.auto_detect == true" value="true">AUTO DETECT</option>
                <option class="dropdown-toggle" ng-repeat="(value, key) in general_settings.single.time_zones['time_zones']" value="{{key}}" ng-selected="key == general_settings.single.time_zone">{{value}}</option>
              </select>
              <div class="controls col-sm-4" style="padding-top: 4px; font-weight: 600;">
                Current Time: {{general_settings.single.current_time}}
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.adjust_for_daylight}}</label>
            <div class="controls col-sm-3" ng-click="fetch_time_zone()">
              <input type="radio" name="dst" value=false ng-model="general_settings.single.dst" ng-checked="general_settings.single.dst == false"> on
              <input type="radio" name="dst" value=true ng-model="general_settings.single.dst" ng-checked="general_settings.single.dst == true"> off
            </div>
          </div> -->

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.order_item_export}}</label>

            <div class="controls col-sm-5" dropdown>
              <button class="dropdown-toggle groove-button">
                <span ng-show="general_settings.single.export_items =='disabled'">Disabled</span>
                <span
                  ng-show="general_settings.single.export_items =='by_order'">{{translations.labels.display_sku_total}}</span>
                <span ng-show="general_settings.single.export_items =='by_sku'">{{translations.labels.display_one_total}}</span>
                <span ng-show="general_settings.single.export_items =='standard_order_export'">{{translations.labels.standard_order_export}}</span>
                <span class="caret"></span>
              </button>

              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('export_items','disabled')">Disabled</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('export_items','by_order')">{{translations.labels.display_sku_total}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('export_items','by_sku')">{{translations.labels.display_one_total}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('export_items','standard_order_export')">{{translations.labels.standard_order_export}}</a></li>
              </ul>
            </div>

            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.order_item_export_message}}"></i>
            </div>

          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.order_tracking_no_order}}</label>

            <div class="controls col-sm-7">
              <textarea class="form-control input-style"
                        ng-model="general_settings.single.tracking_error_order_not_found" ng-blur="update_settings()"
                        class="span8" rows="4"></textarea>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.order_tracking_no_tracking}}</label>

            <div class="controls col-sm-7">
              <textarea class="form-control input-style"
                        ng-model="general_settings.single.tracking_error_info_not_found" ng-blur="update_settings()"
                        class="span8" rows="4"></textarea>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.remove_order_items}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.remove_order_items"
                   groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.remove_order_items}}"></i>
            </div>
          </div>
           <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_imported_skus}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.show_imported_skus"
                   groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.show_imported_skus}}"></i>
            </div>
          </div>

          <br/>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.delete_import_summary}}</label>
        
            <div class="controls col-sm-2 form-inline">
                <button ng-click="delete_import_summary()" class="btn btn-danger btn-sm rounded">
                    Delete
                </button>
            </div>
            <div class="info col-sm-1">
            </div>
        </div>

          <h3>Custom Fields</h3><br/>


          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.custom_order_one}}</label>

            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.custom_field_one"
                     placeholder="{{translations.labels.custom_order_one}}" ng-blur="update_settings()"/>
            </div>
            <div class="info col-sm-1" style="margin-top: -5px">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.custom_fields}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.custom_order_two}}</label>

            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.custom_field_two"
                     placeholder="{{translations.labels.custom_order_two}}" ng-blur="update_settings()"/>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.custom_user_order_one}}</label>

            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.custom_user_field_one"
                     placeholder="{{translations.labels.custom_user_order_one}}" ng-blur="update_settings();"
                     ng-keyup="auto_complete($event, 'custom_user_field_one')"/>
            </div>
          </div>

          <div class="form-group" ng-if="ready_for_auto_complete && custom_user_field_one_auto_complete_data" style="margin-bottom: 0px">
            <ul class="col-sm-offset-3 col-sm-7 auto_complete_menu">
              <li ng-repeat="item in custom_user_field_one_auto_complete_data" ng-click="general_settings.single.custom_user_field_one = item; clear_auto_complete('custom_user_field_one')">{{item}}</li>
            </ul>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.custom_user_order_two}}</label>

            <div class="controls col-sm-7">
              <input type="text" class="form-control input-style"
                     ng-model="general_settings.single.custom_user_field_two"
                     placeholder="{{translations.labels.custom_user_order_two}}" ng-blur="update_settings()"
                     ng-keyup="auto_complete($event, 'custom_user_field_two')"/>
            </div>
          </div>

          <div class="form-group" ng-if="ready_for_auto_complete && custom_user_field_two_auto_complete_data" style="margin-bottom: 0px">
            <ul class="col-sm-offset-3 col-sm-7 auto_complete_menu">
              <li ng-repeat="item in custom_user_field_two_auto_complete_data" ng-click="general_settings.single.custom_user_field_two = item; clear_auto_complete('custom_user_field_two')">{{item}}</li>
            </ul>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.product_weight}} </label>

            <div class="controls col-sm-9" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.product_weight_format}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('product_weight_format','lb')">lb</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_weight_format','oz')">oz</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_weight_format','kg')">kg</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_weight_format','g')">g</a></li>
              </ul>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.product_dimension}} </label>

            <div class="controls col-sm-9" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.product_dimension_unit}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('product_dimension_unit','inches')">inches(in)</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_dimension_unit','centimeter')">centimeter(cm)</a></li>
            
              </ul>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.pre_day_order}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.scheduled_order_import"
                groov-click="update_settings()" class="pull-left"></div>
            </div>
            <div ng-if="general_settings.single.scheduled_order_import==true && general_settings.single.scheduled_import_toggle==true" class="controls col-sm-2" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.schedule_import_mode}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('schedule_import_mode','Daily')">Daily</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('schedule_import_mode','Hourly')">Hourly</a></li>
              </ul>
            </div>
            <div class="info col-sm-2">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="{{translations.tooltips.pre_day_order_import_schedule}}"></i>
            </div>
          </div>

          <div ng-if="general_settings.single.schedule_import_mode != 'Hourly'" class="form-group" ng-show="general_settings.single.scheduled_order_import== '1'">
            <label class="control-label col-sm-3">{{translations.headings.pre_day_import}}</label>

            <div class="controls col-sm-9">
              <timepicker ng-model="general_settings.single.time_to_import_orders" ng-change="show_button=true" show-meridian="true" style="display: inline-block;vertical-align: middle;"></timepicker>
              <span style="display: inline-block;vertical-align: middle;padding-left: 13px;"> Current Time: {{general_settings.single.current_time}}
                  <i class="groove-fa fa fa-clock-o" popover-trigger="mouseenter" groov-popover="{{'To adjust the time zone please <a ng-click=scrollup() href=#/settings/system/general>click here.</a>'}}"></i>
              </span>
            </div>
            <div class="row" ng-show="show_button">
              <div class="col-sm-4 col-sm-offset-3">
                <button class="modal-save-button" ng-click="update_settings()">Update</button>
              </div>
            </div>
          </div>
          <div class="form-group" ng-show="general_settings.single.scheduled_order_import== '1'">
            <label class="control-label col-sm-3">{{translations.headings.import_orders}}</label>

            <div class="controls col-sm-9 form-inline">
              <label class="checkbox col-sm-2">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_mon"
                       ng-change="update_settings()"> {{translations.labels.mon}}
              </label>
              <label class="checkbox col-sm-2">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_tue"
                       ng-change="update_settings()"> {{translations.labels.tue}}
              </label>
              <label class="checkbox">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_wed"
                       ng-change="update_settings()"> {{translations.labels.wed}}
              </label>
            </div>
            <div class="controls col-sm-offset-3 col-sm-9 form-inline">
              <label class="checkbox col-sm-2">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_thurs"
                       ng-change="update_settings()"> {{translations.labels.thu}}
              </label>
              <label class="checkbox col-sm-2">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_fri"
                       ng-change="update_settings()"> {{translations.labels.fri}}
              </label>
              <label class="checkbox col-sm-2">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_sat"
                       ng-change="update_settings()"> {{translations.labels.sat}}
              </label>
              <label class="checkbox">
                <input type="checkbox" ng-model="general_settings.single.import_orders_on_sun"
                       ng-change="update_settings()"> {{translations.labels.sun}}
              </label>
            </div>
          </div>

           <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.recurring_ftp}}</label>
            <div class="controls col-sm-5">
              <rzslider
                rz-slider-model="slider.min"
                rz-slider-high="slider.max"
                rz-slider-options="slider.options">
              </rzslider>
            </div>
            <div class="controls col-sm-4">
              <span style="margin-top: 25px; float: left;"> Current Time: {{general_settings.single.current_time}}
                <i class="groove-fa fa fa-clock-o" popover-trigger="mouseenter" groov-popover="{{'To adjust the time zone please <a ng-click=scrollup() href=#/settings/system/general>click here.</a>'}}"></i>
                  <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.recurring_ftp_order_import_range}}"></i>
              </div>
              </span>
            </div>
            <div class="row" ng-show="show_button">
              <div class="col-sm-4 col-sm-offset-3">
                <button class="modal-save-button" ng-click="update_import_time()">Update</button>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3 ng-binding">{{translations.labels.inactive_logout_time}}</label>
            <div class="controls col-sm-7">
              <div class="col-sm-4" style="padding-left: 0px;">
                <input type="number" class="form-control input-style ng-pristine ng-valid" ng-model="general_settings.single.idle_timeout" ng-blur="update_settings()">
              </div>

              <span class="col-sm-3" style="padding: 4px;">Minutes</span>
              <div class="info col-sm-1">
                <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.inactive_logout_time}}"></i>
              </div>
            </div>
          </div>

        </fieldset>
      </div>
    </div>

  </div>
</div>
