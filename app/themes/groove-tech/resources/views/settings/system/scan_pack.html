<div class="col-lg-10 col-md-10">
  <div class="row" ng-show="scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall' || scan_pack.settings.scan_pack_workflow == 'multi_put_wall'">
    <div class="col-xs-12">
      <h3 ng-show="scan_pack.settings.scan_pack_workflow == 'multi_put_wall'">Product First - Scan to multi put wall settings</h3>
      <h3 ng-show="scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall'">{{translations.headings.product_first_scan_to_put_wall}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>

          <div class="form-group">
            <label class="control-label col-sm-6">The physical container that items will be placed in after scanning
              will be called a </label>

            <div class="col-sm-5">
              <div class="col-sm-5">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.tote_identifier" />
              </div>
            </div>
          </div>

          <div class="form-group" ng-repeat="tote_set in scan_pack.settings.tote_sets">
            <div class="info col-sm-1 text-right" ng-hide="scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall'">
              <div ng-hide="$index == 0">
                <a ng-click="delete_tote_set(tote_set.id)" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-remove"></i></a>&nbsp;
              </div>
            </div>

            <label class="control-label col-sm-4">Total number of {{scan_pack.settings.tote_identifier + 's'}} in set {{tote_set.name}}</label>

            <div class="col-sm-3">
              <div class="col-sm-8">
                <input class="form-control input-style" type="number" ng-blur="update_settings()"
                  ng-model="tote_set.max_totes" />
              </div>
            </div>
            <div class="info col-sm-4">
              <a ng-click="scan_pack_pdf('tote_barcodes', tote_set.id)" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              Print All {{scan_pack.settings.tote_identifier}} Barcodes
            </div>
          </div>

          <div class="form-group" style="padding-left: 150px;" ng-show="scan_pack.settings.scan_pack_workflow == 'multi_put_wall'">
            <div class="info form-group text-center">
              <a ng-click="create_tote_set()" target="_blank" class="generate_pdf"><i
                class="icon-large glyphicon glyphicon-plus-sign"></i></a>&nbsp;
                <div class="form-group", style="padding-left: 275px;">
                  <button class="" ng-click="reset_totes()">Reset Totes</button>
                </div>    
              </div>
            </div>    
          </div>

          <h4>Order Complete Messages <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="The following messages are displayed when the order is complete. Single-Item orders and Multi-Item orders each have their own message."></i>
          </h4>

          <div class="form-group col-sm-12">
            <div class="col-sm-1"></div>
            <div class="controls col-sm-8">
              <div text-angular ng-model="scan_pack.settings.single_item_order_complete_msg"
                ng-blur="update_settings()"></div>
            </div>
          </div>

          <div class="form-group  col-sm-12">
            <div class="col-sm-2"></div>
            <label class="control-label col-sm-4 text-right">Display Single-Item Order Complete Message for seconds.</label>
            <div class="col-sm-3">
              <div class="col-sm-7">
                <input class="form-control input-style" type="number" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.single_item_order_complete_msg_time" />
              </div>
            </div>
          </div>

          <div class="form-group col-sm-12">
            <div class="col-sm-1"></div>
            <div class="controls col-sm-8">
              <div text-angular ng-model="scan_pack.settings.multi_item_order_complete_msg" ng-blur="update_settings()">
              </div>
            </div>
          </div>

          <div class="form-group col-sm-12">
            <div class="col-sm-2"></div>
            <label class="control-label col-sm-4 text-right">Display Multi-Item Order Complete Message for seconds.</label>
            <div class="col-sm-3">
              <div class="col-sm-7">
                <input class="form-control input-style" type="number" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.multi_item_order_complete_msg_time" />
              </div>
            </div>
          </div>

        </fieldset>
      </div>
      <div class="form-group">
        <label class="control-label col-sm-3">Import with service issue status</label>
        <div class="controls form-inline"
          ng-class="{'col-md-1': scan_pack.settings.enable_service_issue_status, 'col-md-2': !scan_pack.settings.enable_service_issue_status}">
          <div toggle-switch ng-model="scan_pack.settings.enable_service_issue_status" groov-click="update_settings()">
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">

    <div class="col-xs-12">
      <h3>{{translations.headings.options}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.ask_tracking_number}}</label>

            <div class="controls col-sm-4 form-inline " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':scan_pack.settings.post_scanning_option=='Verify',
                        'label-warning':scan_pack.settings.post_scanning_option=='Record',
                        'label-info':scan_pack.settings.post_scanning_option=='PackingSlip',
                        'label-danger':scan_pack.settings.post_scanning_option=='Barcode'}">
                <span ng-show="scan_pack.settings.post_scanning_option=='None'" translate>common.none</span>
                <span ng-show="scan_pack.settings.post_scanning_option=='Verify'" translate>common.verify</span>
                <span ng-show="scan_pack.settings.post_scanning_option=='Record'" translate>common.record</span>
                <span ng-show="scan_pack.settings.post_scanning_option=='PackingSlip'"
                  translate>common.packing_slip</span>
                <span ng-show="scan_pack.settings.post_scanning_option=='Barcode'" translate>common.order_barcode</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt('None')" translate>common.none</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt('Verify')" translate>common.verify</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt('Record')" translate>common.record</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt('PackingSlip')"
                    translate>common.packing_slip</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt('Barcode')"
                    translate>common.order_barcode</a>
                </li>
              </ul>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" popover-placement="bottom"
                groov-popover="{{translations.tooltips.ask_post_scanning_functions}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.ask_tracking_number_second}}</label>

            <div class="controls col-sm-4 form-inline " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':scan_pack.settings.post_scanning_option_second=='Verify',
                        'label-warning':scan_pack.settings.post_scanning_option_second=='Record',
                        'label-info':scan_pack.settings.post_scanning_option_second=='PackingSlip',
                        'label-danger':scan_pack.settings.post_scanning_option_second=='Barcode'}">
                <span ng-show="scan_pack.settings.post_scanning_option_second=='None'" translate>common.none</span>
                <span ng-show="scan_pack.settings.post_scanning_option_second=='Verify'" translate>common.verify</span>
                <span ng-show="scan_pack.settings.post_scanning_option_second=='Record'" translate>common.record</span>
                <span ng-show="scan_pack.settings.post_scanning_option_second=='PackingSlip'"
                  translate>common.packing_slip</span>
                <span ng-show="scan_pack.settings.post_scanning_option_second=='Barcode'"
                  translate>common.order_barcode</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt_second('None')"
                    translate>common.none</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt_second('Verify')"
                    translate>common.verify</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt_second('Record')"
                    translate>common.record</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt_second('PackingSlip')"
                    translate>common.packing_slip</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_post_scanning_opt_second('Barcode')"
                    translate>common.order_barcode</a>
                </li>
              </ul>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" popover-placement="bottom"
                groov-popover="{{translations.tooltips.ask_post_scanning_functions_second}}"></i>
            </div>
          </div>

          <div class="form-group"
            ng-show="scan_pack.settings.post_scanning_option=='Record' || scan_pack.settings.post_scanning_option_second=='Record'">
            <label class="control-label col-sm-3"
              style="padding-top: 0px;">{{translations.labels.tracking_number_validation}}</label>

            <div class="controls form-inline"
              ng-class='{"col-md-1": scan_pack.settings.tracking_number_validation_enabled, "col-md-2": !scan_pack.settings.tracking_number_validation_enabled}'>
              <div toggle-switch ng-model="scan_pack.settings.tracking_number_validation_enabled"
                groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                ng-if="!scan_pack.settings.tracking_number_validation_enabled" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.tracking_number_validation}}"></i>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.tracking_number_validation_enabled">
              <label class="col-sm-4 col-md-3" style="padding-top: 0px;">Valid Prefixes</label>
              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.tracking_number_validation_prefixes" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign"
                ng-show="scan_pack.settings.tracking_number_validation_enabled" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.tracking_number_validation}}"></i>
            </div>
          </div>

          <div class="form-group" ng-show="scan_pack.settings.scan_pack_workflow == 'default'">
            <label class="control-label col-sm-3">{{translations.labels.cue_order_for_scan}}</label>

            <div class="controls col-sm-4 form-inline " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':scan_pack.settings.scan_by_packing_slip==true,
                        'label-warning':scan_pack.settings.scan_by_shipping_label==true}">
                <span ng-show="scan_pack.settings.scan_by_shipping_label==true"
                  translate>common.shipping_label_scan</span>
                <span ng-show="scan_pack.settings.scan_by_packing_slip==true" translate>common.packing_slip_scan</span>
                <span ng-show="scan_pack.settings.scan_by_packing_slip_or_shipping_label==true"
                  translate>common.packing_slip_or_shipping_label_scan</span>
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_pre_scanning_opt('packing_slip_scan')"
                    translate>common.packing_slip_scan</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_pre_scanning_opt('shipping_label_scan')"
                    translate>common.shipping_label_scan</a>
                </li>
                <li><a class="dropdown-toggle"
                    ng-click="change_pre_scanning_opt('scan_by_packing_slip_or_shipping_label')"
                    translate>common.packing_slip_or_shipping_label_scan</a>
                </li>
              </ul>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" popover-placement="bottom"
                groov-popover="{{translations.tooltips.cue_order_for_scan}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.requires_assigned_orders}}</label>

            <div class="controls form-inline pull-left"
              ng-class='{"col-sm-1": scan_pack.settings.requires_assigned_orders, "col-sm-2": !scan_pack.settings.requires_assigned_orders }'>
              <div toggle-switch ng-model="scan_pack.settings.requires_assigned_orders" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" ng-if='!scan_pack.settings.requires_assigned_orders'
                groov-popover="{{translations.tooltips.requires_assigned_orders}}"></i>
            </div>
            <div class="col-sm-4 col-md-3 col-lg-3 col-lg-offset-0 col-md-offset-1"
              style="max-width: 130px; top: 5px;"
              ng-show="scan_pack.settings.requires_assigned_orders">
              <button ng-click="per_product_setting('requires_assigned_orders')"
                class="groove-button label label-default label-success">Per Product Setting
              </button>
            </div>
            <div class="info col-sm-1" ng-show="scan_pack.settings.requires_assigned_orders">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.requires_assigned_orders}}"></i>
            </div>
          </div>

          <!--   <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">{{translations.labels.cue_orders_for_scanpack}}</label>

            <div class="controls col-sm-3 col-md-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.scan_by_tracking_number"
                   groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                 popover-trigger="mouseenter" groov-popover="{{translations.tooltips.cue_orders_optons}}"></i>
            </div>
          </div>
 -->
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.enable_click_sku}}</label>

            <div class="controls form-inline pull-left"
              ng-class='{"col-sm-1": scan_pack.settings.enable_click_sku, "col-sm-2": !scan_pack.settings.enable_click_sku }'>
              <div toggle-switch ng-model="scan_pack.settings.enable_click_sku" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" ng-if='!scan_pack.settings.enable_click_sku'
                groov-popover="{{translations.tooltips.enable_click_sku}}"></i>
            </div>
            <div class="col-sm-4 col-md-3 col-lg-3 col-lg-offset-0 col-md-offset-1"
              style="max-width: 130px; top: 5px;"
              ng-show="scan_pack.settings.enable_click_sku">
              <button ng-click="per_product_setting('enable_click_sku')"
                class="groove-button label label-default label-success">Per Product Setting
              </button>
            </div>
            <div class="info col-sm-1" ng-show="scan_pack.settings.enable_click_sku">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.enable_click_sku}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.type_scan_code}}</label>

            <div class="controls form-inline"
              ng-class="{'col-md-1': scan_pack.settings.type_scan_code_enabled, 'col-md-2': !scan_pack.settings.type_scan_code_enabled}">
              <div toggle-switch ng-model="scan_pack.settings.type_scan_code_enabled" groov-click="update_settings()">
              </div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;padding-right: 2px;"
                popover-trigger="mouseenter" ng-if='!scan_pack.settings.type_scan_code_enabled'
                groov-popover="{{translations.tooltips.type_in_counts}}"></i>
            </div>
            <div class="col-sm-3 col-md-2 col-lg-2" ng-show="scan_pack.settings.type_scan_code_enabled">
              <button ng-click="per_product_setting('type_scan_code')"
                class="groove-button label label-default label-success">Per Product Setting
              </button>
            </div>
            <div class="col-sm-2" ng-show="scan_pack.settings.type_scan_code_enabled">
              <input class="form-control input-style" type="text" ng-blur="update_settings()"
                ng-model="scan_pack.settings.type_scan_code" />
            </div>

            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('type_scan')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.type_scan_code}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.escape_string}}</label>
            <div class="controls form-inline"
              ng-class='{"col-sm-1": scan_pack.settings.escape_string_enabled, "col-sm-2": !scan_pack.settings.escape_string_enabled }'>
              <div toggle-switch ng-model="scan_pack.settings.escape_string_enabled" groov-click="update_settings()">
              </div>
              <i ng-show="!scan_pack.settings.escape_string_enabled"
                class="info icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.record_suffix}}" style="left: 5px;"></i>
            </div>
            <div class="col-sm-7 text-left" ng-show="scan_pack.settings.escape_string_enabled" style="display: flex;">
              <i class="info icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.record_suffix}}"></i>
              <label class="col-sm-4 col-md-3">Escape String 1</label>
              <div class="col-sm-8 col-md-10" style="display: flex;">
                <input class="col-sm-2 col-md-3 form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.escape_string" ng-trim="false" />
                <!--  <i class="info icon-large glyphicon glyphicon-info-sign"
                   ng-show="scan_pack.settings.escape_string_enabled" popover-trigger="mouseenter"
                 groov-popover="{{translations.tooltips.record_suffix}}"></i> -->
                <label class="col-sm-2 col-md-3">Record Suffix</label>
                <div class="col-md-4 col-sm-2">
                  <div toggle-switch ng-model="scan_pack.settings.first_escape_string_enabled"
                    groov-click="update_settings()"></div>
                </div>
                <i class="col-md-2 col-sm-2 info icon-large glyphicon glyphicon-info-sign"
                  ng-if="scan_pack.settings.escape_string_enabled" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.escape_string}}"></i>
              </div>
            </div>
            <div class="info col-sm-2">
            </div><br />
            <div class="col-sm-2"></div>
            <div class="col-sm-7" ng-show="scan_pack.settings.escape_string_enabled" style="display: flex;">
              <label class="col-sm-4 col-md-3" style="margin-left: 16px;">Escape String 2</label>
              <div class="col-sm-8 col-md-10" style="display:flex;">
                <input class="col-sm-2 col-md-3 form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.second_escape_string" ng-trim="false" />
                <label class="col-sm-2 col-md-3">Record Suffix</label>
                <div class="col-md-4 col-sm-2">
                  <div toggle-switch ng-model="scan_pack.settings.second_escape_string_enabled"
                    groov-click="update_settings()"></div>
                </div>
                <i class="col-md-2 col-sm-2 info icon-large glyphicon glyphicon-info-sign"
                  ng-if="scan_pack.settings.escape_string_enabled" popover-trigger="mouseenter"
                  groov-popover="{{translations.tooltips.escape_string}}"></i>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">Barcode string removal</label>
            <div class="controls form-inline"
              ng-class='{"col-sm-1": scan_pack.settings.string_removal_enabled, "col-sm-2": !scan_pack.settings.string_removal_enabled }'>
              <div toggle-switch ng-model="scan_pack.settings.string_removal_enabled" groov-click="update_settings()">
              </div>
              <i ng-show="scan_pack.settings.string_removal_enabled==false"
                class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;padding-right: 2px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.string_removal}}"></i>
              <!-- <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                 ng-if="!scan_pack.settings.string_removal_enabled" popover-trigger="mouseenter"
                 groov-popover="{{translations.tooltips.string_removal}}"></i> -->
            </div>
            <div class="col-sm-4 text-left" ng-show="scan_pack.settings.string_removal_enabled">
              <label class="col-sm-4 col-md-3">Removal String</label>
              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.string_removal" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-2" ng-show="scan_pack.settings.string_removal_enabled">
              <i class="icon-large glyphicon glyphicon-info-sign pull-left" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.string_removal}}"></i>
            </div>
          </div>
          <!--  <div class="form-group" ng-show="scan_pack.settings.escape_string_enabled">
            <label class="control-label col-sm-3">{{translations.labels.lot_number}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.record_lot_number" groov-click="update_settings()"></div>
            </div>
          </div>--> 

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.order_num_esc_str_enabled}}</label>
            <div class="controls form-inline"
              ng-class='{"col-sm-1": scan_pack.settings.order_num_esc_str_enabled, "col-sm-2": !scan_pack.settings.order_num_esc_str_enabled }'>
              <div toggle-switch ng-model="scan_pack.settings.order_num_esc_str_enabled" groov-click="update_settings()">
              </div>
              <i ng-show="scan_pack.settings.order_num_esc_str_enabled==false"
                class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;padding-right: 2px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.order_num_esc_str_enabled}}"></i>
            </div>
            <div class="col-sm-4 text-left" ng-show="scan_pack.settings.order_num_esc_str_enabled">
              <label class="col-sm-4 col-md-3">{{translations.labels.order_num_esc_str_removal}}</label>
              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.order_num_esc_str_removal" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-2" ng-show="scan_pack.settings.order_num_esc_str_enabled">
              <i class="icon-large glyphicon glyphicon-info-sign pull-left" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.order_num_esc_str_enabled}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_customer_notes}}</label>

            <div class="controls col-sm-3 col-md-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.show_customer_notes" groov-click="update_settings()">
              </div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.show_customer_notes}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_tags}}</label>

            <div class="controls col-sm-3 col-md-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.show_tags" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.show_tags}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_internal_notes}}</label>

            <div class="controls col-sm-3 col-md-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.show_internal_notes" groov-click="update_settings()">
              </div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.show_internal_notes}}"></i>
            </div>
          </div>
          <div class="form-group">
            <div class="row" ng-if="general_settings.single.show_external_logs_button">
            <label class="control-label col-sm-3">{{translations.labels.send_external_logs}}</label>
              <div class="controls col-sm-3 col-md-2 form-inline">
                <div toggle-switch ng-model="scan_pack.settings.send_external_logs" groov-click="update_settings()">
                </div>
                <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                  popover-trigger="mouseenter" groov-popover="{{translations.tooltips.send_external_logs}}"></i>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3"
              style="padding-top: 0px;">{{translations.labels.intangible_setting}}</label>

            <div class="controls form-inline"
              ng-class='{"col-md-1": scan_pack.settings.intangible_setting_enabled, "col-md-2": !scan_pack.settings.intangible_setting_enabled}'>
              <div toggle-switch ng-model="scan_pack.settings.intangible_setting_enabled"
                groov-click="update_product_intangibleness()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                ng-if="!scan_pack.settings.intangible_setting_enabled" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.intanginle_setting}}"></i>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.intangible_setting_enabled">
              <label class="col-sm-4 col-md-3"
                style="padding-top: 0px;">{{translations.labels.intangible_string}}</label>

              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="text" ng-blur="update_product_intangibleness()"
                  ng-model="scan_pack.settings.intangible_string" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign"
                ng-show="scan_pack.settings.intangible_setting_enabled" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.intangible_setting}}"></i>
            </div>
          </div>


          <div class="form-group " ng-show="scan_pack.settings.intangible_setting_enabled">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Auto-Alias Intangible Items</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.replace_gp_code" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.replace_gp_code_tootip}}"></i>
            </div>
          </div>

          <div class="form-group" ng-show="scan_pack.settings.intangible_setting_enabled">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Auto-Generate Barcode for Intangible
              Items</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.intangible_setting_gen_barcode_from_sku"
                groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.intangible_setting_gen_barcode_from_sku}}"></i>
            </div>
          </div>



          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Post Scan Pause</label>

            <div class="controls form-inline"
              ng-class='{"col-md-1": scan_pack.settings.post_scan_pause_enabled, "col-md-2": !scan_pack.settings.post_scan_pause_enabled}'>
              <div toggle-switch ng-model="scan_pack.settings.post_scan_pause_enabled" groov-click="update_settings()">
              </div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                ng-if="!scan_pack.settings.post_scan_pause_enabled" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.post_scan_pause}}"></i>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.post_scan_pause_enabled">
              <label class="col-sm-4 col-md-3" style="padding-top: 0px;">Seconds</label>

              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="number" step="0.1" min="0.3" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.post_scan_pause_time" />
              </div>
            </div>
            <div class="info col-sm-2">
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Display location during scanning</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.display_location" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.display_location_on_scanning_page}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Display location 2 during scanning</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.display_location2" groov-click="update_settings()"></div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Display location 3 during scanning</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.display_location3" groov-click="update_settings()"></div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Single Scan Order Verification</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.order_verification" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.order_verification}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Return to Orders List After Scanning</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.return_to_orders" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.return_to_orders}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3" style="padding-top: 0px;">Require Serial/Lot Prefix</label>

            <div class="controls form-inline"
              ng-class='{"col-md-1": scan_pack.settings.require_serial_lot, "col-md-2": !scan_pack.settings.require_serial_lot}'>
              <div toggle-switch ng-model="scan_pack.settings.require_serial_lot" groov-click="update_settings()"></div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                ng-if="!scan_pack.settings.require_serial_lot" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.require_serial}}"></i>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.require_serial_lot">
              <label class="col-sm-4 col-md-3" style="padding-top: 0px;">Valid Prefixes</label>
              <div class="col-sm-8 col-md-9">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.valid_prefixes" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" ng-show="scan_pack.settings.require_serial_lot"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.require_serial}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3"
              style="padding-top: 0px;">{{translations.labels.simple_product}}</label>
            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.master_switch"
                groov-click="update_general_settings()" class="pull-left"></div>

              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.simple_product_scanning}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3"
              style="padding-top: 0px;">{{translations.labels.scan_all}}</label>
            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.scan_all_option"
                groov-click="update_settings()" class="pull-left"></div>

              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.scan_all}}"></i>
            </div>
          </div>
          <div class="form-group" ng-if="general_settings.single && general_settings.single.scan_pack_workflow === 'scan_to_cart'">
            <label class="control-label col-sm-3"
              style="padding-top: 0px;">{{translations.labels.scan_to_cart}}</label>
            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.scan_to_cart_option"
                groov-click="update_settings()" class="pull-left"></div>

              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.scan_to_cart}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">Scanning Sequence</label>
            <div class="controls col-sm-4 form-inline " dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':scan_pack.settings.scanning_sequence =='any_sequence',
                        'label-warning':scan_pack.settings.scanning_sequence =='items_sequence',
                        'label-danger':scan_pack.settings.scanning_sequence =='kit_packing_mode'}">
                <span ng-show="scan_pack.settings.scanning_sequence =='any_sequence'" translate>Any Sequence</span>
                <span ng-show="scan_pack.settings.scanning_sequence =='items_sequence'" translate>Force suggested scan
                  sequence for all items</span>
                <!--                 <span ng-show="scan_pack.settings.scanning_sequence =='kits_sequence'" translate>Force suggested scan sequence for kit part items</span> -->
                <span ng-show="scan_pack.settings.scanning_sequence =='kit_packing_mode'" translate>Kit Packing
                  Mode</span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_scanning_seq_opt('any_sequence')" translate>Any
                    Sequence</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_scanning_seq_opt('items_sequence')" translate>Force
                    suggested scan sequence for all items</a>
                </li>
                <!--  <li><a class="dropdown-toggle" ng-click="change_scanning_seq_opt('kits_sequence')" translate>Force suggested scan sequence for kit part items</a>
                </li> -->
                <li><a class="dropdown-toggle" ng-click="change_scanning_seq_opt('kit_packing_mode')" translate>Kit
                    Packing Mode</a>
                </li>
              </ul>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:4px;"
                popover-trigger="mouseenter" popover-placement="bottom"
                groov-popover="{{translations.tooltips.scanning_sequence_tool}}"></i>
            </div>
          </div>

          <!--  <div class="form-group">
            <label class="control-label col-sm-3"
                   style="padding-top: 0px;">Download image for Shipstation products</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.download_ss_image" groov-click="update_settings()"></div> -->
          <!-- <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.display_location_on_scanning_page}}"></i> -->
          <!-- </div>
          </div> -->

        </fieldset>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.scan_actions}}</h3>
      <h4>{{translations.headings.scan_actions_sub_head}}</h4>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.skip_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.skip_code_enabled" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.skip_code_enabled">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.skip_code" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('skip_code')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.remove_skipped}}"></i>
            </div>
          </div>
          <div class="form-group" ng-show="scan_pack.settings.skip_code_enabled">
            <label class="control-label col-sm-3">{{translations.labels.remove_skipped}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.remove_skipped" groov-click="update_settings()"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.note_from_packer_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.note_from_packer_code_enabled"
                groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.note_from_packer_code_enabled">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.note_from_packer_code" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('note_from_packer_code')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.note_from_packer_code}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.service_issue_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.service_issue_code_enabled"
                groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.service_issue_code_enabled">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.service_issue_code" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('service_issue_code')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.service_issue_code}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.restart_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.restart_code_enabled" groov-click="update_settings()">
              </div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.restart_code_enabled">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.restart_code" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('restart_code')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.restart_code}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.click_scan_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.pass_scan" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.pass_scan">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.pass_scan_barcode" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('pass_scan_barcode')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.pass_scan_tooltip}}"></i>
            </div>
          </div>
          <div class="form-group" ng-show="scan_pack.settings.click_scan">
            <label class="control-label col-sm-3">{{translations.labels.click_scan_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.click_scan" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.click_scan">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.click_scan_barcode" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('click_scan_barcode')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.click_scan_tooltip}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.scanned_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.scanned" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.scanned">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.scanned_barcode" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('scanned_barcode')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.scanned_barcode_tooltip}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.partial_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.partial"
                   groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.partial">

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                       ng-model="scan_pack.settings.partial_barcode"/>
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('partial_barcode')" target="_blank" class="generate_pdf"><i
                class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.partial_barcode_tooltip}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.remove_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.remove_enabled" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.remove_enabled">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.remove_barcode" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('remove_barcode')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.remove_barcode_tooltip}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.add_next_code}}</label>

            <div class="controls col-sm-2 col-md-1 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.add_next" groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-4" ng-show="scan_pack.settings.add_next">
              <!-- <label class="control-label col-sm-3 col-md-2">{{translations.labels.scan}}</label> -->

              <div class="col-sm-9 col-md-10">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="scan_pack.settings.add_next_barcode" />
              </div>
            </div>
            <div class="info col-sm-2">
              <a ng-click="scan_pack_pdf('add_next_barcode')" target="_blank" class="generate_pdf"><i
                  class="icon-large glyphicon glyphicon-print"></i></a>&nbsp;
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.add_next_barcode_tooltip}}"></i>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>

  <div class="row" ng-show="general_settings.single.packing_cam">
    <div class="col-xs-12">
      <h3>{{translations.headings.packing_options}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.packing_cam_enabled}}</label>

            <div class="controls col-sm-3 col-md-2 form-inline">
              <div toggle-switch ng-model="scan_pack.settings.packing_cam_enabled" groov-click="update_settings()">
              </div>
              <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                popover-trigger="mouseenter" groov-popover="{{translations.tooltips.packing_cam_enabled}}"></i>
            </div>
          </div>
          <div class="row" ng-show="scan_pack.settings.packing_cam_enabled">
            <!-- <div class="col-xs-12">
              <div class="form-horizontal general-settings-form">
                <div class="form-group">
                  <label class="control-label col-sm-3">{{translations.labels.camera_option}}</label>
                  <div class="controls">
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.camera_option" value="photo"
                        ng-change="update_settings()"> Photo
                    </label>
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.camera_option" value="video"
                        ng-change="update_settings()"> Video
                    </label>
                  </div>
                </div>
                <div class="form-group" ng-show="scan_pack.settings.camera_option=='photo'">
                  <label class="control-label col-sm-3">{{translations.labels.capture_event}}</label>
                  <div class="controls">
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.packing_option" value="before_packing"
                        ng-change="update_settings()"> Before Packing
                    </label>
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.packing_option" value="after_packing"
                        ng-change="update_settings()"> After Packing
                    </label>
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.packing_option" value="after_post_scan_packing_1"
                        ng-change="update_settings()"> After Post Scanning Option #1
                    </label>
                    <label class="control-label">
                      <input type="radio" ng-model="scan_pack.settings.packing_option" value="after_post_scan_packing_2"
                        ng-change="update_settings()"> After Post Scanning Option #2
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="control-label col-sm-3">{{translations.labels.choice_of_resolution}}</label>
                  <div class="controls col-sm-4 form-inline" dropdown>
                    <button class="dropdown-toggle groove-button label label-default"
                      ng-class="{'label-success':scan_pack.settings.resolution == 100, 'label-warning':scan_pack.settings.resolution ==80,'label-danger':scan_pack.settings.resolution ==50}">
                      <span ng-show="scan_pack.settings.resolution == 100" translate>Full</span>
                      <span ng-show="scan_pack.settings.resolution == 80" translate>Good</span>
                      <span ng-show="scan_pack.settings.resolution == 50" translate>Medium</span>
                      <span ng-show="scan_pack.settings.resolution == 25" translate>Low</span>
                    </button>
                    <ul class="dropdown-menu" role="menu">
                      <li><a class="dropdown-toggle" ng-click="change_resolution(100)" translate>Full</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="change_resolution(80)" translate>Good</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="change_resolution(50)" translate>Medium</a>
                      </li>
                      <li><a class="dropdown-toggle" ng-click="change_resolution(25)" translate>Low</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div> -->
            <div class="form-group">
              <label class="control-label col-sm-3">{{translations.labels.capture_image_option}}</label>

              <div class="controls col-sm-3 col-md-2 form-inline">
                  <div class="controls col-md-7">
                  <select class="form-control input-style" ng-model="scan_pack.settings.capture_image_option" ng-change="update_settings()">
                    <option ng-repeat="x in cam_options" value="{{x.value}}" ng-selected="x.value == users.single.switch_language">{{x.name}}</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="control-label col-sm-3">{{translations.labels.email_customer_option}}</label>

              <div class="controls col-sm-3 col-md-2 form-inline">
                <div toggle-switch ng-model="scan_pack.settings.email_customer_option" groov-click="update_settings()">
                </div>
                <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;"
                  popover-trigger="mouseenter" groov-popover="{{translations.tooltips.email_customer_option}}"></i>
              </div>
            </div>

            <div style="border: 1px solid grey;border-radius: 10px;padding: 20px;">
              <div ng-show="scan_pack.settings.email_customer_option">
                <h4>Packing Details Email</h4><br>
                <div class="form-horizontal">
                  <fieldset>
                    <div class="form-group">
                      <label class="control-label col-sm-2">{{translations.labels.email_reply}}</label>
                      <div class="controls col-sm-8">
                        <input type="text" class="form-control input-style" ng-model="scan_pack.settings.email_reply" placeholder="Email address"
                          ng-blur="update_settings()" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="control-label col-sm-2">{{translations.labels.email_subject}}</label>
                      <div class="controls col-sm-8">
                        <input type="text" class="form-control input-style" ng-model="scan_pack.settings.email_subject"
                          ng-blur="update_settings()" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="control-label col-sm-2">{{translations.labels.email_insert_dropdown}}</label>
                      <div class="controls col-sm-4 form-inline d-flex align-items-start" >
                        <button class="dropdown-toggle groove-button label label-success" style="margin-right: 5px;" ng-click="update_insert_txt_in_editor('order_number', 'email_page')">Order Number
                        </button>
                        <button class="dropdown-toggle groove-button label label-success" ng-click="update_insert_txt_in_editor('customer_page_url', 'email_page')">Customer Page Url
                        </button>
                      </div>
                    </div>
                    <div class="form-group">
                      <div class="controls col-sm-10">
                        <div text-angular id="email_page" ng-model="scan_pack.settings.email_message" ng-blur="update_settings()"></div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="control-label col-sm-2">{{translations.labels.email_logo}}</label>
                      <div class="col-sm-4">
                        <input name="email_logo" file-upload type="file" id="email_logo" value="Select" ng-blur="update_packing_cam_image()"
                          required />
                      </div>
                      <div class="col-sm-4" ng-show="scan_pack.settings.email_logo">
                        <img height="100" width="100" src="{{scan_pack.settings.email_logo}}"/>
                      </div>
                    </div>
                  </fieldset>
                </div>
                <br><br>
              </div>
              <h4>Packing Details Customer Page</h4><br>
              <div class="form-horizontal">
                <fieldset>
                  <div class="form-group">
                    <label class="control-label col-sm-2">{{translations.labels.email_insert_dropdown}}</label>
                    <div class="controls col-sm-4 form-inline">
                      <button class="dropdown-toggle groove-button label label-success" ng-click="update_insert_txt_in_editor('order_number', 'customer_page')">Order Number
                      </button>
                    </div>
                  </div>
                  <div class="form-group">
                    <div class="controls col-sm-10">
                      <div text-angular id="customer_page" ng-model="scan_pack.settings.customer_page_message" ng-blur="update_settings()">
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="control-label col-sm-2">{{translations.labels.customer_page_logo}}</label>
                    <div class="col-sm-4">
                      <input name="customer_page_logo" file-upload type="file" id="customer_page_logo" value="Select" ng-blur="update_packing_cam_image()"
                        required />
                    </div>
                    <div class="col-sm-4" ng-show="scan_pack.settings.customer_page_logo">
                      <img height="100" width="100" src="{{scan_pack.settings.customer_page_logo}}"/>
                    </div>
                  </div>
                  <br>
                  <div class="form-group">
                    <label class="control-label col-sm-2">{{translations.labels.scanning_log}}</label>
                    <div>
                      <div toggle-switch ng-model="scan_pack.settings.scanning_log" groov-click="update_settings()">
                      </div>
                    </div>
                  </div>
                </fieldset>
              </div>
            </div>
          </div>
      </div>
      </fieldset>
    </div>
  </div><br>
</div>


<div class="row">
  <div class="col-xs-12">
    <h3>{{translations.headings.feedback}}</h3>

    <div class="form-horizontal general-settings-form">
      <fieldset>
        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.show_success_image}}</label>

          <div class="controls col-sm-2 col-md-1 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.show_success_image" groov-click="update_settings()"></div>
          </div>
          <div class="col-sm-4" ng-show="scan_pack.settings.show_success_image">
            <label class="control-label col-sm-1">{{translations.labels.for}}</label>

            <div class="col-sm-5">
              <input class="form-control input-style" type="number" step="0.1" min="0.3" ng-blur="update_settings()"
                ng-model="scan_pack.settings.success_image_time" />
            </div>
            <label class="control-label col-sm-1">{{translations.labels.seconds}}</label>
          </div>
          <div class="info col-sm-2">
            <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="{{translations.tooltips.feedback}}"></i>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.show_fail_image}}</label>

          <div class="controls col-sm-2 col-md-1 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.show_fail_image" groov-click="update_settings()"></div>
          </div>
          <div class="col-sm-4" ng-show="scan_pack.settings.show_fail_image">
            <label class="control-label col-sm-1">{{translations.labels.for}}</label>

            <div class="col-sm-5">
              <input class="form-control input-style" type="number" step="0.1" min="0.3" ng-blur="update_settings()"
                ng-model="scan_pack.settings.fail_image_time" />
            </div>
            <label class="control-label col-sm-1">{{translations.labels.seconds}}</label>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.show_order_complete_image}}</label>

          <div class="controls col-sm-2 col-md-1 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.show_order_complete_image" groov-click="update_settings()">
            </div>
          </div>
          <div class="col-sm-4" ng-show="scan_pack.settings.show_order_complete_image">
            <label class="control-label col-sm-1">{{translations.labels.for}}</label>

            <div class="col-sm-5">
              <input class="form-control input-style" type="number" step="0.1" min="0.3" ng-blur="update_settings()"
                ng-model="scan_pack.settings.order_complete_image_time" />
            </div>
            <label class="control-label col-sm-1">{{translations.labels.seconds}}</label>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.play_success_sound}}</label>

          <div class="controls col-sm-2 col-md-1 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.play_success_sound" groov-click="update_settings()"></div>
          </div>
        </div>
        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.play_fail_sound}}</label>

          <div class="controls col-sm-2 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.play_fail_sound" groov-click="update_settings()"></div>
          </div>
        </div>

        <div class="form-group">
          <label class="control-label col-sm-3">{{translations.labels.play_order_complete_sound}}</label>

          <div class="controls col-sm-2 form-inline">
            <div toggle-switch ng-model="scan_pack.settings.play_order_complete_sound" groov-click="update_settings()">
            </div>
          </div>
        </div>
      </fieldset>
    </div>
  </div>
</div>
<!-- Multi-Box settings -->
    <div class="row" ng-show="general_settings.single.is_multi_box">
    <div class="col-xs-12">
      <h3>{{translations.headings.multi_box_option}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.multi_box_shipment}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.multi_box_shipments"
                   groov-click="update_general_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.multi_box_shipment}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.per_box_packing_slip}}</label>

            <div class="controls col-sm-3" dropdown>
              <button class="dropdown-toggle groove-button">
                <span ng-show="general_settings.single.per_box_packing_slips =='when_order_is_complete'">{{translations.labels.print_when_order_complete}}</span>
                <span
                  ng-show="general_settings.single.per_box_packing_slips =='when_new_boxes_are_started'">{{translations.labels.print_when_new_box}}</span>
                <span ng-show="general_settings.single.per_box_packing_slips =='manually'">{{translations.labels.manually_print}}</span>
                <span class="caret"></span>
              </button>

              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_packing_slips','when_order_is_complete')">{{translations.labels.print_when_order_complete}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_packing_slips','when_new_boxes_are_started')">{{translations.labels.print_when_new_box}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_packing_slips','manually')">{{translations.labels.manually_print}}</a></li>
              </ul>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.headings.per_box_shipping_label_creation}}</label>

            <div class="controls col-sm-3" dropdown>
              <button class="dropdown-toggle groove-button">
                <span ng-show="general_settings.single.per_box_shipping_label_creation =='per_box_shipping_label_creation_none'">{{translations.labels.per_box_shipping_label_creation_none}}</span>
                <span ng-show="general_settings.single.per_box_shipping_label_creation =='per_box_shipping_label_creation_after_box'">{{translations.labels.per_box_shipping_label_creation_after_box}}</span>
                <span ng-show="general_settings.single.per_box_shipping_label_creation =='per_box_shipping_label_creation_after_order'">{{translations.labels.per_box_shipping_label_creation_after_order}}</span>
                <span class="caret"></span>
              </button>

              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_shipping_label_creation', 'per_box_shipping_label_creation_none')">{{translations.labels.per_box_shipping_label_creation_none}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_shipping_label_creation', 'per_box_shipping_label_creation_after_box')">{{translations.labels.per_box_shipping_label_creation_after_box}}</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('per_box_shipping_label_creation', 'per_box_shipping_label_creation_after_order')">{{translations.labels.per_box_shipping_label_creation_after_order}}</a></li>
              </ul>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
</div>
