
<div class="col-lg-10 col-sm-10 general-settings">
  <!-- Printing Labels -->
  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.product_labels}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.product_barcode_label_size}}</label>
            <div class="controls col-sm-2" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.product_barcode_label_size}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('product_barcode_label_size','3 x 1')">3" x 1"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_barcode_label_size','2 x 1')">2" x 1"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('product_barcode_label_size','1.5 x 1')">1.5" x 1"</a></li>
              </ul>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.product_barcode_label_size}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_primary_bin_loc_in_barcodeslip}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.show_primary_bin_loc_in_barcodeslip"
                   groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.show_primary_bin_loc_in_barcodeslip}}"></i>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.show_sku_in_barcodeslip}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.show_sku_in_barcodeslip"
                   groov-click="update_settings()"></div>
            </div>
          </div>
        </fieldset>
      </div> 
    </div>
  </div>
  <!-- Printing Slips -->
    <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.packing_slips}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.packing_slip_size}}</label>

            <div class="controls col-sm-2" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.packing_slip_size}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 6')">Standard 4" x 6"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','8.5 x 11')">8.5" x 11"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','Custom 4 x 6')">Custom 4" x 6"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 4')">Custom 4" x 4"</a></li>
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_size','4 x 2')">Custom 4" x 2"</a></li>
              </ul>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.packing_slip_size}}"></i>
            </div>
          </div>
          <div class="form-group" ng-show="general_settings.single.packing_slip_size=='8.5 x 11'">
            <label class="control-label col-sm-3">{{translations.labels.packing_slip_orientation}} </label>

            <div class="controls col-sm-3" dropdown>
              <button class="dropdown-toggle groove-button">
                {{general_settings.single.packing_slip_orientation}}
                <span class="caret"></span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_orientation','portrait')">{{translations.labels.portrait}}</a>
                </li>
                <li><a class="dropdown-toggle" ng-click="change_opt('packing_slip_orientation','landscape')">{{translations.labels.landscape}}</a>
                </li>
              </ul>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.packing_slip_message_to_customer}}</label>

            <div class="controls col-sm-7">
              <div ng-controller="generalSettingsCtrl">
                <div text-angular ng-model="general_settings.single.packing_slip_message_to_customer"
                     ng-blur="update_settings()"></div>
              </div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.packing_slip_message_to_customer}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.html_print}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.html_print"
                   groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.html_print}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.display_kit_parts}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.display_kit_parts"
                   groov-click="update_settings()"></div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.display_kit_parts}}"></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">Create Barcode at Import</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.create_barcode_at_import"
                   groov-click="update_settings()"></div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.truncate_order_number_in_packing_slip}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.truncate_order_number_in_packing_slip"
                   groov-click="update_settings()"></div>
            </div>
            <div class="col-sm-6" ng-show="general_settings.single.truncate_order_number_in_packing_slip">
              <label class="col-sm-3 col-md-3">{{translations.labels.truncated_string}}</label>

              <div class="col-sm-8 col-md-6">
                <input class="form-control input-style" type="text" ng-blur="update_settings()"
                  ng-model="general_settings.single.truncated_string" ng-trim="false" />
              </div>
            </div>
            <div class="info col-sm-1">
              <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
                groov-popover="{{translations.tooltips.truncate_order_number_in_packing_slip}}"></i>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
  <!-- Direct Printing Options -->
  <div class="row" ng-show="general_settings.single.direct_printing_options">
    <div class="col-xs-12">
      <h3>
        {{translations.headings.direct_printing_options}}
        <i class="info icon-large glyphicon glyphicon-info-sign" style="padding-left:5px;" popover-trigger="mouseenter" groov-popover="{{translations.tooltips.direct_printing_tooltip}}"></i>
      </h3>
      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.print_post_scanning_barcodes}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.print_post_scanning_barcodes"
                groov-click="update_settings()">
              </div>
              <button class="modal-save-button pull-right" ng-show="general_settings.single.print_post_scanning_barcodes"
                ng-click="remove_localstorage_item('user_selected_printer_order_barcode_label_' + general_settings.single.product_barcode_label_size)">Reset</button>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.print_packing_slips}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.print_packing_slips" groov-click="update_settings()">
              </div>
              <button class="modal-save-button pull-right" ng-show="general_settings.single.print_packing_slips"
               ng-click="remove_localstorage_item('user_selected_printer_packing_slip_' + general_settings.single.packing_slip_size)">Reset</button>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.print_ss_shipping_labels}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.print_ss_shipping_labels"
                groov-click="update_settings()"></div>
              <button class="modal-save-button pull-right" ng-show="general_settings.single.print_ss_shipping_labels"
                ng-click="remove_localstorage_item('user_selected_printer_ss_shipping_label_' + general_settings.single.packing_slip_size)">Reset</button>

            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.print_product_barcode_labels}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.print_product_barcode_labels"
                groov-click="update_settings()">
              </div>
              <button class="modal-save-button pull-right" ng-show="general_settings.single.print_product_barcode_labels"
                ng-click="remove_localstorage_item('user_selected_printer_product_barcode_labels_' + general_settings.single.product_barcode_label_size)">Reset</button>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.print_product_receiving_labels}}</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="general_settings.single.print_product_receiving_labels"
                groov-click="update_settings()">
              </div>
              <button class="modal-save-button pull-right" ng-show="general_settings.single.print_product_receiving_labels"
                ng-click="remove_localstorage_item('user_selected_printer_product_receiving_labels')">Reset</button>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
</div>
