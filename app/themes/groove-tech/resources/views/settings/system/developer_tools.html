
<div class="col-lg-12 col-sm-10 general-settings" ng-if="general_settings.single.enable_developer_tools">
  <!-- Developer Tools -->
  <div class="row">
    <div class="col-xs-12">
      <h3>{{translations.headings.api_keys}}</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">{{translations.labels.api_key}}</label>
            <div class="col-lg-6 col-md-6">
              <div ng-if="general_settings.single.api_key">
                <div class="input-group">
                  <input class="form-control input-style" type="text" ng-model="general_settings.single.api_key.token" readonly ng-trim="false" />
                  <span class='fa fa-copy datagrid-copy input-group-addon' ng-click="copied(general_settings.single.api_key.token)"></span>
                </div>
                <div>
                  <button class="btn btn-danger label" ng-click="delete_api_key(general_settings.single.api_key.id)">{{translations.labels.delete_api_key}}</button>
                  <button class="btn btn-success label" ng-click="regenerate_api_key()">{{translations.labels.regenerate_api_key}}</button>
                </div>
              </div>

              <div ng-if="general_settings.single.api_key === null">
                <button class="btn btn-success" ng-click="regenerate_api_key()">{{translations.labels.generate_api_key}}</button>
              </div>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>

  <!-- create webhooks -->
  <div class="row"></div>
    <div class="col-xs-12">
      <h3>{{translations.headings.create_webhook}}</h3>
      <fieldset>
        <table class="table table-hover table-bordered table-striped table-condensed">
          <caption>Send JSON notifications about GroovePacker events to a URL</caption>
          <thead>
          <tr>
            <th width="3%">
              <div></div>
            </th>
            <th width="17%">
              <div>Events</div>
            </th>
            <th width="60%">
              <div>URLs</div>
            </th>
            <th width="20%">
              <div>Screct Keys</div>
            </th>
          </tr>
          </thead>
          <tbody ng-show="general_settings.single.webhooks.length > 0">
          <tr ng-repeat="webhook in general_settings.single.webhooks" ng-click="setSelected(webhook.id)"
              ng-class="{selected: webhook.checked}">
            <td></td>
            <td class="media-heading pack_link" ng-click="webhook_details(webhook)">{{webhook.event}}</td>
            <td>{{webhook.url}}</td>
            <td>{{webhook.secret_key}}</td>
          </tr>
          </tbody>
        </table>

        <button class="modal-save-button" ng-click="openNewWebhookForm()">Create Webhook</button>
        <button ng-click="deleteWebhooks()" class="modal-remove-button">Remove Selected</button>
      </fieldset>
    </div>
  </div>
</div>
