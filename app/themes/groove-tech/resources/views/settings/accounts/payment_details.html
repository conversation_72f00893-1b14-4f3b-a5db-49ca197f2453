<div class="col-lg-10 col-sm-10 general-settings">
  <!-- Payment Cards -->
  <div class="row">
    <div class="col-xs-12">
      <fieldset>
        <h3>{{translations.headings.credit_cards}}</h3>
        <table class="table table-hover table-bordered table-striped table-condensed">
          <thead>
            <tr>
              <th>
                <div>Card Number</div>
              </th>
              <th>
                <div>Expiry Month</div>
              </th>
              <th>
                <div>Expiry Year</div>
              </th>
              <th>
                <div>Card Type</div>
              </th>
              <th>
                <div>Default</div>
              </th>
            </tr>
          </thead>
          <tbody ng-show="show_table_data == 1">
            <tr ng-repeat="payment in payments.list" ng-click="setSelected(payment.id)"
                ng-class="{selected: payment.checked}">
              <td>x-{{payment.card.last4}}</td>
              <td ng-bind="payment.card.exp_month"></td>
              <td ng-bind="payment.card.exp_year"></td>
              <td ng-bind="payment.card.brand"></td>
              <td><span class="label label-success" ng-show="payment.id == payments.single"> Default</span></td>
            </tr>
          </tbody>
        </table>

        <h3>Bank Account Details</h3>
        <table class="table table-hover table-bordered table-striped table-condensed">
          <thead>
            <tr>
              <th>
                <div>Account Number</div>
              </th>
              <th>
                <div>Routing Number</div>
              </th>
              <th>
                <div>Account Holder Name</div>
              </th>
              <th>
                <div>Account Holder Type</div>
              </th>
              <th>
                <div>Country</div>
              </th>
              <th>
                <div>Default</div>
              </th>
            </tr>
          </thead>
          <tbody ng-show="show_table_data == 1">
            <tr ng-repeat="bank in bankAccounts" ng-click="setSelected(bank.id)"
                ng-class="{selected: bank.checked}">
              <td>x-{{bank.us_bank_account.last4}}</td>
              <td ng-bind="bank.us_bank_account.routing_number"></td>
              <td ng-bind="bank.billing_details.name"></td>
              <td ng-bind="bank.us_bank_account.account_holder_type"></td>
              <td ng-bind="bank.billing_details.address.country"></td>
              <td><span class="label label-success" ng-show="bank.id == payments.single"> Default</span></td>
            </tr>
          </tbody>
        </table>

        <button class="modal-save-button" ng-click="openBankForm()">Add Bank Details</button>
        <button class="modal-save-button" ng-click="openNewForm()">Add a new card</button>
        <button ng-click="setAsDefault()" class="modal-save-button">Make it default</button>
        <button ng-click="deleteCard()" class="modal-remove-button">Remove selected</button>
      </fieldset>
    </div>
  </div>
</div>
