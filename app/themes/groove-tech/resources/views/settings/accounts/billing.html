
<div class="col-lg-12 col-sm-10 general-settings">
  <!-- Account Billing -->
  <div class="row"></div>
    <div class="col-xs-12">
      <h3>{{translations.headings.invoices}}</h3>
      <fieldset>
        <table class="table table-hover table-bordered table-striped table-condensed" ng-if="status == true && invoice_data.length > 0">
          <thead>
          <tr>
            <th width="28%">
              <div>INVOICE NUMBER</div>
            </th>
            <th width="28%">
              <div>CUSTOMER</div>
            </th>
            <th width="28%">
              <div>CREATED</div>
            </th>
            <th>
              <div>Download PDF</div>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr ng-repeat="invoice in invoice_data">
            <td>{{invoice.invoice_number}}</td>
            <td>{{invoice.customer_email}}</td>
            <td>{{invoice.created}}</td>
            <td>
              <a href="{{invoice.pdf_url}}" class="text-success">
                <i class="fa fa-download fa-lg"></i>
              </a>
            </td>
          </tr>
          </tbody>
        </table>
        <h4 ng-if="status == false" class="text-center">{{error_message}}</h4>
        <h4 ng-if="status == true && invoice_data.length == 0" class="text-center">Invoice not found</h4>
      </fieldset>
    </div>
  </div>
</div>
