<div class=" general-settings" nng-controller="modifyPlanCtrl">
  <div class="container-fluid">
   <div class="row">
      <h3 style="font-size: 24px; margin-left: 25px; color: rgba(0, 0, 0, 0.6);" class="text-center"><b>Modify Your Plan</b></h3><br/>
  </div>
  <div class="row">
    <div class="col-md-10 col-md-offset-1">
    <div class="col-md-5 col-md-offset-1 total-user-section">
      <i class="groove-fa fa fa-users fa-5x"></i>
      <ul>
        <li class="left">
          <div class="btn-group-vertical">
            <button type="button" ng-click="calculate('+')"  class="btn transparent no_of_users_asc">
              <i class="fa fa-chevron-up arrow bt"  aria-hidden="true" ng-style="{ 'color' : modify.allow_delete  ?  'red' : '#17C017'}"></i>
            </button>
            <button type="button" class="btn transparent white-bg">
              <span class="no_of_users_label">{{modify.no_of_users}}</span>
              <input type="hidden" class="no_of_users" ng-model="modify.no_of_users" name="no_of_users" value="{{modify.no_of_users}}" />
            </button>
            <button type="button" ng-click="calculate('-')"  class="btn transparent no_of_users_dec">
              <i class="fa fa-chevron-down arrow bt " aria-hidden="true" ng-style="{ 'color' : modify.allow_delete  ?  'red' : '#17C017'}"></i>
            </button>
            </div>
          <!-- <i class="fa fa-chevron-up arrow" aria-hidden="true"></i>
          </li>
          <i class="fa fa-chevron-down arrow" aria-hidden="true"></i> -->
        <li class="right">
            <h3>Total Users</h3>
            <p>$50/User/Month</p>
        </li>
      </ul>
      <h6>Additional users can be added as needed.The minimum billing period for a user is 1 month.</h6>
    </div>
    <div class="col-md-5 col-md-offset-1 total-user-section-right">
      <i class="fa fa-user user-icon"></i>

      <button class="annual check_annual" ng-click="annual(modify.is_annual)"  ng-style="{ 'background' : modify.is_annual ?  '#808080' : '#17C017'}">Save $<span class="discount_annually">{{modify.discount}}</span></button>
      <h3>Lock in a 10% Discount</h3>
      <p>With an Annual Plan</p>
      <h5>This is a pre-payment for one year of service with the number of users currently on the plan.Pre-payed users can not be removed.</h5>
    </div>
    <div class="footer_text">
      <h1>Plan Total $ <span class="price_label ng-binding">{{modify.price}}</span>/{{modify.calender}}<button ng-click="update_plan()" class="btn apply_btn">Apply Changes & Update Plan</button>
        <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
          groov-popover="A request to remove 1 user will be submitted. Please be sure that you have removed the un-needed user(s) from the Users list. In order for the user(s) to be removed from the plan, the number of users in the list must not exceed the number of users on the plan."></i>
      </h1>
      <p>Please note: Custom maintenance, legacy pricing plans and other plan additions besides users may affect the plan total. </p>
    </div>
  </div>
  </div>
  </div>
</div>
<style type="text/css">
  .total-user-section i.user-icon {
     font-size: 45px;
     display: flow-root;
     padding: 10px 0;
     text-align: center;
  }
 .footer_text h1 span {
     margin-right: 10px;
  }
   
  .transparent {
    background: transparent;
    padding: 0px;
  }
   i.fa.fa-user.user-icon {
    font-size: 45px;
    display: flow-root;
    padding: 10px 0;
    text-align: center;
  }
   
  .total-user-section li input[type="text"] {
    width: 50px;
    font-size: 54px;
    border-radius: 3px;
    box-shadow: none;
    border: 2px solid #666;
    outline: inherit;
    font-weight: bold;
    text-align: center;
    line-height: 56px;
    height: 55px;
    margin: 0;
    vertical-align: middle;
    position: relative;
    padding: 5px;
  }
   .total-user-section li i .fa.arrow {
     display: block;
     width: 45px;
     font-size: 27px;
     font-family: fontawesome;
     color: green;
     line-height: 7px;
  }
   .total-user-section li.left {
       display: inline-block;
  }
  .total-user-section {
    text-align: center;
  }
 .total-user-section h3 {
     margin: 0px;
        font-weight: bold;
  }
   .total-user-section li.right {
     width: 55%;
     display: inline-block;
     font-weight: bold;
  }
 .total-user-section h6 {
     text-align: center;
     font-size: 15px;
     font-weight: 600;
  }
   .total-user-section-right h3 {
       font-weight: 600;
  }
   .total-user-section-right h6 {
     background: #17C017;
     color: #fff;
     text-align: center;
     max-width: 40%;
     padding: 8px;
     border-radius: 2px;
     font-size: 18px;
     margin: 10px auto;
  }
 .total-user-section-right {
     text-align: center;
  }
   .total-user-section-right h5 {
       line-height: 20px;
       font-weight: 600;
  }
 .footer_text {
     width: 100%;
     float: left;
     text-align: center;
  }
 .footer_text h1 {
     font-size: 18px;
  }
 .apply_btn {
     background: #17C017;
     color: #fff;
     font-size: 18px;
      margin-left: 20px;
    border-radius: 7px;
  }
 i.fa.fa-chevron-up.arrow {
     color: #17C017;
     font-size: 24px;
  }
 i.fa.fa-chevron-down.arrow {
     color: #17C017;
     font-size: 24px;
  }
  .total-user-section-right p {
      font-weight: 600;
  }
  .footer_text h1 {
      font-weight: 600;
  }
  .footer_text p {
      font-weight: 600;
  }
  .total-user-section ul {
    margin: 0px;
    padding: 0px;
    margin-top: 30px;
  }
  
  .white-bg {
    background: #fff;
    border: 1px solid #000;
    box-shadow: 0px 1px 2px #000;
    padding: 8px 15px;
    font-size: 28px;
    font-weight: bold;
  }
  .check_annual{
    background: #17C017;
     color: #fff;
     font-size: 18px;
      margin-left: 20px;
    border-radius: 7px;
  }
</style>

