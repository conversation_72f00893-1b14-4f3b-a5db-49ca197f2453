<!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '3608005302579688');
  fbq('track', 'PageView');
  </script>
  <noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=3608005302579688&ev=PageView&noscript=1"
  /></noscript>
  <!-- End Facebook Pixel Code -->

<div class="container-fluid">
  <div class="row bottom-well" style="width: 90vw; display: flex;">
    <div class="col-lg-2 col-md-2">
      <div class="row">
        <accordion>
          <accordion-group is-open="tabs[0].open">
            <accordion-heading>
              <i></i>
              <a ui-sref="settings.stores">Store Settings</a>
            </accordion-heading>
            <li ng-class="{active:current_page=='show_stores'}" ui-sref="settings.stores"
            ui-sref-opts="{reload: true}">Show All</li>
            <li ui-sref="settings.stores.create" ng-show="settings.stores.allow">Create Store</li>
            <li class="disabled" ng-hide="settings.stores.allow" popover-trigger="mouseenter"
                groov-popover="You have reached your store limit">Create Store
            </li>

            <li ng-click='import_all_orders()'>Import Orders</li>
          </accordion-group>
          <accordion-group is-open="tabs[1].open">
            <accordion-heading>
              <a ui-sref="settings.users">Users & Permissions</a>
            </accordion-heading>
            <li ng-class="{active:current_page=='show_users'}" ui-sref="settings.users"
            ui-sref-opts="{reload: true}">Show All</li>
            
            <!-- Create User button - always show, will handle slot checking internally -->
            <li ui-sref="settings.users.create">Create User</li>
          </accordion-group>
          <accordion-group is-open="tabs[2].open">
            <accordion-heading>
              <a ui-sref="settings.system.general">System Settings</a>
            </accordion-heading>
            <li ng-class="{active: current_page=='general'}" ui-sref="settings.system.general"
            ui-sref-opts="{reload: true}">General</li>
            <li ng-class="{active: current_page=='scan_pack'}" ui-sref="settings.system.scan_pack"
            ui-sref-opts="{reload: true}">Scan &amp; Pack</li>
            <li ng-class="{active: current_page=='printing'}" ui-sref="settings.system.printing"
            ui-sref-opts="{reload: true}">Printing Settings</li>
            <li ng-if="general_settings.single.enable_developer_tools" ng-class="{active: current_page=='developer_tools'}" ui-sref="settings.system.developer_tools"
            ui-sref-opts="{reload: true}">Developer Tools</li>
          </accordion-group>
          <accordion-group is-open="tabs[3].open">
            <accordion-heading>
              <a ui-sref="settings.export.backup_restore">Backup & Export</a>
            </accordion-heading>
            <li ng-class="{active:current_page =='backup'}" ui-sref="settings.export.backup_restore"
            ui-sref-opts="{reload: true}">Backup and
              Restore
            </li>
            <li ng-class="{active:current_page =='order_exception'}" ng-show="current_user.can('view_packing_ex')"
                ui-sref="settings.export.order_exception" ui-sref-opts="{reload: true}">Exceptions Export
            </li>
            <li ng-class="{active:current_page =='order_export'}" ng-show="current_user.can('view_packing_ex')"
                ui-sref="settings.export.order_export" ui-sref-opts="{reload: true}">Order Export
            </li>
            <li ng-class="{active:current_page =='stats_export'}" ng-show="current_user.can('view_packing_ex') && general_settings.single.groovelytic_stat"
                ui-sref="settings.export.stats_export" ui-sref-opts="{reload: true}">User Stats Export
            </li>
             <li ng-class="{active:current_page =='daily_packed'}" ng-show="general_settings.single.daily_packed_toggle && current_user.can('view_packing_ex')"
                ui-sref="settings.export.daily_packed" ui-sref-opts="{reload: true}">Daily Packed % Report
            </li>
            <li ng-class="{active:current_page =='serial_export'}" ng-show="current_user.can('view_packing_ex')"
                ui-sref="settings.export.serial_export" ui-sref-opts="{reload: true}">Serial Export
            </li>
          </accordion-group>
          <accordion-group is-open="tabs[4].open">
            <accordion-heading>
            <a ui-sref="settings.accounts.card_details">Account Settings</a>
            </accordion-heading>
            <li ng-class="{active:current_page=='show_card_details'}" ui-sref="settings.accounts.card_details"
            ui-sref-opts="{reload: true}">Payment Method
            </li>
            <li ng-class="{active:current_page=='modify_plan'}" ui-sref="settings.accounts.modify_plan">Modify Plan
            </li>
            <li ng-class="{active:current_page=='cost_calculator'}" ui-sref="settings.accounts.cost_calculator">Cost Calculator
            </li>
            <li ng-class="{active:current_page=='billing'}" ui-sref="settings.accounts.billing">Billing
            </li>
          </accordion-group>
        </accordion>
      </div>
    </div>
    <div ui-view style="width: 84%;"></div>
  </div>
</div>
