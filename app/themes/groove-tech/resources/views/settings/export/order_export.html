<div class="col-lg-10 col-sm-10 general-settings">
  <div class="row">
    <div class="col-xs-12">
      <h3>Order Export Options</h3>

      <div class="form-horizontal general-settings-form">
        <fieldset>
          <div class="form-group">
            <label class="control-label col-sm-3">Auto Email Export</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="export_settings.single.auto_email_export"
                   groov-click="update_export_settings()"></div>
            </div>
          </div>
          <div ng-show="export_settings.single.auto_email_export=='1'">
            <div class="form-group">
              <div class="controls col-sm-offset-3 col-sm-5">
                <input type="text" class="form-control input-style" ng-model="export_settings.single.order_export_email"
                       placeholder="Email address" ng-blur="update_export_settings()"/>
              </div>
            </div>
            <div class="form-group">
              <label class="control-label col-sm-3">Time for sending order export email</label>

              <div class="controls col-sm-7">
                <timepicker ng-model="export_settings.single.time_to_send_export_email" ng-change="show_button=true" show-meridian="true" style="display: inline-block;vertical-align: middle;">
                </timepicker>
                <span style="display: inline-block;vertical-align: middle;padding-left: 13px;">
                  Current Time: {{export_settings.current_time}}
                  <i class="groove-fa fa fa-clock-o" popover-trigger="mouseenter" groov-popover="{{'To adjust the time zone please <a ng-click=scrollup() href=#/settings/system/general> click here. </a>'}}"></i>
                </span>
              </div>

            </div>
            <div class="row" ng-show="show_button">
              <div class="col-sm-4 col-sm-offset-3">
                <button class="modal-save-button" ng-click="update_export_settings()">Update</button>
              </div>
            </div>
            <div class="form-group">
              <label class="control-label col-sm-3">Send Email On</label>

              <div class="controls col-sm-9 form-inline">
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_mon"
                         ng-change="update_export_settings()"> mon
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_tue"
                         ng-change="update_export_settings()"> tue
                </label>
                <label class="checkbox">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_wed"
                         ng-change="update_export_settings()"> wed
                </label>
              </div>
              <div class="controls col-sm-9 col-sm-offset-3 form-inline">
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_thu"
                         ng-change="update_export_settings()"> thu
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_fri"
                         ng-change="update_export_settings()"> fri
                </label>
                <label class="checkbox col-sm-2">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_sat"
                         ng-change="update_export_settings()"> sat
                </label>
                <label class="checkbox">
                  <input type="checkbox" ng-model="export_settings.single.send_export_email_on_sun"
                         ng-change="update_export_settings()"> sun
                </label>
              </div>
            </div>
            <div class="form-group">
              <label class="control-label col-sm-3">The email will include all orders scanned </label>

              <div class="controls col-sm-3 well-main " dropdown>
                <button class="dropdown-toggle groove-button label label-default" ng-class="{'label-success':export_settings.single.export_orders_option=='on_same_day',
                                    'label-warning':export_settings.single.export_orders_option=='since_last_export'}">
                  <span
                    ng-show="export_settings.single.export_orders_option=='on_same_day'">on the same calendar day</span>
                  <span
                    ng-show="export_settings.single.export_orders_option=='since_last_export'">since the last export</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                  <li><a class="dropdown-toggle" ng-click="change_option('export_orders_option','on_same_day')">on the
                    same calendar day</a></li>
                  <li><a class="dropdown-toggle" ng-click="change_option('export_orders_option','since_last_export')">since
                    the last export</a></li>
                </ul>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">FTP/SFTP Export</label>

            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="export_settings.single.auto_ftp_export"
                   groov-click="update_export_settings()"></div>
            </div>
          </div>
          <div class="col-md-offset-3" ng-show="export_settings.single.auto_ftp_export=='1'">
            <div class="form-group" ng-show="export_settings.single.auto_ftp_export">
              <label class="control-label col-sm-3 remove_padding_left">FTP/SFTP Address</label>
              <div class="controls col-sm-5 remove_padding_right remove_padding_left">
                <input type="search" class="form-control input-style" placeholder="ftp.yourserver.com/files/"
                  autocomplete="off" ng-model="export_settings.single.host" ng-blur="update_export_settings()" />
              </div>
              <label class="control-label ftp_align_text col-sm-1 remove_padding"><input type="radio" class="pull-left"
                  ng-model="export_settings.single.connection_method" value="ftp" checked="checked"
                  ng-change="update_export_settings()" />&nbsp;&nbsp;FTP</label>
              <label class="control-label ftp_align_text col-sm-2"><input type="radio" class="pull-left"
                  ng-model="export_settings.single.connection_method" value="sftp" ng-change="update_export_settings()">&nbsp;&nbsp;SFTP</label>
            </div>
            <div class="form-group" ng-show="export_settings.single.auto_ftp_export">
              <label class="control-label col-sm-3">Username</label>
              <div class="controls col-sm-3 remove_padding_left">
                <input type="search" class="form-control input-style col-sm-6" ng-model="export_settings.single.username"
                  autocomplete="off" ng-blur="update_export_settings()" />
              </div>
              <label class="control-label col-sm-3">Password</label>
              <div class="controls col-sm-3 remove_padding_left">
                <input type="password" class="form-control input-style" ng-model="export_settings.single.password"
                  autocomplete="off" ng-blur="update_export_settings()" />
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">Include Partially Scanned Orders</label>
            <div class="controls col-sm-2 form-inline">
              <div toggle-switch ng-model="export_settings.single.include_partially_scanned_orders"
                   groov-click="update_export_settings()"></div>
                   <i class="icon-large glyphicon glyphicon-info-sign" popover-trigger="mouseenter"
              groov-popover="Unless this switch is enabled only orders that have the scanned status are included."></i>
            </div>
          </div>

          <div class="form-group">
            <label class="control-label col-sm-3">Order export type</label>

            <div class="controls col-sm-3 form-inline" dropdown>
              <button class="dropdown-toggle groove-button label label-default" ng-class="{
                                'label-warning':export_settings.single.order_export_type=='order_with_serial_lot',
                                'label-success':export_settings.single.order_export_type=='include_all'}">
                <span
                  ng-show="export_settings.single.order_export_type=='do_not_include'">Do not include individual order items</span>
                <span ng-show="export_settings.single.order_export_type=='order_with_serial_lot'">Only include order items with Serial/Lot numbers</span>
                <span ng-show="export_settings.single.order_export_type=='include_all'">Include individual order items</span>
                <span ng-show="export_settings.single.order_export_type=='removed_only'">Only include orders with removed or added items</span>
                <span ng-show="export_settings.single.order_export_type=='partially_scanned_only'">Only include partially scanned orders</span>
              </button>
              <ul class="dropdown-menu" role="menu">
                <li><a class="dropdown-toggle" ng-click="change_option('order_export_type','include_all')" >Include individual order items</a></li>
                <li><a class="dropdown-toggle" ng-click="change_option('order_export_type','do_not_include')">Do not include individual order items</a></li>
                <li><a class="dropdown-toggle" ng-click="change_option('order_export_type','order_with_serial_lot')">Only include order items with Serial/Lot numbers</a></li>
                <li><a class="dropdown-toggle" ng-click="change_option('order_export_type','removed_only')">Only include orders with removed or added items</a></li>
                <!-- TODO: Implemented when GROOV-2630 completes -->
                <li><a class="dropdown-toggle" ng-click="change_option('order_export_type','partially_scanned_only')">Only include partially scanned orders</a></li>
              </ul>
            </div>
          </div>
          <div class="form-group col-sm-6">
            <label class="control-label">Start Date</label>

            <div class="container-fluid form-horizontal">
              <div class="row">
                <div class="control-label timepicker-label col-md-8">
                  <p class=" input-group">
                    <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy"
                           ng-model="exports.start.time" is-open="exports.start.open" ng-required="true"
                           close-text="Close"/>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default"
                                                    ng-click="open_picker($event,exports.start)"><i
                                              class="glyphicon glyphicon-calendar"></i></button>
                                        </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group col-sm-6">
            <label class="control-label">End Date</label>

            <div class="container-fluid form-horizontal">
              <div class="row">
                <div class="control-label timepicker-label col-md-8">
                  <p class=" input-group">
                    <input type="text" class="form-control" datepicker-popup="dd-MMMM-yyyy" ng-model="exports.end.time"
                           is-open="exports.end.open" ng-required="true" close-text="Close"/>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default"
                                                    ng-click="open_picker($event,exports.end)"><i
                                              class="glyphicon glyphicon-calendar"></i></button>
                                        </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="col-sm-2 text-center col-sm-offset-4">
            <button ng_click="download_csv('exports')" class="modal-save-button">Export Now</button>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
</div>

<style type="text/css">
  .control-label.timepicker-label .dropdown-menu{
    padding-bottom: 38px !important;
  }
</style>
