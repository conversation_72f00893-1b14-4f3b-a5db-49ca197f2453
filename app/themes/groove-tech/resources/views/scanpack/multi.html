<div class="row bottom-well" ng-if="fetching_label_related_data">
  <div class="pack_image">
    <img src="/assets/images/loading.gif" alt="Loading..." width="230px"/>
  </div>
</div>

<div class="row bottom-well" ng-if="fetching_label_related_data != true">
  <div class="pack_image" ng-if="scan_pack.settings.scan_pack_workflow == 'default'">
    <img ng-show="current_state == 'scanpack.rfo'" src="/assets/images/RFO_background.png" alt="RFO background"/>

    <h3 ng-show="current_state == 'scanpack.rfp.recording'">Please create the shipping label, adhere it to the package,
      and scan the tracking number to continue.</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.verifying'">Please scan the shipping label.</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.no_tracking_info'">Your Scan & Pack settings call for shipping label
      verification but no tracking information was imported with this order. Please scan your confirmation code or press
      enter to continue.</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.no_match'">The tracking number on the shipping label you just scanned
      does not match the tracking number imported with that order. Please verify that you have the correct shipping
      label and try your scan again. If you are aware of the issue and would like to proceed with the next order please
      scan your confirmation code to continue.</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.confirmation.order_edit'">Scan Confirmation Code with 'Order edit'
      permission</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.confirmation.product_edit'">Scan Confirmation Code with 'Product edit'
      permission</h3>

    <h3 ng-show="current_state == 'scanpack.rfp.confirmation.cos'">Scan Confirmation Code with 'Order edit'
      permission</h3>

    <div ui-view></div>
  </div>

  <div class="pack_image" ng-show="(scan_pack.settings.scan_pack_workflow == 'product_first_scan_to_put_wall' || scan_pack.settings.scan_pack_workflow == 'multi_put_wall') && scan_pack.settings.workflow_state == undefined">

    <div ng-show="current_user_data.tote_set == undefined">
      <h1><b>Please scan one of your {{scan_pack.settings.tote_identifier}}(s) to begin.</b></h1>
      <div class="row">
        <img src="/assets/images/tote-full-arrow.png" alt="Tote" style="width: 20%;padding: 30px 0px;"/>
      </div>
      <div class="row">
        <div class="col-md-4 col-md-offset-4">
          <input type="text" ng-model="data.input" ng-keydown="assign_user_tote_set($event)" autofocus="autofocus" tabindex="1"
          class="form-control search-box text-center" placeholder="Scan a {{scan_pack.settings.tote_identifier}} Barcode"/>
        </div>
      </div>

      <div ui-view></div>

    </div>

    <div ng-hide="current_user_data.tote_set == undefined">
      <div class="row text-right" ng-show="scan_pack.settings.scan_pack_workflow == 'multi_put_wall'" style="padding: 0px 10px 0px 0px;">
        <button class="" ng-click="launch_assign_user_tote_set()">{{scan_pack.settings.tote_identifier}} Set {{current_user_data.tote_set.name}}</button>
      </div>

      <h1><b>Please scan any item to continue</b></h1>
      
      <img src="/assets/images/cart-transparent-png.png" alt="Shopping Cart" style="width: 20%;"/>

      <div class="row">
        <div class="col-md-5 col-md-offset-4">
          <input type="text" ng-model="data.input" ng-keydown="input_enter($event)" autofocus="autofocus" tabindex="1"
          class="form-control search-box text-center" placeholder="Scan or Enter a Product Barcode"/>
        </div>
      </div>

      <div ui-view></div>
      <div class="scan_pack_success_fail row">
        <div class="middle col-xs-12" ng-show="scan_pack_state=='success'">
          <img class="col-xs-8 col-xs-offset-2" ng-src="{{scan_pack.scan_states.success.image.src}}" alt=""/>
        </div>
      </div>
    </div>
  </div>
</div>


