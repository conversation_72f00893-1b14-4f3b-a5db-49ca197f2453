<div class="row scan_pack_input scan_pack_title scanpack-bottom-well" ng-show="data.order.se_old_split_shipments.length"
  style="font-size: 20px;">
  <b>{{translations.labels.se_old_split_shipments}}</b>
  <div style="padding: 10px;">
    <div ng-repeat="shipment in data.order.se_old_split_shipments" style="font-weight: bold;">
      <div class=row>
        <div class="col-md-2">
          <i class="fa fa-trash" aria-hidden="true"
            ng-click="$event.preventDefault();delete_shipment_order('{{shipment.id}}')"></i>&ensp;
          <a href ng-click="$event.preventDefault();get_order_details('{{shipment.increment_id}}')"
            style="color: #4c864c;">{{shipment.increment_id}}</a>&ensp;
        </div>
        <div class="col-md-10">
          {{shipment.status}}
        </div>
      </div>
    </div>
    </br>
    <div class="text-center">
      <b>{{translations.labels.remove_shipment}}</b>
      <div class="shipment_handling_bar_1" ng-click="data.order.se_old_split_shipments = false">
        <b>{{translations.labels.remove_acknowledge_shipment}}</b>
      </div>
    </div>
  </div>
</div>

<div class="row scan_pack_input scan_pack_title scanpack-bottom-well"
  ng-show="data.order.se_old_combined_shipments.length" style="font-size: 20px;">
  <b>{{translations.labels.se_old_combined_shipments}}</b>
  <div style="padding: 10px;">
    <div ng-repeat="shipment in data.order.se_old_combined_shipments" style="font-weight: bold;">
      <div class=row>
        <div class="col-md-2">
          <i class="fa fa-trash" aria-hidden="true"
            ng-click="$event.preventDefault();delete_shipment_order('{{shipment.id}}')"></i>&ensp;
          <a href ng-click="$event.preventDefault();get_order_details('{{shipment.increment_id}}')"
            style="color: #4c864c;">{{shipment.increment_id}}</a>&ensp;
        </div>
        <div class="col-md-10">
          {{shipment.status}}
        </div>
      </div>
    </div>
    </br>
    <div class="text-center">
      <b>{{translations.labels.remove_shipment}}</b>
      <div class="shipment_handling_bar_1" ng-click="data.order.se_old_combined_shipments = false">
        <b>{{translations.labels.remove_acknowledge_shipment}}</b>
      </div>
    </div>
  </div>
</div>

<div class="row scan_pack_input scan_pack_title" ng-show="data.order.se_all_shipments.present" style="font-size: 20px;">
  <div class="row">
    <div class="col-md-11 text-center">
      <b><i class="groove-fa fa fa-exclamation-triangle"></i>&nbsp;{{translations.labels.se_additional_shipments}}</b>
    </div>
    <div class="col-md-1">
      <i class="fa fa-caret-up" aria-hidden="true" ng-click="hide_se_info(scan_pack.settings.show_expanded_shipments)"
        ng-show="scan_pack.settings.show_expanded_shipments"></i>
      <i class="fa fa-caret-down" aria-hidden="true" ng-click="hide_se_info(scan_pack.settings.show_expanded_shipments)"
        ng-hide="scan_pack.settings.show_expanded_shipments"></i>
      &ensp;
    </div>
  </div>
  <div ng-show="scan_pack.settings.show_expanded_shipments" class="text-center">
    <div style="padding: 10px;">
      <div ng-repeat="shipment in data.order.se_all_shipments.shipments" style="font-weight: bold;">
        <a ui-sref="scanpack.rfp.default({order_num: '{{shipment.increment_id}}', store_order_id: '{{shipment.store_order_id}}' })"
          ui-sref-opts="{reload:true}" style="color: #4c864c;">{{shipment.increment_id}}</a>&ensp;Contains
        {{shipment.items_count}} item(s)
      </div>
    </div>
    <b class="text-center">Click on another shipment to display it for scanning.</b>
  </div>
</div>

<div class="row" ng-show="data.order.next_item.record_serial">
  <div class="alert alert-info">
    <h4>Serial number will be needed for this product</h4>
  </div>
</div>
<div class="row">
  <div class="icons-list-style">
    <div class="col-lg-3 col-md-3 text-center" id='awaiting-order'>
      <div id='awaiting-order-count'>
        {{data.raw.awaiting}}
      </div>
      <ng-pluralize count="data.raw.awaiting" when="{'1': 'Awaiting Order',
               'other': 'Awaiting Orders'}">
      </ng-pluralize><br>
      <strong>Remaining</strong>
    </div>
    <div class="col-lg-1  col-md-1" ng-click="reset_order()">
      <i class="glyphicon glyphicon-repeat"></i>

      <div class="icons-style">Restart</div>
    </div>
    <div class="col-lg-1 col-md-1 pull-left" ng-hide="true">
      <i class="glyphicon glyphicon-pause"></i>

      <div class="icons-style">
        Pause
      </div>
    </div>
    <div class="col-lg-1 col-md-1 pull-left" ng-click="add_note()">
      <i class="glyphicon glyphicon-list-alt "></i>

      <div class="icons-style">Add Note</div>
    </div>

    <div class="col-lg-1 col-md-1 pull-right col-md-pull-1 col-lg-pull-1" ng-click="generate_box_packing_slip()"
      ng-if="general_settings.single.multi_box_shipments">
      <i class="glyphicon glyphicon-print"></i>

      <div class="icons-style">Print</div>
    </div>
  </div>
</div>
<div class="confirmation-notes">
  <div class="row alert alert-note"
    ng-show="data.scan_pack_settings.show_internal_notes && data.order.notes_internal.length && !show_internal_notes">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close" ng-click="show_internal_notes=true">
      <span aria-hidden="true">&times;</span></button>
    <h4>{{data.order.notes_internal}}</h4>
  </div>
  <div class="row alert alert-note" ng-show="data.order.notes_toPacker.length && !show_packer_notes">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close" ng-click="show_packer_notes=true"><span
        aria-hidden="true">&times;</span></button>
    <h4>{{data.order.notes_toPacker}}</h4>
  </div>
  <div class="row alert alert-note"
    ng-show="data.scan_pack_settings.show_customer_notes && data.order.customer_comments.length && !show_customer_notes">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close" ng-click="show_customer_notes=true">
      <span aria-hidden="true">&times;</span></button>
    <h4>{{data.order.customer_comments}}</h4>
  </div>
  <div class="row alert alert-note" ng-show="data.scan_pack_settings.show_tags && data.order.tags.length && !show_tags">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close" ng-click="show_tags=true">
      <span aria-hidden="true">&times;</span></button>
    <h4>{{data.order.tags}}</h4>
  </div>

</div>
<div class="row scanpack-bottom-well">
  <div class="row">
    <div class="col-lg-3 col-md-3">
      <div class="scanpack-well">
        <div class="well-heading">
          <span>{{ unscanned_count }} Items Left</span>
        </div>
        <div class="row" ng-repeat="unscanned_item in data.order.unscanned_items | limitTo: 10">
          <div class="media">
            <a class="pull-left" href="">
              <img class="media-object" width="64px" ng-src="{{ unscanned_item.images[0].image }}" />
              <span ng-show="unscanned_item.product_type =='individual'"><img src="/assets/images/top.png"></span>
            </a>
            <div class="media-body">
              <span class="item-count pull-right">X {{unscanned_item.qty_remaining}}</span>
              <span class="media-heading pack_link" ng-click="product_details(unscanned_item.product_id)"
                tooltip="{{unscanned_item.name}}">{{ unscanned_item.name | cut:false:(90) }}<br />
                {{ unscanned_item.sku }}</span>
            </div>
          </div>
          <div class="sub-products" ng-show="unscanned_item.product_type =='individual'">
            <div class="sub-products-position">
              <div class="media" ng-repeat='child_item in unscanned_item.child_items'>
                <a class="pull-left" href="">
                  <img class="media-object" width="44px" ng-src="{{ child_item.images[0].image }}" />
                </a>

                <div class="media-body">
                  <span class="item-count pull-right">X {{child_item.qty_remaining}}</span>
                  <span class="media-heading pack_link" ng-click="product_details(child_item.product_id)">{{
                    child_item.name }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="sub-products-bottom" ng-show="unscanned_item.product_type =='individual'">
            <img src="/assets/images/bottom.png">
          </div>
          <hr class="scanpack-hr" width="95%">
          <hr class="scanpack-hr-border" width="96.5%">
        </div>
      </div>
    </div>
    <div class="col-lg-6 col-md-6">
      <div class="packing-well" style="margin-top: 0px;">
        <span class="pack_link" ng-click="order_details(data.order.id)">Now Packing #{{ data.order.increment_id}}</span>
      </div>

      <div class="scanpack-image-well">
        <h3 ng-show="data.order.next_item.scanned_qty ==0">Please scan this product next</h3>

        <h3 ng-show="data.order.next_item.scanned_qty !=0">{{data.order.next_item.scanned_qty}} of {{
          data.order.next_item.qty }} have been scanned</h3>

        <div class="text-left">
          <h5>
            &nbsp;
            <span class="pull-left"><strong tooltip="{{data.order.next_item.name}}">{{data.order.next_item.name |
                limitTo:45}}</strong></span>
            <span class="pull-right" ng-class="{pointer: scan_pack.settings.enable_click_sku}"
              ng-click="autoscan_barcode()"><strong>{{data.order.next_item.sku}}</strong></span><br /><br />
            <span ng-if="scan_pack.settings.display_location==true" class="pull-right">
              <h4>{{data.order.next_item.location | cut:true:25:'...'}}</h4>
            </span>
          </h5>
          <h4>{{data.order.next_item.qty_remaining}} more to scan.</h4>

        </div>

        <div class="display_inline_span">
          <span ng-if="scan_pack.settings.display_location2==true">
            <h4>{{data.order.next_item.location2 | cut:true:25:'...'}}</h4>
          </span>
          <span ng-if="scan_pack.settings.display_location3==true">
            <h4>{{data.order.next_item.location3 | cut:true:25:'...'}}</h4>
          </span> </span>
        </div>

        <div class=" alert alert-info" ng-show="data.order.next_item.skippable">This product can be skipped, scan the
          skip code to skip
        </div>

        <div class="scan_product_images">
          <div class="scan_product_times img-rounded" ng-show="data.order.next_item.qty > 1">
            <div class="text-center pull-left"><i class="glyphicon glyphicon-remove"></i><br />TIMES</div>
            <div class="times_number pull-right">{{data.order.next_item.qty}}</div>
          </div>
          <div ng-click="item_image_index = item_image_index + 1"
            ng-show="item_image_index < data.order.next_item.images.length - 1" class="nav-right img-circle"><i
              class="glyphicon glyphicon-step-forward"></i></div>
          <div ng-click="item_image_index = item_image_index - 1" ng-show="item_image_index > 0"
            class="nav-left img-circle"><i class="glyphicon glyphicon-step-backward"></i></div>
          <span class="img-style-helper"></span>
          <img ng-repeat="image in data.order.next_item.images" ng-show="item_image_index == $index" class="img-style"
            ng-src="{{ image.image }}" />

          <div style="background: rgba(19, 45, 19, 0.40)" class="special_product_instructions" ng-show="data.order.next_item.custom_product_display_1==true || data.order.next_item.custom_product_display_2==true || data.order.next_item.custom_product_display_3==true ||
          data.order.next_item_instruction">


            <div ng-show="data.order.next_item_instruction">
              <h4>Special instruction for the product:</h4>
              <div ng-bind-html="data.order.next_item.instruction"></div>
            </div>

            <div
              ng-show="data.order.next_item.custom_product_1.length && data.order.next_item.custom_product_display_1==true">
              <!-- <h4>Custom Product Field 1:</h4> -->
              <h3> {{data.order.next_item.custom_product_1}}</h3>
            </div>
            <div
              ng-show="data.order.next_item.custom_product_2.length && data.order.next_item.custom_product_display_2==true">
              <!-- <h4>Custom Product Field 2:</h4> -->
              <h3> {{data.order.next_item.custom_product_2}}</h3>
            </div>
            <div
              ng-show="data.order.next_item.custom_product_3.length && data.order.next_item.custom_product_display_3==true">
              <!-- <h4>Custom Product Field 3:</h4> -->
              <h3> {{data.order.next_item.custom_product_3}}</h3>
            </div>
          </div>
        </div>

      </div>
      <div class="product-scan-outside-well">
        <span class="indicator-ready pull-left"></span>

        <div class="product-scan-well">

          <span class="col-lg-1 col-md-1  glyphicon glyphicon-barcode barcode-icon"></span>
          <span class="col-lg-11 col-md-11 product-scan-status">
            <input type="text" ng-model="data.input" ng-keydown="input_enter($event)" autofocus="autofocus" tabindex="1"
              class="search-box product-scan-btn text-center" id='ready_product_scan'
              placeholder="Ready For Product Scan" />
          </span>
        </div>
      </div>
      <!--
      <div class="scan-details-well">
          <div class="scan-details">
              <div class="col-lg-7 col-md-7 scan-details-left">
                  <span>Perfect Scans: 43</span><br>
                  <span>Perfect Orders: 16</span><br>
                  <span>Scan Accuracy: 96.2%</span><br>
                  <span>Avg Scan Time: 9.2 sec</span><br>
                  <span>Last Scan Time: 8.3 sec</span>
              </div>
              <div class="col-lg-5 col-lg-5 scan-details-right">
                  <span>Recent Bonus:</span><br>
                  <span>Suggested Scans</span>
                  <span style="font-size:20px"> 889,394</span>
              </div>
          </div>
      </div>
      -->
    </div>
    <div class="col-lg-3 col-md-3 pull-right" ng-if="!general_settings.single.multi_box_shipments"">
      <div class=" scanpack-well">
      <div class="right-well-heading">
        <span class="col-lg-8 col-md-8">
          {{scanned_count}} Items Packed
        </span>
        <span class="col-lg-4 col-md-4 download-icon-styling">
          <img src="/assets/images/download-icon.png">
        </span>
      </div>
      <div class="row" ng-repeat="scanned_item in data.order.scanned_items"
        ng-show="scanned_item.product_type =='single'">
        <div class="media">
          <a class="pull-left" href="">
            <img class="media-object" ng-src="{{scanned_item.images[0].image}}" width="64px">
          </a>

          <div class="media-body">
            <span class="item-count pull-right">X {{scanned_item.scanned_qty}}</span>
            <span class="media-heading pack_link" ng-click="product_details(scanned_item.product_id)"
              tooltip="{{scanned_item.name}}">{{ scanned_item.name | cut:false:90 }}<br>
              {{ scanned_item.sku }}</span>
          </div>
        </div>
        <hr class="scanpack-hr" width="95%">
        <hr class="scanpack-hr-border" width="96.5%">
      </div>
    </div>
  </div>


  <!-- multibox items -->
  <div class="col-lg-3 col-md-3 pull-right" ng-if="general_settings.single.multi_box_shipments">
    <div class="scanpack-well">
      <div class="right-well-heading">
        <span class="col-lg-8 col-md-8">
          {{scanned_count}} Items Packed
        </span>
        <span class="col-lg-4 col-md-4 download-icon-styling">
          <img src="/assets/images/download-icon.png">
        </span>
      </div>

      <div class="right-well-heading" ng-if="data.current_box" style="border-radius: 0; margin-top: 0px;">
        <span class="col-lg-2 col-md-2" ng-if="data.prev_box">
          <span class="glyphicon glyphicon-circle-arrow-left" ng-click="prev_next_box('prev')"></span>
        </span>

        <span class="col-lg-8 col-md-8">
          {{data.current_box.name}}
        </span>

        <span class="col-lg-2 col-md-2" ng-if="data.next_box">
          <span class="glyphicon glyphicon-circle-arrow-right" ng-click="prev_next_box('next')"></span>
        </span>

        <span class="col-lg-2 col-md-2" ng-if="!data.next_box && !data.box_is_empty">
          <span class="glyphicon glyphicon-plus-sign" ng-click="create_box()"></span>
        </span>
      </div>

      <div class="row Custom-box-row" ng-repeat="scanned_item in data.order.scanned_items"
        ng-show="scanned_item.product_type" style="position: relative;">

        <div ng-if="scanned_item.product_type =='single'">
          <div
            ng-repeat="box_item in data.order.order_item_boxes | filter: { order_item_id: scanned_item.order_item_id, box_id: data.current_box.id, kit_id: scanned_item.kit_product_id}"
            ng-mouseover="show_hide_remove_btn('show',scanned_item.order_item_id)"
            ng-mouseleave="show_hide_remove_btn('hide',scanned_item.order_item_id)">
          </div>
        </div>

        <!-- order item boxes for kit -->

        <div ng-if="scanned_item.product_type == 'individual'">
          <div ng-repeat="child_item in scanned_item.child_items">
            <div
              ng-repeat="box_item in data.order.order_item_boxes | filter: { order_item_id: child_item.order_item_id, box_id: data.current_box.id, kit_id: child_item.kit_product_id}"
              style="margin-bottom: 5px;">

              <div class="box-item-container custom-box-red" style="margin-bottom: 20px; margin-left: 10px;">
                <div class="d-flex custom-box-detail">
                  <div>
                    <span ng-click="remove_from_box(child_item.order_item_id, child_item.kit_product_id)"
                      id="remove_{{child_item.kit_product_id}}" class="remove-button">
                      <span class="glyphicon glyphicon-minus-sign" style="color: red;"></span>
                    </span>
                  </div>
                  <div class="d-flex w-100 custom-detail">
                    <div class="pack_link" ng-click="product_details(scanned_item.product_id)"
                      tooltip="{{scanned_item.name}}">
                      KIT: {{scanned_item.name}}<br>
                    </div>
                    <div class="d-flex">
                      <a class="pull-left" href="">
                        <img class="media-object" ng-src="{{child_item.images[0].image}}" width="64px">
                      </a>
                      <span class="media-heading pack_link" ng-click="product_details(child_item.product_id)">
                        ITEM: {{child_item.name | cut:false:90 }}<br>
                        {{ child_item.sku }}
                      </span>
                      <span class="item-count pull-right">X {{box_item.item_qty}}</span>
                    </div>
                  </div>
                </div>
              </div>
              <hr class="scanpack-hr" width="95%">
              <hr class="scanpack-hr-border" width="96.5%">
            </div>
          </div>
        </div>

        <!-- with order item boxes -->

        <div ng-if="scanned_item.product_type =='single' && !scanned_item.kit_product_id">
          <div
            ng-repeat="box_item in data.order.order_item_boxes | filter: { order_item_id: scanned_item.order_item_id, box_id: data.current_box.id, kit_id: scanned_item.kit_product_id} "
            style="margin-bottom: 5px;">
            <div class="box-item-container custom-box-red custom-box-red-first custom-box-detail"
              style="margin-bottom: 20px; margin-left: 10px; ">
              <div class="red-icon">
                <span ng-click="remove_from_box(scanned_item.order_item_id, scanned_item.kit_product_id)"
                  id="remove_{{scanned_item.order_item_id}}" style="height: 40px; cursor:pointer;">
                  <span class="glyphicon glyphicon-minus-sign" style="color: red;"></span>
                </span>
              </div>
              <div class="d-flex">
                <a class="pull-left" href="">
                  <img class="media-object" ng-src="{{scanned_item.images[0].image}}" width="64px">
                </a>
                <span class="media-heading  pack_link" ng-click="product_details(scanned_item.product_id)"
                  tooltip="{{scanned_item.name}}">
                  <span class="media-heading">
                    {{ scanned_item.name | cut:false:90 }}<br>
                    {{ scanned_item.sku }}</span>
                  <span class="item-count pull-right">X {{box_item.item_qty}}</span>

                </span>
              </div>
            </div>
            <hr class="scanpack-hr" width="95%">
            <hr class="scanpack-hr-border" width="96.5%">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

</div>
</div>



</div>
</div>
<!--
<div class="row">

<div class="time time-display">
<span>00:35:27</span>
</div>
<div class="progress-meter">
<img src="/assets/images/progress_bar.png" height="23px" width="89%"
        >
</div>

<div class="scan-timer pull-right">
<img src="/assets/images/scan-time.png" height="89px" width="103px">
</div>
</div>
<div class="row">
<div class="scan-time pull-right">
<span>2</span>
</div>
</div>

-->

<style>
  .Custom-box-row .media-heading {
    display: flex;
    width: 100%;
    max-width: 170px;
  }

  .Custom-box-row .custom-box-red {
    display: flex;
    gap: 10px;
    margin-top: 10px;

  }

  .Custom-box-row .padding-left-23 {
    width: calc(100% - 14px);
  }

  .Custom-box-row .item-count {
    min-width: 30px;
    justify-content: end;
    display: flex;
    padding: 0 5px;
    white-space: nowrap;
    padding-left: 10px;
  }

  .Custom-box-row .custom-box-detail {
    align-items: center;
  }

  .Custom-box-row .custom-detail {
    flex-direction: column;
  }
</style>