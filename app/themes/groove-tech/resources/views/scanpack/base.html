<!-- Facebook Pixel Code -->
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '1629910453881743');
  fbq('track', 'PageView');
  </script>
  <noscript><img height="1" width="1" style="display:none"
  src="https://www.facebook.com/tr?id=1629910453881743&ev=PageView&noscript=1"
  /></noscript>
  <!-- End Facebook Pixel Code -->

  <div class="container-fluid">
  <div class="row scan_pack_input" ng-if="fetching_label_related_data != true && (!data.order.next_item || current_state != 'scanpack.rfp.default') && scan_pack.settings.scan_pack_workflow == 'default'">
    <div class="col-xs-7 scan_pack_title">
      {{data.title}}
       <i ng-if="data.msg != nil" class="icon-large glyphicon glyphicon-info-sign ng-scope" popover-trigger="mouseenter" popover-placement='right' groov-popover="This screen allows you to quickly add barcodes for new items that are in this order. To continue just right-click the barcode area beside each product to edit it and scan the product barcode to add it. For a quick demo video with more info click the link to the right." style="cursor: pointer;">
      </i>
    </div>
    <div class="col-xs-5 scan_pack_title">
      <p class="pull-right" id="popup_video" ng-if="data.msg != nil" ng-click="show_video()" style="cursor: pointer;">{{data.msg}}</p>
    </div>
      <input type="text" ng-model="data.input"  ng-keydown="input_enter($event)" autofocus="autofocus" tabindex="1"
             class="form-control search-box" placeholder="Scan..."/>
    </div>

  </div>
  <div ui-view></div>
  <div class="scan_pack_success_fail row">
    <div class="middle col-xs-12" ng-show="scan_pack_state=='success'">
      <img class="col-xs-8 col-xs-offset-2" ng-src="{{scan_pack.scan_states.success.image.src}}" alt=""/>
    </div>
    <div class="middle col-xs-12" ng-show="scan_pack.scan_states.fail.image.enabled && scan_pack_state=='fail'">
      <img class="col-xs-8 col-xs-offset-2" ng-src="{{scan_pack.scan_states.fail.image.src}}" alt=""/>
    </div>
    <div class="middle col-xs-12"
         ng-show="scan_pack.scan_states.order_complete.image.enabled && scan_pack_state=='order_complete'">
      <img class="col-xs-8 col-xs-offset-2" ng-src="{{scan_pack.scan_states.order_complete.image.src}}" alt=""/>
    </div>
    <div class="middle col-xs-6" ng-show="scan_pack_state=='on_demand_1'">
      <img class="col-xs-8 col-xs-offset-2 circle-image " ng-src="{{'/assets/images/countdown_1.png'}}" alt=""/>
    </div>
     <div class="middle col-xs-6" ng-show="scan_pack_state=='on_demand_2'"  >
      <img class="col-xs-8 col-xs-offset-2 circle-image " ng-src="{{'/assets/images/countdown_2.png'}}" alt=""/>
    </div>
     <div class="middle col-xs-6" ng-show="scan_pack_state=='on_demand_3'">
      <img class="col-xs-8 col-xs-offset-2 circle-image " ng-src="{{'/assets/images/countdown_3.png'}}" alt=""/>
    </div>
  </div>
</div>


<style type="text/css">
  .app-modal-video-window .modal-dialog {
    top: 20%;
  }

  .close-video-button  {
    top: -21px;
    right: 23px;
  }

  .circle-image {
    left: 348px;
    top: 290px;
    width: 63%
  }

</style>
