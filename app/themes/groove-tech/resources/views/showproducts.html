<div class="container-fluid">
  <div class="row bottom-well">
    <div class="col-lg-2 col-md-2 accordion-parent" style="width: 147px;">
      <div class="row">
        <accordion class="panel-body-new">
         <accordion-group is-open="tabs[0].open">
            <accordion-heading>
              <i ng-click="products.setup.search=''" ui-sref="products.type.filter.page({type:'product',filter:'active',page:1})"
                 ng-click="products.setup.search=''" ui-sref-opts="{reload: true}"></i>
              <a>Products</a>
            </accordion-heading>
            <li ng-click="create_product()">Create</li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='all' && products.setup.is_kit == 0}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'product',filter:'all',page:1})"
              ui-sref-opts="{reload: true}">Show All
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='active' && products.setup.is_kit == 0}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'product',filter:'active',page:1})"
              ui-sref-opts="{reload: true}">Active Products
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='inactive' && products.setup.is_kit == 0}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'product',filter:'inactive',page:1})"
              ui-sref-opts="{reload: true}">Inactive Products
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='new' && products.setup.is_kit == 0}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'product',filter:'new',page:1})"
              ui-sref-opts="{reload: true}">New <span
              class="pull-right badge badge-important"
              ng-show="products.setup.is_kit==0 && products.products_count.new>0">{{products.products_count.new}}</span>
            </li>
          </accordion-group>
          <accordion-group is-open="tabs[1].open">
            <accordion-heading>
              <a ui-sref="products.type.filter.page({type:'kit',filter:'active',page:1})"
                 ng-click="products.setup.search=''">Kits</a>
            </accordion-heading>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='all' && products.setup.is_kit == 1}"
              ng-click="products.setup.search=''" ui-sref="products.type.filter.page({type:'kit',filter:'all',page:1})" ui-sref-opts="{reload: true}">
              Show All
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='active' && products.setup.is_kit == 1}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'kit',filter:'active',page:1})"
              ui-sref-opts="{reload: true}">Active Kits
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='inactive' && products.setup.is_kit == 1}"
              ng-click="products.setup.search=''"
              ui-sref="products.type.filter.page({type:'kit',filter:'inactive',page:1})"
              ui-sref-opts="{reload: true}">Inactive Kits
            </li>
            <li
              ng-class="{active: products.setup.search=='' && products.setup.filter=='new' && products.setup.is_kit == 1}"
              ng-click="products.setup.search=''" ui-sref="products.type.filter.page({type:'kit',filter:'new',page:1})" ui-sref-opts="{reload: true}">
              New Kits <span class="pull-right badge badge-important"
                             ng-show="products.setup.is_kit==1 && products.products_count.new>0">{{products.products_count.new}}</span>
            </li>
          </accordion-group>
          <accordion-group is-open="tabs[2].open">
            <accordion-heading>
              <a>Inventory</a>
            </accordion-heading>
            <li ng-click="recount_or_receive_inventory()">Receive or Recount Inventory</li>
            <li ng-if="inventory_report_toggle == true" ng-class="{active: page_type=='inventory'}"
             ui-sref="products.inventory_report" ng-click="inventory_report()" ui-sref-opts="{reload: true}"> Inventory Report</li>
          </accordion-group>
        </accordion>
      </div>
    </div>
    <div class="col-lg-10 col-md-10">
      <div class="row" ng-if="inventory_report_page != true">
        <div class="col-lg-9 col-md-9">
          <div class="col-lg-offset-3 col-md-offset-3 col-lg-9 col-md-9 pull-left">
            <div>
              <input type="text" id="product-search-query" autofocus="autofocus" class="form-control search-box"
                     ng-model="products.setup.search" placeholder="Search">

              <span>
              <label style="cursor: pointer;font-weight: lighter;" groov-popover='When enabled, search by extra fields like categories to find products which contain it.' popover-trigger="mouseenter" popover-placement="bottom">
                <input type="checkbox"
                        ng-model="products.setup.advanced_search"
                        ng-click="products.toggle_advanced_search(!products.setup.advanced_search);products.setup.advanced_search = !products.setup.advanced_search;">
                Search all fields
              </label>
            </span>
            </div>
          </div>
        </div>
        <div class="col-lg-1 col-md-1" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Edit <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu" style="width: 280px;">
            <li class="dropdown-toggle" ng-if="gridOptions.selections.show_delete"><a ng-click="product_delete()">Delete<span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle disabled" ng-if="!gridOptions.selections.show_delete">
              <a tooltip="Inactive Products can be deleted from the Inactive products list.">Delete<span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="product_duplicate()">Duplicate <span
              class=" pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="product_barcode()">Generate barcode from SKU <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="numeric_product_barcode()">Generate  Numeric Barcode <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="product_barcode_label()">Print Product Barcode Labels <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="product_receiving_label()">Print Receiving Labels <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle"><a ng-click="backup_product_csv()">Export Products to CSV <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
             <li class="dropdown-toggle"><a ng-click="broken_image_export()">Broken/Missing image export <span
              class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li class="dropdown-toggle" ng-if="gridOptions.selections.show_delete"><a ng-click="find_inactive_product()">Find Inactive Products <span
              class="pull-right badge badge-important"></span></a>
            </li>
          </ul>
        </div>
        <div class="col-lg-2 col-md-2" dropdown>
          <button type="button" class="groove-button dropdown-toggle">
            Change Status <span class="caret"></span>
          </button>
          <ul class="dropdown-menu" role="menu">
            <li><a ng-click="product_change_status('active')">Active <span class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a ng-click="product_change_status('inactive')">Inactive <span class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
            <li><a ng-click="product_change_status('new')">New <span class="pull-right badge badge-important">{{products.setup.select_all? gridOptions.paginate.total_items: gridOptions.selections.selected_count}}</span></a>
            </li>
          </ul>
        </div>

      </div>
      <div class="row" ng-if="inventory_report_page != true">
        <div class="col-xs-12">
          <div groov-data-grid="gridOptions" groov-list="products.list"></div>
        </div>
      </div>
      <div ui-view></div>
    </div>
  </div>
</div>
<style type="text/css">
 /* .accordion-parent {
    width: 16.66666667% !important;
  }*/
</style>
