.grid-editable-field, .grid-editing-mode, .grid-copyable-field, .grid-simple-text{
  padding: 0.8rem 0.3rem;
  min-height: 3rem;
}

.table-parent{
  display: table-caption;
  td{
    vertical-align: middle !important;
    &:hover{
      .grid-editable-field, .grid-copyable-field{
        background-color: rgb(230, 228, 228);
        border-color: #8AC007;
        border-style: solid;
        border-width: thin;
        border-radius: 3px 0 0 3px;
        min-width: 5rem;
        box-shadow: 1px 2px 5px rgba(0, 0, 1, .21);
        margin: -0.3rem -0.25rem;
      }
    }
  }
  th.active{
    background-color: black !important;
  }
  textarea, input{
    font-size: 1.3rem;
    line-height: 1.4rem;
    padding: 0.2rem;
  }
}

.table-parent .tooltip-inner{
  width: auto;
  min-width: 30rem;
  max-width: 50rem;
}

.datagrid-pencil{
  cursor: pointer;
  color: #8AC007 - 50;
  font-size: 2rem;
  vertical-align: text-top;
  border: 1px solid #8AC007;
  padding: 0.5rem 0.8rem;
  margin-top: -0.9rem;
  margin-bottom: -0.3rem;
  margin-right: -3.8rem !important;
  background-color: #8AC007 + 15;
  border-radius: 0 3px 3px 0;
  box-shadow: 1px 2px 5px rgba(0, 0, 1, .21);
}

.fa.pull-right{
  margin-left: 0;
}

.datagrid-copy{
  cursor: pointer;
  color: #8AC007 - 50;
  font-size: 2rem;
  vertical-align: text-top;
  border: 1px solid #8AC007;
  padding: 0.5rem 0.8rem;
  margin-top: -0.9rem;
  margin-bottom: -0.3rem;
  margin-right: -4.1rem !important;
  background-color: #8AC007 + 15;
  border-radius: 0 3px 3px 0;
  box-shadow: 1px 2px 5px rgba(0, 0, 1, .21);
}

.table-parent::-webkit-scrollbar, .suwala-doubleScroll-scroll-wrapper::-webkit-scrollbar {
  width: 12px;
}

.table-parent::-webkit-scrollbar-track, .suwala-doubleScroll-scroll-wrapper::-webkit-scrollbar-track {
  &:hover{
    -webkit-box-shadow: inset 0 0 8px rgba(53, 65, 56, 0.90);
  }
  border-radius: 10px;
}

.table-parent::-webkit-scrollbar-thumb, .suwala-doubleScroll-scroll-wrapper::-webkit-scrollbar-thumb{
  border-radius: 10px;
  //background: #628c3a;
  &:hover {
    background: #74a03e;
  }
}
