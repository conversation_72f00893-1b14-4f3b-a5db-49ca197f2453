.package-details-font {
  font-family: 'Poppins'
}

.packing-cam-info {
  .packing-cam-header {
    padding: 20px;

    .packing-cam-logo {
      img {
        display: block;
        margin-left: auto;
        margin-right: auto;
        min-height: 100px;
      }
    }
  }

  .packing-list,
  .packing-logs {
    background: #f5f4f4;
    padding: 15px;
    border-radius: 10px;
    color: #545353;
  }

  .packing-list-log-content {
    padding: 0 0 0 0px;
  }

  .packing-list-logs {
    overflow-y: scroll;
    max-height:400px;

    padding-right: 15px;
  }
  .table-responsive{
    border: 0;
    .table thead tr {
      all: revert;
      th {
      background-color: transparent;
      padding: 2px 5px;
      box-shadow: none;
      border-right: 0;
      border-bottom: 0;
      color: #545353;
      text-align: left;
      }
    }
    .table tbody tr td{
      text-align: left;
    }
  }

}
.packing-list section h3{
  margin-top: 0px;
}
.packing-cam-info .packing-list, .packing-cam-info .packing-logs {
    border: solid 2px #e0e0e0;
}
.packing-cam-info .packing-cam-header .packing-cam-logo img {
  min-height: 0px;
}
.packing-cam-info .packing-cam-header {
  padding-top: 0px;
}
.packing-cam-header-content h4:first-child{
margin-top: 0px;
}
.packing-cam-logo {
  margin-top: 5px;
}
.packing-cam-images {
  padding: 10px 0px 30px 0px;
}
.gallery{
  overflow-x: auto;
  padding: 0 15px;
  .d-flex{
    display: flex;
    justify-content: center;
    div{
      img{
        width: 282px;
        height: 180px;
        object-fit: cover;
        @media(max-width:412px){
            width: 100px;
            height: 100px;
        }
      }
    }
  }
}

.packing-cam-images {
  padding: 30px;
}

.d-flex {
  display: flex;
  justify-content: center;
}

@media(max-width :767px){
  .packing-cam-header-content {
    word-break: break-word;
    text-align: justify;
  }
  .packing-cam-info{
    padding-left: 0px;
    padding-right: 0px;
  }
  .setup_pricing{
    padding-left: 0px;
    padding-right: 0px;
  }
  .gradient-well-inner {
    width: 94vw;
  }
}
