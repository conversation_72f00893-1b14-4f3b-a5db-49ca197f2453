groovepacks.config(['$stateProvider', '$urlRouterProvider', '$httpProvider', 'hotkeysProvider', 'cfpLoadingBarProvider', '$translateProvider', '$urlMatcherFactoryProvider', 'ngClipProvider', '$locationProvider', 'KeepaliveProvider', 'IdleProvider',
  function ($stateProvider, $urlRouterProvider, $httpProvider, hotkeysProvider, cfpLoadingBarProvider, $translateProvider, $urlMatcherFactoryProvider, ngClipProvider, $locationProvider, KeepaliveProvider, IdleProvider) {

    ngClipProvider.setPath("/swf/ZeroClipboard.swf");

    $urlRouterProvider.otherwise("/");
    $urlRouterProvider.when('/scanandpack/rfp', '/scanandpack');
    $urlRouterProvider.when('/settings', '/settings/stores');
    $urlRouterProvider.when('/settings/system', '/settings/system/general');
    $urlMatcherFactoryProvider.strictMode(false);
    $httpProvider.defaults.useXDomain = true;
    $httpProvider.defaults.withCredentials = false;
    delete $httpProvider.defaults.headers.common["X-Requested-With"];
    $httpProvider.defaults.headers.common["Accept"] = "application/json";
    $httpProvider.defaults.headers.common["Content-Type"] = "application/json";
    $httpProvider.defaults.headers.common['Access-Control-Allow-Origin'] = '*';
    $httpProvider.defaults.headers.post['Access-Control-Allow-Origin'] = '*';
    $httpProvider.interceptors.push("authInterceptor");
    $stateProvider
    .state('login', {
      url: '/',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/login.html'
        },
        'container': {
          templateUrl: '/assets/views/login.html',
          controller: 'authCtrl'
        }
      }
    })
    .state('forgetpass', {
      url: '/users/password/edit',
      templateUrl: '/assets/views/reset_password.html',
      controller: 'authCtrl'
    })
    .state('subscriptions', {
      url: '/subscriptions',
      templateUrl: "/assets/admin_views/subscriptions/subscription.html",
      controller: 'subscriptionCtrl'
    })
    .state('create_subscriptions', {
      url: '/subscription/new',
      templateUrl: "/assets/admin_views/subscriptions/new_subscription.html",
      controller: 'subscriptionCtrl'
    })
    .state('show', {
      url: '/subscription/show',
      templateUrl: "/assets/admin_views/subscriptions/show.html",
      controller: 'subscriptionCtrl'
    })
    .state('shopify_callback', {
      url: '/shopify/callback',
      templateUrl: "/assets/admin_views/subscriptions/callback.html",
      controller: 'subscriptionCtrl'
    })
    .state('shopify_payment_failed', {
      url: '/shopify/payment_failed',
      templateUrl: "/assets/admin_views/subscriptions/payment_failed.html",
      controller: 'subscriptionCtrl'
    })
    .state('shopify_updated_plan', {
      url: '/shopify/updated_plan',
      templateUrl: "/assets/admin_views/subscriptions/updated_plan.html",
      controller: 'subscriptionCtrl'
    })
    .state('finalize_payment', {
      url: '/shopify/finalize_payment',
      templateUrl: "/assets/admin_views/subscriptions/finalize_payment.html",
      controller: 'subscriptionCtrl'
    })
    .state('shopify_complete', {
      url: '/shopify/complete',
      templateUrl: "/assets/admin_views/subscriptions/complete.html"
    })
    .state('shopify_failed', {
      url: '/shopify/failed',
      templateUrl: "/assets/admin_views/subscriptions/failed.html"
    })
    .state('existing_customer', {
      url: '/subscription/existing_customer',
      templateUrl: "/assets/admin_views/subscriptions/existing_customer.html",
      controller: 'subscriptionCtrl'
    })
    .state('cost_calc', {
      url: '/settings/cost_calculator',
      templateUrl: "/assets/admin_views/cost_calculator.html",
      controller: 'costCalculatorCtrl'
    })
    .state('load_bigcommerce', {
      url: '/bigcommerce/load',
      templateUrl: "/assets/admin_views/load.html",
      controller: 'bigcommerceCtrl'
    })
    .state('package_details', {
      url: '/package-details/:email/:order_number',
      templateUrl: "/assets/admin_views/packing-cam/package-details.html",
      controller: 'packingCamCtrl'
    })
    .state('home', {
      url: '/home',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/base.html'
        },
        'container': {
          templateUrl: '/assets/views/showorders.html',
          controller: 'ordersCtrl'
        }
      }
    })
    .state('orders', {
      url: '/orders',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/base.html'
        },
        'container': {
          templateUrl: '/assets/views/showorders.html',
          controller: 'ordersCtrl'
        }
      }
    })
    .state('orders.filter', {
      url: '/{filter:all|awaiting|partially_scanned|onhold|serviceissue|cancelled|scanned}', params: {filter: 'awaiting'},
      template: "<div ui-view></div>", abstract: true
    })
    .state('orders.filter.page', {
      url: '/{page:[0-9]+}', template: "<div ui-view></div>", params: {page: '1'},
      controller: 'ordersFilterCtrl'
    })
    .state('orders.filter.page.single', {
      url: '/{order_id:[0-9]+}', params: {new_order: {value: false}}, template: "<div ui-view></div>",
      controller: 'ordersSingleCtrl'
    })

    .state('products', {
      url: '/products',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/base.html'
        },
        'container': {
          templateUrl: '/assets/views/showproducts.html',
          controller: 'productsCtrl'
        }
      }
    })
    .state('products.type', {
      url: '/{type:product|kit}',
      params: {type: 'product'},
      template: "<div ui-view></div>",
      abstract: true
    })
    .state('products.type.filter', {
      url: '/{filter:all|active|inactive|new}', params: {filter: 'active'}, template: "<div ui-view></div>",
      abstract: true
    })
    .state('products.type.filter.page', {
      url: '/{page:[0-9]+}', params: {page: '1'}, template: "<div ui-view></div>",
      controller: 'productsFilterCtrl'
    })
    .state('products.type.filter.page.single', {
      url: '/{product_id:[0-9]+}', params: {new_product: {value: false}}, template: "<div ui-view></div>",
      controller: 'productsSingleCtrl'
     })
      .state('products.inventory_report', {
        url: '/inventory_report', params: {inventory: {value: true}, type: 'inventory'}, templateUrl: '/assets/views/show_inventory.html', controller: 'productsCtrl'
      })

    .state('scanpack', {
      url: '/scanandpack',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/base.html'
        },
        'container': {
          templateUrl: '/assets/views/scanpack/base.html',
          controller: 'scanPackCtrl'
        }
      },
      abstract: true
    })
    .state('scanpack.rfo', {url: '', templateUrl: '/assets/views/scanpack/multi.html', controller: 'scanPackRfoCtrl'})
    .state('scanpack.rfp', {
      url: '/rfp/:order_num/:username/:store_order_id/:order_by_number', templateUrl: "/assets/views/scanpack/rfpbase.html", controller: 'scanPackRfpCtrl',
      abstract: true
    })
    .state('scanpack.rfp.default', {
      url: '', controller: 'scanPackRfpDefaultCtrl',
      templateUrl: '/assets/views/scanpack/rfpdefault.html'
    })
    .state('scanpack.rfp.recording', {
      url: '/recording', templateUrl: '/assets/views/scanpack/multi.html',
      controller: 'scanPackRecordingCtrl'
    })
    .state('scanpack.rfp.verifying', {
      url: '/verifying', templateUrl: '/assets/views/scanpack/multi.html',
      controller: 'scanPackRecordingCtrl'
    })
    .state('scanpack.rfp.no_tracking_info', {
      url: '/no_tracking_info', templateUrl: '/assets/views/scanpack/multi.html',
      controller: 'scanPackRecordingCtrl'
    })
    .state('scanpack.rfp.no_match', {
      url: '/no_match', templateUrl: '/assets/views/scanpack/multi.html',
      controller: 'scanPackRecordingCtrl'
    })
    .state('scanpack.rfp.product_edit', {
      url: '/product_edit', templateUrl: '/assets/views/scanpack/productedit.html',
      controller: 'scanPackProductEditCtrl'
    })
    .state('scanpack.rfp.product_edit.single', {
      url: '/{product_id:[0-9]+}',
      template: '',
      controller: 'productsSingleCtrl'
    })
    .state('scanpack.rfp.confirmation', {
      url: '/confirmation', controller: 'scanPackConfCtrl',
      templateUrl: '/assets/views/scanpack/multi.html', abstract: true
    })
    .state('scanpack.rfp.confirmation.order_edit', {url: '/order_edit'})
    .state('scanpack.rfp.confirmation.product_edit', {url: '/product_edit'})
    .state('scanpack.rfp.confirmation.cos', {url: '/cos'})

    .state('settings', {
      url: '/settings',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/base.html'
        },
        'container': {
          templateUrl: '/assets/views/settings/base.html',
          controller: 'settingsCtrl'
        }
      },
      abstract: true
    })

      //.state('settings.detailed_import', {url:'/detailed_import',templateUrl:'/assets/views/settings/csv_detailed.html',
      //    controller:'csvDetailed'})

      .state('settings.users', {
        url: '/users',
        templateUrl: '/assets/views/settings/users.html',
        controller: 'usersCtrl'
      })
      .state('settings.users.create', {url: '/create', controller: 'usersSingleCtrl'})
      .state('settings.users.single', {url: '/{user_id:[0-9]+}', controller: 'usersSingleCtrl'})
      .state('settings.stores', {
        url: '/stores',
        templateUrl: '/assets/views/settings/stores.html',
        controller: 'storesCtrl'
      })
      .state('settings.stores.ebay', {
        url: '/ebay?ebaytkn&tknexp&username&redirect&editstatus&name&status&storetype&storeid&inventorywarehouseid&importimages&importproducts&messagetocustomer&tenantname',
        controller: 'storeSingleCtrl'
      })
      .state('settings.stores.create', {url: '/create', controller: 'storeSingleCtrl'})
      .state('settings.stores.single', {url: '/{storeid:[0-9]+}', controller: 'storeSingleCtrl'})

      .state('settings.system', {url: '/system', template: '<div ui-view></div>', abstract: true})
      .state('settings.system.general', {
        url: '/general', templateUrl: '/assets/views/settings/system/general.html',
        controller: 'generalSettingsCtrl'
      })
      .state('settings.system.scan_pack', {
        url: '/scan_pack', templateUrl: '/assets/views/settings/system/scan_pack.html',
        controller: 'scanPackSettingsCtrl'
      })
      .state('settings.system.printing', {
        url: '/printing', templateUrl: '/assets/views/settings/system/printing.html',
        controller: 'generalSettingsCtrl'
      })
      .state('settings.system.developer_tools', {
        url: '/developer_tools', templateUrl: '/assets/views/settings/system/developer_tools.html',
        controller: 'generalSettingsCtrl'
      })
      .state('settings.system.warehouses', {
        url: '/warehouses', templateUrl: '/assets/views/settings/system/warehouses.html',
        controller: 'warehousesCtrl'
      })

      .state('settings.accounts', {url: '/accounts', template: '<div ui-view></div>', abstract: true})
      .state('settings.accounts.card_details', {
        url: '/card_details', templateUrl: '/assets/views/settings/accounts/payment_details.html',
        controller: 'paymentsCtrl'
      })
      .state('settings.accounts.cost_calculator', {
        url: '/cost_calculator', templateUrl: '/assets/views/settings/accounts/cost_calculator.html',
        controller: 'costCalculatorCtrl'
      })
      .state('settings.accounts.modify_plan', {
        url: '/modify_plan', templateUrl: '/assets/views/settings/accounts/modify_plan.html',
        controller: 'modifyPlanCtrl'
      })
      .state('settings.accounts.billing', {
        url: '/billing', templateUrl: '/assets/views/settings/accounts/billing.html',
        controller: 'billingCtrl'
      })

      .state('settings.export', {url: '/export', template: '<div ui-view></div>', abstract: true})
      .state('settings.export.backup_restore', {
        url: '/backup_restore',
        templateUrl: '/assets/views/settings/export/backup.html',
        controller: 'showBackupCtrl'
      })
      .state('settings.export.order_exception', {
        url: '/order_exception',
        templateUrl: '/assets/views/settings/export/order_exceptions.html',
        controller: 'exportOrderExceptionCtrl'
      })
      .state('settings.export.order_export', {
        url: '/order_export',
        templateUrl: '/assets/views/settings/export/order_export.html',
        controller: 'orderExportCtrl'
      })
      .state('settings.export.stats_export', {
        url: '/stats_export',
        templateUrl: '/assets/views/settings/export/stat_export.html',
        controller: 'statExportCtrl'
      })
      .state('settings.export.daily_packed', {
        url: '/daily_packed',
        templateUrl: '/assets/views/settings/export/daily_packed.html',
        controller: 'dailyPackedCtrl'
      })
      .state('settings.export.serial_export', {
        url: '/serial_export',
        templateUrl: '/assets/views/settings/export/order_serials.html',
        controller: 'exportOrderExceptionCtrl'
      });

      hotkeysProvider.cheatSheetHotkey = ['mod+f1', 'g', 'G'];
      hotkeysProvider.cheatSheetDescription = '(or \'g\') Show / hide this help menu';
      cfpLoadingBarProvider.includeSpinner = false;
      $translateProvider.useStaticFilesLoader({
        prefix: '/assets/translations/locale-',
        suffix: '.json'
      });
      $translateProvider.preferredLanguage('en').fallbackLanguage('en');

      // IdleProvider.idle(5);
      IdleProvider.timeout(5);
      KeepaliveProvider.interval(10);
    }]).run(['$rootScope', '$state', '$urlRouter', '$timeout', 'auth', 'hotkeys', 'logger', '$location',
    function ($rootScope, $state, $urlRouter, $timeout, auth, hotkeys, logger, $location) {

      $rootScope.get_tenant = function() {
        var host = $location.host();
        url = host.split('.')[0]
        return url
      }

      $rootScope.get_protocol = function() {
        var protocol = $location.protocol();
        return protocol
      }

      $rootScope.api_url = $rootScope.get_protocol() + '://' + $rootScope.get_tenant() + '.' + $('#site_host').val();

      $rootScope.$on('$stateChangeStart', function (e, to, toParams, from, fromParams) {
        // If user not logged in
        if(!auth.isLoggedIn() && ["login", "subscriptions", "create_subscriptions", "load_bigcommerce", "existing_customer"].indexOf(to.name) == -1) {
          $state.go('login');
        }

        var register_hot_keys = function () {
          if (!hotkeys.get('ctrl+alt+e')) {
            hotkeys.add({
              combo: 'ctrl+alt+e',
              description: 'Opens log',
              allowIn: ['INPUT', 'SELECT', 'TEXTAREA'],
              callback: function (event, hotkey) {
                event.preventDefault();
                var current_user = auth.get();
                if (current_user != null &&
                  typeof(current_user) != {} &&
                  current_user.role.name == "Super Super Admin") {
                  logger.open();
              }
            }
          });
          }
        }

        if (jQuery.isEmptyObject(auth.get())) {
          if (!from.abstract || from.name == '') {
            //e.preventDefault();
          }
          if (auth.isLoggedIn()) {
            auth.check().then(function () {
              if (auth.isLoggedIn()) {
                var result = auth.prevent(to.name, toParams);
                if (result && result.to) {
                  register_hot_keys();
                  $state.go(result.to, result.params);
                } else if (!jQuery.isEmptyObject(auth.get())) {
                  $urlRouter.sync();
                }
              } else {
                e.preventDefault();
                $state.go('login');
              }
            })
          }
        } else {
          var result = auth.prevent(to.name, toParams);
          register_hot_keys();
          $rootScope.$broadcast("connect-to-socket-server");
          if (result && result.to) {
            e.preventDefault();
            $state.go(result.to, result.params);
          } else {
            if (to.name === 'products' || to.name === 'products.type' || to.name === 'products.type.filter') {
              e.preventDefault();
              $state.go('products.type.filter.page');
            } else if (to.name === 'orders' || to.name === 'orders.filter') {
              e.preventDefault();
              $state.go('orders.filter.page');
            }
          }
        }
      });
    }]);
