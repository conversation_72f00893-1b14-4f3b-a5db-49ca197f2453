groovepacks_admin.config(['$stateProvider', '$urlRouterProvider', 'hotkeysProvider', 'cfpLoadingBarProvider', '$translateProvider', 
  '$urlMatcherFactoryProvider', 'ngClipProvider', '$locationProvider', '$httpProvider', 'ngClipProvider',
  function ($stateProvider, $urlRouterProvider, hotkeysProvider, cfpLoadingBarProvider, $translateProvider, $urlMatcherFactoryProvider, 
    ngClipProvider, $locationProvider, $httpProvider, ngClipProvider) {

    ngClipProvider.setPath("/swf/ZeroClipboard.swf");

    $urlRouterProvider.otherwise("/");
    $urlRouterProvider.when('/tools', '/admin_tools');

    $urlMatcherFactoryProvider.strictMode(false);
    $httpProvider.defaults.useXDomain = true;
    $httpProvider.defaults.withCredentials = false;
    delete $httpProvider.defaults.headers.common["X-Requested-With"];
    $httpProvider.defaults.headers.common["Accept"] = "application/json";
    $httpProvider.defaults.headers.common["Content-Type"] = "application/json";
    $httpProvider.defaults.headers.common['Access-Control-Allow-Origin'] = '*';
    $httpProvider.defaults.headers.post['Access-Control-Allow-Origin'] = '*';
    $httpProvider.interceptors.push("authInterceptor");

    $stateProvider
    .state('login', {
      url: '/',
      views: {
        'header': {
          templateUrl: '/assets/views/headers/login.html'
        },
        'container': {
          templateUrl: '/assets/views/login.html',
          controller: 'authCtrl'
        }
      } 
    })
    .state('home', {url: '/home',
      views: {
        'header': {
          templateUrl: '/assets/admin_views/header.html'
        },
        'container': {
          templateUrl: '/assets/admin_views/base.html', 
          controller: 'adminToolsCtrl'
        }
      }
    })
    .state('tools', {
      url: '/admin_tools', 
      views: {
        'header': {
          templateUrl: '/assets/admin_views/header.html'
        },
        'container': {
          templateUrl: '/assets/admin_views/base.html', 
          controller: 'adminToolsCtrl'
        }
      }
    })
    .state('tools.type', {url: '/tenant', 
      templateUrl: "/assets/admin_views/tools/admin_tools.html", 
      controller: 'adminToolsCtrl' 
    })
    .state('tools.delayed_jobs', {url: '/delayed_jobs', 
      templateUrl: "/assets/admin_views/tools/delayed_jobs.html", 
      controller: 'DelayedJobCtrl'
    })
      // .state('tools.type', {
      //   url: '/{type:tenant}',
      //   params: {type: 'tenant'},
      //   template: "<div ui-view></div>",
      //   abstract: true
      // })
      .state('tools.type.page', {
        url: '/{page:[0-9]+}', params: {page: '1'}, template: "<div ui-view></div>",
        controller: 'tenantsFilterCtrl'
      })
      .state('tools.type.page.single', {
        url: '/{tenant_id:[0-9]+}', template: "<div ui-view></div>",
        controller: 'tenantsSingleCtrl'
      })

      hotkeysProvider.cheatSheetHotkey = ['mod+f1', 'g', 'G'];
      hotkeysProvider.cheatSheetDescription = '(or \'g\') Show / hide this help menu';
      cfpLoadingBarProvider.includeSpinner = false;
      $translateProvider.useStaticFilesLoader({
        prefix: '/assets/translations/locale-',
        suffix: '.json'
      });
      $translateProvider.preferredLanguage('en').fallbackLanguage('en');
    }]).run(['$rootScope', '$state', '$urlRouter', '$timeout', 'auth', '$location', function ($rootScope, $state, $urlRouter, $timeout, auth, $location) {

      $rootScope.get_tenant = function() {
        var host = $location.host();
        url = host.split('.')[0]
        return url
      }

      $rootScope.get_protocol = function() {
        var protocol = $location.protocol();
        return protocol
      }

      // To get current Url
      $rootScope.api_url = $rootScope.get_protocol() + '://' + $rootScope.get_tenant() + '.' + $('#site_host').val();

      $rootScope.$on('$stateChangeStart', function (e, to, toParams, from, fromParams) {
        if (jQuery.isEmptyObject(auth.get())) {
          if (!from.abstract || from.name == '') {
        //e.preventDefault();
      }
      if (auth.isLoggedIn()) {
        auth.check().then(function () {
          var result = auth.prevent(to.name, toParams);
          if (result && result.to) {
            $state.go(result.to, result.params);
          } else if (!jQuery.isEmptyObject(auth.get())) {
            $urlRouter.sync();
          }
        })
      }
    } else {
      var result = auth.prevent(to.name, toParams);
      if (result && result.to) {
        e.preventDefault();
        $state.go(result.to, result.params);
      } else {
        if (to.name === 'tools' || to.name === 'tools.type') {
          e.preventDefault();
          $state.go('tools.type.page');
        }
      }
    }

  });
}]);
