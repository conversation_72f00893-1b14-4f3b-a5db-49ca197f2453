networks:
  groovepacker:
    driver: bridge

services:
  web:
    build:
      context: .
      args:
        RAILS_ENV: production
    environment:
      - RAILS_ENV=production
      - DB_HOST=${DB_HOST}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - SOCKET_ENDPOINT=${SOCKET_ENDPOINT}
      - SITE_HOST=${SITE_HOST}
      - GROOV_ANALYTIC=${GROOV_ANALYTIC}
      - HOST_NAME=${HOST_NAME}
    networks:
      - groovepacker
    command: bundle exec rails server -b 0.0.0.0 -p 3000

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - groovepacker
    depends_on:
      - web
