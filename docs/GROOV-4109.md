
# GROOV-4109: User Role Count Logic and Labeling Implementation

## Overview
This document outlines the implementation of proper user role count logic and labeling for regular and admin only users, addressing the incorrect coupling between user types and implementing independent slot management.

## Problem Statement

### Before Implementation
- Regular and admin user slots were incorrectly tied together
- Increasing admin users reduced regular user slots
- User creation wasn't properly restricted based on remaining available slots
- System allowed creating users beyond allowed limits
- Role label "Administrative" was confusing with existing roles like "Admin" and "Super Admin"

### Issues Identified
1. **Incorrect Slot Coupling**: Regular and admin users shared the same slot pool
2. **Wrong User Creation Behavior**: No proper validation for slot availability
3. **Confusing Role Labels**: "Administrative" vs "Admin" vs "Super Admin"
4. **Missing Slot Indicators**: No visible indication of remaining slots
5. **Inconsistent UI Behavior**: Multiple create buttons instead of single intelligent button

## Solution Implementation

### 1. Backend API Changes

#### Current API Response Structure
The `/users.json` endpoint returns an array of user objects, where each user object contains the count information:

```json
[
  {
    "id": 72,
    "username": "suppsource",
    "active": true,
    "last_activity": "2025-07-16T13:24:02.000-04:00",
    "role": {
      "id": 2,
      "name": "Super Admin"
    },
    "max_administrative_users": 2,
    "current_administrative_users": 0,
    "max_regular_users": 8,
    "current_regular_users": 8
  },
  {
    "id": 73,
    "username": "packer1",
    "active": true,
    "last_activity": "2025-04-30T10:05:14.000-04:00",
    "role": {
      "id": 3,
      "name": "Admin"
    },
    "max_administrative_users": 2,
    "current_administrative_users": 0,
    "max_regular_users": 8,
    "current_regular_users": 8
  }
  // ... more users with same count fields
]
```

#### Key Implementation Details
- **Current approach**: Extract count information from first user object (since all users have identical count fields)
- **Count duplication**: Each user object contains the same count fields for consistency
- **Frontend processing**: Calculate remaining slots (`max - current`) in the frontend

### 2. Frontend Implementation

#### Files Modified
1. `app/assets/javascripts/groovepacks_common/services/users.js` - Updated to extract counts from first user object
2. `app/assets/javascripts/groovepacks/controllers/settings/users/usersSingleModal.js` - Added role filtering and validation logic
3. `app/assets/javascripts/groovepacks/controllers/settings/users/usersSingleCtrl.js` - Added slot checking before modal opening
4. `app/assets/javascripts/groovepacks/controllers/settings/users/usersCtrl.js` - Updated role display mapping
5. `app/themes/groove-tech/resources/views/modals/settings/user/info.html` - Updated dropdown and custom option logic
6. `app/themes/groove-tech/resources/views/settings/base.html` - Simplified to single create user button
7. `app/themes/groove-tech/resources/views/settings/users.html` - Added user slot information display

#### Core Functions Added

##### User Count Extraction (users.js)
```javascript
// Extract user counts from first user (since all users have same counts)
if (data.length > 0) {
  object.user_counts = {
    max_administrative_users: data[0].max_administrative_users,
    current_administrative_users: data[0].current_administrative_users,
    max_regular_users: data[0].max_regular_users,
    current_regular_users: data[0].current_regular_users,
    admin_slots_remaining: data[0].max_administrative_users - data[0].current_administrative_users,
    regular_slots_remaining: data[0].max_regular_users - data[0].current_regular_users
  };
}
```

##### `getAvailableRoles()` Function
```javascript
scope.getAvailableRoles = function() {
  if (scope.edit_status) {
    if (!scope.originalRole) {
      return scope.users.roles;
    }
    
    if (scope.originalRole.name === 'Administrative') {
      return scope.users.roles.filter(function(role) {
        return role.name === 'Administrative';
      });
    } else {
      return scope.users.roles.filter(function(role) {
        return role.name !== 'Administrative';
      });
    }
  }
  
  if (!scope.users.user_counts) {
    return scope.users.roles.filter(function(role) {
      return role.name !== 'Administrative';
    });
  }
  
  var regularSlotsRemaining = scope.users.user_counts.regular_slots_remaining;
  var adminSlotsRemaining = scope.users.user_counts.admin_slots_remaining;
  
  if (regularSlotsRemaining > 0) {
    return scope.users.roles.filter(function(role) {
      return role.name !== 'Administrative';
    });
  } else if (adminSlotsRemaining > 0) {
    return scope.users.roles.filter(function(role) {
      return role.name === 'Administrative';
    });
  } else {
    return [];
  }
};
```

##### Slot Checking Logic
```javascript
myscope.checkSlotsAndOpenModal = function() {
  if (typeof $stateParams['user_id'] !== 'undefined') {
    myscope.openUserModal();
    return;
  }
  
  users.list.get($scope.users).then(function() {
    var userCounts = $scope.users.user_counts;
    var regularSlotsRemaining = userCounts.regular_slots_remaining;
    var adminSlotsRemaining = userCounts.admin_slots_remaining;
    
    if (regularSlotsRemaining <= 0 && adminSlotsRemaining <= 0) {
      $state.go('settings.accounts.modify_plan');
      return;
    }
    
    myscope.openUserModal();
  });
};
```

### 3. User Interface Changes

#### Role Display Mapping
- **Backend**: "Administrative" role name
- **Frontend Display**: "Admin Only" label
- **Implementation**: Template-level mapping in dropdowns and user grids

#### Menu Structure
- **Before**: Create buttons "Create User"
- **After**: Single "Create User" button with intelligent behavior

#### Custom Option Logic
```html
<option value="" ng-if="(edit_status && users.single.role && users.single.role.name !== 'Administrative') || (!edit_status && users.user_counts && users.user_counts.regular_slots_remaining > 0)">Custom</option>
```

## Behavior Matrix

### User Creation Scenarios

| Regular Slots   | Admin Slots     | Dropdown Shows         | Custom Option | Default Role     |
|---------------  |-----------------|------------------------|---------------|------------------|
| Available (5/7) | Any             | All regular roles      | ✅      Yes   | Scan & Pack User |
| Full (7/7)      | Available (0/2) | Only "Admin Only"      |  ❌ No        | Administrative   |
| Full (7/7)      | Full (2/2)      | Redirect to Modify Plan| N/A           | N/A              |

### User Editing Scenarios

| User Type           | Dropdown Shows    | Custom Option | Dropdown State |
|---------------------|-------------------|---------------|----------------|
| Regular User        | All regular roles | ✅ Yes        | Enabled        |
| Administrative User | Only "Admin Only" | ❌ No         | Disabled       |

### Role Filtering Logic

#### When Creating New Users:
1. **Regular slots available**: Show all roles except "Administrative" + "Custom"
2. **Regular slots full + admin slots available**: Show only "Administrative"
3. **Both slots full**: Redirect to modify plan

#### When Editing Users:
1. **Regular users**: Show all regular roles + "Custom", hide "Administrative"
2. **Administrative users**: Show only "Administrative" (disabled dropdown)

## Key Features

### 1. Independent Slot Management
- Regular users: Count against `max_regular_users`
- Admin Only users: Count against `max_administrative_users`
- No cross-interference between slot types

### 2. Intelligent Role Selection
- Dynamic role filtering based on slot availability
- Automatic default role selection
- Context-aware "Custom" option visibility

### 3. Edit Protection
- Administrative users cannot change roles (dropdown disabled)
- Regular users cannot be changed to Administrative through editing
- Original role preservation during custom selection

### 4. Fallback Behavior
- Admin Only users can fall back to regular slots if admin slots unavailable
- Graceful handling of edge cases

## Testing Scenarios

### ✅ Verified Working Scenarios

1. **Regular Slots Available (5/7 regular, 0/2 admin)**
   - Create user: Shows regular roles + Custom, hides Admin Only
   - Edit regular user: Shows regular roles + Custom, hides Admin Only
   - Edit admin user: Shows only Admin Only (disabled)

2. **Regular Slots Full (7/7 regular, 0/2 admin)**
   - Create user: Shows only Admin Only, hides Custom
   - Edit regular user: Shows regular roles + Custom, hides Admin Only
   - Edit admin user: Shows only Admin Only (disabled)

3. **Both Slots Full (7/7 regular, 2/2 admin)**
   - Create user: Redirects to Modify Plan
   - Edit any user: Still works normally

4. **Custom Role Selection**
   - Regular users + Custom: Administrative role hidden from dropdown
   - Admin users: Custom option not available

## Technical Implementation Details

### Data Flow
1. **API Call**: `/users.json` returns users array with count fields in each user object
2. **Data Processing**: Extract counts from first user object and calculate remaining slots
3. **Role Filtering**: Apply slot-based filtering in `getAvailableRoles()`
4. **UI Rendering**: Display appropriate roles and options

### State Management
- `originalRole`: Preserved during editing to maintain filtering logic
- `edit_status`: Determines creation vs editing behavior
- `user_counts`: Central source for slot availability

### Error Handling
- Graceful fallback when user counts unavailable
- Validation before user creation
- Proper error messages for slot limitations

## Conclusion

The implementation successfully addresses all requirements:
- ✅ Independent slot management for regular and admin users
- ✅ Proper role filtering based on slot availability
- ✅ Clear UI labeling ("Admin Only" vs "Administrative")
- ✅ Single intelligent "Create User" button
- ✅ Protected editing for Administrative users
- ✅ Context-aware "Custom" option behavior

The solution maintains backward compatibility while providing the correct user management behavior as specified in the requirements.
