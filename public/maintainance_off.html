<!DOCTYPE html>
<html>
  <head>
    <title>The page you were looking for doesn't exist (503)</title>
    <style type="text/css">
      body {
        /*font-family: 'Play', sans-serif;*/
        /*font-family: 'Helvetica Neue';*/
        font-size: 14px;
        line-height: 1.42857143;
        color: #333;
      }
      .container-well {
        background: url('https://s3-us-west-2.amazonaws.com/groove-staging/background_large.png') fixed;
        /*font-family: 'Play', sans-serif;*/
      }
      .container-fluid{
        padding-right:30px;
        padding-left:30px;
        *zoom:1;
      }
      .gradient-well {
        border: 5px solid rgba(201, 201, 201, .69);
        padding: 0px;
        border-radius: 20px;
        box-shadow: 1px 1px 10px rgba(0,0,1,0.86);
      }
      .gradient-well-inner {
        position: relative;
        border: 2px solid rgba(202, 203, 203, .15);
        /*border-radius: @border-radius-base + 10;*/
        border-radius: 14px;
        padding-top: 10px;
        padding-bottom: 10px;
        background: url('https://s3-us-west-2.amazonaws.com/groove-staging/brush_metal_bkg_large.png') no-repeat center center fixed;
      }
      .container-fluid:before,.container-fluid:after{
        display:table;
        content:"";
        line-height:0;
      }
      .row{
        margin-left: -15px;
        margin-right: -15px;
        /*margin-left:-20px;*/
        *zoom:1;
      }
      .row:before,.row:after{
        display:table;content:"";
        line-height:0;
      }
      .row:after{clear:both;}
      .row.bottom-well {
        margin-top: 10px;
      }
      .main-body {
        margin-top: 28px;
      }
      .container {
        width: 1135px;
        margin-right: auto;
        margin-left: auto;
        padding-left: 15px;
        padding-right: 15px;
      }
      .box-outer {
        position: relative;
        float: left;
        width: 100%;
      }
      .box {
        height: 50px;
        top: 0px;
        left: 0px;
        width: 100%;
        position: absolute;
        background: rgba(237, 237, 237, 0.41);
        border: 2px solid rgba(241, 241, 241, .19);
        box-shadow: 0px 1px 1px rgba(200,200,200,0.75),0px -1px 1px rgba(200,200,200,0.75);
        border-left: 0px;
        border-right: 0px;
        font-family: inherit;
      }
      .col-lg-12, .col-md-12, .col-sm-12, .col-xs-12 {
        position: relative;
        width: 45%;
      }
      h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
        font-family: 'Play', sans-serif;
        font-weight: 500;
        line-height: 1.1;
        color: inherit;
      }
      h3, .h3 {
        font-size: 24px;
      }
      h4, .h4 {
        font-size: 18px;
      }
      a {
        color: #428bca;
        text-decoration: none;
      }
      a:hover {
        color: #30699A;
        text-decoration: underline;
      }
    </style>
    <link href='https://fonts.googleapis.com/css?family=Play:400,700' rel='stylesheet' type='text/css'>
  </head>
  <body class="container-well">
    <div class="container main-body">
      <div class="gradient-well">
        <div class="container-fluid gradient-well-inner">
          <div class="row bottom-well">
            <div class="row">
              <div class="box-outer">
                <div class="box"></div>
                <div >
                  <center>
                    <img src="https://s3-us-west-2.amazonaws.com/groove-staging/logo.png" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" alt="GroovePacker"/>
                  </center>
                </div>
                <div>
                  <br/>
                  <br/>
                  <br/>
                  <center>
                    <h3>Maintenance is in Progress. GroovePacker will be available shortly.</h3>
                    <h4>More information about this upgrade may be available in the
                      <a href="https://www.groovepacker.com/release-notes">release notes</a>.</h4>
                  </center>
                  <br/>
                  <br/>
                  <br/>
                  <br/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>





