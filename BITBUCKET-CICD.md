# Bitbucket Pipelines CI/CD for AWS ECS

Complete guide for setting up automated deployments to AWS ECS using Bitbucket Pipelines with proper staging and production environment separation.

## 🏗️ Architecture Overview

```
Git Push → Bitbucket Pipeline → Build Image → Push to ECR → Deploy to ECS
    ↓
Branch: develop → Staging Environment
Branch: main/master → Production Environment
```

## 📋 Environment Strategy

### **Repository Structure:**
- **ECR Repositories:**
  - `groovepacker-webclient-staging`
  - `groovepacker-webclient-prod`
- **ECS Clusters:**
  - `groovepacker-staging`
  - `groovepacker-production`
- **Load Balancers:**
  - `groovepacker-staging-alb`
  - `groovepacker-prod-alb`

### **Branch Strategy:**
- `develop` branch → Auto-deploy to **Staging**
- `main/master` branch → Auto-deploy to **Production**
- Feature branches → Build only (no deployment)

---

## 🔐 Step 1: AWS Setup for CI/CD

### 1.1 Create IAM User for Bitbucket
1. Go to **AWS Console** → **IAM** → **Users**
2. Click **Create user**
3. **User name**: `bitbucket-cicd`
4. **Access type**: **Programmatic access**
5. Click **Next**

### 1.2 Create IAM Policy
1. Click **Attach policies directly**
2. Click **Create policy**
3. Use **JSON** tab and paste:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ecs:UpdateService",
                "ecs:DescribeServices",
                "ecs:DescribeTaskDefinition",
                "ecs:RegisterTaskDefinition"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "arn:aws:iam::*:role/ecsTaskExecutionRole"
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
            "Resource": "*"
        }
    ]
}
```

4. **Policy name**: `BitbucketECSDeployPolicy`
5. Click **Create policy**

### 1.3 Attach Policy to User
1. Go back to user creation
2. Search and select `BitbucketECSDeployPolicy`
3. Click **Create user**
4. **IMPORTANT**: Copy the **Access Key ID** and **Secret Access Key**

---

## 🔧 Step 2: Bitbucket Repository Setup

### 2.1 Repository Variables
1. Go to your Bitbucket repository
2. **Settings** → **Pipelines** → **Repository variables**
3. Add these variables:

**AWS Credentials:**
- `AWS_ACCESS_KEY_ID`: [Your IAM user access key]
- `AWS_SECRET_ACCESS_KEY`: [Your IAM user secret] (**Secured**)
- `AWS_DEFAULT_REGION`: `us-east-1`

**AWS Account Info:**
- `AWS_ACCOUNT_ID`: [Your 12-digit AWS account ID]

**Staging Environment:**
- `STAGING_ECR_REPO`: `groovepacker-webclient-staging`
- `STAGING_CLUSTER`: `groovepacker-staging`
- `STAGING_SERVICE`: `groovepacker-staging-service`

**Production Environment:**
- `PROD_ECR_REPO`: `groovepacker-webclient-prod`
- `PROD_CLUSTER`: `groovepacker-production`
- `PROD_SERVICE`: `groovepacker-prod-service`

### 2.2 Enable Pipelines
1. **Settings** → **Pipelines** → **Settings**
2. Enable **Pipelines**

---

## 📝 Step 3: Create Bitbucket Pipeline Configuration

Create `bitbucket-pipelines.yml` in your repository root:

```yaml
image: atlassian/default-image:3

definitions:
  services:
    docker:
      memory: 2048

  steps:
    - step: &build-and-test
        name: Build and Test
        services:
          - docker
        script:
          - echo "Building Docker image..."
          - docker build -f Dockerfile.aws -t groovepacker-webclient .
          - echo "Running tests..."
          # Add your test commands here
          # - docker run --rm groovepacker-webclient bundle exec rspec

    - step: &deploy-to-staging
        name: Deploy to Staging
        deployment: staging
        services:
          - docker
        script:
          - echo "Deploying to Staging..."
          - export ECR_REPO_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$STAGING_ECR_REPO"
          - echo "ECR Repository URI:$ECR_REPO_URI"

          # Install AWS CLI
          - apt-get update && apt-get install -y awscli

          # Login to ECR
          - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REPO_URI

          # Build and tag image
          - docker build -f Dockerfile.aws -t $STAGING_ECR_REPO .
          - docker tag $STAGING_ECR_REPO:latest $ECR_REPO_URI:latest
          - docker tag $STAGING_ECR_REPO:latest $ECR_REPO_URI:$BITBUCKET_COMMIT

          # Push to ECR
          - docker push $ECR_REPO_URI:latest
          - docker push $ECR_REPO_URI:$BITBUCKET_COMMIT

          # Update ECS service
          - aws ecs update-service --cluster $STAGING_CLUSTER --service $STAGING_SERVICE --force-new-deployment
          - echo "Staging deployment completed!"

    - step: &deploy-to-production
        name: Deploy to Production
        deployment: production
        trigger: manual
        services:
          - docker
        script:
          - echo "Deploying to Production..."
          - export ECR_REPO_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$PROD_ECR_REPO"
          - echo "ECR Repository URI:$ECR_REPO_URI"

          # Install AWS CLI
          - apt-get update && apt-get install -y awscli

          # Login to ECR
          - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REPO_URI

          # Build and tag image
          - docker build -f Dockerfile.aws -t $PROD_ECR_REPO .
          - docker tag $PROD_ECR_REPO:latest $ECR_REPO_URI:latest
          - docker tag $PROD_ECR_REPO:latest $ECR_REPO_URI:$BITBUCKET_COMMIT

          # Push to ECR
          - docker push $ECR_REPO_URI:latest
          - docker push $ECR_REPO_URI:$BITBUCKET_COMMIT

          # Update ECS service
          - aws ecs update-service --cluster $PROD_CLUSTER --service $PROD_SERVICE --force-new-deployment
          - echo "Production deployment completed!"

pipelines:
  branches:
    develop:
      - step: *build-and-test
      - step: *deploy-to-staging

    main:
      - step: *build-and-test
      - step: *deploy-to-production

    master:
      - step: *build-and-test
      - step: *deploy-to-production

  default:
    - step: *build-and-test

  pull-requests:
    '**':
      - step: *build-and-test
```

---

## 🏗️ Step 4: AWS Infrastructure Setup

### 4.1 Create ECR Repositories
```bash
# Staging repository
aws ecr create-repository --repository-name groovepacker-webclient-staging --region us-east-1

# Production repository
aws ecr create-repository --repository-name groovepacker-webclient-prod --region us-east-1
```

### 4.2 Create ECS Clusters
```bash
# Staging cluster
aws ecs create-cluster --cluster-name groovepacker-staging

# Production cluster
aws ecs create-cluster --cluster-name groovepacker-production
```

### 4.3 Create Task Definitions
Create separate task definitions for each environment with different configurations.

**Staging Task Definition** (`task-definition-staging.json`):
```json
{
  "family": "groovepacker-webclient-staging",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "groovepacker-webclient-staging",
      "image": "ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/groovepacker-webclient-staging:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "RAILS_ENV",
          "value": "staging"
        },
        {
          "name": "SOCKET_ENDPOINT",
          "value": "staging-socket-endpoint"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/groovepacker-webclient-staging",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

**Production Task Definition** (`task-definition-prod.json`):
```json
{
  "family": "groovepacker-webclient-prod",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "groovepacker-webclient-prod",
      "image": "ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/groovepacker-webclient-prod:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "RAILS_ENV",
          "value": "production"
        },
        {
          "name": "SOCKET_ENDPOINT",
          "value": "production-socket-endpoint"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/groovepacker-webclient-prod",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

---

## 🚀 Step 5: Deployment Workflow

### 5.1 Development Workflow
```bash
# 1. Create feature branch
git checkout -b feature/new-feature

# 2. Make changes and commit
git add .
git commit -m "Add new feature"

# 3. Push to trigger build (no deployment)
git push origin feature/new-feature

# 4. Create pull request to develop
# Pipeline runs build and tests only
```

### 5.2 Staging Deployment
```bash
# 1. Merge to develop branch
git checkout develop
git merge feature/new-feature
git push origin develop

# 2. Pipeline automatically:
#    - Builds Docker image
#    - Pushes to staging ECR
#    - Deploys to staging ECS
```

### 5.3 Production Deployment
```bash
# 1. Merge develop to main
git checkout main
git merge develop
git push origin main

# 2. Pipeline builds and waits for manual approval
# 3. Go to Bitbucket → Pipelines → Click "Deploy to Production"
# 4. Pipeline deploys to production ECS
```

---

## 🔧 Step 6: Environment-Specific Configurations

### 6.1 Create Environment Files

**For Staging** (`.env.staging`):
```bash
RAILS_ENV=staging
SOCKET_ENDPOINT=wss://staging-socket.yourdomain.com
SITE_HOST=staging.yourdomain.com
GROOV_ANALYTIC=staging-analytics-endpoint
```

**For Production** (`.env.production`):
```bash
RAILS_ENV=production
SOCKET_ENDPOINT=wss://socket.yourdomain.com
SITE_HOST=yourdomain.com
GROOV_ANALYTIC=production-analytics-endpoint
```

---

## 📊 Step 7: Monitoring and Security

### 7.1 Bitbucket Notifications
1. **Repository Settings** → **Pipelines** → **Notifications**
2. Add Slack/Email notifications for deployment status

### 7.2 Security Best Practices
- Store secrets in **AWS Parameter Store**
- Use **Secured variables** in Bitbucket
- Rotate access keys regularly
- Use least-privilege IAM policies

---

## 🎯 Benefits of This CI/CD Setup

✅ **Automated deployments** - No manual intervention
✅ **Environment separation** - Staging and production isolated
✅ **Git-based workflow** - Deploy from branches
✅ **Manual production approval** - Safety gate
✅ **Rollback capability** - Easy to revert
✅ **Monitoring** - Full deployment visibility
✅ **Security** - Proper secrets management

This provides enterprise-grade CI/CD with proper environment separation!
