# AWS ECS Deployment Guide

Complete guide for deploying GroovePacker Web Client to AWS using ECS, ECR, Application Load Balancer, Auto Scaling, and Route53.

## 🏗️ Architecture Overview

```
Internet → Route53 → ALB → ECS Service → ECR Image
                    ↓
                Target Groups → Health Checks
                    ↓
                Auto Scaling Group
```

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Docker installed locally
- Domain name for Route53 (optional)

## 🚀 Step 1: ECR Setup

### Create ECR Repository

```bash
# Create ECR repository
aws ecr create-repository \
    --repository-name groovepacker/webclient \
    --region us-east-1

# Get login token and login to ECR
aws ecr get-login-password --region us-east-1 | \
    docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com
```

### Build and Push Image

```bash
# Build production image (without nginx for ECS)
docker build -f Dockerfile.aws -t groovepacker-webclient .

# Tag for ECR
docker tag groovepacker-webclient:latest \
    <account-id>.dkr.ecr.us-east-1.amazonaws.com/groovepacker/webclient:latest

# Push to ECR
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/groovepacker/webclient:latest
```

## 🐳 Step 2: Create AWS-Optimized Dockerfile

Create `Dockerfile.aws` for ECS deployment:

```dockerfile
FROM ruby:2.6.10-slim

# Install system dependencies
RUN apt-get update -qq && apt-get install -y \
    build-essential \
    libpq-dev \
    libsqlite3-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy Gemfile and install gems
COPY Gemfile Gemfile.lock ./
RUN bundle install --deployment --without development test

# Copy application code
COPY . .

# Precompile assets for production
RUN RAILS_ENV=production bundle exec rake assets:precompile

# Create non-root user
RUN groupadd -r rails && useradd -r -g rails rails
RUN chown -R rails:rails /app
USER rails

EXPOSE 3000

CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0", "-p", "3000", "-e", "production"]
```

## ⚙️ Step 3: ECS Task Definition

Create `task-definition.json`:

```json
{
  "family": "groovepacker-webclient",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "groovepacker-webclient",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/groovepacker/webclient:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "RAILS_ENV",
          "value": "production"
        },
        {
          "name": "SOCKET_ENDPOINT",
          "value": "wss://socket.yourdomain.com"
        },
        {
          "name": "SITE_HOST",
          "value": "yourdomain.com"
        }
      ],
      "secrets": [
        {
          "name": "SECRET_KEY_BASE",
          "valueFrom": "arn:aws:ssm:us-east-1:<account-id>:parameter/groovepacker/secret_key_base"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/groovepacker-webclient",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "curl -f http://localhost:3000/health || exit 1"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

## 🔧 Step 4: Create ECS Cluster and Service

### Create ECS Cluster

```bash
# Create ECS cluster
aws ecs create-cluster \
    --cluster-name groovepacker-cluster \
    --capacity-providers FARGATE \
    --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1
```

### Register Task Definition

```bash
# Register task definition
aws ecs register-task-definition \
    --cli-input-json file://task-definition.json
```

### Create Application Load Balancer

```bash
# Create ALB
aws elbv2 create-load-balancer \
    --name groovepacker-alb \
    --subnets subnet-12345678 subnet-87654321 \
    --security-groups sg-12345678 \
    --scheme internet-facing \
    --type application \
    --ip-address-type ipv4

# Create target group
aws elbv2 create-target-group \
    --name groovepacker-tg \
    --protocol HTTP \
    --port 3000 \
    --vpc-id vpc-12345678 \
    --target-type ip \
    --health-check-path /health \
    --health-check-interval-seconds 30 \
    --health-check-timeout-seconds 5 \
    --healthy-threshold-count 2 \
    --unhealthy-threshold-count 3

# Create listener
aws elbv2 create-listener \
    --load-balancer-arn <alb-arn> \
    --protocol HTTP \
    --port 80 \
    --default-actions Type=forward,TargetGroupArn=<target-group-arn>
```

## 🚀 Step 5: Create ECS Service

Create `service-definition.json`:

```json
{
  "serviceName": "groovepacker-webclient-service",
  "cluster": "groovepacker-cluster",
  "taskDefinition": "groovepacker-webclient",
  "desiredCount": 2,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": ["subnet-12345678", "subnet-87654321"],
      "securityGroups": ["sg-12345678"],
      "assignPublicIp": "ENABLED"
    }
  },
  "loadBalancers": [
    {
      "targetGroupArn": "<target-group-arn>",
      "containerName": "groovepacker-webclient",
      "containerPort": 3000
    }
  ],
  "healthCheckGracePeriodSeconds": 300
}
```

```bash
# Create ECS service
aws ecs create-service \
    --cli-input-json file://service-definition.json
```

## 📈 Step 6: Auto Scaling Setup

```bash
# Register scalable target
aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --resource-id service/groovepacker-cluster/groovepacker-webclient-service \
    --scalable-dimension ecs:service:DesiredCount \
    --min-capacity 2 \
    --max-capacity 10

# Create scaling policy
aws application-autoscaling put-scaling-policy \
    --service-namespace ecs \
    --resource-id service/groovepacker-cluster/groovepacker-webclient-service \
    --scalable-dimension ecs:service:DesiredCount \
    --policy-name groovepacker-cpu-scaling \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration '{
        "TargetValue": 70.0,
        "PredefinedMetricSpecification": {
            "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
        },
        "ScaleOutCooldown": 300,
        "ScaleInCooldown": 300
    }'
```

## 🌐 Step 7: Route53 Setup

```bash
# Create hosted zone (if you own the domain)
aws route53 create-hosted-zone \
    --name yourdomain.com \
    --caller-reference $(date +%s)

# Create A record pointing to ALB
aws route53 change-resource-record-sets \
    --hosted-zone-id Z123456789 \
    --change-batch '{
        "Changes": [{
            "Action": "CREATE",
            "ResourceRecordSet": {
                "Name": "gp55.yourdomain.com",
                "Type": "A",
                "AliasTarget": {
                    "DNSName": "<alb-dns-name>",
                    "EvaluateTargetHealth": true,
                    "HostedZoneId": "<alb-hosted-zone-id>"
                }
            }
        }]
    }'
```

## 🔒 Step 8: SSL/TLS Setup

### Request SSL Certificate

```bash
# Request certificate from ACM
aws acm request-certificate \
    --domain-name yourdomain.com \
    --subject-alternative-names "*.yourdomain.com" \
    --validation-method DNS \
    --region us-east-1

# Add HTTPS listener to ALB
aws elbv2 create-listener \
    --load-balancer-arn <alb-arn> \
    --protocol HTTPS \
    --port 443 \
    --certificates CertificateArn=<certificate-arn> \
    --default-actions Type=forward,TargetGroupArn=<target-group-arn>
```

## 🔐 Step 9: Security & Secrets

### Store Secrets in Parameter Store

```bash
# Store Rails secret key
aws ssm put-parameter \
    --name "/groovepacker/secret_key_base" \
    --value "your-secret-key-base" \
    --type "SecureString"
```

## 📊 Step 10: Monitoring & Logging

### CloudWatch Log Group

```bash
# Create log group
aws logs create-log-group \
    --log-group-name /ecs/groovepacker-webclient \
    --retention-in-days 30
```

## 🚀 Step 11: Deployment Pipeline

### Create deployment script `deploy.sh`:

```bash
#!/bin/bash
set -e

# Variables
ECR_REPO="<account-id>.dkr.ecr.us-east-1.amazonaws.com/groovepacker/webclient"
CLUSTER_NAME="groovepacker-cluster"
SERVICE_NAME="groovepacker-webclient-service"

# Build and push image
docker build -f Dockerfile.aws -t groovepacker-webclient .
docker tag groovepacker-webclient:latest $ECR_REPO:latest

# Push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REPO
docker push $ECR_REPO:latest

# Update ECS service
aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --force-new-deployment
```

## 🔍 Troubleshooting

### Common Issues

**Service fails to start:**
- Check CloudWatch logs: `/ecs/groovepacker-webclient`
- Verify security groups allow traffic on port 3000

**Health checks failing:**
- Add health check endpoint to Rails app
- Verify health check path in target group

### Useful Commands

```bash
# Check service status
aws ecs describe-services \
    --cluster groovepacker-cluster \
    --services groovepacker-webclient-service

# Force new deployment
aws ecs update-service \
    --cluster groovepacker-cluster \
    --service groovepacker-webclient-service \
    --force-new-deployment
```

## 💡 Key Points for ECS Deployment

1. **No nginx needed**: ALB handles load balancing and SSL termination
2. **WebSocket handling**: If you need WebSockets, configure ALB for WebSocket support or use a separate service
3. **Health checks**: Add `/health` endpoint to your Rails app
4. **Secrets**: Use AWS Parameter Store or Secrets Manager
5. **Logging**: CloudWatch logs are automatically configured
6. **Auto scaling**: Based on CPU/memory metrics

This setup provides a production-ready, scalable deployment on AWS ECS with proper monitoring, security, and auto-scaling capabilities.
