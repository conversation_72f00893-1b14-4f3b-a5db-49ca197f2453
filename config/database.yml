# MySQL.  Versions 4.1 and 5.0 are recommended.
#
# Install the MYSQL driver
#   gem install mysql2
#
# Ensure the MySQL gem is defined in your Gemfile
#   gem 'mysql2'
#
# And be sure to use new-style password hashing:
#   http://dev.mysql.com/doc/refman/5.0/en/old-client.html
development:
  adapter: sqlite3
  encoding: utf8
  reconnect: false
  database: groovepacks_development
  pool: 5
  username: <%= ENV['DB_USERNAME'] %>
  password: <%= ENV['DB_PASSWORD'] %>
  socket: /private/tmp/mysql.sock

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  adapter: sqlite3
  encoding: utf8
  reconnect: false
  database: groovepacks_test
  pool: 5
  username: <%= ENV['DB_USERNAME'] %>
  password: <%= ENV['DB_PASSWORD'] %>
  #socket: /private/tmp/mysql.sock
  socket: /var/run/mysqld/mysqld.sock

production:
  adapter: sqlite3
  encoding: utf8
  reconnect: false
  database: groovepacks_production_split
  pool: 5
  host: <%= ENV['DB_HOST'] %>
  username: <%= ENV['DB_USERNAME'] %>
  password: <%= ENV['DB_PASSWORD'] %>
  port: 3306
  #socket: /var/run/mysqld/mysqld.sock

staging:
  adapter: sqlite3
  encoding: utf8
  reconnect: false
  database: groovepacks_production
  pool: 5
  host: <%= ENV['DB_HOST'] %>
  username: <%= ENV['DB_USERNAME'] %>
  password: <%= ENV['DB_PASSWORD'] %>
  port: 3306
  #socket: /var/run/mysqld/mysqld.sock
