# GroovePacker Web Client

A Rails 3.2 application for GroovePacker's web interface, containerized with Docker for easy development and deployment.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Git

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd groovepacker-webclient
   ```

2. **Configure local hosts (Required)**

   Add the following entries to your `/etc/hosts` file:
   ```bash
   # GroovePacker Local Development
   127.0.0.1 gp55.localpacker.com
   127.0.0.1 localhost.localpacker.com
   127.0.0.1 api.localpacker.com
   ```

   **How to edit /etc/hosts:**
   ```bash
   # On macOS/Linux
   sudo nano /etc/hosts

   # Or using vim
   sudo vim /etc/hosts
   ```

3. **Build and start the application**
   ```bash
   # Build the Docker image
   docker-compose build

   # Start the services
   docker-compose up
   ```

4. **Access the application**
   - Web Application: http://gp55.localpacker.com
   - Direct Rails Server: http://localhost:3000

## 🏗️ Architecture

### Services

- **web**: Rails 3.2 application (Ruby 2.6.10)
- **nginx**: Reverse proxy and load balancer
- **Network**: Custom `groovepacker` network for multi-service communication

### Key Features

- ✅ **Bundle caching**: Gems are cached in Docker volumes for faster builds
- ✅ **Asset compilation**: Conditional asset precompilation (development vs production)
- ✅ **Multi-service ready**: Network configured for backend and analytics services
- ✅ **Hot reloading**: Code changes reflected immediately in development

## 🔧 Development

### Running Commands

```bash
# Start services
docker-compose up

# Start in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild after Gemfile changes
docker-compose build web

### Adding Dependencies

```bash
# Add a new gem to Gemfile, then rebuild
docker-compose build web
docker-compose up
```

### Connecting Other Services

If you have backend or analytics repositories, add this to their docker-compose files:

```yaml
networks:
  groovepacker:
    external: true

services:
  your-service:
    networks:
      - groovepacker
```

## 🚀 Production Deployment

### Environment Setup

1. **Prepare environment variables**

   Create a `.env` file with production values:
   ```bash
   SOCKET_ENDPOINT=socket-endpoint
   SITE_HOST=production-domain.com
   GROOV_ANALYTIC=analytics-endpoint
   GROOV_ANALYTIC_URL=analytics-endpoint-rul
   ```

3. **Create nginx.prod.conf**

   Create a production nginx configuration:
   ```bash
   cp nginx.conf nginx.prod.conf
   # Edit nginx.prod.conf for production settings
   ```

### Deployment Commands

```bash
# Build production image
docker-compose -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.prod.yml up -d

# View production logs
docker-compose -f docker-compose.prod.yml logs -f

# Stop production services
docker-compose -f docker-compose.prod.yml down
```

### Production Checklist

- [ ] Environment variables configured
- [ ] SSL certificates in place
- [ ] nginx.prod.conf created and configured
- [ ] Database connection tested
- [ ] Domain DNS pointing to server
- [ ] Firewall configured (ports 80, 443)
- [ ] Monitoring and logging set up

## 🔍 Troubleshooting

### Common Issues

**Build fails with sqlite3 errors:**
- The Dockerfile includes `libsqlite3-dev` - this should be resolved

**Bundle install is slow:**
- Bundle cache is enabled via Docker volumes

**Can't access gp55.localpacker.com:**
- Check `/etc/hosts` file configuration
- Ensure Docker services are running

**Assets not loading:**
- In development: Assets compile on-the-fly
- In production: Assets are precompiled during build

**Legacy xmlparser gem issues (Ubuntu servers):**
```bash
sudo apt-get install libexpat1-dev
```

**Modern Javascript build failures:**
- Try running `rake assets:precompile --trace` locally for debugging

### Useful Commands

```bash
# Check running containers
docker ps

# Check Docker networks
docker network ls

# Inspect the groovepacker network
docker network inspect groovepacker-webclient_groovepacker

# Clean up Docker resources
docker system prune

# View container logs
docker-compose logs web
docker-compose logs nginx
```

## 📁 Project Structure

```
.
├── app/                    # Rails application code
├── config/                 # Rails configuration
├── docker-compose.yml      # Development services
├── docker-compose.prod.yml # Production services
├── Dockerfile             # Container definition
├── nginx.conf             # Nginx configuration
├── Gemfile                # Ruby dependencies
└── README.md              # This file
```

## 🤝 Contributing

1. Make changes to the codebase
2. Test locally with `docker-compose up`
3. Ensure all services start correctly
4. Submit pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section above
- Review Docker and nginx logs
- Ensure all prerequisites are met
