# AWS ECS Deployment - UI Guide

Complete step-by-step guide for deploying GroovePacker Web Client to AWS using the AWS Console (Web Interface). No command line experience required!

## 🎯 Overview

We'll deploy your Rails app to AWS using:
- **ECR** (Elastic Container Registry) - Stores your Docker image
- **ECS** (Elastic Container Service) - Runs your containers
- **ALB** (Application Load Balancer) - Distributes traffic
- **VPC** (Virtual Private Cloud) - Network security

## 📋 Prerequisites

1. **AWS Account** - Sign up at [aws.amazon.com](https://aws.amazon.com)
2. **Docker Desktop** - Running on your local machine
3. **AWS CLI** - For pushing Docker images (we'll install this)

---

## 🚀 Step 1: Set Up AWS CLI (One-time setup)

### Install AWS CLI

**On macOS:**
```bash
brew install awscli
```

**On Windows:**
Download from: https://aws.amazon.com/cli/

**On Linux:**
```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

### Get AWS Credentials

1. Go to [AWS Console](https://console.aws.amazon.com)
2. Click your name (top right) → **Security credentials**
3. Scroll down to **Access keys**
4. Click **Create access key**
5. Choose **Command Line Interface (CLI)**
6. Click **Next** → **Create access key**
7. **IMPORTANT**: Copy both the Access Key ID and Secret Access Key

### Configure AWS CLI
```bash
aws configure
```
Enter:
- **AWS Access Key ID**: [paste from above]
- **AWS Secret Access Key**: [paste from above]
- **Default region name**: `us-east-1`
- **Default output format**: `json`

---

## 🐳 Step 2: Create Container Registry (ECR)

### 2.1 Navigate to ECR
1. Go to [AWS Console](https://console.aws.amazon.com)
2. Search for **"ECR"** in the search bar
3. Click **Elastic Container Registry**

### 2.2 Create Repository
1. Click **Create repository**
2. **Repository name**: `groovepacker-webclient`
3. Leave other settings as default
4. Click **Create repository**

### 2.3 Push Your Docker Image
1. Click on your repository name
2. Click **View push commands** (top right)
3. Follow the 4 commands shown (they'll look like this):

```bash
# 1. Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin [your-account-id].dkr.ecr.us-east-1.amazonaws.com

# 2. Build your image
docker build -f Dockerfile.aws -t groovepacker-webclient .

# 3. Tag your image
docker tag groovepacker-webclient:latest [your-account-id].dkr.ecr.us-east-1.amazonaws.com/groovepacker-webclient:latest

# 4. Push your image
docker push [your-account-id].dkr.ecr.us-east-1.amazonaws.com/groovepacker-webclient:latest
```

**✅ Success**: You should see your image in the ECR repository

---

## 🏗️ Step 3: Create ECS Cluster

### 3.1 Navigate to ECS
1. Search for **"ECS"** in AWS Console
2. Click **Elastic Container Service**

### 3.2 Create Cluster
1. Click **Create cluster**
2. **Cluster name**: `groovepacker-cluster`
3. **Infrastructure**: Select **AWS Fargate (serverless)**
4. Leave other settings as default
5. Click **Create**

**✅ Success**: Your cluster should show as "Active"

---

## 📋 Step 4: Create Task Definition

### 4.1 Create Task Definition
1. In ECS Console, click **Task definitions** (left sidebar)
2. Click **Create new task definition**
3. **Task definition family**: `groovepacker-webclient`

### 4.2 Configure Infrastructure
1. **Launch type**: **AWS Fargate**
2. **Operating system/Architecture**: **Linux/X86_64**
3. **CPU**: **0.25 vCPU**
4. **Memory**: **0.5 GB**

### 4.3 Configure Container
1. **Container name**: `groovepacker-webclient`
2. **Image URI**: `[your-account-id].dkr.ecr.us-east-1.amazonaws.com/groovepacker-webclient:latest`
   (Copy this from your ECR repository)
3. **Port mappings**:
   - **Container port**: `3000`
   - **Protocol**: `TCP`
   - **Port name**: `groovepacker-3000-tcp`

### 4.4 Environment Variables
1. Scroll down to **Environment variables**
2. Add:
   - **Key**: `RAILS_ENV`, **Value**: `production`
   - **Key**: `SOCKET_ENDPOINT`, **Value**: `your-socket-endpoint`
   - **Key**: `SITE_HOST`, **Value**: `your-domain.com`

### 4.5 Logging
1. Scroll down to **Logging**
2. Check **Use log collection**
3. **Log driver**: `awslogs`
4. **Log group**: `/ecs/groovepacker-webclient`
5. **Log region**: `us-east-1`
6. **Log stream prefix**: `ecs`

### 4.6 Create Task Definition
1. Click **Create** at the bottom

**✅ Success**: Your task definition should be created

---

## 🌐 Step 5: Create Application Load Balancer

### 5.1 Navigate to EC2 Load Balancers
1. Search for **"EC2"** in AWS Console
2. Click **EC2**
3. In left sidebar, scroll down to **Load Balancing** → **Load Balancers**

### 5.2 Create Load Balancer
1. Click **Create load balancer**
2. Choose **Application Load Balancer**
3. Click **Create**

### 5.3 Basic Configuration
1. **Load balancer name**: `groovepacker-alb`
2. **Scheme**: **Internet-facing**
3. **IP address type**: **IPv4**

### 5.4 Network Mapping
1. **VPC**: Select your default VPC
2. **Mappings**: Select **at least 2 availability zones**
3. Check the boxes for 2-3 different zones

### 5.5 Security Groups
1. Click **Create new security group**
2. **Security group name**: `groovepacker-alb-sg`
3. **Description**: `Security group for GroovePacker ALB`
4. **Inbound rules**:
   - **Type**: `HTTP`, **Port**: `80`, **Source**: `0.0.0.0/0`
   - **Type**: `HTTPS`, **Port**: `443`, **Source**: `0.0.0.0/0`
5. Click **Create security group**
6. Go back to Load Balancer creation and select this security group

### 5.6 Listeners and Routing
1. **Protocol**: `HTTP`, **Port**: `80`
2. **Default action**: **Create target group**
3. **Target group name**: `groovepacker-tg`
4. **Target type**: **IP addresses**
5. **Protocol**: `HTTP`, **Port**: `3000`
6. **VPC**: Select your default VPC
7. **Health check path**: `/` (or `/health` if you have a health endpoint)
8. Click **Next** → **Create target group**

### 5.7 Create Load Balancer
1. Select your newly created target group
2. Click **Create load balancer**

**✅ Success**: Load balancer should show as "Provisioning" then "Active"

---

## 🚀 Step 6: Create ECS Service

### 6.1 Navigate to Your Cluster
1. Go back to **ECS** → **Clusters**
2. Click on `groovepacker-cluster`

### 6.2 Create Service
1. Click **Create** in the Services tab
2. **Launch type**: **Fargate**

### 6.3 Configure Service
1. **Task definition**: Select `groovepacker-webclient`
2. **Service name**: `groovepacker-service`
3. **Number of tasks**: `1` (start with 1, can scale later)

### 6.4 Configure Network
1. **VPC**: Select your default VPC
2. **Subnets**: Select the same subnets you used for the load balancer
3. **Security group**: **Create new security group**
   - **Name**: `groovepacker-ecs-sg`
   - **Inbound rules**:
     - **Type**: `Custom TCP`, **Port**: `3000`, **Source**: Select the ALB security group

### 6.5 Configure Load Balancer
1. **Load balancer type**: **Application Load Balancer**
2. **Load balancer**: Select `groovepacker-alb`
3. **Target group**: Select `groovepacker-tg`
4. **Container to load balance**: `groovepacker-webclient:3000`

### 6.6 Create Service
1. Click **Create**

**✅ Success**: Service should show as "Running" after a few minutes

---

## 🎉 Step 7: Test Your Deployment

### 7.1 Get Your Load Balancer URL
1. Go to **EC2** → **Load Balancers**
2. Click on `groovepacker-alb`
3. Copy the **DNS name** (looks like: `groovepacker-alb-123456789.us-east-1.elb.amazonaws.com`)

### 7.2 Test in Browser
1. Paste the DNS name in your browser
2. You should see your Rails application!

**🎉 Congratulations!** Your app is now running on AWS!

---

## 🔧 Step 8: Set Up Custom Domain (Optional)

### 8.1 Navigate to Route53
1. Search for **"Route53"** in AWS Console
2. Click **Route 53**

### 8.2 Create Hosted Zone (if you own a domain)
1. Click **Create hosted zone**
2. **Domain name**: `yourdomain.com`
3. Click **Create hosted zone**

### 8.3 Create Record
1. Click **Create record**
2. **Record name**: `gp55` (for gp55.yourdomain.com)
3. **Record type**: `A`
4. **Alias**: **Yes**
5. **Route traffic to**: **Alias to Application and Classic Load Balancer**
6. **Region**: `US East (N. Virginia)`
7. **Load balancer**: Select your `groovepacker-alb`
8. Click **Create records**

**✅ Success**: Your app will be available at `gp55.yourdomain.com`

---

## 🔒 Step 9: Add SSL Certificate (Optional)

### 9.1 Request Certificate
1. Search for **"Certificate Manager"** in AWS Console
2. Click **AWS Certificate Manager**
3. Click **Request certificate**
4. **Domain name**: `yourdomain.com`
5. **Add another name**: `*.yourdomain.com`
6. **Validation method**: **DNS validation**
7. Click **Request**

### 9.2 Validate Certificate
1. Click on your certificate
2. Click **Create records in Route 53** for each domain
3. Wait for validation (can take up to 30 minutes)

### 9.3 Add HTTPS Listener
1. Go to **EC2** → **Load Balancers**
2. Click on your load balancer
3. Go to **Listeners** tab
4. Click **Add listener**
5. **Protocol**: `HTTPS`, **Port**: `443`
6. **Default actions**: Forward to your target group
7. **Security policy**: Use default
8. **Certificate**: Select your certificate
9. Click **Add**

**✅ Success**: Your app now supports HTTPS!

---

## 🔍 Troubleshooting

### Common Issues

**Service won't start:**
1. Check **ECS** → **Clusters** → **Services** → **Events** tab
2. Look for error messages

**Can't access the app:**
1. Check security groups allow traffic
2. Verify target group health checks are passing
3. Check load balancer DNS name is correct

**Health checks failing:**
1. Make sure your app responds to HTTP requests on port 3000
2. Check the health check path in your target group

### Useful Console Locations

- **View logs**: **CloudWatch** → **Log groups** → `/ecs/groovepacker-webclient`
- **Monitor performance**: **ECS** → **Clusters** → **Metrics** tab
- **Check costs**: **Billing** → **Cost Explorer**

---

## 💰 Cost Estimation

**Monthly costs (approximate):**
- **Fargate**: ~$15-30/month for 1 container
- **Load Balancer**: ~$20/month
- **Data transfer**: ~$5-10/month
- **Total**: ~$40-60/month

**Cost optimization tips:**
- Start with 1 container, scale as needed
- Use Fargate Spot for development environments
- Monitor usage in Cost Explorer

---

## 🔄 Making Updates

### To deploy new code:
1. Build and push new Docker image to ECR (Step 2.3)
2. Go to **ECS** → **Services** → **Update service**
3. Check **Force new deployment**
4. Click **Update**

Your new code will be deployed automatically!

---

## 🎯 Next Steps

Once your basic deployment is working:
1. Set up auto-scaling (ECS Console → Services → Auto Scaling)
2. Add monitoring and alerts (CloudWatch)
3. Set up CI/CD pipeline (CodePipeline)
4. Add database (RDS)

**🎉 You've successfully deployed to AWS ECS!**
