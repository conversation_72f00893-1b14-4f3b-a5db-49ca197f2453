#!/usr/bin/env ruby.exe
  # This command will automatically be run when you run "rails" with Rails 3 gems installed from the root of your application.
 require 'rubygems' # if ruby .7
 require 'rails/commands/server'
 require 'rack'
 require 'webrick'
 require 'webrick/https'
  
  module Rails
      class Server < ::Rack::Server
         def default_options
              super.merge({
                  :Port => 3001,
                  :environment => (ENV['RAILS_ENV'] || "development").dup,
                  :daemonize => false,
                  :debugger => false,
                  :config => File.expand_path("config.ru"),
                  :SSLEnable => true,
                  :SSLVerifyClient => OpenSSL::SSL::VERIFY_NONE,
                  :SSLPrivateKey => OpenSSL::PKey::RSA.new(
                        File.open("config/cert/ss/gpacks.cert.key").read),
                  :SSLCertificate => OpenSSL::X509::Certificate.new(
                         File.open("config/cert/ss/gpacks.cert.crt").read),
                  :SSLCertName => [["CN", WEBrick::Utils::getservername]],
              })
          end
      end
  end
 
 APP_PATH = File.expand_path('../../config/application',  __FILE__)
 require File.expand_path('../../config/boot',  __FILE__)
 require 'rails/commands'
