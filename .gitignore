# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile ~/.gitignore_global

# Ignore bundler config
/.bundle

/vendor/bundle
/public/assets

# Ignore the default SQLite database.
/db/*.sqlite3

# Ignore all logfiles and tempfiles.
log/*.log
tmp/
rspec.xml
node_modules/
protractor_report/
test/
coverage/
pids/

#Ignore byebug history
.byebug_history

.ruby-version
.ruby-gemset
