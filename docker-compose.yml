version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - RAILS_ENV=development
    volumes:
      - .:/app
      - bundle_cache:/usr/local/bundle
    command: bundle exec rails server -b 0.0.0.0 -p 3000

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - web

volumes:
  bundle_cache:
