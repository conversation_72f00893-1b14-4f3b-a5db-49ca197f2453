version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - RAILS_ENV=development
      - DB_HOST=db
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
    volumes:
      - .:/app
      - bundle_cache:/usr/local/bundle
    depends_on:
      - db
    command: bundle exec rails server -b 0.0.0.0 -p 3000

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - web

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=groovepacks_development
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
  bundle_cache: