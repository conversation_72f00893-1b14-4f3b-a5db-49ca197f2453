FROM ruby:2.6.10-slim

# Install system dependencies
RUN apt-get update -qq && apt-get install -y \
    build-essential \
    libpq-dev \
    libsqlite3-dev \
    nodejs \
    npm \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy Gemfile and install gems
COPY Gemfile Gemfile.lock ./
RUN bundle install

# Copy application code
COPY . .

# Conditionally precompile assets only for production
ARG RAILS_ENV=development
RUN if [ "$RAILS_ENV" = "production" ]; then \
      RAILS_ENV=production bundle exec rake assets:precompile; \
    fi

EXPOSE 3000

CMD ["rails", "server", "-b", "0.0.0.0", "-p", "3000"]
