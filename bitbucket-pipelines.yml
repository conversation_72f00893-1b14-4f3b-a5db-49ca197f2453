image: atlassian/default-image:5

clone:
  depth: full

definitions:
  caches:
    bundler: vendor/bundle
  services:
    docker:
      memory: 2048

options:
  docker: true

pipelines:
 branches:
    master:
      - step:
          name: Wait for manual trigger
          script:
            - echo "Waiting for manual trigger"
      - step:
          image: capsens/ruby-node-yarn:2.6.2
          name: Create Build
          caches:
            - bundler
          trigger: manual
          script:
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Set archived repo
            - echo "deb http://archive.debian.org/debian stretch main contrib non-free" | tee /etc/apt/sources.list
            # Disable outdated signature check
            - echo 'Acquire::Check-Valid-Until "false";' | tee /etc/apt/apt.conf.d/99no-check-valid-until
            - echo 'APT::Get::AllowUnauthenticated "true";' | tee /etc/apt/apt.conf.d/99unauth
            # Update and install zip with signature check bypassed
            - apt-get -o Acquire::AllowInsecureRepositories=true -o Acquire::AllowDowngradeToInsecureRepositories=true -o Acquire::Check-Valid-Until=false update
            - apt-get install -y --allow-unauthenticated zip
            - rm -rf *
            - mkdir -p groove
            - cd groove
            - git clone -b master --single-branch *****************:groovepacker/groovepacker-webclient.git .
            - ls -al
            - bundle install --path vendor/bundle
            - bundle binstubs --all
            - RAILS_ENV=production bundle exec rake assets:precompile
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set default.region $AWS_DEFAULT_REGION
            - aws s3 cp s3://groove-code-deploy/appspec.yml .
            - aws s3 cp s3://groove-code-deploy/remove.sh .
            - aws s3 cp s3://groove-code-deploy/qz_license/qz-private-key.pem .
            - aws s3 cp s3://groove-code-deploy/qz_license/digital-certificate.txt .
            - aws s3 cp s3://groove-code-deploy/ssls/production/groovepacker.crt .
            - aws s3 cp s3://groove-code-deploy/ssls/production/groovepacker.key .
            - aws s3 cp s3://groove-code-deploy/groovepacker-web-client/production/nginx.conf .
            - aws s3 cp s3://groove-code-deploy/groovepacker-web-client/production/deploy-ruby-2.6.2.sh deploy.sh
            - aws s3 cp s3://groove-code-deploy/groovepacker/production/.env.production .env.production
            - ls -al
            - zip -r ../groovepacker-webclient.zip ./ -x '*.git*' -x 'tmp*' -x 'log/*' -x 'vendor/bundle/*' -x 'groovepacker-webclient.tgz'
          artifacts:
            - groovepacker-webclient.zip
      - step:
          name: Uploading to S3
          services:
            - docker
          script:
            - pipe: atlassian/aws-code-deploy:1.5.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
                COMMAND: 'upload'
                APPLICATION_NAME: groovepacker-web-client
                S3_BUCKET: groovepacker-angular-deployments
                ZIP_FILE: 'groovepacker-webclient.zip'
                DEBUG: 'true'
      - step:
          name: Deploying with CodeDeploy on Production
          deployment: production
          trigger: manual
          services:
            - docker
          script:
            - pipe: atlassian/aws-code-deploy:1.5.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
                COMMAND: 'deploy'
                APPLICATION_NAME: groovepacker-web-client
                DEPLOYMENT_GROUP: gp-angular-prod
                S3_BUCKET: groovepacker-angular-deployments
                IGNORE_APPLICATION_STOP_FAILURES: 'true'
                FILE_EXISTS_BEHAVIOR: 'OVERWRITE'
                WAIT: 'true'
                DEBUG: 'true'
    staging:
      - step:
          name: Wait for manual trigger
          script:
            - echo "Waiting for manual trigger"
      - step:
          image: capsens/ruby-node-yarn:2.6.2
          name: Create Build
          caches:
            - bundler
          trigger: manual
          script:
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            - unzip awscliv2.zip
            - ./aws/install
            - aws --version
            # Set archived repo
            - echo "deb http://archive.debian.org/debian stretch main contrib non-free" | tee /etc/apt/sources.list
            # Disable outdated signature check
            - echo 'Acquire::Check-Valid-Until "false";' | tee /etc/apt/apt.conf.d/99no-check-valid-until
            - echo 'APT::Get::AllowUnauthenticated "true";' | tee /etc/apt/apt.conf.d/99unauth
            # Update and install zip with signature check bypassed
            - apt-get -o Acquire::AllowInsecureRepositories=true -o Acquire::AllowDowngradeToInsecureRepositories=true -o Acquire::Check-Valid-Until=false update
            - apt-get install -y --allow-unauthenticated zip
            - rm -rf *
            - mkdir -p groove
            - cd groove
            - git clone -b staging --single-branch *****************:groovepacker/groovepacker-webclient.git .
            - ls -al
            - bundle install --path vendor/bundle
            - bundle binstubs --all
            - RAILS_ENV=staging bundle exec rake assets:precompile
            - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
            - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
            - aws configure set default.region $AWS_DEFAULT_REGION
            - aws s3 cp s3://groove-code-deploy/appspec.yml .
            - aws s3 cp s3://groove-code-deploy/remove.sh .
            - aws s3 cp s3://groove-code-deploy/ssls/staging/groovepacker.crt .
            - aws s3 cp s3://groove-code-deploy/ssls/staging/groovepacker.key .
            - aws s3 cp s3://groove-code-deploy/groovepacker-web-client/staging/nginx.conf .
            - aws s3 cp s3://groove-code-deploy/groovepacker-web-client/staging/deploy-ruby-2.6.2.sh deploy.sh
            - aws s3 cp s3://groove-code-deploy/groovepacker/staging/.env.staging .env.staging
            - aws s3 cp s3://groove-code-deploy/qz_license/qz-private-key.pem .
            - aws s3 cp s3://groove-code-deploy/qz_license/digital-certificate.txt .
            - ls -al
            - zip -r ../groovepacker-webclient-staging.zip ./ -x '*.git*' -x 'tmp*' -x 'log/*' -x 'vendor/bundle/*' -x 'groovepacker-webclient.tgz'
          artifacts:
            - groovepacker-webclient-staging.zip
      - step:
          name: Uploading to S3
          services:
            - docker
          script:
            - pipe: atlassian/aws-code-deploy:1.5.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
                COMMAND: 'upload'
                APPLICATION_NAME: groovepacker-web-client
                S3_BUCKET: groove-deployments-staging
                ZIP_FILE: 'groovepacker-webclient-staging.zip'
                DEBUG: 'true'
      - step:
          name: Deploying with CodeDeploy on Staging
          deployment: staging
          trigger: manual
          services:
            - docker
          script:
            - pipe: atlassian/aws-code-deploy:1.5.1
              variables:
                AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
                AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
                AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION}
                COMMAND: 'deploy'
                APPLICATION_NAME: groovepacker-web-client
                DEPLOYMENT_GROUP: staging
                S3_BUCKET: groove-deployments-staging
                IGNORE_APPLICATION_STOP_FAILURES: 'true'
                FILE_EXISTS_BEHAVIOR: 'OVERWRITE'
                WAIT: 'true'
                DEBUG: 'true'
