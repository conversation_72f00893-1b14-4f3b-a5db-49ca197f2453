# Order Scan

## Order scan by barcode

input is barcode which is increment_id in the orders table

Example

`GET /scan_pack/scan_order_by_barcode`

Each barcode scan will check the status and provide you with associated next_state. Based on this the client can 

 select which state it uses next 


```js

```


## Order Edit Confirmation Code

Use this action to confirm code for order edits i.e., 


## Product Edit Confirmation Code

Use this action to confirm code for product_edits

## Product Scan

Coming up
