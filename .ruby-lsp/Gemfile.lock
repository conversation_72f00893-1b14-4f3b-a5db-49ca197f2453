GEM
  remote: http://rubygems.org/
  specs:
    actionmailer (********)
      actionpack (= ********)
      mail (~> 2.5.4)
    actionpack (********)
      activemodel (= ********)
      activesupport (= ********)
      builder (~> 3.0.0)
      erubis (~> 2.7.0)
      journey (~> 1.0.4)
      rack (~> 1.4.5)
      rack-cache (~> 1.2)
      rack-test (~> 0.6.1)
      sprockets (~> 2.2.1)
    activemodel (********)
      activesupport (= ********)
      builder (~> 3.0.0)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
      arel (~> 3.0.2)
      tzinfo (~> 0.3.29)
    activeresource (********)
      activemodel (= ********)
      activesupport (= ********)
    activesupport (********)
      i18n (~> 0.6, >= 0.6.4)
      multi_json (~> 1.0)
    angular-ui-bootstrap-rails (0.11.0)
    angularjs-rails (1.2.18)
    arel (3.0.3)
    better_errors (1.0.1)
      coderay (>= 1.0.0)
      erubis (>= 2.6.6)
    binding_of_caller (0.7.2)
      debug_inspector (>= 0.0.1)
    bootstrap-sass (*******)
      sass (~> 3.2)
    builder (3.0.4)
    byebug (9.0.6)
    celluloid (0.15.1)
      timers (~> 1.1.0)
    coderay (1.1.0)
    coffee-rails (3.2.2)
      coffee-script (>= 2.2.0)
      railties (~> 3.2.0)
    coffee-script (2.2.0)
      coffee-script-source
      execjs
    coffee-script-source (1.6.3)
    concurrent-ruby (1.2.2)
    debug_inspector (0.0.2)
    dotenv (2.0.1)
    dotenv-rails (2.0.1)
      dotenv (= 2.0.1)
    erubis (2.7.0)
    execjs (2.9.0)
    ffi (1.15.5)
    fingerprintjs-rails (0.5.3)
    hike (1.2.3)
    httparty (0.15.6)
      multi_xml (>= 0.5.2)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    intercom-rails (0.2.28)
      activesupport (> 3.0)
    journey (1.0.4)
    jquery-fileupload-rails (0.4.1)
      actionpack (>= 3.1)
      railties (>= 3.1)
    jquery-rails (3.0.4)
      railties (>= 3.0, < 5.0)
      thor (>= 0.14, < 2.0)
    jquery-ui-rails (4.0.5)
      railties (>= 3.1.0)
    json (1.8.6)
    listen (2.0.0)
      celluloid (>= 0.15.1)
      rb-fsevent (>= 0.9.3)
      rb-inotify (>= 0.9)
    mail (2.5.5)
      mime-types (~> 1.16)
      treetop (~> 1.4.8)
    method_source (0.8.2)
    mime-types (1.25.1)
    momentjs-rails (2.1.0)
      railties (>= 3.1)
    multi_json (1.7.9)
    multi_xml (0.6.0)
    ng-rails-csrf (0.1.0)
    polyglot (0.3.5)
    pry (0.10.1)
      coderay (~> 1.1.0)
      method_source (~> 0.8.1)
      slop (~> 3.4)
    quiet_assets (1.0.2)
      railties (>= 3.1, < 5.0)
    rack (1.4.7)
    rack-cache (1.15.0)
      rack (>= 0.4)
    rack-ssl (1.3.4)
      rack
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (********)
      actionmailer (= ********)
      actionpack (= ********)
      activerecord (= ********)
      activeresource (= ********)
      activesupport (= ********)
      bundler (~> 1.0)
      railties (= ********)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      rack-ssl (~> 1.3.2)
      rake (>= 0.8.7)
      rdoc (~> 3.4)
      thor (>= 0.14.6, < 2.0)
    rake (10.5.0)
    rb-fsevent (0.9.4)
    rb-inotify (0.9.3)
      ffi (>= 0.5.0)
    rdoc (3.12.2)
      json (~> 1.4)
    sass (3.4.25)
    sass-rails (3.2.6)
      railties (~> 3.2.0)
      sass (>= 3.1.10)
      tilt (~> 1.3)
    slop (3.6.0)
    spring (1.0.0)
    spring-commands-rspec (1.0.1)
      spring (>= 0.9.1)
    sprockets (2.2.3)
      hike (~> 1.2)
      multi_json (~> 1.0)
      rack (~> 1.0)
      tilt (~> 1.1, != 1.3.0)
    sqlite3 (1.3.11)
    thor (1.2.2)
    tilt (1.4.1)
    timers (1.1.0)
    treetop (1.4.15)
      polyglot
      polyglot (>= 0.3.1)
    tzinfo (0.3.55)
    uglifier (2.2.1)
      execjs (>= 0.3.0)
      multi_json (~> 1.0, >= 1.0.2)
    webrick (1.3.1)

PLATFORMS
  ruby

DEPENDENCIES
  angular-ui-bootstrap-rails
  angularjs-rails (>= 1.2.18)
  better_errors (>= 0.2.0)
  binding_of_caller (>= 0.6.8)
  bootstrap-sass (= 3.1)
  byebug (~> 9.0.6)
  coffee-rails (~> 3.2.1)
  dotenv-rails
  fingerprintjs-rails
  httparty
  intercom-rails
  jquery-fileupload-rails
  jquery-rails
  jquery-ui-rails
  json
  listen
  momentjs-rails
  ng-rails-csrf
  pry
  quiet_assets (>= 1.0.1)
  rails (~> 3.2, >= ********)
  rake (< 11)
  sass-rails (~> 3.2.6)
  spring-commands-rspec
  sqlite3
  uglifier (>= 1.0.3)
  webrick (~> 1.3.1)

RUBY VERSION
   ruby 2.6.2p47

BUNDLED WITH
   1.17.3
