source 'http://rubygems.org'

gem 'rails', '~> 3.2', '>= 3.2.22.5'

# Bundle edge Rails instead:
# gem 'rails', :git => 'git://github.com/rails/rails.git'

ruby '2.6.2'

gem 'sqlite3'

gem 'json'

# Gems used only for assets and not required
# in production environments by default.
group :assets do
  gem 'sass-rails',   '~> 3.2.6'
  gem 'coffee-rails', '~> 3.2.1'
  gem 'uglifier', '>= 1.0.3'
  gem 'jquery-fileupload-rails'
  gem 'jquery-ui-rails'
  # gem 'therubyracer'#,'~> 0.11.4', :platform => :ruby
  gem 'angularjs-rails','>= 1.2.18'
  gem 'jquery-rails'
  # gem 'less-rails-bootstrap'
  gem 'bootstrap-sass', '3.1'
  gem 'angular-ui-bootstrap-rails'
  gem 'momentjs-rails'
  gem 'fingerprintjs-rails'
end

group :development do
  gem 'webrick', '~> 1.3.1'
  gem 'quiet_assets', '>= 1.0.1'
  gem 'better_errors', '>= 0.2.0'
  gem 'binding_of_caller', '>= 0.6.8'
end

group :development,:test do
  # gem 'rspec-rails', '>= 2.13.2'
  # gem 'rspec_junit_formatter'
  # gem 'factory_girl_rails', '>= 4.1.0'
  # gem 'protractor-rails'
  gem 'pry'
  # gem 'parallel_tests'
  gem 'byebug', '~> 9.0.6'
end

# group :test do
#   gem 'database_cleaner', '>= 0.9.1'
#   gem 'email_spec', '>= 1.4.0'
#   gem 'launchy', '>= 2.1.2'
#   gem 'cucumber-rails', '>= 1.3.0', :require => false
#   gem 'capybara', '>= 2.0.1'
#   gem 'mocha', :require => 'mocha/api'
#   gem 'test-unit'
#   gem "codeclimate-test-reporter", require: nil
# end

gem 'spring-commands-rspec'
gem 'listen'
gem 'intercom-rails'
gem 'ng-rails-csrf'
gem 'dotenv-rails'
gem 'httparty'

gem 'rake', '< 11'
